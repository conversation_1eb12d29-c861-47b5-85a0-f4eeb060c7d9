## 单元测试规则

## 一、基本原则规范
- **AIR原则**
    - **自动化（Automatic）**：测试全自动执行，通过断言验证结果，禁止人工检查。
    - **独立性（Independent）**：测试之间无依赖，可单独运行；分层测试（DAO/Service/Controller隔离）。
    - **可重复（Repeatable）**：不受外部环境（数据库、网络）影响，每次结果一致。

- **BCDE设计原则**
    - **边界值（Border）**：覆盖循环边界、特殊取值（如`null`、空集合）。
    - **正确性（Correct）**：验证合法输入是否输出预期结果。
    - **文档结合（Design）**：测试用例需与设计文档逻辑对齐。
    - **错误路径（Error）**：强制覆盖非法输入、异常流程（如抛出`IllegalArgumentException`）。

### 二 、依赖
| 依赖 | 说明 | Maven示例                                    |
|------|------|--------------------------------------------|
| JUnit Jupiter | 核心测试引擎 | `org.junit.jupiter:junit-jupiter:3.14.0`   |
| Mockito Core | Mock对象创建与行为模拟 | `org.mockito:mockito-core:3.14.0`          |
| Mockito JUnit Jupiter | 注解支持与集成扩展 | `org.mockito:mockito-junit-jupiter:3.14.0` |
| AssertJ（可选） | 流式断言提升可读性 | `org.assertj:assertj-core:3.24.2`          |

**注**：避免混用JUnit 4与5，需排除`junit-vintage-engine`。

## 三、测试代码结构规范

### 1. 测试类命名
- **格式**：被测试类名 + Test（如`UserServiceTest`）。
- **位置**：`src/test/java`下相同包结构。

### 2. 测试方法命名
- **格式**：方法名_场景_预期结果（如`getUserById_notExist_throwException`）。
- **使用`@DisplayName`注解补充描述**（如"当用户ID不存在时抛出异常"）。

### 3. 生命周期注解
- `@BeforeAll` // 初始化全局资源（如数据库连接）
- `@BeforeEach` // 每个测试前的准备（如重置Mock）
- `@AfterEach`  // 清理临时数据
- `@AfterAll`   // 释放资源

## 四、Mockito使用规范

### 1. Mock对象创建
- **优先使用注解**：
```
@ExtendWith(MockitoExtension.class) // 启用Mockito支持
class UserServiceTest {
@Mock
private UserDao userDao;
@InjectMocks
private UserService userService; // 自动注入Mock依赖
}
```
避免手动Mockito.mock()，减少样板代码。

### 2. Stubbing行为定义
- **明确指定返回值/异常**：
```
when(userDao.findById(1L)).thenReturn(Optional.of(new User())); // 正常返回
when(userDao.save(any())).thenThrow(DataAccessException.class); // 模拟异常
```
```java
   // 遇到  DataMapper  接口时， 这样进行埋点数据 使用两次 when  最后返回要mock 的对象
when(qpQuotationDataMapper.entity(QpQuotation.class)).thenReturn(qpQuotationDataMapper);
when(qpQuotationDataMapper.entity(QpQuotation.class).selectOne(quotationId, true)).thenReturn(qpQuotation);
```
```java
   // 遇到使用 BQLQueryFactory 时，结合代码参考该规则
List<SameOccNumDto> sameOccNumDtoList = new ArrayList<>();
  sameOccNumDtoList.add(new SameOccNumDto(1L, "Engineer"));
        sameOccNumDtoList.add(new SameOccNumDto(2L, "Engineer"));
BQLQuery<Tuple> bqlQuery = mock(BQLQuery.class);
when(bqlQueryFactory.select(any(), any())).thenReturn(bqlQuery);
when(bqlQuery.from((Expression<?>) any())).thenReturn(bqlQuery);
when(bqlQuery.leftJoin(any())).thenReturn(bqlQuery);
when(bqlQuery.on(any(Predicate.class))).thenReturn(bqlQuery);
when(bqlQuery.where(any(Predicate.class))).thenReturn(bqlQuery);
when(bqlQuery.findList(SameOccNumDto.class)).thenReturn(sameOccNumDtoList);

```

### 参考单元测试文件

```java

package com.insgeek.business.quote.feilv;

/**
 * @Description:
 * @Date: 2025-06-09  15:26
 * @Author: YuanSiYuan
 */

import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.entity.QpQuote;
import com.insgeek.protocol.data.client.entity.QpQuoteInfo;
import com.insgeek.protocol.platform.common.dto.entity.IgDuty;
import com.insgeek.protocol.platform.common.dto.entity.IgPlanConfig;
import com.insgeek.protocol.platform.rule.client.RuleClient;
import com.insgeek.protocol.platform.rule.client.RuleExecuteClient;
import com.insgeek.protocol.platform.rule.dto.RuleInstanceDto;
import com.insgeek.protocol.platform.rule.dto.RuleStreamRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FeilvEmployerConfigServiceTest {

    @InjectMocks
    private FeilvEmployerConfigService feilvEmployerConfigService;
    @Mock
    RuleExecuteClient ruleExecuteClient;
    @Mock
    RuleClient ruleClient;
    @Mock
    private DataMapper<QpQuoteInfo> qpQuoteInfoDataMapper;
    @Mock
    private DataMapper<QpQuote> qpQuoteDataMapper;
    @Mock
    private DataMapper<IgPlanConfig> igPlanConfigDataMapper;
    @Mock
    private DataMapper<IgDuty> igDutyDataMapper;

    private static final Long TEST_QUOTE_ID = 1L;
    private static final Long CONFIG_ID = 100L;

    @BeforeEach
    void setUp() {
        // 初始化通用mock行为
        when(qpQuoteDataMapper.entity(QpQuote.class)).thenReturn(qpQuoteDataMapper);
        when(igPlanConfigDataMapper.entity(IgPlanConfig.class)).thenReturn(igPlanConfigDataMapper);
        when(igDutyDataMapper.entity(IgDuty.class)).thenReturn(igDutyDataMapper);
        when(qpQuoteInfoDataMapper.entity(QpQuoteInfo.class)).thenReturn(qpQuoteInfoDataMapper);
    }

    @Test
    @DisplayName("当正常流程时返回费率计算结果")
    void testGetFeilvReturnDtos_normalCase() {
        // 准备测试数据
        List<Long> configLists = Collections.singletonList(CONFIG_ID);

        // 创建模拟的IgPlanConfig对象
        IgPlanConfig planConfig = new IgPlanConfig();
        planConfig.setId(CONFIG_ID);
        planConfig.setName("测试计划");

        // 创建模拟的QpQuote对象
        QpQuote qpQuote = new QpQuote();
        qpQuote.setId(TEST_QUOTE_ID);
        qpQuote.setQuoteInfoId(1000L);
        qpQuote.setOccupation("IT");
        qpQuote.setExpandHospitalization("1");
        qpQuote.setAddGrant("1");
        qpQuote.setOpeningInsuredCount(10);
        qpQuote.setOpeningAvgAge(35);
        qpQuote.setOpeningFemaleProportion(new BigDecimal("0.5"));
        qpQuote.setExpandTwentyFour("1");

        // 创建模拟的QpQuoteInfo对象
        QpQuoteInfo qpQuoteInfo = new QpQuoteInfo();
        qpQuoteInfo.setId(1000L);
        qpQuoteInfo.setEnterpriseNature("私营");

        // 创建模拟的IgDuty对象
        IgDuty igDuty = new IgDuty();
        igDuty.setPlanConfigId(CONFIG_ID);

        // 配置mock行为
        when(igPlanConfigDataMapper.select(configLists, true)).thenReturn(Collections.singletonList(planConfig));
        when(qpQuoteDataMapper.selectOne(TEST_QUOTE_ID, true)).thenReturn(qpQuote);
        when(qpQuoteInfoDataMapper.selectOne(1000L, true)).thenReturn(qpQuoteInfo);
        when(igDutyDataMapper.select(any(DataCondition.class), eq(true))).thenReturn(Collections.singletonList(igDuty));

        // 模拟规则执行结果
        RuleInstanceDto ruleInstanceDto = new RuleInstanceDto();
        ruleInstanceDto.setId(2629745579843800763L);

        ResponseVO responseVO = new ResponseVO();
        responseVO.setData(Collections.singletonList(ruleInstanceDto));
        when(ruleExecuteClient.execute(anyLong(), any(RuleStreamRequest.class))).thenReturn(responseVO);

        // 模拟规则结果查询
        Map<Long, Map<String, String>> mockResult = new HashMap<>();
        mockResult.put(2629745579843800763L, Collections.singletonMap("factor", "1.2"));

        ResponseVO responseVO1 = new ResponseVO<>();
        responseVO1.setData(mockResult);
        when(ruleClient.findEnvironmentVariablesValuePostSecond(anyList()))
                .thenReturn(responseVO1);

        // 执行测试
        Map<Long, Map<String, String>> result = feilvEmployerConfigService.getFeilvReturnDtos(TEST_QUOTE_ID, configLists);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(2629745579843800763L)).containsEntry("factor", "1.2");

        // 验证交互次数
        verify(igPlanConfigDataMapper, times(1)).select(configLists, true);
        verify(qpQuoteDataMapper, times(1)).selectOne(TEST_QUOTE_ID, true);
        verify(ruleExecuteClient, times(1)).execute(anyLong(), any(RuleStreamRequest.class));
    }
}
```

- **边界场景**：覆盖空集合、超时、连续调用（thenReturn().thenThrow()）。

### 3. 验证交互行为
- **使用verify()检查调用次数与参数**：
```
verify(userDao, times(1)).deleteById(1L); // 精确调用1次
verify(userDao, never()).update(any());   // 从未调用
```

## 五、断言与验证规范

### 1. 断言选择优先级
- **JUnit 5断言**：基础验证（assertEquals、assertThrows）。
- 断言只需要一次
``` java
assertThat(actual).isEqualTo(expected);

```

### 2. 验证顺序与冗余调用
- **顺序验证**：`InOrder inOrder = inOrder(mockA, mockB);`
- **禁止冗余调用**：`verifyNoMoreInteractions(mockObj);`

## 六、测试数据管理

### 1. 参数化测试
``` java
@ParameterizedTest
@CsvSource({"1, true", "2, false"})
void testUserActiveStatus(Long userId, boolean expected) {
when(userDao.isActive(userId)).thenReturn(expected);
assertThat(userService.isUserActive(userId)).isEqualTo(expected);
}
```

### 2. 测试数据工厂
- 使用javafaker 或静态工厂方法生成随机数据，避免硬编码。

## 七、持续集成与报告

### 八、覆盖率要求
- **粗粒度**：类/方法覆盖率 ≥65%。
- **细粒度**：分支/条件覆盖率 ≥60%（JaCoCo统计）。 



