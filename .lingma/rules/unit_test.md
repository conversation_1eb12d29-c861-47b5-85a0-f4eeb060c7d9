## 单元测试规则

### 一、基本原则规范

#### 1.1 AIR原则
- **自动化（Automatic）**：测试全自动执行，通过断言验证结果，禁止人工检查。
- **独立性（Independent）**：测试之间无依赖，可单独运行；分层测试（DAO/Service/Controller隔离）。
- **可重复（Repeatable）**：不受外部环境（数据库、网络）影响，每次结果一致。

#### 1.2 BCDE设计原则
- **边界值（Border）**：覆盖循环边界、特殊取值（如`null`、空集合）。
- **正确性（Correct）**：验证合法输入是否输出预期结果。
- **文档结合（Design）**：测试用例需与设计文档逻辑对齐。
- **错误路径（Error）**：强制覆盖非法输入、异常流程（如抛出`IllegalArgumentException`）。

### 二、依赖管理

#### 2.1 核心依赖

| 依赖                    | 说明            | Maven示例                                      |
|-----------------------|---------------|----------------------------------------------|
| JUnit Jupiter         | 核心测试引擎        | `org.junit.jupiter:junit-jupiter:5.10.1`     |
| Mockito Core          | Mock对象创建与行为模拟 | `org.mockito:mockito-core:5.7.0`            |
| Mockito JUnit Jupiter | 注解支持与集成扩展     | `org.mockito:mockito-junit-jupiter:5.7.0`   |
| AssertJ               | 流式断言提升可读性     | `org.assertj:assertj-core:3.24.2`           |

**注意**：
- 避免混用JUnit 4与5，需排除`junit-vintage-engine`

### 三、测试代码结构规范

#### 3.1 测试类命名与组织

- **格式**：被测试类名 + Test（如`UserServiceTest`）
- **位置**：`src/test/java`下相同包结构
- **文件管理**：新增单元测试时，如果测试文件已存在，则在相同文件下新增测试用例；如果不存在，则需要自动新建文件

#### 3.2 测试方法命名

- **格式**：方法名_场景_预期结果（如`getUserById_notExist_throwException`）
- **使用`@DisplayName`注解补充中文描述**（如"当用户ID不存在时抛出异常"）
- **Given-When-Then模式**：测试方法内部按照Given（准备）、When（执行）、Then（验证）结构组织

```java
@Test
@DisplayName("当用户ID不存在时应抛出异常")
void getUserById_notExist_throwException() {
    // Given
    Long userId = 999L;
    when(userDao.findById(userId)).thenReturn(Optional.empty());
    
    // When & Then
    assertThrows(UserNotFoundException.class, 
        () -> userService.getUserById(userId));
}
```

#### 3.3 生命周期注解

- `@BeforeAll` - 初始化全局资源（如数据库连接）
- `@BeforeEach` - 每个测试前的准备（如重置Mock）
- `@AfterEach` - 清理临时数据
- `@AfterAll` - 释放资源
- `@Timeout` - 设置测试超时时间（如`@Timeout(5)`）

### 四、Mockito使用规范

#### 4.1 Mock对象创建

**优先使用注解方式**：

```java
@ExtendWith(MockitoExtension.class) // 启用Mockito支持
class UserServiceTest {
    @Mock
    private UserDao userDao;
    
    @InjectMocks
    private UserService userService; // 自动注入Mock依赖
}
```

**Spring环境下使用@MockBean**：

```java
@SpringBootTest
class UserServiceIntegrationTest {
    @MockBean
    private UserDao userDao; // Spring容器中的Mock Bean
    
    @Autowired
    private UserService userService;
}
```

**选择标准**：
- 纯单元测试：使用`@Mock`
- Spring集成测试：使用`@MockBean`
- 避免手动`Mockito.mock()`，减少样板代码

#### 4.2 Stubbing行为定义

**基础Stubbing**：

```java
// 正常返回
when(userDao.findById(1L)).thenReturn(Optional.of(new User()));
// 模拟异常
when(userDao.save(any())).thenThrow(new DataAccessException("Database error"));
// 连续调用
when(userDao.count()).thenReturn(10L).thenReturn(11L).thenThrow(new RuntimeException());
```

**DataMapper接口Mock**：

```java
// 链式调用Mock
when(qpQuotationDataMapper.entity(QpQuotation.class)).thenReturn(qpQuotationDataMapper);
when(qpQuotationDataMapper.entity(QpQuotation.class).selectOne(quotationId, true))
    .thenReturn(qpQuotation);
```

**BQLQueryFactory Mock**：

```java
// 复杂查询链Mock
List<SameOccNumDto> sameOccNumDtoList = Arrays.asList(
    new SameOccNumDto(1L, "Engineer"),
    new SameOccNumDto(2L, "Engineer")
);
BQLQuery<Tuple> bqlQuery = mock(BQLQuery.class);
when(bqlQueryFactory.select(any(), any())).thenReturn(bqlQuery);
when(bqlQuery.from((Expression<?>) any())).thenReturn(bqlQuery);
when(bqlQuery.leftJoin(any())).thenReturn(bqlQuery);
when(bqlQuery.on(any(Predicate.class))).thenReturn(bqlQuery);
when(bqlQuery.where(any(Predicate.class))).thenReturn(bqlQuery);
when(bqlQuery.findList(SameOccNumDto.class)).thenReturn(sameOccNumDtoList);
```

**静态方法Mock（推荐方式）**：

```java
@ExtendWith(MockitoExtension.class)
class UserServiceTest {
    @Mock
    private UserDao userDao;
    
    @InjectMocks
    private UserService userService;
    
    @Test
    void testWithStaticMock() {
        try (MockedStatic<MessageUtil> messageUtilMock = mockStatic(MessageUtil.class)) {
            // Given
            messageUtilMock.when(() -> MessageUtil.get(any())).thenReturn("测试消息");
            
            // When
            String result = userService.processMessage();
            
            // Then
            assertThat(result).contains("测试消息");
        } // 自动关闭MockedStatic
    }
}
```

**边界场景覆盖**：
- 空集合：`when(dao.findAll()).thenReturn(Collections.emptyList())`
- null值：`when(dao.findById(any())).thenReturn(null)`
- 超时：`when(service.slowMethod()).thenAnswer(invocation -> { Thread.sleep(1000); return "result"; })`

#### 4.3 验证交互行为

**基础验证**：

```java
// 精确调用次数
verify(userDao, times(1)).deleteById(1L);
// 从未调用
verify(userDao, never()).update(any());
// 至少调用一次
verify(userDao, atLeastOnce()).findById(any());
// 最多调用两次
verify(userDao, atMost(2)).save(any());
```

**参数验证**：

```java
// 精确参数匹配
verify(userDao).save(argThat(user -> user.getName().equals("张三")));
// 参数捕获
ArgumentCaptor<User> userCaptor = ArgumentCaptor.forClass(User.class);
verify(userDao).save(userCaptor.capture());
User capturedUser = userCaptor.getValue();
assertThat(capturedUser.getName()).isEqualTo("张三");
```

**顺序验证**：

```java
InOrder inOrder = inOrder(userDao, emailService);
inOrder.verify(userDao).save(any());
inOrder.verify(emailService).sendEmail(any());
```

**无多余交互验证**：

```java
verifyNoMoreInteractions(userDao);
```

### 五、断言与验证规范

#### 5.1 断言选择优先级

**JUnit 5断言**（基础验证）：

```java
// 基本断言
assertEquals(expected, actual);
assertTrue(condition);
assertNotNull(object);

// 异常断言
Exception exception = assertThrows(IllegalArgumentException.class, 
    () -> userService.createUser(null));
assertEquals("用户信息不能为空", exception.getMessage());

// 超时断言
assertTimeout(Duration.ofSeconds(2), () -> userService.slowOperation());
```

**AssertJ断言**（复杂验证，推荐）：

```java
// 对象断言
assertThat(user)
    .isNotNull()
    .extracting(User::getName, User::getAge)
    .containsExactly("张三", 25);

// 集合断言
assertThat(userList)
    .hasSize(2)
    .extracting(User::getName)
    .containsExactlyInAnyOrder("Alice", "Bob");

// 条件过滤
assertThat(userList)
    .filteredOn(user -> user.getAge() > 18)
    .hasSize(1);

// 异常断言
assertThatThrownBy(() -> userService.deleteUser(null))
    .isInstanceOf(IllegalArgumentException.class)
    .hasMessage("用户ID不能为空");
```

#### 5.2 自定义断言

```java
// 自定义断言方法
public static void assertUserEquals(User expected, User actual) {
    assertThat(actual)
        .usingRecursiveComparison()
        .ignoringFields("id", "createTime")
        .isEqualTo(expected);
}
```

### 六、测试数据管理

#### 6.1 参数化测试

**CSV数据源**：

```java
@ParameterizedTest
@CsvSource({
    "1, true, 张三",
    "2, false, 李四",
    "3, true, 王五"
})
@DisplayName("测试用户激活状态")
void testUserActiveStatus(Long userId, boolean expected, String userName) {
    // Given
    when(userDao.isActive(userId)).thenReturn(expected);
    
    // When
    boolean result = userService.isUserActive(userId);
    
    // Then
    assertThat(result).isEqualTo(expected);
}
```

**方法数据源**：

```java
@ParameterizedTest
@MethodSource("provideUserTestData")
void testUserValidation(User user, boolean expectedValid) {
    boolean result = userService.validateUser(user);
    assertThat(result).isEqualTo(expectedValid);
}

static Stream<Arguments> provideUserTestData() {
    return Stream.of(
        Arguments.of(new User("张三", 25), true),
        Arguments.of(new User("", 25), false),
        Arguments.of(new User("李四", -1), false)
    );
}
```

#### 6.2 测试数据工厂

**使用Builder模式**：

```java
public class UserTestDataBuilder {
    private String name = "默认用户";
    private Integer age = 25;
    private String email = "<EMAIL>";
    
    public static UserTestDataBuilder aUser() {
        return new UserTestDataBuilder();
    }
    
    public UserTestDataBuilder withName(String name) {
        this.name = name;
        return this;
    }
    
    public UserTestDataBuilder withAge(Integer age) {
        this.age = age;
        return this;
    }
    
    public User build() {
        return new User(name, age, email);
    }
}

// 使用示例
User user = aUser().withName("张三").withAge(30).build();
```

**使用JavaFaker**：

```java
public class TestDataFactory {
    private static final Faker faker = new Faker(Locale.CHINA);
    
    public static User createRandomUser() {
        return User.builder()
            .name(faker.name().fullName())
            .age(faker.number().numberBetween(18, 65))
            .email(faker.internet().emailAddress())
            .build();
    }
}
```

### 七、持续集成与报告

#### 7.1 覆盖率要求

- **类/方法覆盖率**：≥ 65%
- **分支/条件覆盖率**：≥ 60%
- **排除文件**：配置类、DTO、枚举等

### 八、最佳实践与常见问题

#### 8.1 测试类模板

```java
@ExtendWith(MockitoExtension.class)
@DisplayName("用户服务测试")
class UserServiceTest {
    
    @Mock
    private UserRepository userRepository;
    
    @Mock
    private EmailService emailService;
    
    @InjectMocks
    private UserService userService;
    
    @BeforeEach
    void setUp() {
        // 每个测试前的初始化
    }
    
    @Nested
    @DisplayName("创建用户测试")
    class CreateUserTest {
        
        @Test
        @DisplayName("正常创建用户")
        void shouldCreateUserSuccessfully() {
            // Given
            User user = aUser().withName("张三").build();
            when(userRepository.save(any(User.class))).thenReturn(user);
            
            // When
            User result = userService.createUser(user);
            
            // Then
            assertThat(result).isNotNull();
            verify(userRepository).save(user);
            verify(emailService).sendWelcomeEmail(user.getEmail());
        }
        
        @Test
        @DisplayName("用户信息为空时抛出异常")
        void shouldThrowExceptionWhenUserIsNull() {
            // When & Then
            assertThatThrownBy(() -> userService.createUser(null))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("用户信息不能为空");
        }
    }
}
```

#### 8.2 常见问题FAQ

**Q: 如何测试私有方法？**
A: 不直接测试私有方法，通过测试调用私有方法的公有方法来间接测试。

**Q: 如何处理静态方法依赖？**
A: 使用`MockedStatic`或将静态方法封装为可注入的服务。

**Q: 测试数据库操作时如何保证数据隔离？**
A: 使用`@Transactional`和`@Rollback`注解，或使用内存数据库H2。

**Q: 如何测试异步方法？**
A: 使用`CompletableFuture.get()`或`@Async`测试工具。

#### 8.3 性能测试

```java
@Test
@Timeout(value = 2, unit = TimeUnit.SECONDS)
void shouldCompleteWithinTimeout() {
    // 测试方法必须在2秒内完成
    userService.performSlowOperation();
}
```