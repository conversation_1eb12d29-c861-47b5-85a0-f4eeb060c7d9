{"item": [{"request": {"method": "GET", "description": "", "header": [], "url": {"path": ["quotation", "base", "query", ":entityKey"], "query": [], "host": "{{insgeek-business-quote}}", "raw": "{{insgeek-business-quote}}/quotation/base/query/{entityKey}"}}, "response": [{"originalRequest": {"method": "GET", "description": "", "header": [], "url": {"path": ["quotation", "base", "query", ":entityKey"], "query": [], "host": "{{insgeek-business-quote}}", "raw": "{{insgeek-business-quote}}/quotation/base/query/{entityKey}"}}, "_postman_previewlanguage": "json", "code": 200, "responseTime": 53, "name": "获取字段-Example", "header": [{"name": "date", "key": "date", "value": "周一, 12 6月 202310:30:16 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"code\": 0,\n    \"message\": \"\",\n    \"data\": {\n        /**\n         * 唯一标识\n         * compare_type=1，2 有数据\n         */\n        \"id\": 0,\n        /**\n         * 返回类型\n         * 1：不对比，2：两版本对比，3：多版本对比\n         */\n        \"compareType\": 0,\n        \"data\": { //返回值集合\n            \"key\": {\n                \"fieldCode\": \"\", //字段编码\n                \"fieldName\": \"\", //字段名称\n                \"name\": \"\", //字段名称\n                \"fieldType\": \"\", //字段类型\n                \"changeFlag\": false, //更变标识\n                \"val\": {}, //值\n                \"oldVal\": {}, //历史值\n                \"vals\": [ //组值\n                    {}\n                ],\n                \"order\": 0, //顺序\n                \"groupCode\": \"\", //组\n                \"group\": \"\", //组\n                \"stateFlag\": 0, //状态\n                \"displayConditions\": false, //显示条件\n                \"changeType\": 0, //0 无  1 增加 2 删除\n                \"options\": [\n                    {\n                        \"dictKey\": \"\",\n                        \"dictValue\": \"\",\n                        \"defaultFlg\": false\n                    }\n                ]\n            }\n        }\n    },\n    \"error\": {},\n    \"page\": {\n        \"is_start\": false,\n        \"is_end\": false,\n        \"total\": 0,\n        \"pageNum\": 0,\n        \"pageSize\": 0\n    }\n}", "status": "OK"}], "name": "获取字段"}, {"request": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n  \"id\": 0,\n  \"data\": {\n    \"\": {\n      \"fieldCode\": \"\",\n      \"changeFlag\": false,\n      \"val\": {},\n      \"oldVal\": {}\n    }\n  }\n}"}, "url": {"path": ["quotation", "base", "save", ":entity_key"], "query": [], "host": "{{insgeek-business-quote}}", "raw": "{{insgeek-business-quote}}/quotation/base/save/{entity_key}"}}, "response": [{"originalRequest": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n    \"id\": 0, //唯一标识\n    \"data\": { //数据\n        \"key\": {\n            \"fieldCode\": \"\", //字段编码\n            \"changeFlag\": false, //更变标识\n            \"val\": {}, //值\n            \"oldVal\": {} //历史值\n        }\n    }\n}"}, "url": {"query": [], "host": "{{insgeek-business-quote}}"}}, "_postman_previewlanguage": "json", "code": 200, "responseTime": 44, "name": "根据key保存数据-Example", "header": [{"name": "date", "key": "date", "value": "周一, 12 6月 202310:30:16 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"code\": 0,\n    \"message\": \"\",\n    \"data\": 0,\n    \"error\": {},\n    \"page\": {\n        \"is_start\": false,\n        \"is_end\": false,\n        \"total\": 0,\n        \"pageNum\": 0,\n        \"pageSize\": 0\n    }\n}", "status": "OK"}], "name": "根据key保存数据"}, {"request": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n  \"id\": 0,\n  \"data\": {\n    \"\": {\n      \"fieldCode\": \"\",\n      \"changeFlag\": false,\n      \"val\": {},\n      \"oldVal\": {}\n    }\n  }\n}"}, "url": {"path": ["quotation", "base", "query", ":entity_key"], "query": [], "host": "{{insgeek-business-quote}}", "raw": "{{insgeek-business-quote}}/quotation/base/query/{entity_key}"}}, "response": [{"originalRequest": {"method": "POST", "description": "", "header": [{"key": "Content-Type", "value": "application/json", "type": "text", "description": ""}], "body": {"mode": "raw", "options": {"raw": {"language": "json"}}, "raw": "{\n    \"id\": 0, //唯一标识\n    \"data\": { //数据\n        \"key\": {\n            \"fieldCode\": \"\", //字段编码\n            \"changeFlag\": false, //更变标识\n            \"val\": {}, //值\n            \"oldVal\": {} //历史值\n        }\n    }\n}"}, "url": {"query": [], "host": "{{insgeek-business-quote}}"}}, "_postman_previewlanguage": "json", "code": 200, "responseTime": 78, "name": "查询数据-Example", "header": [{"name": "date", "key": "date", "value": "周一, 12 6月 202310:30:16 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"code\": 0,\n    \"message\": \"\",\n    \"data\": [\n        {\n            /**\n             * 唯一标识\n             * compare_type=1，2 有数据\n             */\n            \"id\": 0,\n            /**\n             * 返回类型\n             * 1：不对比，2：两版本对比，3：多版本对比\n             */\n            \"compareType\": 0,\n            \"data\": { //返回值集合\n                \"key\": {\n                    \"fieldCode\": \"\", //字段编码\n                    \"fieldName\": \"\", //字段名称\n                    \"name\": \"\", //字段名称\n                    \"fieldType\": \"\", //字段类型\n                    \"changeFlag\": false, //更变标识\n                    \"val\": {}, //值\n                    \"oldVal\": {}, //历史值\n                    \"vals\": [ //组值\n                        {}\n                    ],\n                    \"order\": 0, //顺序\n                    \"groupCode\": \"\", //组\n                    \"group\": \"\", //组\n                    \"stateFlag\": 0, //状态\n                    \"displayConditions\": false, //显示条件\n                    \"changeType\": 0, //0 无  1 增加 2 删除\n                    \"options\": [\n                        {\n                            \"dictKey\": \"\",\n                            \"dictValue\": \"\",\n                            \"defaultFlg\": false\n                        }\n                    ]\n                }\n            }\n        }\n    ],\n    \"error\": {},\n    \"page\": {\n        \"is_start\": false,\n        \"is_end\": false,\n        \"total\": 0,\n        \"pageNum\": 0,\n        \"pageSize\": 0\n    }\n}", "status": "OK"}], "name": "查询数据"}, {"request": {"method": "GET", "description": "", "header": [], "url": {"path": ["quotation", "base", "query", ":entity_key", "by", ":id"], "query": [], "host": "{{insgeek-business-quote}}", "raw": "{{insgeek-business-quote}}/quotation/base/query/{entity_key}/by/{id}"}}, "response": [{"originalRequest": {"method": "GET", "description": "", "header": [], "url": {"path": ["quotation", "base", "query", ":entity_key", "by", ":id"], "query": [], "host": "{{insgeek-business-quote}}", "raw": "{{insgeek-business-quote}}/quotation/base/query/{entity_key}/by/{id}"}}, "_postman_previewlanguage": "json", "code": 200, "responseTime": 61, "name": "查询数据-Example", "header": [{"name": "date", "key": "date", "value": "周一, 12 6月 202310:30:16 GMT", "description": "The date and time that the message was sent"}, {"name": "server", "key": "server", "value": "Apache-Coyote/1.1", "description": "A name for the server"}, {"name": "transfer-encoding", "key": "transfer-encoding", "value": "chunked", "description": "The form of encoding used to safely transfer the entity to the user. Currently defined methods are: chunked, compress, deflate, gzip, identity."}, {"name": "content-type", "key": "content-type", "value": "application/json;charset=UTF-8"}], "body": "{\n    \"code\": 0,\n    \"message\": \"\",\n    \"data\": {\n        /**\n         * 唯一标识\n         * compare_type=1，2 有数据\n         */\n        \"id\": 0,\n        /**\n         * 返回类型\n         * 1：不对比，2：两版本对比，3：多版本对比\n         */\n        \"compareType\": 0,\n        \"data\": { //返回值集合\n            \"key\": {\n                \"fieldCode\": \"\", //字段编码\n                \"fieldName\": \"\", //字段名称\n                \"name\": \"\", //字段名称\n                \"fieldType\": \"\", //字段类型\n                \"changeFlag\": false, //更变标识\n                \"val\": {}, //值\n                \"oldVal\": {}, //历史值\n                \"vals\": [ //组值\n                    {}\n                ],\n                \"order\": 0, //顺序\n                \"groupCode\": \"\", //组\n                \"group\": \"\", //组\n                \"stateFlag\": 0, //状态\n                \"displayConditions\": false, //显示条件\n                \"changeType\": 0, //0 无  1 增加 2 删除\n                \"options\": [\n                    {\n                        \"dictKey\": \"\",\n                        \"dictValue\": \"\",\n                        \"defaultFlg\": false\n                    }\n                ]\n            }\n        }\n    },\n    \"error\": {},\n    \"page\": {\n        \"is_start\": false,\n        \"is_end\": false,\n        \"total\": 0,\n        \"pageNum\": 0,\n        \"pageSize\": 0\n    }\n}", "status": "OK"}], "name": "查询数据"}], "info": {"schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "name": "QuotationController-20230612103016", "description": "QuotationController"}}