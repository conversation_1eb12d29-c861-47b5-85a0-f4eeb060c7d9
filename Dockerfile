FROM harbor.insgeek.cn/base/centos:latest
MAINTAINER	 yangdong dong.yang@insgeek.1com

RUN mkdir -pv /data/{logs,target}


COPY target/APP.jar /data/target/



ENV JAVA_HOME /opt/jdk1.8.0_251
ENV PATH $PATH:/opt/jdk1.8.0_251/bin
ENV LANG C.UTF-8

WORKDIR /data/
ENTRYPOINT ["/bin/bash","-c","java -Dfile.encoding=utf-8 -Duser.timezone=GMT+08 \
-Dsentinel.datasource.file=/data/logs \
-javaagent:/data/skywalking-agent.jar=agent.service_name=${PROJECT_NAME},collector.backend_service=${SW_SERVER} \
-Dspring.cloud.nacos.username=nacos -Dspring.cloud.nacos.password=${NACOS_PASSWD} \
-server -Xmx${Xmx}m -Xms${Xms}m -Xmn${Xmn}m -Xss512k -XX:ParallelGCThreads=20 \
-XX:+UseParNewGC -XX:+UseConcMarkSweepGC -XX:+HeapDumpOnOutOfMemoryError -XX:MaxGCPauseMillis=850 \
-XX:+CMSParallelRemarkEnabled -XX:CMSInitiatingOccupancyFraction=75 -Xloggc:/data/logs/gc.log \
-jar  /data/target/APP.jar"]