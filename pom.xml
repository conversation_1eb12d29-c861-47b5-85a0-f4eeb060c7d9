<?xml version="1.0" encoding="UTF-8"?>

<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.insgeek.boot</groupId>
        <artifactId>insgeek-boot</artifactId>
        <version>1.0.0-rc10</version>
    </parent>
    <groupId>com.insgeek.business</groupId>
    <artifactId>insgeek-business-quote</artifactId>
    <version>1.0.0-rc3</version>
    <description>insgeek-business-quote 询报价系统</description>

    <properties>
        <protocol-business>1.0.0-rc44</protocol-business>
        <protocol-business-insurance>1.0.0-rc69</protocol-business-insurance>
        <protocol-business-policy>1.0.0-rc18</protocol-business-policy>
        <protocol-business-group.version>1.0.0-rc5</protocol-business-group.version>
        <protocol-business-finance.version>1.0.0-rc10</protocol-business-finance.version>
        <protocol-business-quote.version>1.0.0-rc48</protocol-business-quote.version>
        <protocol-platform-user.version>1.0.0-rc17</protocol-platform-user.version>
        <protocol-platform-rule.version>1.0.0-rc5</protocol-platform-rule.version>
        <protocol-platform-common.version>1.0.0-rc11</protocol-platform-common.version>
        <protocol-platform-data.version>1.0.0-rc3</protocol-platform-data.version>
        <protocol-platform-metadata.version>1.0.0-rc2</protocol-platform-metadata.version>
        <insurance-meta-tools-version>1.0.0-rc4</insurance-meta-tools-version>
        <insurance-meta-util>1.0.0-rc1</insurance-meta-util>
        <protocol-platform-flow.version>1.0.0-rc2</protocol-platform-flow.version>
        <protocol-platform-message>1.0.0-rc2</protocol-platform-message>
        <!--    <redisson.version>3.5.0</redisson.version>-->
        <spring-mock.version>2.0.8</spring-mock.version>
        <powermock-module-junit4.version>2.0.0</powermock-module-junit4.version>
        <powermock-api-mockito2.version>2.0.0</powermock-api-mockito2.version>
        <insgeek-orm-version>1.0.0-rc5</insgeek-orm-version>
        <java.version>1.8</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <maven.compiler.encoding>UTF-8</maven.compiler.encoding>
        <org.redisson.redisson-spring-boot-starter>3.12.5</org.redisson.redisson-spring-boot-starter>
        <protocol-dataapp-bi.version>1.0.0-rc12</protocol-dataapp-bi.version>
        <cn.afterturn.easypoi.version>4.4.0</cn.afterturn.easypoi.version>
        <protocol-platform-integration.version>1.0.0-rc2</protocol-platform-integration.version>
        <xxl-job-core>2.2.0</xxl-job-core>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-mongodb</artifactId>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.google.zxing/core -->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.4.1</version>
        </dependency>
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-platform-fileservice</artifactId>
            <version>1.0.0-rc1</version>
        </dependency>
        <!-- platform-user  Begin -->
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-platform-data</artifactId>
            <version>${protocol-platform-data.version}</version>
        </dependency>
        <!-- platform-user  End -->
        <dependency>
            <groupId>com.insgeek.boot</groupId>
            <artifactId>insgeek-commons</artifactId>
            <version>1.0.0-rc16</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-freemarker</artifactId>
        </dependency>
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-platform-message</artifactId>
            <version>${protocol-platform-message}</version>
        </dependency>
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-platform-metadata</artifactId>
            <version>${protocol-platform-metadata.version}</version>
        </dependency>
        <!-- platform-user  Begin -->
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-platform-user</artifactId>
            <version>${protocol-platform-user.version}</version>
        </dependency>
        <!-- platform-user  End -->
        <!-- platform-flow  Begin -->
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-platform-flow</artifactId>
            <version>${protocol-platform-flow.version}</version>
        </dependency>
        <!-- platform-flow  End -->
        <!-- platform-rule  Begin -->
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-platform-rule</artifactId>
            <version>${protocol-platform-rule.version}</version>
        </dependency>
        <!-- platform-rule  End -->
        <!-- platform-common  Begin -->
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-platform-common</artifactId>
            <version>${protocol-platform-common.version}</version>
        </dependency>
        <!-- platform-common  End -->
        <!-- insurance  Begin -->
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-business-insurance</artifactId>
            <version>${protocol-business-insurance}</version>
        </dependency>
        <!-- insurance  End -->
        <!-- quote  Begin -->
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-business-quote</artifactId>
            <version>${protocol-business-quote.version}</version>
        </dependency>
        <!-- quote End -->
        <!-- quote  Begin -->
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-business-policy</artifactId>
            <version>${protocol-business-policy}</version>
        </dependency>
        <!-- quote End -->
        <!-- finance  Begin -->
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-business-finance</artifactId>
            <version>${protocol-business-finance.version}</version>
        </dependency>
        <!-- finance End -->
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-business</artifactId>
            <version>${protocol-business}</version>
        </dependency>
        <!-- Spring Begin -->
        <dependency>
            <groupId>com.insgeek.boot</groupId>
            <artifactId>insgeek-boot-starter-web</artifactId>
            <!--<version>0.0.3-rc51</version>-->
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-mock</artifactId>
            <version>${spring-mock.version}</version>
        </dependency>
        <!-- Spring End -->
        <!--lombok-->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        <!--单元测试支持 Begin -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.10.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.12.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>3.12.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>5.10.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>3.12.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-inline</artifactId>
            <version>3.12.4</version>
            <scope>test</scope>
        </dependency>
        <!--单元测试支持 End -->
        <!-- RocketMq -->
        <dependency>
            <groupId>com.insgeek.boot</groupId>
            <artifactId>insgeek-boot-starter-mq</artifactId>
            <version>0.0.3-rc37</version>
            <exclusions>
                <exclusion>
                    <groupId>org.redisson</groupId>
                    <artifactId>redisson</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>hutool-all</artifactId>
                    <groupId>cn.hutool</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- redis  Begin-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>${org.redisson.redisson-spring-boot-starter}</version>
        </dependency>
        <!-- redis  end-->
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-business-group</artifactId>
            <version>${protocol-business-group.version}</version>
        </dependency>
        <!--easypoi begin-->
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
            <version>${cn.afterturn.easypoi.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-annotation</artifactId>
            <version>${cn.afterturn.easypoi.version}</version>
        </dependency>
        <!--easypoi end-->
        <dependency>
            <groupId>com.insgeek.insurance.meta</groupId>
            <artifactId>insurance-meta-tools</artifactId>
            <version>${insurance-meta-tools-version}</version>
        </dependency>
        <dependency>
            <groupId>com.insgeek.components</groupId>
            <artifactId>insgeek-orm</artifactId>
            <version>${insgeek-orm-version}</version>
        </dependency>
        <dependency>
            <groupId>com.insgeek.insurance.meta</groupId>
            <artifactId>insurance-meta-util</artifactId>
            <version>${insurance-meta-util}</version>
        </dependency>
        <dependency>
            <groupId>com.scireum</groupId>
            <artifactId>parsii</artifactId>
            <version>4.0</version>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>${org.redisson.redisson-spring-boot-starter}</version>
        </dependency>
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-dataapp-bi</artifactId>
            <version>${protocol-dataapp-bi.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>protocol-platform-common</artifactId>
                    <groupId>com.insgeek.protocol</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.freemarker</groupId>
            <artifactId>freemarker</artifactId>
            <version>2.3.31</version>
        </dependency>
        <dependency>
            <groupId>com.insgeek.protocol</groupId>
            <artifactId>protocol-platform-integration</artifactId>
            <version>${protocol-platform-integration.version}</version>
        </dependency>
        <dependency>
            <groupId>com.xuxueli</groupId>
            <artifactId>xxl-job-core</artifactId>
            <version>${xxl-job-core}</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-web</artifactId>
            <version>2.17.1</version>
        </dependency>

    </dependencies>
    <profiles>
        <profile>
            <id>sonar</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <sonar.host.url>http://sonarqube.insgeek.cn</sonar.host.url>
                <sonar.login>admin</sonar.login>
                <sonar.password>rZXYoahooS2Euu6B</sonar.password>
                <sonar.projectName>insgeek-business-quote-DEV</sonar.projectName>
            </properties>
        </profile>
    </profiles>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.0</version>
                <configuration>
                    <compilerArgs>
                        <arg>-Xmaxerrs</arg>
                        <arg>1000</arg>
                        <arg>-Xmaxwarns</arg>
                        <arg>2000</arg>
                    </compilerArgs>
                    <source>8</source>
                    <target>8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.sonarsource.scanner.maven</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>3.8.0.2131</version>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>sonar-maven-plugin</artifactId>
                <version>2.0</version>
            </plugin>
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <version>0.7.5.201505241946</version>
                <executions>
                    <execution>
                        <id>default-prepare-agent</id>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-report</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>default-check</id>
                        <phase>prepare-package</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <rule>
                                    <element>CLASS</element>
                                    <includes>
                                        <include>com.insgeek.*.*</include>
                                    </includes>
                                    <limits>
                                        <limit>
                                            <counter>LINE</counter>
                                            <value>COVEREDRATIO</value>
                                            <minimum>0.00</minimum>
                                        </limit>
                                    </limits>
                                </rule>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
