package com.insgeek.business.standard.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;

import java.time.LocalDate;
import java.time.format.DateTimeParseException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;

@DisplayName("AgeCalculatorUtil 测试")
class AgeCalculatorUtilTest {

    @Nested
    @DisplayName("calculateAge 方法测试")
    class CalculateAgeTest {


        @Test
        @DisplayName("计算今天出生的年龄")
        void testCalculateAgeBornToday() {
            String today = LocalDate.now().toString();
            int age = AgeCalculatorUtil.calculateAge(today);
            assertThat(age).isZero();
        }

        @Test
        @DisplayName("计算昨天出生的年龄")
        void testCalculateAgeBornYesterday() {
            String yesterday = LocalDate.now().minusDays(1).toString();
            int age = AgeCalculatorUtil.calculateAge(yesterday);
            assertThat(age).isZero();
        }

        @ParameterizedTest
        @ValueSource(strings = {
                "invalid-date",
                "2022/01/01",
                "01-01-2022",
                "2022.01.01",
                "22-01-01"
        })
        @DisplayName("无效日期格式抛出异常")
        void testCalculateAgeInvalidDateFormat(String invalidDate) {
            assertThatThrownBy(() -> AgeCalculatorUtil.calculateAge(invalidDate))
                    .isInstanceOf(IllegalArgumentException.class)
                    .hasMessageContaining("date format error, example : yyyy-MM-dd")
                    .hasCauseInstanceOf(DateTimeParseException.class);
        }

        @Test
        @DisplayName("未来日期计算年龄")
        void testCalculateAgeFutureDate() {
            String futureDate = LocalDate.now().plusYears(1).toString();
            int age = AgeCalculatorUtil.calculateAge(futureDate);
            assertThat(age).isEqualTo(-1);
        }
    }

    @Test
    @DisplayName("测试createAgeCalculatorUtil方法")
    void testCreateAgeCalculatorUtil() {
        AgeCalculatorUtil util = AgeCalculatorUtil.createAgeCalculatorUtil();
        assertThat(util).isNotNull();
    }
}