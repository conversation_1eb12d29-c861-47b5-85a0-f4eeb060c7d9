package com.insgeek.business.standard.util;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * @Description: PersonNumUtils单元测试类
 * @Date: 2025-08-11
 */
class PersonNumUtilsTest {

    @Test
    void getMaleNum_withPositiveEvenNumber_returnsHalf() {
        // 测试正偶数情况
        assertEquals(5, PersonNumUtils.getMaleNum(10));
        assertEquals(1, PersonNumUtils.getMaleNum(2));
    }

    @Test
    void getMaleNum_withPositiveOddNumber_returnsHalfPlusOne() {
        // 测试正奇数情况，余数分配给男性
        assertEquals(6, PersonNumUtils.getMaleNum(11));
        assertEquals(1, PersonNumUtils.getMaleNum(1));
        assertEquals(3, PersonNumUtils.getMaleNum(5));
    }

    @Test
    void getMaleNum_withZeroOrNegative_returnsZero() {
        // 测试零或负数情况
        assertEquals(0, PersonNumUtils.getMaleNum(0));
        assertEquals(0, PersonNumUtils.getMaleNum(-1));
        assertEquals(0, PersonNumUtils.getMaleNum(-10));
    }

    @Test
    void getFemaleNum_withPositiveEvenNumber_returnsHalf() {
        // 测试正偶数情况
        assertEquals(5, PersonNumUtils.getFemaleNum(10));
        assertEquals(1, PersonNumUtils.getFemaleNum(2));
    }

    @Test
    void getFemaleNum_withPositiveOddNumber_returnsHalfMinusOne() {
        // 测试正奇数情况，余数分配给男性，女性为一半向下取整
        assertEquals(5, PersonNumUtils.getFemaleNum(11));
        assertEquals(0, PersonNumUtils.getFemaleNum(1));
        assertEquals(2, PersonNumUtils.getFemaleNum(5));
    }

    @Test
    void getFemaleNum_withZeroOrNegative_returnsZero() {
        // 测试零或负数情况
        assertEquals(0, PersonNumUtils.getFemaleNum(0));
        assertEquals(0, PersonNumUtils.getFemaleNum(-1));
        assertEquals(0, PersonNumUtils.getFemaleNum(-10));
    }
}