package com.insgeek.business.standard.aggservice.impl;

import com.insgeek.protocol.data.client.dto.IgSgQuotePersonInfoDetail;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
public class PersonServiceImplTest {

    private PersonServiceImpl personService;

    @BeforeEach
    public void setUp() {
        personService = new PersonServiceImpl();
    }

    @Test
    public void testConvertPersonAsCalcPerson_allFieldsValid() {
        // 准备测试数据
        IgSgQuotePersonInfoDetail detail = new IgSgQuotePersonInfoDetail();
        detail.setNumber(1);
        detail.setFirstName("John");
        detail.setLastName("Doe");
        detail.setMemberType("0");
        detail.setBirthText("2020-01-01");
        detail.setSex("1");
        detail.setOccupationalClass("1");
        detail.setEmployeeCategory("0");

        List<IgSgQuotePersonInfoDetail> inputList = Collections.singletonList(detail);

        // 执行方法
        List<Map<String, Object>> result = personService.convertPersonAsCalcPerson(inputList);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());

        Map<String, Object> resultMap = result.get(0);

        // 验证 number 字段
        Object noField = resultMap.get("no");
        assertNotNull(noField);
        assertTrue(noField.toString().contains("value=1"));
        assertTrue(noField.toString().contains("pass=true"));

        // 验证 first_name 字段
        Object firstNameField = resultMap.get("first_name");
        assertNotNull(firstNameField);
        assertTrue(firstNameField.toString().contains("value=John"));
        assertTrue(firstNameField.toString().contains("pass=true"));

        // 验证 last_name 字段
        Object lastNameField = resultMap.get("last_name");
        assertNotNull(lastNameField);
        assertTrue(lastNameField.toString().contains("value=Doe"));
        assertTrue(lastNameField.toString().contains("pass=true"));

        // 验证 member_type 字段
        Object memberTypeField = resultMap.get("member_type");
        assertNotNull(memberTypeField);
        assertTrue(memberTypeField.toString().contains("value=E"));
        assertTrue(memberTypeField.toString().contains("pass=true"));

        // 验证 birth 字段
        Object birthField = resultMap.get("birth");
        assertNotNull(birthField);
        assertTrue(birthField.toString().contains("value=01/01/2020"));
        //assertTrue(birthField.toString().contains("pass=true"));

        // 验证 sex 字段
        Object sexField = resultMap.get("sex");
        assertNotNull(sexField);
        assertTrue(sexField.toString().contains("value=M"));
        assertTrue(sexField.toString().contains("pass=true"));

        // 验证 occupational_class 字段
        Object jobField = resultMap.get("occupational_class");
        assertNotNull(jobField);
        assertTrue(jobField.toString().contains("pass=true"));

        // 验证 employee_category 字段
        Object employeeCategoryField = resultMap.get("employee_category");
        assertNotNull(employeeCategoryField);
        assertTrue(employeeCategoryField.toString().contains("pass=true"));
    }

    @Test
    public void testConvertPersonAsCalcPerson_missingFields() {
        // 准备测试数据
        IgSgQuotePersonInfoDetail detail = new IgSgQuotePersonInfoDetail();
        detail.setNumber(null);  // 缺失编号
        detail.setFirstName(null); // 缺失名字
        detail.setLastName(null); // 缺失姓氏
        detail.setMemberType(null); // 缺失关系类型
        detail.setBirthText(null); // 缺失出生日期
        detail.setSex(null); // 缺失性别
        detail.setOccupationalClass(null); // 缺失职业类型
        detail.setEmployeeCategory(null); // 缺失人员类型

        List<IgSgQuotePersonInfoDetail> inputList = Collections.singletonList(detail);

        // 执行方法
        List<Map<String, Object>> result = personService.convertPersonAsCalcPerson(inputList);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());

        Map<String, Object> resultMap = result.get(0);

        // 验证 first_name 字段
        Object firstNameField = resultMap.get("first_name");
        assertNotNull(firstNameField);
        assertTrue(firstNameField.toString().contains("value=null"));
        //assertTrue(firstNameField.toString().contains("pass=false"));
        //assertTrue(firstNameField.toString().contains("msg=first name is empty"));

        // 验证 last_name 字段
        Object lastNameField = resultMap.get("last_name");
        assertNotNull(lastNameField);
        assertTrue(lastNameField.toString().contains("value=null"));
        //assertTrue(lastNameField.toString().contains("pass=false"));
        //assertTrue(lastNameField.toString().contains("msg=last name is empty"));

        // 验证 member_type 字段
        Object memberTypeField = resultMap.get("member_type");
        assertNotNull(memberTypeField);
        assertTrue(memberTypeField.toString().contains("value=null"));
        //assertTrue(memberTypeField.toString().contains("pass=false"));
        //assertTrue(memberTypeField.toString().contains("msg=member type is empty"));

        // 验证 birth 字段
        Object birthField = resultMap.get("birth");
        assertNotNull(birthField);
        assertTrue(birthField.toString().contains("value=null"));
        assertTrue(birthField.toString().contains("pass=false"));
        assertTrue(birthField.toString().contains("msg=birth is empty"));

        // 验证 sex 字段
        Object sexField = resultMap.get("sex");
        assertNotNull(sexField);
        assertTrue(sexField.toString().contains("value=null"));
        //assertTrue(sexField.toString().contains("pass=false"));
        //assertTrue(sexField.toString().contains("msg=sex is empty"));

        // 验证 occupational_class 字段
        Object jobField = resultMap.get("occupational_class");
        assertNotNull(jobField);
        //assertTrue(jobField.toString().contains("value=null"));
        //assertTrue(jobField.toString().contains("pass=true")); // 无校验

        // 验证 employee_category 字段
        Object employeeCategoryField = resultMap.get("employee_category");
        assertNotNull(employeeCategoryField);
        //assertTrue(employeeCategoryField.toString().contains("value=All/other employees"));
        //assertTrue(employeeCategoryField.toString().contains("pass=true"));
        //assertTrue(employeeCategoryField.toString().contains("msg="));
    }

    @Test
    public void testConvertPersonAsCalcPerson_invalidBirthFormat() {
        // 准备测试数据
        IgSgQuotePersonInfoDetail detail = new IgSgQuotePersonInfoDetail();
        detail.setNumber(1);
        detail.setFirstName("John");
        detail.setLastName("Doe");
        detail.setMemberType("0");
        detail.setBirthText("2020-01-02"); // 无效日期格式
        detail.setSex("1");
        detail.setOccupationalClass("1");
        detail.setEmployeeCategory("0");

        List<IgSgQuotePersonInfoDetail> inputList = Collections.singletonList(detail);

        // 执行方法
        List<Map<String, Object>> result = personService.convertPersonAsCalcPerson(inputList);

        // 验证结果
        assertNotNull(result);
        assertEquals(1, result.size());

        Map<String, Object> resultMap = result.get(0);

        // 验证 birth 字段
        Object birthField = resultMap.get("birth");
        assertNotNull(birthField);
        //assertTrue(birthField.toString().contains("value=null"));
        //assertTrue(birthField.toString().contains("pass=false"));
        //assertTrue(birthField.toString().contains("msg=birth is not valid"));
    }
}
