package com.insgeek.business.standard.service.impl;

import com.insgeek.boot.web.context.IdentityContext;
import com.insgeek.boot.web.pojo.dto.CurrencyAmount;
import com.insgeek.boot.web.util.MessageUtil;
import com.insgeek.business.quote.common.exception.QuoteException;
import com.insgeek.business.standard.aggservice.impl.StandardOrderServiceImpl;
import com.insgeek.business.standard.enums.StandardTypeEnum;
import com.insgeek.business.standard.interceptor.CustomContext;
import com.insgeek.business.standard.mapper.StandardOrderDetailMapper;
import com.insgeek.business.standard.mapper.StandardOrderMapper;
import com.insgeek.business.standard.mapper.StandardTenantInfoMapper;
import com.insgeek.business.standard.service.StandardPlanService;
import com.insgeek.protocol.data.client.dto.IgStandardOrder;
import com.insgeek.protocol.data.client.dto.IgStandardOrderDetail;
import com.insgeek.protocol.data.client.dto.IgStandardTenantInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class StandardOrderServiceImplTest {

    @InjectMocks
    private StandardOrderServiceImpl standardOrderService;

    @Mock
    private StandardOrderMapper standardOrderMapper;

    @Mock
    private StandardOrderDetailMapper standardOrderDetailMapper;

    @Mock
    private StandardPlanService standardPlanService;

    @Mock
    private StandardTenantInfoMapper standardTenantInfoMapper;

    @Mock
    private CustomContext customContext;

    @Mock
    private IdentityContext identityContext;

    @Mock
    private MessageUtil messageUtil;

    private static final Long USER_ID = 1001L;
    private static final Long ORDER_ID = 2001L;
    private static final Long CHANNEL_ID = 3001L;

    @BeforeEach
    public void setUp() {
    }

    @Test
    public void testGetInsuredProcessNotice_WhenOrderIsNull_ShouldThrowException() {
        Long orderId = 1L;

        // 模拟 standardOrderMapper 返回 null
        when(standardOrderMapper.entity(IgStandardOrder.class)).thenReturn(standardOrderMapper);
        when(standardOrderMapper.entity(IgStandardOrder.class).selectOne(orderId, true)).thenReturn(null);
        try (MockedStatic<MessageUtil> mockedStatic = mockStatic(MessageUtil.class)) {

            // 当调用静态方法时返回模拟的错误消息
            mockedStatic.when(() -> MessageUtil.get("b_b_quote_238"))
                    .thenReturn("订单不存在");

            // 执行测试方法，捕获异常
            QuoteException exception = assertThrows(QuoteException.class, () -> {
                standardOrderService.getInsuredProcessNotice(orderId);
            });

            // 验证异常信息和内容
            assertEquals(-1, exception.getCode());
            assertEquals("订单不存在", exception.getMessage());

        }
    }

    @Test
    public void testGetInsuredProcessNotice_WhenEmployerPlanAndNoDetails_ShouldThrowException() {
        Long orderId = 1L;
        IgStandardOrder standardOrder = mock(IgStandardOrder.class);
        when(standardOrder.getStandardType()).thenReturn(StandardTypeEnum.EMPLOYER_PLAN.getValue());

        when(standardOrderMapper.entity(IgStandardOrder.class)).thenReturn(standardOrderMapper);
        when(standardOrderMapper.entity(IgStandardOrder.class).selectOne(orderId, true)).thenReturn(standardOrder);
        when(standardOrderDetailMapper.getDetailListByOrderId(orderId)).thenReturn(Collections.emptyList());

        QuoteException exception = assertThrows(QuoteException.class, () -> {
            standardOrderService.getInsuredProcessNotice(orderId);
        });

        assertEquals(-1, exception.getCode());
    }

    @Test
    public void testGetInsuredProcessNotice_WhenEmployerPlanAndGetSuccessfully() {
        Long orderId = 1L;
        Long standardPlanId = 100L;

        IgStandardOrder standardOrder = mock(IgStandardOrder.class);
        when(standardOrder.getStandardType()).thenReturn(StandardTypeEnum.EMPLOYER_PLAN.getValue());

        IgStandardOrderDetail detail = mock(IgStandardOrderDetail.class);
        when(detail.getStandardPlanId()).thenReturn(standardPlanId);

        when(standardOrderMapper.entity(IgStandardOrder.class)).thenReturn(standardOrderMapper);
        when(standardOrderMapper.entity(IgStandardOrder.class).selectOne(orderId, true)).thenReturn(standardOrder);
        when(standardOrderDetailMapper.getDetailListByOrderId(orderId)).thenReturn(Collections.singletonList(detail));

        when(standardPlanService.getInsuredProcessNotice(standardPlanId)).thenReturn("投保须知内容");

        String result = standardOrderService.getInsuredProcessNotice(orderId);

        assertNotNull(result);
        assertEquals("投保须知内容", result);
    }

    @Test
    public void testGetInsuredProcessNotice_WhenNotEmployerPlanAndGetSuccessfully() {
        Long orderId = 1L;
        Long standardPlanId = 200L;

        IgStandardOrder standardOrder = mock(IgStandardOrder.class);
        when(standardOrder.getStandardType()).thenReturn("OTHER_TYPE");
        when(standardOrder.getStandardPlanId()).thenReturn(standardPlanId);

        when(standardOrderMapper.entity(IgStandardOrder.class)).thenReturn(standardOrderMapper);
        when(standardOrderMapper.entity(IgStandardOrder.class).selectOne(orderId, true)).thenReturn(standardOrder);

        when(standardPlanService.getInsuredProcessNotice(standardPlanId)).thenReturn("非雇主投保须知内容");

        String result = standardOrderService.getInsuredProcessNotice(orderId);

        assertNotNull(result);
        assertEquals("非雇主投保须知内容", result);
    }


    @Test
    public void testCopyOrder_WhenOrderIsNull_ShouldThrowQuoteException() {
        when(standardOrderMapper.entity(IgStandardOrder.class)).thenReturn(standardOrderMapper);
        when(standardOrderMapper.selectOne(ORDER_ID, true)).thenReturn(null);

        try (MockedStatic<MessageUtil> mockedStatic = mockStatic(MessageUtil.class)) {
            String expectedMessage = "订单不存在";
            mockedStatic.when(() -> MessageUtil.get("b_b_quote_238")).thenReturn(expectedMessage);

            QuoteException exception = assertThrows(QuoteException.class, () -> {
                standardOrderService.copyOrder(ORDER_ID);
            });

            assertEquals(-1, exception.getCode());
            assertEquals(expectedMessage, exception.getMessage());
        }

    }

    @Test
    public void testCopyOrder_WhenBrokerIdNotMatch_ShouldThrowQuoteException() {
        IgStandardOrder order = mock(IgStandardOrder.class);
        when(order.getBrokerId()).thenReturn(999L); // 不匹配的经纪人ID

        when(standardOrderMapper.entity(IgStandardOrder.class)).thenReturn(standardOrderMapper);
        when(standardOrderMapper.selectOne(ORDER_ID, true)).thenReturn(order);

        try (MockedStatic<MessageUtil> mockedStatic = mockStatic(MessageUtil.class)) {
            String expectedMessage = "订单不存在或无权限";
            mockedStatic.when(() -> MessageUtil.get("b_b_quote_238")).thenReturn(expectedMessage);

            QuoteException exception = assertThrows(QuoteException.class, () -> {
                standardOrderService.copyOrder(ORDER_ID);
            });

            assertEquals(-1, exception.getCode());
            assertEquals(expectedMessage, exception.getMessage());
        }

    }

    @Test
    public void testCopyOrder_WhenNotEmployerPlan_ShouldThrowQuoteException() {
        IgStandardOrder order = mock(IgStandardOrder.class);
        when(order.getBrokerId()).thenReturn(USER_ID);
        when(order.getStandardType()).thenReturn("OTHER_TYPE"); // 不是雇主计划

        when(standardOrderMapper.entity(IgStandardOrder.class)).thenReturn(standardOrderMapper);
        when(standardOrderMapper.selectOne(ORDER_ID, true)).thenReturn(order);

        try (
                MockedStatic<MessageUtil> messageUtilMockedStatic = mockStatic(MessageUtil.class);
                MockedStatic<IdentityContext> identityContextMockedStatic = mockStatic(IdentityContext.class)
        ) {
            String expectedMessage = "仅支持复制雇主标品订单";
            messageUtilMockedStatic.when(() -> MessageUtil.get("b_b_quote_249")).thenReturn(expectedMessage);
            identityContextMockedStatic.when(IdentityContext::getUserId).thenReturn(USER_ID);

            QuoteException exception = assertThrows(QuoteException.class, () -> {
                standardOrderService.copyOrder(ORDER_ID);
            });

            assertEquals(-1, exception.getCode());
            assertEquals(expectedMessage, exception.getMessage());
        }

    }

    @Test
    public void testCopyOrder_WhenSuccess_ShouldReturnNewOrderId() {
        try (
                MockedStatic<IdentityContext> identityContextMockedStatic = mockStatic(IdentityContext.class);
                MockedStatic<CustomContext> customContextMockedStatic = mockStatic(CustomContext.class);
        ) {
            // 准备数据
            IgStandardOrder sourceOrder = mock(IgStandardOrder.class);
            when(sourceOrder.getBrokerId()).thenReturn(USER_ID);
            when(sourceOrder.getStandardType()).thenReturn(StandardTypeEnum.EMPLOYER_PLAN.getValue());
            when(sourceOrder.getInsurancePeriod()).thenReturn(12);
            when(sourceOrder.getPersonNum()).thenReturn(10);
            when(sourceOrder.getGroupName()).thenReturn("测试公司");
            when(sourceOrder.getGroupProvince()).thenReturn("上海");
            when(sourceOrder.getGroupCity()).thenReturn("上海");
            when(sourceOrder.getGroupCounty()).thenReturn("浦东");
            when(sourceOrder.getGroupAddress()).thenReturn("测试地址");
            when(sourceOrder.getGroupSocialCode()).thenReturn("91310115MA1K3YJ123");
            when(sourceOrder.getContactPerson()).thenReturn("张三");
            when(sourceOrder.getContactMobile()).thenReturn("***********");
            when(sourceOrder.getContactEmail()).thenReturn("<EMAIL>");
            when(sourceOrder.getChannelSerialNo()).thenReturn("SN20250401");
            when(sourceOrder.getPayeeName()).thenReturn("收款方");
            when(sourceOrder.getPayeeBankName()).thenReturn("银行");
            when(sourceOrder.getPayeeBankNo()).thenReturn("**********");
            when(sourceOrder.getEndTime()).thenReturn(ZonedDateTime.now());

            // 模拟订单明细
            IgStandardOrderDetail detail = mock(IgStandardOrderDetail.class);
            when(detail.getStandardPlanId()).thenReturn(100L);
            when(detail.getOccupationType()).thenReturn("1");
            when(detail.getPersonNum()).thenReturn(5);
            when(detail.getPlanConfigId()).thenReturn(1L);
            when(detail.getSequence()).thenReturn("1");
            when(detail.getPipeLineNum()).thenReturn("pipeline123");
            when(detail.getPremium()).thenReturn(CurrencyAmount.valueOf(BigDecimal.valueOf(1000), "CNY"));
            when(detail.getPlanId()).thenReturn(1000L);
            when(standardOrderMapper.entity(IgStandardOrder.class)).thenReturn(standardOrderMapper);
            when(standardOrderDetailMapper.entity(IgStandardOrderDetail.class)).thenReturn(standardOrderDetailMapper);

            when(standardOrderMapper.selectOne(ORDER_ID, true)).thenReturn(sourceOrder);
            when(standardOrderDetailMapper.getDetailListByOrderId(ORDER_ID)).thenReturn(Collections.singletonList(detail));

            // 模拟插入订单返回的ID
            IgStandardOrder insertedOrder = mock(IgStandardOrder.class);
            when(insertedOrder.getId()).thenReturn(3001L);
            when(standardOrderMapper.insertOne(any(IgStandardOrder.class))).thenReturn(insertedOrder);

            identityContextMockedStatic.when(IdentityContext::getUserId).thenReturn(USER_ID);
            customContextMockedStatic.when(CustomContext::getChannelId).thenReturn(CHANNEL_ID);
            when(standardTenantInfoMapper.getChannelInfo(CHANNEL_ID)).thenReturn(new IgStandardTenantInfo().setStandardPaymentProcess("0"));
            Long newOrderId = standardOrderService.copyOrder(ORDER_ID);

            // 验证结果
            assertNotNull(newOrderId);
            assertEquals(3001L, newOrderId);
        }

    }


}
