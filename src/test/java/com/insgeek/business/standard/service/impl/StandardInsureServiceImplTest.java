package com.insgeek.business.standard.service.impl;

import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.common.dto.QuoteDetailDto;
import com.insgeek.business.quote.external.bi.service.BiService;
import com.insgeek.business.quote.frontend.dto.manage.QpQuoteDto;
import com.insgeek.business.standard.aggservice.PersonService;
import com.insgeek.business.standard.dto.vo.rsp.StandardPersonCheckResultDto;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.dto.IgStandardOrder;
import com.insgeek.protocol.data.client.dto.IgStandardOrderDetail;
import com.insgeek.protocol.data.client.entity.QpCustomer;
import com.insgeek.protocol.data.client.entity.QpQuote;
import com.insgeek.protocol.data.client.entity.QpQuoteInfo;
import com.insgeek.protocol.data.client.entity.QpQuoteInfoGroup;
import com.insgeek.protocol.insurance.client.PersonClient;
import com.insgeek.protocol.platform.common.dto.entity.IgCompany;
import com.insgeek.protocol.platform.common.dto.entity.IgInsurance;

import com.insgeek.protocol.platform.rule.client.RuleClient;
import com.insgeek.protocol.platform.rule.client.RuleExecuteClient;
import com.insgeek.protocol.platform.user.client.TenantClient;

import com.insgeek.protocol.platform.user.dto.TenantClassDto;
import org.junit.jupiter.api.BeforeEach;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.ZonedDateTime;
import java.util.*;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class StandardInsureServiceImplTest {

    @Mock
    private DataMapper<QpQuoteInfo> quoteInfoDataMapper;

    @Mock
    private DataMapper<IgStandardOrder> standardOrderMapper;

    @Mock
    private DataMapper<IgStandardOrderDetail> igStandardOrderDetailDataMapper;

    @Mock
    private DataMapper<QpQuote> quoteDataMapper;

    @Mock
    private DataMapper<QpQuoteInfoGroup> quoteInfoGroupDataMapper;


    @Mock
    private PersonService personService;

    @Mock
    private PersonClient personClient;

    @Mock
    private RuleExecuteClient ruleExecuteClient;

    @Mock
    private RuleClient ruleClient;

    @Mock
    private DataMapper<IgInsurance> igInsuranceDataMapper;

    @Mock
    private DataMapper<IgCompany> companyDataMapper;

    @Mock
    private BiService biService;

    @Mock
    private TenantClient tenantClient;
    @InjectMocks
    private StandardInsureServiceImpl standardInsureService;

    @Mock
    private DataMapper<QpCustomer> customerDataMapper;

    private QpQuoteInfo qpQuoteInfo;
    private IgStandardOrder igStandardOrder;
    private List<IgStandardOrderDetail> orderDetailList;

    @BeforeEach
    public void setUp() {
        // 初始化通用mock行为
        when(quoteInfoDataMapper.entity(QpQuoteInfo.class)).thenReturn(quoteInfoDataMapper);
        //when(igStandardOrderDetailDataMapper.entity(IgStandardOrderDetail.class)).thenReturn(igStandardOrderDetailDataMapper);
        when(quoteDataMapper.entity(QpQuote.class)).thenReturn(quoteDataMapper);
        List<QpQuote> quotes = new ArrayList<>();
        QpQuote quote =  new QpQuote();
        quote.setId(1L);
        quote.setStandardOrderDetailId(1L);
        quotes.add(quote);
        when(quoteDataMapper.select(any(DataCondition.class), eq(true))).thenReturn(quotes);


        // 初始化测试数据
        qpQuoteInfo = new QpQuoteInfo();
        qpQuoteInfo.setId(1L);
        qpQuoteInfo.setGroupId(100L);
        
        igStandardOrder = new IgStandardOrder();
        igStandardOrder.setId(1L);
        igStandardOrder.setInsurancePeriod(12);
        igStandardOrder.setChannelId(1000L);
        igStandardOrder.setStartTime(ZonedDateTime.now().minusYears(1));
        igStandardOrder.setEndTime(ZonedDateTime.now());
        
        orderDetailList = new ArrayList<>();
        IgStandardOrderDetail detail = new IgStandardOrderDetail();
        detail.setId(1L);
        detail.setPipeLineNum("pipeline1");
        orderDetailList.add(detail);
    }

    @Test
    @SuppressWarnings("unchecked")
    public void testUpdateQuoteInfoForMakeQuote_validOrderAndDetails_shouldUpdateQuoteInfo() {
        // 准备测试数据
        QuoteDetailDto resp = mock(QuoteDetailDto.class);
        when(resp.getQpQuoteDto()).thenReturn(mock(QpQuoteDto.class));
        when(resp.getQpQuoteDto().getId()).thenReturn("1");

        IgStandardOrder mockOrder = new IgStandardOrder();
        mockOrder.setId(1L);
        mockOrder.setInsurancePeriod(12);
        mockOrder.setStartTime(ZonedDateTime.now().minusYears(1));
        mockOrder.setEndTime(ZonedDateTime.now());
        mockOrder.setJobCategory("1");

        List<IgStandardOrderDetail> mockDetails = new ArrayList<>();
        IgStandardOrderDetail detail = new IgStandardOrderDetail();
        detail.setId(1L);
        detail.setPipeLineNum("pipeline1");
        detail.setCloneSourceDetailId(1L);
        mockDetails.add(detail);

        // 配置mock行为
        when(quoteInfoDataMapper.selectOne(anyLong(), eq(true))).thenReturn(qpQuoteInfo);
        //when(standardOrderMapper.selectOne(anyLong(), eq(true))).thenReturn(mockOrder);

        // 创建mock的DataMapper链式调用
        DataMapper<IgStandardOrderDetail> detailDataMapper = mock(DataMapper.class);
        //when(detailDataMapper.entity(IgStandardOrderDetail.class)).thenReturn(detailDataMapper);
        //when(detailDataMapper.select(any(DataCondition.class), eq(true))).thenReturn(mockDetails);
        //when(detailDataMapper.selectOne(any(Long.class), eq(true))).thenReturn(mockDetails.get(0));

        // 创建mock的DataMapper链式调用
        QpQuoteInfoGroup quoteInfoGroup = new QpQuoteInfoGroup();
        when(quoteInfoGroupDataMapper.entity(QpQuoteInfoGroup.class)).thenReturn(quoteInfoGroupDataMapper);
        when(quoteInfoGroupDataMapper.selectOne(any(Long.class), eq(true))).thenReturn(quoteInfoGroup);

        ResponseVO<List<TenantClassDto>> tenantClassDto = new ResponseVO<>(200, "success", new ArrayList<>());
        when(tenantClient.tenant_info_no_auth(any())).thenReturn(tenantClassDto);


        IgCompany company = new IgCompany();
        when(companyDataMapper.entity(IgCompany.class)).thenReturn(companyDataMapper);
        when(companyDataMapper.selectOne(any(), eq(true))).thenReturn(company);


        List<IgInsurance> igInsurances = new ArrayList();
        IgInsurance igInsurance = new IgInsurance();
        igInsurances.add(igInsurance);
        when(igInsuranceDataMapper.entity(IgInsurance.class)).thenReturn(igInsuranceDataMapper);
        when(igInsuranceDataMapper.select(any(DataCondition.class), eq(true))).thenReturn(igInsurances);

        //mock customerDataMapper
        List<QpCustomer> customers = new ArrayList<>();
        QpCustomer customer = new QpCustomer();
        customer.setId(1L);
        customers.add(customer);

        when(customerDataMapper.entity(QpCustomer.class)).thenReturn(customerDataMapper);
        //when(customerDataMapper.select(any(DataCondition.class), eq(true))).thenReturn(customers);
        when(customerDataMapper.selectOne(any(), eq(true))).thenReturn(customer);

        // mock
        StandardPersonCheckResultDto personResult = new StandardPersonCheckResultDto();
        when(personService.getPersonList(anyString())).thenReturn(personResult);


        // 创建mock的personService返回数据
        personResult = Mockito.mock(StandardPersonCheckResultDto.class);
        //when(personClient.getPersonList(anyString())).thenReturn(personResult);

        // 配置personResult返回数据
        //when(personResult.getPersonalList()).thenReturn(createMockPersonList());

        // 执行测试
        QpQuoteInfo result = standardInsureService.updateQuoteInfoForMakeQuote(resp, mockOrder, 1000L, mockDetails);

    }

}
