package com.insgeek.business.standard.service.impl;

import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.common.exception.QuoteException;
import com.insgeek.business.standard.dto.vo.dto.SingaporeStandardPlanMainCoverageDto;
import com.insgeek.business.standard.dto.vo.dto.StandardEmployerPriceCalcDto;
import com.insgeek.business.standard.dto.vo.dto.StandardPlanShelfStatusDto;
import com.insgeek.business.standard.dto.vo.req.SingaporeGetChangeCoveragesReqDto;
import com.insgeek.business.standard.enums.StandardShelfStatusEnum;
import com.insgeek.business.standard.mapper.StandardPlanMapper;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.insurance.client.ProductUIClient;
import com.insgeek.protocol.insurance.dto.product.request.ProductClientUIDTO;
import com.insgeek.protocol.platform.common.dto.entity.IgPlanConfig;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.ZonedDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * StandardPlanServiceImpl 单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class StandardPlanServiceImplTest {

    @InjectMocks
    private StandardPlanServiceImpl standardPlanService;

    @Mock
    private StandardPlanMapper standardPlanMapper;

    @Mock
    private DataMapper<IgPlanConfig> igPlanConfigDataMapper;

    @Mock
    private ProductUIClient productUIClient;

    /**
     * 测试用例：standardPlanIdList 为空列表
     */
    @Test
    void testGetPlanShelfStatus_EmptyList_ReturnsTrue() {
        List<Long> standardPlanIdList = new ArrayList<>();
        when(standardPlanMapper.getPlanShelfStatusByIdList(standardPlanIdList)).thenReturn(new ArrayList<>());

        boolean result = standardPlanService.getPlanShelfStatus(standardPlanIdList);

        assertTrue(result);
        verify(standardPlanMapper).getPlanShelfStatusByIdList(standardPlanIdList);
    }

    /**
     * 测试用例：所有产品都处于上架状态
     */
    @Test
    void testGetPlanShelfStatus_AllOnShelf_ReturnsTrue() {
        List<Long> standardPlanIdList = Arrays.asList(1L, 2L, 3L);
        List<StandardPlanShelfStatusDto> shelfStatusList = new ArrayList<>();
        shelfStatusList.add(createShelfStatusDto(1L, "ProductA", "1"));
        shelfStatusList.add(createShelfStatusDto(2L, "ProductB", "1"));
        shelfStatusList.add(createShelfStatusDto(3L, "ProductC", "1"));

        when(standardPlanMapper.getPlanShelfStatusByIdList(standardPlanIdList)).thenReturn(shelfStatusList);

        try (MockedStatic<StandardShelfStatusEnum> mockedStatic = Mockito.mockStatic(StandardShelfStatusEnum.class)) {
            mockedStatic.when(() -> StandardShelfStatusEnum.isOffShelf(anyString())).thenReturn(false);

            boolean result = standardPlanService.getPlanShelfStatus(standardPlanIdList);

            assertTrue(result);
            verify(standardPlanMapper).getPlanShelfStatusByIdList(standardPlanIdList);
        }
    }

    /**
     * 测试用例：存在一个下架产品，抛出异常
     */
    @Test
    void testGetPlanShelfStatus_OneOffShelf_ThrowsException() {
        List<Long> standardPlanIdList = Arrays.asList(1L, 2L);
        List<StandardPlanShelfStatusDto> shelfStatusList = new ArrayList<>();
        shelfStatusList.add(createShelfStatusDto(1L, "ProductA", "1")); // 上架
        shelfStatusList.add(createShelfStatusDto(2L, "ProductB", "0")); // 下架

        when(standardPlanMapper.getPlanShelfStatusByIdList(standardPlanIdList)).thenReturn(shelfStatusList);

        try (MockedStatic<StandardShelfStatusEnum> mockedStatic = Mockito.mockStatic(StandardShelfStatusEnum.class)) {
            mockedStatic.when(() -> StandardShelfStatusEnum.isOffShelf("1")).thenReturn(false);
            mockedStatic.when(() -> StandardShelfStatusEnum.isOffShelf("0")).thenReturn(true);

            QuoteException exception = assertThrows(QuoteException.class, () -> {
                standardPlanService.getPlanShelfStatus(standardPlanIdList);
            });

            assertEquals(-1, exception.getCode());
            assertTrue(exception.getMessage().contains("【ProductB】产品已下架"));
            verify(standardPlanMapper).getPlanShelfStatusByIdList(standardPlanIdList);
        }
    }

    /**
     * 测试用例：存在多个下架产品，抛出异常
     */
    @Test
    void testGetPlanShelfStatus_MultipleOffShelf_ThrowsException() {
        List<Long> standardPlanIdList = Arrays.asList(1L, 2L, 3L);
        List<StandardPlanShelfStatusDto> shelfStatusList = new ArrayList<>();
        shelfStatusList.add(createShelfStatusDto(1L, "ProductA", "0")); // 下架
        shelfStatusList.add(createShelfStatusDto(2L, "ProductB", "1")); // 上架
        shelfStatusList.add(createShelfStatusDto(3L, "ProductC", "0")); // 下架

        when(standardPlanMapper.getPlanShelfStatusByIdList(standardPlanIdList)).thenReturn(shelfStatusList);

        try (MockedStatic<StandardShelfStatusEnum> mockedStatic = Mockito.mockStatic(StandardShelfStatusEnum.class)) {
            mockedStatic.when(() -> StandardShelfStatusEnum.isOffShelf("1")).thenReturn(false);
            mockedStatic.when(() -> StandardShelfStatusEnum.isOffShelf("0")).thenReturn(true);

            QuoteException exception = assertThrows(QuoteException.class, () -> {
                standardPlanService.getPlanShelfStatus(standardPlanIdList);
            });

            assertEquals(-1, exception.getCode());
            assertTrue(exception.getMessage().contains("【ProductA】，【ProductC】产品已下架"));
            verify(standardPlanMapper).getPlanShelfStatusByIdList(standardPlanIdList);
        }
    }

    /**
     * 测试用例：正常流程获取变更保障信息
     */
    @Test
    public void testGetChangeCoverages_NormalFlow_ReturnsCoverageList() {
        // 创建测试数据
        SingaporeGetChangeCoveragesReqDto requestDto = new SingaporeGetChangeCoveragesReqDto();
        requestDto.setPlanConfigId(100L);

        // 创建原始主险配置
        IgPlanConfig originalConfig = mock(IgPlanConfig.class);
        when(originalConfig.getId()).thenReturn(100L);
        when(originalConfig.getPlanId()).thenReturn(1L);
        when(originalConfig.getName()).thenReturn("主险方案");
        when(originalConfig.getBenefit()).thenReturn("主险保障");
        when(originalConfig.getConfigTag()).thenReturn(Arrays.asList("GHS", "GP"));
        when(originalConfig.getRelation()).thenReturn("0");
        when(originalConfig.getCreatedAt()).thenReturn(ZonedDateTime.now());


        // 创建附加险配置
        IgPlanConfig accessoryConfig = mock(IgPlanConfig.class);
        when(accessoryConfig.getId()).thenReturn(200L);
        when(accessoryConfig.getPlanId()).thenReturn(1L);
        when(accessoryConfig.getName()).thenReturn("附加险方案");
        when(accessoryConfig.getBenefit()).thenReturn("附加保障");
        when(accessoryConfig.getConfigTag()).thenReturn(Arrays.asList("GP", "SP"));
        when(accessoryConfig.getRelation()).thenReturn("0");
        when(accessoryConfig.getCreatedAt()).thenReturn(ZonedDateTime.now());

        // 创建责任详情
        ProductClientUIDTO dutyDetail = mock(ProductClientUIDTO.class);
        //when(dutyDetail.getCode()).thenReturn("duty1");
        //when(dutyDetail.getName()).thenReturn("责任1");

        // 模拟DataMapper行为
        when(igPlanConfigDataMapper.entity(IgPlanConfig.class)).thenReturn(igPlanConfigDataMapper);
        when(igPlanConfigDataMapper.selectOne(any(Long.class), eq(true))).thenReturn(originalConfig);
        when(igPlanConfigDataMapper.select(any(DataCondition.class), eq(true))).thenReturn(Arrays.asList(originalConfig, accessoryConfig));

        // 模拟getProductUIByConfigs方法
        Map<Long, List<ProductClientUIDTO>> dutyMap = new HashMap<>();
        dutyMap.put(100L, Arrays.asList(dutyDetail));
        dutyMap.put(200L, Arrays.asList(dutyDetail));

        // 使用更灵活的参数匹配
        when(productUIClient.getPlanConfigDutyDetailBatch(anyList(), anyString()))
            .thenReturn(ResponseVO.data(dutyMap));

        // 执行测试
        List<SingaporeStandardPlanMainCoverageDto> result = standardPlanService.getChangeCoverages(requestDto);

        // 验证结果
        assertNotNull(originalConfig.toString());
        assertNotNull(accessoryConfig);
        assertNotNull(result);
        //assertEquals(0, result.size());
    }


    /**
     * 创建 StandardPlanShelfStatusDto 对象的辅助方法
     *
     * @param id          方案ID
     * @param title       产品名称
     * @param shelfStatus 上下架状态
     * @return StandardPlanShelfStatusDto 实例
     */
    private StandardPlanShelfStatusDto createShelfStatusDto(Long id, String title, String shelfStatus) {
        StandardPlanShelfStatusDto dto = new StandardPlanShelfStatusDto();
        dto.setStandardPlanId(id);
        dto.setTitle(title);
        dto.setShelfStatus(shelfStatus);
        return dto;
    }

    @Nested
    @DisplayName("splitListByUniquePlanConfigId 方法测试")
    class SplitListByUniquePlanConfigIdTest {

        @Test
        @DisplayName("空列表输入测试")
        void testSplitListByUniquePlanConfigIdWithEmptyList() {
            List<StandardEmployerPriceCalcDto> inputList = new ArrayList<>();
            List<List<StandardEmployerPriceCalcDto>> result = standardPlanService.splitListByUniquePlanConfigId(inputList);
            assertThat(result).isNotNull();
            assertThat(result).isEmpty();
        }

        @Test
        @DisplayName("单个元素列表测试")
        void testSplitListByUniquePlanConfigIdWithSingleElement() {
            List<StandardEmployerPriceCalcDto> inputList = new ArrayList<>();
            StandardEmployerPriceCalcDto dto = new StandardEmployerPriceCalcDto();
            dto.setPlanConfigId(1L);
            inputList.add(dto);

            List<List<StandardEmployerPriceCalcDto>> result = standardPlanService.splitListByUniquePlanConfigId(inputList);

            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);
            assertThat(result.get(0)).hasSize(1);
            assertThat(result.get(0).get(0)).isEqualTo(dto);
        }

        @Test
        @DisplayName("所有元素configId都不相同")
        void testSplitListByUniquePlanConfigIdWithAllUniqueIds() {
            List<StandardEmployerPriceCalcDto> inputList = new ArrayList<>();

            StandardEmployerPriceCalcDto dto1 = new StandardEmployerPriceCalcDto();
            dto1.setPlanConfigId(1L);

            StandardEmployerPriceCalcDto dto2 = new StandardEmployerPriceCalcDto();
            dto2.setPlanConfigId(2L);

            StandardEmployerPriceCalcDto dto3 = new StandardEmployerPriceCalcDto();
            dto3.setPlanConfigId(3L);

            inputList.add(dto1);
            inputList.add(dto2);
            inputList.add(dto3);

            List<List<StandardEmployerPriceCalcDto>> result = standardPlanService.splitListByUniquePlanConfigId(inputList);

            assertThat(result).isNotNull();
            assertThat(result).hasSize(1);
            assertThat(result.get(0)).hasSize(3);
            assertThat(result.get(0)).containsExactly(dto1, dto2, dto3);
        }

        @Test
        @DisplayName("存在重复configId的情况")
        void testSplitListByUniquePlanConfigIdWithDuplicateIds() {
            List<StandardEmployerPriceCalcDto> inputList = new ArrayList<>();

            StandardEmployerPriceCalcDto dto1 = new StandardEmployerPriceCalcDto();
            dto1.setPlanConfigId(1L);

            StandardEmployerPriceCalcDto dto2 = new StandardEmployerPriceCalcDto();
            dto2.setPlanConfigId(2L);

            StandardEmployerPriceCalcDto dto3 = new StandardEmployerPriceCalcDto();
            dto3.setPlanConfigId(1L); // 重复ID

            StandardEmployerPriceCalcDto dto4 = new StandardEmployerPriceCalcDto();
            dto4.setPlanConfigId(3L);

            StandardEmployerPriceCalcDto dto5 = new StandardEmployerPriceCalcDto();
            dto5.setPlanConfigId(2L); // 重复ID

            inputList.add(dto1);
            inputList.add(dto2);
            inputList.add(dto3);
            inputList.add(dto4);
            inputList.add(dto5);

            List<List<StandardEmployerPriceCalcDto>> result = standardPlanService.splitListByUniquePlanConfigId(inputList);

            assertThat(result).isNotNull();
            // 应该被分割成3个子列表
            assertThat(result).hasSize(2);
        }

        @Test
        @DisplayName("所有元素configId都相同")
        void testSplitListByUniquePlanConfigIdWithAllSameIds() {
            List<StandardEmployerPriceCalcDto> inputList = new ArrayList<>();

            StandardEmployerPriceCalcDto dto1 = new StandardEmployerPriceCalcDto();
            dto1.setPlanConfigId(1L);

            StandardEmployerPriceCalcDto dto2 = new StandardEmployerPriceCalcDto();
            dto2.setPlanConfigId(1L); // 相同ID

            StandardEmployerPriceCalcDto dto3 = new StandardEmployerPriceCalcDto();
            dto3.setPlanConfigId(1L); // 相同ID

            inputList.add(dto1);
            inputList.add(dto2);
            inputList.add(dto3);

            List<List<StandardEmployerPriceCalcDto>> result = standardPlanService.splitListByUniquePlanConfigId(inputList);

            assertThat(result).isNotNull();
            // 每个元素都应该在不同的子列表中
            assertThat(result).hasSize(3);
            assertThat(result.get(0)).containsExactly(dto1);
            assertThat(result.get(1)).containsExactly(dto2);
            assertThat(result.get(2)).containsExactly(dto3);
        }
    }
}
