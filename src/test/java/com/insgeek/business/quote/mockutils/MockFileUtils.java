package com.insgeek.business.quote.mockutils;

import cn.hutool.core.io.FileUtil;
import org.apache.commons.codec.Charsets;

import java.io.File;
import java.net.URL;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023年08月02日 18:04:00
 */
public class MockFileUtils {
    public static String readTestClassPathAsString(String path) {
        URL resource = MockFileUtils.class.getClassLoader().getResource(path);
        File file = null;
        try {
            file = new File(resource.toURI());
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return FileUtil.readString(file, Charsets.UTF_8);
    }
}