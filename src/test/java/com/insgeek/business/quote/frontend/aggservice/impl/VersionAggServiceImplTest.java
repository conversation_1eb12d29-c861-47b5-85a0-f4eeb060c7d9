package com.insgeek.business.quote.frontend.aggservice.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.boot.web.context.IdentityContext;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.common.feign.api.PlatformUserFeignService;
import com.insgeek.business.quote.common.service.QpQuoteApproveService;
import com.insgeek.business.quote.frontend.dto.version.VersionDto;
import com.insgeek.business.quote.frontend.enums.sql.table.TableNameEnums;
import com.insgeek.protocol.data.client.entity.QpQuoteApprove;
import com.insgeek.protocol.platform.common.client.PlatformDataClient;
import com.insgeek.protocol.platform.user.dto.UserDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;


@Slf4j
@RunWith(MockitoJUnitRunner.class)
class VersionAggServiceImplTest {

    MockMvc mockMvc;

    @Mock
    private PlatformDataClient platformDataClient;

    @Mock
    private PlatformUserFeignService platformUserFeignService;

    @InjectMocks
    VersionAggServiceImpl versionAggService;

    @Mock
    private QpQuoteApproveService approveService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(versionAggService).build();
    }

    @Test
    @SuppressWarnings("unchecked")
    void generateExtendInfo() throws Exception {
        String json = "{\"id\":104847620,\"amount\":50000,\"deduction\":0,\"once_deduction\":0,\"once_quota\":0,\"payment\":100,\"product_id\":104592126,\"qp_product_id\":104779030}";
        List<VersionDto> versionDtoList = Collections.singletonList(new VersionDto().setEntityKey("QUOTE").setEntityValue(json));
        IdentityContext.getIdentity().setUserId(1997L);
        UserDto userDto = new UserDto();
        userDto.setName("test");
        Mockito.doReturn(userDto).when(platformUserFeignService).findById(1997L);
        String extendInfo = this.versionAggService.generateExtendInfo(json);
        Map<String, Object> map = JacksonUtils.readValue(extendInfo, new TypeReference<Map<String, Object>>() {
        });
        Map<String, String> extend = (Map<String, String>) map.get("extend");
        assertEquals(extend.get("owner_name"), "test");
        Mockito.when(platformDataClient.listDataByPost(eq(TableNameEnums.QP_VERSION.getTableName()),anyInt(),anyInt(),anyMap())).thenReturn(ResponseVO.data(Collections.emptyList()));
        Mockito.when(platformDataClient.addPlatformData(eq(TableNameEnums.QP_VERSION.getTableName()),anyList())).thenReturn(ResponseVO.<List<Long>>builder().build());
        this.versionAggService.insertNewDateVersion(versionDtoList);

    }

    @Test
    void teawt(){
        QpQuoteApprove qpQuoteApprove = approveService.insertNewStatusByQuote(1L);
        System.out.println(qpQuoteApprove);
    }


}