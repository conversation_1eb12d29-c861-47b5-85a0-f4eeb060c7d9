package com.insgeek.business.quote.frontend.aggservice.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.frontend.dto.manage.QpQuoteDetailDto;
import com.insgeek.business.quote.frontend.dto.manage.QpQuoteGroupDto;
import com.insgeek.business.quote.frontend.dto.manage.QuoteDynamicDto;
import com.insgeek.business.quote.frontend.dto.version.VersionDto;
import com.insgeek.business.quote.frontend.enums.sql.QuoteSql;
import com.insgeek.business.quote.frontend.enums.sql.table.TableNameEnums;
import com.insgeek.protocol.data.client.entity.QpQuote;
import com.insgeek.protocol.platform.common.client.BqlPermissionClient;
import com.insgeek.protocol.platform.common.client.PlatformDataClient;
import com.insgeek.protocol.platform.common.dto.entity.IgPlan;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.*;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
class QuoteManageAggServiceImplTest {

    MockMvc mockMvc;

    @Mock
    private BqlPermissionClient bqlPermissionClient;

    @Mock
    private PlatformDataClient platformDataClient;

    @InjectMocks
    QuoteManageAggServiceImpl quoteManageAggService;


    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(quoteManageAggService).build();
    }

    @Test
    @SuppressWarnings("rawtypes")
    void queryQuoteDynamic() {
        String json = "[\n" +
                "    {\n" +
                "        \"id\":114608236,\n" +
                "        \"object_id\":114607212,\n" +
                "        \"version_code\":\"QUOTE_V5\",\n" +
                "        \"version_name\":\"QUOTE_UPDATE\",\n" +
                "        \"entity_hash\":1311998238,\n" +
                "        \"entity_key\":\"QUOTE\",\n" +
                "        \"operation_type\":3,\n" +
                "        \"operation_time\":1647496950512,\n" +
                "        \"entity_value\":\"{\\\"qp_quote_dto\\\": {\\\"status\\\":1},\\\"extend\\\":{\\\"owner_name\\\":\\\"保险极客官方账号\\\"}}\",\n" +
                "        \"business_id\":114607212,\n" +
                "        \"description\":\"null\",\n" +
                "        \"owner_id\":1997,\n" +
                "        \"business_type\":\"defaultBusiType\",\n" +
                "        \"tenant_id\":2014,\n" +
                "        \"delete_flg\":0,\n" +
                "        \"created_at\":\"2022-03-17 14:02:30\",\n" +
                "        \"created_by\":1997,\n" +
                "        \"updated_at\":\"2022-03-17 14:02:30\",\n" +
                "        \"updated_by\":1997\n" +
                "    }\n" +
                "]";
        List<QuoteDynamicDto> dynamicDtoList = JacksonUtils.readValue(json, new TypeReference<List<QuoteDynamicDto>>() {
        });
        Mockito.when(bqlPermissionClient.queryNoPermission(anyString())).thenReturn(ResponseVO.builder().data(dynamicDtoList).build());

//        List<QuoteDynamicDto> quoteDynamicDtoList = quoteManageAggService.queryQuoteDynamic(1L);
//        quoteDynamicDtoList.forEach(item -> assertEquals(item.getOwnerName(), "保险极客官方账号"));
    }

    @Test
    @SuppressWarnings("rawtypes")
    void queryQuoteDynamic2() {
        String json = "[\n" +
                "    {\n" +
                "        \"id\":114608236,\n" +
                "        \"object_id\":114607212,\n" +
                "        \"version_code\":\"QUOTE_V5\",\n" +
                "        \"owner_name\":\"test\",\n" +
                "        \"version_name\":\"QUOTE_UPDATE\",\n" +
                "        \"entity_hash\":1311998238,\n" +
                "        \"entity_key\":\"QUOTE\",\n" +
                "        \"operation_type\":3,\n" +
                "        \"operation_time\":1647496950512,\n" +
                "        \"entity_value\":\"{\\\"qp_quote_dto\\\": {\\\"status\\\":1}}\",\n" +
                "        \"business_id\":114607212,\n" +
                "        \"description\":\"null\",\n" +
                "        \"owner_id\":1997,\n" +
                "        \"business_type\":\"defaultBusiType\",\n" +
                "        \"tenant_id\":2014,\n" +
                "        \"delete_flg\":0,\n" +
                "        \"created_at\":\"2022-03-17 14:02:30\",\n" +
                "        \"created_by\":1997,\n" +
                "        \"updated_at\":\"2022-03-17 14:02:30\",\n" +
                "        \"updated_by\":1997\n" +
                "    }\n" +
                "]";
        List<QuoteDynamicDto> dynamicDtoList = JacksonUtils.readValue(json, new TypeReference<List<QuoteDynamicDto>>() {
        });
        Mockito.when(bqlPermissionClient.queryNoPermission(anyString())).thenReturn(ResponseVO.builder().data(dynamicDtoList).build());

//        List<QuoteDynamicDto> quoteDynamicDtoList = quoteManageAggService.queryQuoteDynamic(1L);
//        quoteDynamicDtoList.forEach(item -> assertEquals(item.getOwnerName(), "test"));
    }


    @Test
    void queryVersion() {
        String resultBql = String.format(QuoteSql.QUOTE_VERSION_DETAIL_SQL.getSql(), 1L);

        String noResultBql = String.format(QuoteSql.QUOTE_VERSION_DETAIL_SQL.getSql(), 2L);

        VersionDto versionDto = new VersionDto().setVersionCode("VERSION_1");
        Mockito.when(bqlPermissionClient.queryNoPermission(resultBql)).thenReturn(ResponseVO.builder().data(Collections.singletonList(versionDto)).build());
        Mockito.when(bqlPermissionClient.queryNoPermission(noResultBql)).thenReturn(ResponseVO.builder().data(Collections.emptyList()).build());

        assertEquals("VERSION_1",quoteManageAggService.queryVersion(1L).getVersionCode());
        assertNull(quoteManageAggService.queryVersion(2L).getVersionCode());

    }

    //@Test
    void setQuoteDetailGroup() {
        QpQuote noQpQuote1 = new QpQuote().setPlanId(0L).setCustomerId(2L);
        QpQuote noQpQuote2 = new QpQuote().setPlanId(1L).setCustomerId(2L);
        QpQuote quote = new QpQuote().setPlanId(1L).setCustomerId(1L);

        QpQuoteDetailDto qpQuoteDetailDto = new QpQuoteDetailDto();
        String resultBql = String.format(QuoteSql.QUOTE_GROUP_SQL.getSql(), 1L);

        String noResultBql = String.format(QuoteSql.QUOTE_GROUP_SQL.getSql(), 2L);

        List<QpQuoteGroupDto> quoteGroupDtoList = new ArrayList<>();
        quoteGroupDtoList.add(new QpQuoteGroupDto().setGroupName("test_group").setGroupAccount("test_account"));

        IgPlan igPlan = new IgPlan();
        igPlan.setId(111L);
        igPlan.setPlanName("test_plan");
        Mockito.when(platformDataClient.getPlatformDataInfo(eq(TableNameEnums.IG_PLAN.getTableName()), anyLong()))
                .thenReturn(ResponseVO.builder().data(igPlan).build());

        Mockito.doReturn(ResponseVO.builder().data(quoteGroupDtoList).build()).when(bqlPermissionClient).query(resultBql);
        Mockito.doReturn(ResponseVO.builder().data(Collections.emptyList()).build()).when(bqlPermissionClient).query(noResultBql);

        quoteManageAggService.setQuoteDetailGroup(noQpQuote1,qpQuoteDetailDto);
        assertNull(qpQuoteDetailDto.getGroup());

        quoteManageAggService.setQuoteDetailGroup(noQpQuote2,qpQuoteDetailDto);
        assertNull(qpQuoteDetailDto.getGroup());

        quoteManageAggService.setQuoteDetailGroup(quote,qpQuoteDetailDto);
        assertEquals("test_group",qpQuoteDetailDto.getGroup().getGroupName());
        assertEquals("test_account",qpQuoteDetailDto.getGroup().getGroupAccount());
        assertEquals(111L,qpQuoteDetailDto.getGroup().getPlanId());
        assertEquals("test_plan",qpQuoteDetailDto.getGroup().getPlanName());
    }
}