package com.insgeek.business.quote.common.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.insgeek.business.quote.backend.aggservice.impl.CheckListServiceImpl;
import com.insgeek.business.quote.backend.aggservice.impl.QuoteDutyServiceImpl;
import com.insgeek.business.quote.backend.dto.quote.QuoteDutysDto;
import com.insgeek.business.quote.common.dao.constant.QpFileDetailsConst;
import com.insgeek.business.quote.common.dao.mapper.QuoteDutyMapper;
import com.insgeek.business.quote.common.dao.mapper.QuoteDutySplitMapper;
import com.insgeek.business.quote.common.dto.QpQuoteContractCustomerDto;
import com.insgeek.business.quote.common.dto.param.H5ChannelSubmitParam;
import com.insgeek.business.quote.common.enums.QuoteSourceEnum;
import com.insgeek.business.quote.common.service.QuoteRenewService;
import com.insgeek.business.quote.frontend.aggservice.impl.VersionAggServiceImpl;
import com.insgeek.business.quote.frontend.dto.manage.ProductTypeMapSpecialRuleDto;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.dto.QuoteDutyDTO;
import com.insgeek.protocol.data.client.dto.SaveDutyDto;
import com.insgeek.protocol.data.client.entity.*;
import com.insgeek.protocol.insurance.client.CopyDutyRuleInstanceClient;
import com.insgeek.protocol.insurance.client.DutyClient;
import com.insgeek.protocol.insurance.client.SpecialRulesClient;
import com.insgeek.protocol.insurance.dto.config.PlanConfigRulesDTO;
import com.insgeek.protocol.insurance.dto.config.PlanConfigStandardDTO;
import com.insgeek.protocol.insurance.dto.geekplus.group.GroupDetailDTO;
import com.insgeek.protocol.platform.common.dto.entity.IgProduct;
import com.insgeek.protocol.platform.common.dto.entity.IgProductType;
import com.insgeek.protocol.platform.common.dto.insurance.config.DutyDTO;
import com.insgeek.protocol.platform.common.dto.insurance.config.PlanConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.*;

/**
 * @ClassName: RateAbstract
 * @Description:
 * @Author: YYY
 * @Date: 2022/7/15 9:27
 **/
@Slf4j
@RunWith(MockitoJUnitRunner.class)
class SaveDutysServiceTest {

    MockMvc mockMvc;

    @InjectMocks
    @Spy
    @Autowired
    private QuoteDutyServiceImpl dutyService;

    @Mock
    private QuoteDutyMapper quoteDutyMapper;
    @Mock
    private QuoteDutySplitMapper quoteDutySplitMapper;
    @SuppressWarnings("all")
    @Mock
    private DataMapper<QpQuoteConfig> quoteConfigDataMapper;
    @SuppressWarnings("all")
    @Mock
    private DataMapper<QpQuote> quoteDataMapper;
    //@Resource
    //private QueryFeginService queryFeginService;
    @Mock
    private CheckListServiceImpl checkListService;
    @Mock
    private VersionAggServiceImpl versionAggService;
    @Mock
    private RedisTemplate redisTemplate;
    @SuppressWarnings("all")
    @Mock
    private DataMapper<IgProduct> productDataMapper;
    @SuppressWarnings("all")
    @Mock
    private DataMapper<IgProductType> productTypeDataMapper;
    @SuppressWarnings("all")
    @Mock
    private DataMapper<QpProduct> qpProductDataMapper;
    @Mock
    private SpecialRulesClient specialRulesClient;
    @Mock
    private ProductTypeMapSpecialRuleDto productTypeMapSpecialRuleDto;
    @Mock
    private RuleServices ruleServices;
    @Mock
    private CopyDutyRuleInstanceClient copyDutyRuleInstanceClient;
    @Mock
    private DataMapper<QpQuoteDuty> qpQuoteDutyDataMapper;
    @Mock
    private DataMapper<QpQuoteDutySplit> qpQuoteDutySplitDataMapper;
    @Mock
    private DutyClient dutyClient;
    @Mock
    private QuoteRenewService quoteRenewService;
    @Mock
    private DataMapper<QpQuoteInfo> qpQuoteInfoDataMapper;
    @Mock
    private DataMapper<QpQuote> qpQuoteDataMapper;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        //ReflectionTestUtils.setField(channelService,"verification",false);
        mockMvc = MockMvcBuilders.standaloneSetup(dutyService).build();
    }

    private QpQuoteInfo getQpQuoteInfo() {
        QpQuoteInfo info = new QpQuoteInfo();
        info.setQuoteSourceFlag(QuoteSourceEnum.CHANNEL.getCode());
        info.setCustomerId(NumberUtils.LONG_ZERO);
        info.setTenantChannelProductId(111111L);
        return info;
    }

    @Mock
    private DataMapper<QpTenantChannelProduct> channelProductDataMapper;


    @Test
    void saveDutys() {
        try {
            Mockito.doReturn(quoteDataMapper).when(quoteDataMapper).entity(QpQuote.class);
            Mockito.when(quoteDataMapper.updateOne(any(QpQuote.class), any(Boolean.class))).thenReturn(1);
            Mockito.when(quoteDataMapper.updateOne(any(QpQuote.class))).thenReturn(1);
            Mockito.when(quoteDataMapper.insertOne(any(QpQuote.class))).thenReturn(getQpQuote());
            Mockito.when(quoteDataMapper.insertOne(any(QpQuote.class), any(Boolean.class))).thenReturn(getQpQuote());
            Mockito.when(quoteDataMapper.selectOne(any(), any())).thenReturn(getQpQuote());
            Mockito.when(quoteDataMapper.select(any(QpQuote.class), any(Boolean.class))).thenReturn(Lists.newArrayList(getQpQuote()));
            Mockito.when(qpQuoteDutyDataMapper.batchSuperUpdate(anyList())).thenReturn(4);

            Mockito.doReturn(quoteConfigDataMapper).when(quoteConfigDataMapper).entity(QpQuoteConfig.class);
            Mockito.when(quoteConfigDataMapper.updateOne(any(QpQuoteConfig.class), any(Boolean.class))).thenReturn(1);
            Mockito.when(quoteConfigDataMapper.updateOne(any(QpQuoteConfig.class))).thenReturn(1);
            Mockito.when(quoteConfigDataMapper.insertOne(any(QpQuoteConfig.class))).thenReturn(getQpQuoteConfig());
            Mockito.when(quoteConfigDataMapper.insertOne(any(QpQuoteConfig.class), any(Boolean.class))).thenReturn(getQpQuoteConfig());
            Mockito.when(quoteConfigDataMapper.selectOne(any(), any())).thenReturn(getQpQuoteConfig());
            Mockito.when(quoteConfigDataMapper.select(any(QpQuoteConfig.class), any(Boolean.class))).thenReturn(Lists.newArrayList(getQpQuoteConfig()));

            //Mockito.doReturn(getQuoteDutyCache()).when(dutyService.queryQuoteDutys());

//        Mockito.when(quoteDutyMapper.selectListByEntity(any(QpQuoteDuty.class),true)).thenReturn(getDutys());
            Mockito.when(quoteDutyMapper.selectListByEntity(any(QpQuoteDuty.class), anyBoolean())).thenReturn(Lists.newArrayList());
            Mockito.when(quoteDutyMapper.selectListByEntity(any(QpQuoteDuty.class))).thenReturn(getDutys());
            Mockito.when(quoteDutyMapper.insertList(any())).thenReturn(Lists.newArrayList(1 + ""));
            Mockito.doNothing().when(quoteDutyMapper).deleteListWithoutPermission(any());
            Mockito.doNothing().when(quoteDutyMapper).updateOne(any());

            Mockito.doNothing().when(checkListService).checkProductContent(anyList());
            Mockito.doNothing().when(versionAggService).insertNewDateVersion(anyList());

            Mockito.when(quoteDutySplitMapper.insertList(any())).thenReturn(Lists.newArrayList(1 + "", 2 + "", 3 + "", 4 + ""));
            Mockito.doNothing().when(quoteDutySplitMapper).deleteListWithoutPermission(anyList());
            SaveDutyDto saveDutyDto = new SaveDutyDto(Boolean.FALSE, Boolean.FALSE, Boolean.FALSE);
            dutyService.saveDutys(getQpQuoteConfig().getId(), getQuoteDutyDTOs(), saveDutyDto);

        } catch (Exception e) {

        }
    }

    private List<QpQuoteDuty> getDutys() {
        QpQuoteDuty duty = new QpQuoteDuty()
                .setAmount(null)
//                .setBusinessType("1321420")
                .setAgentRatio(null)
                .setDeduction(null)
                .setCostRate(new BigDecimal(1))
                .setProductId(1198890883173478073L)
                .setQuoteConfigId(getQpQuoteConfig().getId())
                .setOnceDeduction(null)
                .setOnceQuota(null);
        QpQuoteDuty duty2 = new QpQuoteDuty()
                .setAmount(null)
//                .setBusinessType("1321300")
                .setAgentRatio(null)
                .setDeduction(null)
                .setCostRate(new BigDecimal(1))
                .setProductId(114460549L)
                .setQuoteConfigId(getQpQuoteConfig().getId())
                .setOnceDeduction(null)
                .setOnceQuota(null);
        return Lists.newArrayList(duty, duty2);
    }

    private List<QuoteDutyDTO> getQuoteDutyDTOs() {
        QuoteDutyDTO duty = new QuoteDutyDTO();
        duty.setAmount(null);
        duty.setBusinessType("1321420");
        duty.setType("1321420");
        duty.setAgentRatio(null);
        duty.setDeduction(null);
        duty.setCostRate(new BigDecimal(1));
        duty.setProductId(1198890883173478073L);
        duty.setQuoteConfigId(getQpQuoteConfig().getId());
        duty.setOnceDeduction(null);
        duty.setOnceQuota(null);
        duty.setDutySplit(getDutySplit());
        duty.setProperty(3);

        QuoteDutyDTO duty2 = new QuoteDutyDTO();
        duty2.setId(2L);
        duty2.setAmount(null);
        duty2.setBusinessType("1321300");
        duty2.setType("1321300");
        duty2.setAgentRatio(null);
        duty2.setDeduction(null);
        duty2.setCostRate(new BigDecimal(1));
        duty2.setProductId(114460549L);
        duty2.setQuoteConfigId(getQpQuoteConfig().getId());
        duty2.setOnceDeduction(null);
        duty2.setOnceQuota(null);
        duty2.setProperty(null);

        return Lists.newArrayList(duty, duty2);
    }

    private List<QpQuoteDutySplit> getDutySplit() {
        QpQuoteDutySplit split = new QpQuoteDutySplit()
                .setAmount(null)
                .setDeduction(null)
                .setEndAmount(null)
                .setNoMedicalRatio(new BigDecimal(1))
                .setOnceDeduction(null)
                .setOnceQuota(null)
                .setStartAmount(null)
                .setOtcRange(null)
                .setPayment(1)
                .setProperty(3);
        return Lists.newArrayList(split);
    }

    public Map<String, List<QuoteDutysDto>> getQuoteDutyCache() {

        String str = "{\n" +
                "  \"1321420\": [\n" +
                "    \n" +
                "      {\n" +
                "        \"type\": \"1321420\",\n" +
                "        \"product_id\": 1198890883173478073,\n" +
                "        \"property\": 8,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    \n" +
                "  ],\n" +
                "  \"1321300\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321300\",\n" +
                "        \"product_id\": 114460549,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321300\",\n" +
                "        \"product_id\": 1198890883173478070,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    \n" +
                "  ],\n" +
                "  \"1321400\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321400\",\n" +
                "        \"product_id\": 1198890883173478071,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1321600\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321600\",\n" +
                "        \"product_id\": 104593281,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321600\",\n" +
                "        \"product_id\": 1022103157322393169,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321600\",\n" +
                "        \"product_id\": 1154327419201666033,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1321920\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321920\",\n" +
                "        \"product_id\": 1001577319636238614,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321920\",\n" +
                "        \"product_id\": 1154327453561403928,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321920\",\n" +
                "        \"product_id\": 1154327470741273454,\n" +
                "        \"property\": 9,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1321601\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321601\",\n" +
                "        \"product_id\": 1198890951892954672,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1321602\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321602\",\n" +
                "        \"product_id\": 1198890951892954686,\n" +
                "        \"property\": 5,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1111000\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1111000\",\n" +
                "        \"product_id\": 104593284,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1111000\",\n" +
                "        \"product_id\": 1022105390705387421,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1111000\",\n" +
                "        \"product_id\": 1154327419201665955,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1321603\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321603\",\n" +
                "        \"product_id\": 1198890951892955025,\n" +
                "        \"property\": 5,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1420020\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1420020\",\n" +
                "        \"product_id\": 104597808,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1420020\",\n" +
                "        \"product_id\": 1154327384841927544,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"WGZ90\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"WGZ90\",\n" +
                "        \"product_id\": 1022105459424863995,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1111003\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1111003\",\n" +
                "        \"product_id\": 104593994,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1111003\",\n" +
                "        \"product_id\": 1154327419201665945,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1331000\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1331000\",\n" +
                "        \"product_id\": 1198890934713085942,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1311000\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1311000\",\n" +
                "        \"product_id\": 103338066,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1311000\",\n" +
                "        \"product_id\": 103406438,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1311000\",\n" +
                "        \"product_id\": 104593230,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1311000\",\n" +
                "        \"product_id\": 1022105390705387420,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1311000\",\n" +
                "        \"product_id\": 1154327384841927300,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1420040\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1420040\",\n" +
                "        \"product_id\": 104598691,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1420040\",\n" +
                "        \"product_id\": 1154327384841927396,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1311400\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1311400\",\n" +
                "        \"product_id\": 1072392998110753653,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1311400\",\n" +
                "        \"product_id\": 1119212574043407185,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1311400\",\n" +
                "        \"product_id\": 1154327453561403951,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1311400\",\n" +
                "        \"product_id\": 1262356274876679273,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1311200\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1311200\",\n" +
                "        \"product_id\": 1198890934713085836,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"2815000\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2815000\",\n" +
                "        \"product_id\": 1154327367662058442,\n" +
                "        \"property\": 5,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"2813000\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2813000\",\n" +
                "        \"product_id\": 1154325598135532211,\n" +
                "        \"property\": 5,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"2811000\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2811000\",\n" +
                "        \"product_id\": 1054908426766903457,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2811000\",\n" +
                "        \"product_id\": 1063652980181560392,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2811000\",\n" +
                "        \"product_id\": 1154325580955663019,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2811000\",\n" +
                "        \"product_id\": 1154327367662058441,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1321120\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321120\",\n" +
                "        \"product_id\": 104592131,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321120\",\n" +
                "        \"product_id\": 104592132,\n" +
                "        \"property\": 2,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321120\",\n" +
                "        \"product_id\": 104592167,\n" +
                "        \"property\": 3,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321120\",\n" +
                "        \"product_id\": 1154327384841927569,\n" +
                "        \"property\": 8,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321120\",\n" +
                "        \"product_id\": 1154327384841927589,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321120\",\n" +
                "        \"product_id\": 1154327384841927635,\n" +
                "        \"property\": 2,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321120\",\n" +
                "        \"product_id\": 1154327384841927652,\n" +
                "        \"property\": 3,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321120\",\n" +
                "        \"product_id\": 1154327470741273381,\n" +
                "        \"property\": 9,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1321220\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 103339419,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 104592122,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 104592123,\n" +
                "        \"property\": 2,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 104592124,\n" +
                "        \"property\": 3,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 114455545,\n" +
                "        \"property\": 2,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 925938616226604763,\n" +
                "        \"property\": 8,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 958652763644964103,\n" +
                "        \"property\": 8,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 973190626646505953,\n" +
                "        \"property\": 8,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 1074369593599980816,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 1074369593599980948,\n" +
                "        \"property\": 8,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 1128820312805345167,\n" +
                "        \"property\": 9,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 1154327384841927185,\n" +
                "        \"property\": 8,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 1154327384841927223,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 1154327384841927245,\n" +
                "        \"property\": 2,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 1154327384841927266,\n" +
                "        \"property\": 3,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 1154327470741273375,\n" +
                "        \"property\": 9,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321220\",\n" +
                "        \"product_id\": 1158413616727342325,\n" +
                "        \"property\": 2,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1321440\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321440\",\n" +
                "        \"product_id\": 1198890883173478113,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1322200\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1322200\",\n" +
                "        \"product_id\": 103346504,\n" +
                "        \"property\": 5,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1322200\",\n" +
                "        \"product_id\": 114315973,\n" +
                "        \"property\": 5,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1322200\",\n" +
                "        \"product_id\": 1154327419201665999,\n" +
                "        \"property\": 5,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1321430\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321430\",\n" +
                "        \"product_id\": 1198890883173478076,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1322300\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1322300\",\n" +
                "        \"product_id\": 1198890934713085842,\n" +
                "        \"property\": 5,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1321410\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321410\",\n" +
                "        \"product_id\": 1198890883173478072,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1322400\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1322400\",\n" +
                "        \"product_id\": 1198890934713085905,\n" +
                "        \"property\": 5,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1322500\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1322500\",\n" +
                "        \"product_id\": 1198890934713085840,\n" +
                "        \"property\": 5,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1321930\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321930\",\n" +
                "        \"product_id\": 1001577319636238617,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321930\",\n" +
                "        \"product_id\": 1154327453561403945,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1420030\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1420030\",\n" +
                "        \"product_id\": 104596942,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1420030\",\n" +
                "        \"product_id\": 1154327384841927419,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1420010\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1420010\",\n" +
                "        \"product_id\": 104594065,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1420010\",\n" +
                "        \"product_id\": 1154327384841927437,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1420050\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1420050\",\n" +
                "        \"product_id\": 104597883,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1420050\",\n" +
                "        \"product_id\": 1154327384841927524,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"2814000\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2814000\",\n" +
                "        \"product_id\": 1054908426766903468,\n" +
                "        \"property\": 5,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2814000\",\n" +
                "        \"product_id\": 1063652980181560388,\n" +
                "        \"property\": 5,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2814000\",\n" +
                "        \"product_id\": 1154327367662058439,\n" +
                "        \"property\": 5,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1112000\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1112000\",\n" +
                "        \"product_id\": 104822356,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1112000\",\n" +
                "        \"product_id\": 1154327384841927562,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1112000\",\n" +
                "        \"product_id\": 1198890883173478123,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"2816000\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2816000\",\n" +
                "        \"product_id\": 1047483390384790927,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2816000\",\n" +
                "        \"product_id\": 1047483390384790931,\n" +
                "        \"property\": 2,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2816000\",\n" +
                "        \"product_id\": 1054908426766903525,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2816000\",\n" +
                "        \"product_id\": 1063652980181560384,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2816000\",\n" +
                "        \"product_id\": 1154327367662058438,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"2812000\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2812000\",\n" +
                "        \"product_id\": 1054908426766903466,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2812000\",\n" +
                "        \"product_id\": 1063652980181560390,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2812000\",\n" +
                "        \"product_id\": 1154325598135532151,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"2812000\",\n" +
                "        \"product_id\": 1154327367662058440,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1321130\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321130\",\n" +
                "        \"product_id\": 104592409,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321130\",\n" +
                "        \"product_id\": 1154327402021796391,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1321230\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321230\",\n" +
                "        \"product_id\": 103339420,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321230\",\n" +
                "        \"product_id\": 104592126,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321230\",\n" +
                "        \"product_id\": 1154327384841927283,\n" +
                "        \"property\": 1,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1321450\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1321450\",\n" +
                "        \"product_id\": 1198890883173478077,\n" +
                "        \"property\": 4,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ],\n" +
                "  \"1322100\": [\n" +
                "    \n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1322100\",\n" +
                "        \"product_id\": 104593264,\n" +
                "        \"property\": 5,\n" +
                "        \"qp_product_id\": 0\n" +
                "      },\n" +
                "      {\n" +
                "        \n" +
                "        \"type\": \"1322100\",\n" +
                "        \"product_id\": 1154327419201666024,\n" +
                "        \"property\": 5,\n" +
                "        \"qp_product_id\": 0\n" +
                "      }\n" +
                "    ]\n" +
                "}";
        Map<String, List<QuoteDutysDto>> result = com.insgeek.boot.commons.json.JacksonUtils.readValue(str, new TypeReference<Map<String, List<QuoteDutysDto>>>() {
        });
        return result;
    }

    public static void main(String[] args) {
        SaveDutysServiceTest test = new SaveDutysServiceTest();
        Map<String, List<QuoteDutysDto>> quoteDutyCache = test.getQuoteDutyCache();
        System.out.println(quoteDutyCache);
    }

    private QpQuoteInfoUser getQpQuoteInfoUser() {
        return new QpQuoteInfoUser()
                .setId(1L)
                .setQuoteInfoId(getQpQuoteInfo().getId())
                .setUserName("test")
                .setMobilePhone("13223456567")
                .setBirthDate(null)
                .setDocumentType(null)
                .setDocumentNo("110101199003074696")
                .setSex("1")
                .setTelephone("13223456567")
                .setMedicareAddress("srwer")
                .setMedicareType("1")
                .setMedicareAddressCode("1");

    }

    private List<PlanConfigDTO> getConfigTemplateList() {
        PlanConfigDTO dto = new PlanConfigDTO();
        dto.setCode(0 + "")
                .setId(1L)
                .setBenefit("1")
                .setName("dfdsfdsf")
                .setCategory(null)
                .setClientType(null)
                .setComments(1 + "")
                .setCostPrice(null)
                .setIsHaveMillion(null)
                .setName("rtrt")
                .setTemplate(null)
                .setRelation(null)
                .setRealMandayPrice(null)
                .setPlatform(null)
                .setPlanConfigType(null)
                .setIsSalary(null);
        DutyDTO dutyDTO = new DutyDTO();
        dutyDTO.setId(103339179L)
                .setAmount(null)
                .setDeduction(null)
                .setOnceDeduction(null)
                .setOnceQuota(null)
                .setOtcRange(null)
                .setPayment(100)
                .setPayment(103338066);
        dutyDTO.setPkgId(1L);
        dutyDTO.setPkgName("phg_namw");
        dto.setDuties(Lists.newArrayList(dutyDTO));
        return Lists.newArrayList(dto);
    }

    private QpCustomerQuoteRelation getQpCustomerQuoteRelation() {
        QpCustomerQuoteRelation quoteRelation = new QpCustomerQuoteRelation();
        quoteRelation.setId(1L)
                .setBizModeFlag(1)
                .setContractCustomerId(1L);
        return quoteRelation;
    }

    private GroupDetailDTO getGroupDetailDTO() {
        GroupDetailDTO groupDetailDTO = new GroupDetailDTO();
        groupDetailDTO.setGroupAccount("1");
        groupDetailDTO.setContactMail("1");
        groupDetailDTO.setContactMobile("1");
        groupDetailDTO.setAddress("1");
        groupDetailDTO.setParentId("1");
        groupDetailDTO.setContactPerson("1");
        return groupDetailDTO;
    }

    private QpQuoteContractCustomerDto getQpQuoteContractCustomerDto() {
        QpQuoteContractCustomerDto dto = new QpQuoteContractCustomerDto();
        dto.setMainEnterpriseFlag(null)
                .setCustomerId(getCustomer().getId())
                .setContacts("1")
                .setQuoteContractId(getQpQuoteContract().getId())
                .setEnterpriseId(1L)
                .setEnterpriseName("33")
                .setTenantId(11L)
                .setSuperiorEnterpriseId(1L)
                .setMobilePhone("44")
                .setOfficeAddress("4556")
                .setMailbox("<EMAIL>");
        return dto;

    }

    private QpQuoteContract getQpQuoteContract() {
        return new QpQuoteContract()
                .setId(1L)
                .setQuoteInfoId(getQpQuoteInfo().getId())
                .setQuoteName(getQpQuoteInfo().getName())
                .setQuoteSourceFlag(QuoteSourceEnum.CHANNEL.getCode());
    }

    private H5ChannelSubmitParam getH5ChannelSubmitParam() {
        H5ChannelSubmitParam param = new H5ChannelSubmitParam();
        // param.setCompanyId(96L);
        param.setQuoteInfoId(1L);
        param.setTenantChannelProductId(1239377753167296926L);
        param.setStartData(null);
        param.setEndData(null);
        param.setSuration(12);
        param.setFileDetails(Lists.newArrayList(getFileDetails()));
        param.setCustomer(getCustomer());
        param.setCustomerOther(getCustomerOther());
        return param;
    }

    private QpTenantChannelProduct getQpTenantChannelProduct() {
        return new QpTenantChannelProduct()
                .setId(1239377753167296926L)
                .setChannelName("newName")
                .setStatusFlag(null)
                .setChannelConfigId(123L)
                .setCommissionRate(BigDecimal.ONE)
                .setPtTenantId(1239378113944549447L)
                .setSecretAddress("N+VfyX3CWiKWlxeCBDPRFU7PEGrEMe93OxiquLaf21uCR9wUvB0pCdGUHzLVmdqr");
    }

    private QpCustomer getCustomer() {
        return new QpCustomer()
                .setTopLevelId(1L)
                .setId(1L)
                .setQuoteSourceFlag(1)
                .setUnifiedSocialCreditCode("ds239847239749")
                .setOrganizationCode("127894921")
                .setCustomerId(1L)
                .setContacts("公司联系人名称")
                .setEnterpriseName("公司名称")
                .setMailbox("<EMAIL>")
                .setMobilePhone("13654806584")
                .setOfficeAddress("XXXXXluldld")
                .setSuperiorId(0L)
                .setTenantId(1239378113944549447L)
                .setOwnerId(1L);
    }

    private QpCustomerOther getCustomerOther() {
        return new QpCustomerOther()
                .setId(1L)
                .setCustomerId(getCustomer().getId())
                .setChannelFlag(null)
                .setTenantId(1239378113944549447L)
                .setRenewalFlag(null)
                .setReputationFlag(null)
                .setCountyCode(1 + "")
                .setCountryName("china")
                .setProvinceCode("11")
                .setProvinceName("XX省")
                .setCityCode("111")
                .setCityName("市")
                .setCountyCode("1111")
                .setCountyName("旗县")
                .setCredentialsType(1 + "")
                .setCredentialsNo(1 + "");
    }

    private QpFileDetails getFileDetails() {
        return new QpFileDetails()
                .setId(1L)
                .setBusId(1L)
                .setBusType(QpFileDetailsConst.BusType.QUOTE_CHANNEL.getCode())
                .setBusClassify(1L)
                .setOssKey("4444444hhh");
    }

    public PlanConfigRulesDTO getPlanConfigRulesDTO() {
        PlanConfigRulesDTO planConfigRulesDTO = new PlanConfigRulesDTO();
        planConfigRulesDTO.setId(1L);
        planConfigRulesDTO.setAfterRelativeType(Lists.newArrayList("1"));
        planConfigRulesDTO.setChooseConfigRule(null);
        planConfigRulesDTO.setPlanConfigId(1L);
        planConfigRulesDTO.setAgeCheckMode(null);
        planConfigRulesDTO.setAgeSwitch(Lists.newArrayList(1 + ""));
        planConfigRulesDTO.setAvgAge(1);
        planConfigRulesDTO.setAvgAgeDefault(1);
        planConfigRulesDTO.setCanAddUser(null);
        planConfigRulesDTO.setCanDelUser(null);
        planConfigRulesDTO.setUserRefundWithClaim(null);
        planConfigRulesDTO.setSpouseRule(null);
        planConfigRulesDTO.setRelativeDelayDays(1);
        planConfigRulesDTO.setParentsRule(null);
        planConfigRulesDTO.setMaxPerson(2);
        planConfigRulesDTO.setRiskPersonProportion(1D);
        planConfigRulesDTO.setMinInsureDefault(1);
        planConfigRulesDTO.setJobCategoryRule(null);
        return planConfigRulesDTO;
    }

    public List<PlanConfigStandardDTO> getPlanConfigStandardDTOList() {
        PlanConfigStandardDTO dto = new PlanConfigStandardDTO();
        dto.setId(1L)
                .setPlanConfigId(1L)
                .setCompanyId(96L)
                .setBillType(null)
                .setHealthNotify("1")
                .setInsuranceRule("1")
                .setStandardType(null);
        return Lists.newArrayList(dto);
    }

    public QpQuote getQpQuote() {
        return new QpQuote()
                .setId(1L)
                .setQuoteInfoId(getQpQuoteInfo().getId());
    }

    public QpQuoteConfig getQpQuoteConfig() {
        return new QpQuoteConfig()
                .setId(1L)
                .setQuoteId(getQpQuote().getId());
    }
}
