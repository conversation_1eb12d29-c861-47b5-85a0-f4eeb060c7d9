package com.insgeek.business.quote.common.service.impl;

import com.google.common.collect.Lists;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.common.enums.QuoteChannelSql;
import com.insgeek.business.quote.frontend.aggservice.impl.QuoteManageAggServiceImpl;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.entity.QpTenantChannelProduct;
import com.insgeek.protocol.platform.user.dto.UserDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static org.mockito.ArgumentMatchers.any;

/**
 * @ClassName: RateAbstract
 * @Description:
 * @Author: YYY
 * @Date: 2022/7/15 9:27
 **/
@Slf4j
@RunWith(MockitoJUnitRunner.class)
class TenantChannelProductTest {

    MockMvc mockMvc;

    @InjectMocks
    private TenantChannelProductServiceImpl tenantChannelProductService;

    @Mock
    private DataMapper<QpTenantChannelProduct> qpTenantChannelProductDataMapper;

    @Mock
    private QuoteManageAggServiceImpl quoteManageAggService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(tenantChannelProductService).build();
    }

    @Test
    void advTenantChannelProduct() {
        try {
            Mockito.doReturn(qpTenantChannelProductDataMapper).when(qpTenantChannelProductDataMapper)
                    .entity(QpTenantChannelProduct.class);
            Mockito.when(qpTenantChannelProductDataMapper.select(any(QpTenantChannelProduct.class))).thenReturn(getQpTenantChannelProductList());
            Mockito.when(qpTenantChannelProductDataMapper.selectOne(getQpTenantChannelProduct().getId(), true)).thenReturn(getQpTenantChannelProduct());
            Mockito.when(qpTenantChannelProductDataMapper.insertOne(any(QpTenantChannelProduct.class))).thenReturn(getQpTenantChannelProduct());
            Mockito.when(qpTenantChannelProductDataMapper.updateOne(any(QpTenantChannelProduct.class))).thenReturn(1);
            Mockito.when(qpTenantChannelProductDataMapper.updateOne(any(QpTenantChannelProduct.class), true)).thenReturn(1);
            Mockito.when(qpTenantChannelProductDataMapper.deleteAll(any(List.class), true)).thenReturn(1);
            tenantChannelProductService.add(getQpTenantChannelProduct());
            tenantChannelProductService.modify(getQpTenantChannelProduct());
            tenantChannelProductService.remove(1239377753167296926L, true);
        } catch (Exception e) {
            System.out.println();
        }

    }

    @Test
    void queryCHANNEL() {
        try {


            String countBql = String.format(QuoteChannelSql.QUOTE_CHANNEL_LIST_COUNT_SQL.getSql(), getQpTenantChannelProduct().getPtTenantId()) + packageQuoteChannelListSql(1);
            System.out.println(countBql);
            Mockito.doReturn(mapCount()).when(quoteManageAggService).checkAndConvertNoPermission(Map.class, countBql, false);
            String orderStr = " ORDER BY " + "updated_at" + (1 == 1 ? " desc" : " asc");
            String listBql = QuoteChannelSql.QUOTE_CHANNEL_LIST_SQL.getSql() + "(" + getQpTenantChannelProduct().getPtTenantId() + ") " + packageQuoteChannelListSql(1) +
                    orderStr + " LIMIT " + ((1 - 1) * 10) + "," + 10;
            Mockito.when(quoteManageAggService.checkAndConvertNoPermission(QpTenantChannelProduct.class, listBql, false)).thenReturn(Lists.newArrayList(getQpTenantChannelProductList()));

            Mockito.when(quoteManageAggService.packListRespVoAction(any(List.class), any(), any(), any())).thenReturn(ResponseVO.data(getQpTenantChannelProductList()));

            ResponseVO updated_at = tenantChannelProductService.queryChannelList(getQpTenantChannelProduct().getPtTenantId() + "", 1, "updated_at", 1, 1, 10);
            log.info(JacksonUtils.writeAsString(updated_at));
        } catch (Exception e) {
            System.out.println();
        }
    }

    @Test
    void getDataID() {
        try {
            Mockito.doReturn(qpTenantChannelProductDataMapper).when(qpTenantChannelProductDataMapper)
                    .entity(QpTenantChannelProduct.class);
            Mockito.when(qpTenantChannelProductDataMapper.selectOne(getQpTenantChannelProduct().getId(), true)).thenReturn(getQpTenantChannelProduct());
            log.info("configid={}", tenantChannelProductService.getTenantChannelProduct(getQpTenantChannelProduct().getSecretAddress()));
        } catch (Exception e) {
            System.out.println("XXX");
        }
    }

    @Test
    void getLoginIdString() {
        try {
            Mockito.doReturn(qpTenantChannelProductDataMapper).when(qpTenantChannelProductDataMapper)
                    .entity(QpTenantChannelProduct.class);
            Mockito.when(qpTenantChannelProductDataMapper.selectOne(getQpTenantChannelProduct().getId(), true)).thenReturn(getQpTenantChannelProduct());

            String userSql = QuoteChannelSql.USER_SQL_WHERE.getSql() + " where tenant_id = " + 1239378113944549447L;
            Mockito.when(quoteManageAggService.checkAndConvertNoPermission(UserDto.class, userSql, false)).thenReturn(getUserDtoList());


            // log.info("configid={}", tenantChannelProductService.getLoginIdString(getQpTenantChannelProduct().getSecretAddress()));
        } catch (Exception e) {
            System.out.println("XXX");
        }
    }

    private String packageQuoteChannelListSql(Integer statusFlag) {
        return (Objects.isNull(statusFlag) || Objects.equals(statusFlag, NumberUtils.INTEGER_ZERO)) ? "" : " AND status_flag in(" + statusFlag + ")";
    }

    private List<UserDto> getUserDtoList() {
        UserDto userDto = new UserDto();
        userDto.setId(1239999L);
        userDto.setTenantId(1239378113944549447L);
        userDto.setAdminFlg(1);
        return Lists.newArrayList(userDto);
    }

    private List<QpTenantChannelProduct> getQpTenantChannelProductList() {

        return Lists.newArrayList(getQpTenantChannelProduct());
    }

    private List<Map> mapData() {
        Map map = new HashMap();
        map.put("id", 1239377753167296926L);
        map.put("pt_tenant_id", 1239378113944549447L);
        map.put("name", "newName");
        map.put("status_flag", 1L);
        map.put("commission_rate", BigDecimal.ONE);
        map.put("secret_address", "N+VfyX3CWiKWlxeCBDPRFU7PEGrEMe93OxiquLaf21uCR9wUvB0pCdGUHzLVmdqr");
        return Lists.newArrayList(map);
    }

    private List<Map> mapCount() {
        Map map = new HashMap();
        map.put("count", 1);
        return Lists.newArrayList(map);
    }


    private QpTenantChannelProduct getQpTenantChannelProduct() {
        return new QpTenantChannelProduct()
                .setId(1239377753167296926L)
                .setChannelName("newName")
                .setStatusFlag(null)
                .setChannelConfigId(123L)
                .setCommissionRate(BigDecimal.ONE)
                .setPtTenantId(1239378113944549447L)
                .setSecretAddress("N+VfyX3CWiKWlxeCBDPRFU7PEGrEMe93OxiquLaf21uCR9wUvB0pCdGUHzLVmdqr");
    }


}
