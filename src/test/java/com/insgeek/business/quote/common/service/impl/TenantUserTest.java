package com.insgeek.business.quote.common.service.impl;

import com.google.common.collect.Lists;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.backend.aggservice.impl.TenantUserServiceImpl;
import com.insgeek.business.quote.common.dto.UrlDto;
import com.insgeek.business.quote.common.dto.UserDto;
import com.insgeek.business.quote.common.enums.QuoteChannelSql;
import com.insgeek.business.quote.frontend.aggservice.impl.QuoteManageAggServiceImpl;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.entity.QpTenantUser;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

/**
 * @ClassName: RateAbstract
 * @Description:
 * @Author: YYY
 * @Date: 2022/7/15 9:27
 **/
@Slf4j
@RunWith(MockitoJUnitRunner.class)
class TenantUserTest {

    MockMvc mockMvc;

    @InjectMocks
    private TenantUserServiceImpl tenantUserService;

    @Mock
    private DataMapper<QpTenantUser> qpTenantUserDataMapper;

    @Mock
    private QuoteManageAggServiceImpl quoteManageAggService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(tenantUserService).build();
    }

    @Test
    void advUser() {
        try {
            Mockito.doReturn(qpTenantUserDataMapper).when(qpTenantUserDataMapper)
                    .entity(QpTenantUser.class);
            Mockito.when(qpTenantUserDataMapper.select(any(QpTenantUser.class))).thenReturn(getQpTenantUserList());
            Mockito.when(qpTenantUserDataMapper.selectOne(getQpTenantUser().getId(), true)).thenReturn(getQpTenantUser());
            Mockito.when(qpTenantUserDataMapper.insertOne(any(QpTenantUser.class))).thenReturn(getQpTenantUser());
            Mockito.when(qpTenantUserDataMapper.updateOne(any(QpTenantUser.class))).thenReturn(1);
            Mockito.when(qpTenantUserDataMapper.deleteAll(any(List.class), true)).thenReturn(1);
            tenantUserService.add(getQpTenantUser());
            tenantUserService.modify(getQpTenantUser());
            tenantUserService.remove(Lists.newArrayList(1L));
        } catch (Exception e) {
            System.out.println();
        }

    }

    @Test
    void queryUser() {
        try {
            String countBql = String.format(QuoteChannelSql.QUOTE_USER_LIST_COUNT_SQL.getSql(), 1234568L) + packageQuoteUseristSql(1 + "");
            Mockito.doReturn(mapCount()).when(quoteManageAggService).checkAndConvertNoPermission(Map.class, countBql, false);
            String orderStr = " ORDER BY " + "updated_at" + (1 == 1 ? " desc" : " asc");
            String listBql = QuoteChannelSql.QUOTE_USER_LIST_SQL.getSql() + "(" + 1234568L + ") " + packageQuoteUseristSql(1 + "") +
                    orderStr + " LIMIT " + ((1 - 1) * 10) + "," + 10;
            Mockito.when(quoteManageAggService.checkAndConvertNoPermission(QpTenantUser.class, listBql, false)).thenReturn(Lists.newArrayList(getQpTenantUser()));
            String userSql = QuoteChannelSql.USER_SQL.getSql() + "(" + org.apache.commons.lang3.StringUtils.join(Lists.newArrayList(1L), ",") + ")";

            Mockito.when(quoteManageAggService.checkAndConvertNoPermission(UserDto.class, userSql, false)).thenReturn(getUserDaoList());
            Mockito.when(quoteManageAggService.packListRespVoAction(any(List.class), any(), any(), any())).thenReturn(ResponseVO.data(Lists.newArrayList(getQpTenantUser())));

            ResponseVO updated_at = tenantUserService.queryUserList(1234568 + "", "1", "updated_at", 1, 1, 10);
            log.info(JacksonUtils.writeAsString(updated_at));
        } catch (Exception e) {
            System.out.println();
        }

    }

    private String packageQuoteUseristSql(String userType) {
        return (StringUtils.isEmpty(userType)) ? "" : " AND user_type in(" + userType + ")";
    }

    private List<UserDto> getUserDaoList() {
        UserDto userDto = new UserDto();
        userDto.setId(1L);
        userDto.setName("newName");
        userDto.setEmail("<EMAIL>");
        return Lists.newArrayList(userDto);
    }

    private List<Map> mapData() {
        Map map = new HashMap();
        map.put("id", 1);
        map.put("pt_tenant_id", 1234568L);
        map.put("user_type", 1);
        map.put("user_id", 1L);
        map.put("name", "named");
        map.put("maill", "<EMAIL>");
        return Lists.newArrayList(map);
    }

    private List<Map> mapCount() {
        Map map = new HashMap();
        map.put("count", 1);
        return Lists.newArrayList(map);
    }

    @Test
    void createUrl() throws Exception {
        UrlDto urlDto = new UrlDto(1239378113944549447L, 1239377753167296926L);
        log.info("createUrl ={}", JacksonUtils.writeAsString(urlDto));
        String afterEncryption = urlDto.getAfterEncryption();
        log.info("afterEncryption={}", afterEncryption);
    }

    private QpTenantUser getQpTenantUser() {
        return new QpTenantUser()
                .setPtTenantId(1234568L)
                .setUserType(1)
                .setUserId(1L)
                .setUserName("name1")
                .setMaill("<EMAIL>").setId(1L);
    }

    private List<QpTenantUser> getQpTenantUserList() {
        return Lists.newArrayList(
                new QpTenantUser().setPtTenantId(1234568L)
                        .setUserType(1)
                        .setUserId(2L)
                        .setUserName("name12")
                        .setMaill("<EMAIL>"),
                new QpTenantUser().setPtTenantId(1234568L)
                        .setUserType(2)
                        .setUserId(0L)
                        .setUserName("name12")
                        .setMaill("<EMAIL>"));
    }


}
