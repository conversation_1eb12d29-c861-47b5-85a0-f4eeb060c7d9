package com.insgeek.business.quote.common.utils;

import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.common.enums.dict.TenantType;
import com.insgeek.protocol.platform.user.client.TenantClient;
import com.insgeek.protocol.platform.user.client.UserDataScopeClient;
import com.insgeek.protocol.platform.user.dto.TenantClassDto;
import com.insgeek.protocol.platform.user.dto.TenantDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
class TenantUtilTest {

    MockMvc mockMvc;

    @Mock
    private TenantClient tenantClient;

    @Mock
    private UserDataScopeClient userDataScopeClient;

    @InjectMocks
    TenantUtil tenantUtil;


    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(tenantUtil).build();
    }

    @Test
    void getClassCodeByTenant() {
        TenantClassDto tenantClassDto = new TenantClassDto();
        tenantClassDto.setTenantTag(1);
        Mockito.doReturn(ResponseVO.builder().data(Collections.singletonList(tenantClassDto)).build()).when(tenantClient).tenant_info_no_auth(Collections.singletonList(2014L));

        String tenantTag = tenantUtil.getClassCodeByTenant(2014L);
        assertEquals(tenantTag, TenantType.TENANT_CLASS_INNER.getValue());
    }

    @Test
    void getTenantClassCode() {
        TenantDto tenantDto = new TenantDto();
        tenantDto.setClassCode("1");
        IdentityUtil.setTenantId(2014L);
        Mockito.doReturn(ResponseVO.builder().data(tenantDto).build()).when(userDataScopeClient).findById(2014L);

        assertEquals(tenantUtil.getTenantClassCode(),TenantType.TENANT_CLASS_INNER.getValue());
    }

}