package com.insgeek.business.quote.common.service.impl;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.insgeek.business.quote.common.dto.param.FieldDataParam;
import com.insgeek.business.quote.common.dto.param.FieldDefinitionParam;
import com.insgeek.business.quote.common.enums.CurrencyStateEnum;
import com.insgeek.business.quote.common.service.DefinitionFieldService;
import com.insgeek.business.quote.common.service.DefinitionTableService;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.entity.QpFieldData;
import com.insgeek.protocol.data.client.entity.QpFieldDefinition;
import com.insgeek.protocol.data.client.entity.QpTableDefinition;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @title: DynamicDataTest
 * @projectName insgeek-business-quote
 * @description: 1111
 * @date 2023/4/49:49
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
class FieldDataTest {
    MockMvc mockMvc;

    @InjectMocks
    private FieldDataServiceImpl fieldDataService;

    @Mock
    private DataMapper<QpFieldData> fieldDataDataMapper;

    @Mock
    private DefinitionTableService definitionTableService;
    @Mock
    private DefinitionFieldService definitionFieldService;

    @Mock
    private DefaultDefinitionService defaultDefinitionService;


    private static List<QpTableDefinition> tableDefinitions = getQpTableDefinition();
    private static QpTableDefinition tableDefinition = tableDefinitions.get(0);

    private static List<QpFieldDefinition> fieldDefinitions = getQpFieldDefinition();
    private static QpFieldDefinition fieldDefinition = fieldDefinitions.get(0);

    private static List<QpFieldData> fieldDatas = getQpFieldData();
    private static QpFieldData fieldData = fieldDatas.get(0);

    private static List<FieldDataParam> fieldDataParams = getFieldDataParam();
    private static FieldDataParam fieldDataParam = fieldDataParams.get(0);

    private static List<FieldDefinitionParam> fieldDefinitionParams = getFieldDefinitionParam();
    private static FieldDefinitionParam fieldDefinitionParam = fieldDefinitionParams.get(0);

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(fieldDataService).build();
    }

    @Test
    void save() {
        initData();
        try {
            fieldDataService.savePlugs(fieldData.getDataId(), fieldData.getDataGroup(), tableDefinition.getTableCode(), fieldDataParams, false, "NOT", "NOT", 1);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    void deleteData() {
        initData();
        try {
            fieldDataService.deleteData(fieldData.getDataId(), fieldData.getDataGroup(), tableDefinition.getTableCode(), 1 + "", "");
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    void savePlugsAll() {
        initData();
        try {
            fieldDataService.savePlugsAll(fieldData.getDataId(), tableDefinition.getTableCode(), getdataParamMapList(), false, false, "NOT", "NOT", 1);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    void savePlugsAllmitate() {
        initData();
        try {
            fieldDataService.savePlugsAllmitate(fieldData.getDataId(), tableDefinition.getTableCode(), getdataParamMapList2(), false, false, "NOT", "NOT", 1);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    void getDataGroup() {
        initData();
        try {
            fieldDataService.getDataGroup(fieldData.getDataId(), tableDefinition.getTableCode(), fieldData.getDataGroup());
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    void queryByTableCodeAndDataId() {
        initData();
        try {
            fieldDataService.queryByTableCodeAndDataId(fieldData.getDataId(), fieldData.getDataGroup(), tableDefinition.getTableCode());
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }


    @Test
    void queryAllByTableCodeAndDataId() {
        initData();
        try {
            fieldDataService.queryAllByTableCodeAndDataId(fieldData.getDataId(), fieldData.getDataGroup(), tableDefinition.getTableCode());
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    void queryAllmitate() {
        initData();
        try {
            fieldDataService.queryAllmitate(fieldData.getDataId(), fieldData.getDataGroup(), tableDefinition.getTableCode());
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    private void initData() {
        Mockito.doReturn(fieldDataDataMapper).when(fieldDataDataMapper).entity(QpFieldData.class);
        Mockito.when(fieldDataDataMapper.insertOne(fieldData, true)).thenReturn(fieldData);
        Mockito.when(fieldDataDataMapper.insertAll(fieldDatas, true)).thenReturn(fieldDatas);
        Mockito.when(fieldDataDataMapper.updateOne(fieldData, true)).thenReturn(1);
        Mockito.when(fieldDataDataMapper.selectOne(fieldData.getId(), true)).thenReturn(fieldData);
        Mockito.when(fieldDataDataMapper.entity(QpFieldData.class).select(any(DataCondition.class), any(Boolean.class))).thenReturn(fieldDatas);
        Mockito.when(fieldDataDataMapper.select(any(QpFieldData.class), any(Boolean.class))).thenReturn(fieldDatas);
        Mockito.when(fieldDataDataMapper.select(any(List.class), any(Boolean.class))).thenReturn(fieldDatas);

        Mockito.when(definitionTableService.findById(tableDefinition.getId())).thenReturn(tableDefinition);
        Mockito.when(definitionTableService.findByTableCode(tableDefinition.getTableCode())).thenReturn(tableDefinition);
        Mockito.when(definitionTableService.query(tableDefinition)).thenReturn(tableDefinitions);
        Mockito.when(definitionTableService.modify(tableDefinition)).thenReturn(tableDefinition);

        Mockito.when(definitionFieldService.queryByTableDefinitionId(1L, true)).thenReturn(fieldDefinitions);
        Mockito.when(definitionFieldService.save(1L, fieldDefinitionParams)).thenReturn(fieldDefinitions);
        Mockito.when(definitionFieldService.savePlugs(1L, fieldDefinitionParams)).thenReturn(fieldDefinitions);
        Mockito.when(definitionFieldService.modify(fieldDefinition)).thenReturn(fieldDefinition);

        /*try (MockedStatic<AbsDefinitionService> serviceMockedStatic = Mockito.mockStatic(AbsDefinitionService.class)) {
            serviceMockedStatic.when(() -> AbsDefinitionService.getInstance(any())).thenReturn(defaultDefinitionService);
        }*/


    }

    private static List<QpTableDefinition> getQpTableDefinition() {
        QpTableDefinition tableDefinition = new QpTableDefinition();
        tableDefinition.setId(1L)
                .setTableCode("1")
                .setTableStatus(Integer.valueOf(CurrencyStateEnum.YES.getCode()))
                .setTableType(1)
                .setTableTitle("ceshi");
        return Lists.newArrayList(tableDefinition);
    }

    private static List<QpFieldDefinition> getQpFieldDefinition() {
        QpFieldDefinition fieldDefinition = new QpFieldDefinition();
        fieldDefinition.setId(1L)
                .setTableDefinitionId(getQpTableDefinition().get(0).getId())
                .setFieldCode("abc")
                .setFieldName("ceshi")
                .setFieldStatus(1)
                .setFieldVal("3")
                .setFieldRequired(2)
                .setEditCondition("");
        return Lists.newArrayList(fieldDefinition);
    }

    private static List<FieldDefinitionParam> getFieldDefinitionParam() {
        FieldDefinitionParam param = new FieldDefinitionParam();
        param.setFieldCode("abc")
                .setValType(1)
                .setFieldName("ceshi")
                .setTableDefinitionId(1L)
                .setFieldStatus(1);
        return Lists.newArrayList(param);
    }


    private static List<QpFieldData> getQpFieldData() {
        QpFieldData fieldData = new QpFieldData()
                .setDataGroup("0")
                .setFieldDefinitionId(getQpFieldDefinition().get(0).getId())
                .setFieldVal("12")
                .setId(1L)
                .setDataId(1 + "");
        return Lists.newArrayList(fieldData);
    }

    private static List<FieldDataParam> getFieldDataParam() {
        FieldDataParam param = new FieldDataParam();
        param.setFieldCode(getFieldDefinitionParam().get(0).getFieldCode())
                .setFieldVal("12");
        return Lists.newArrayList(param);
    }

    private static List<Map<String, List<FieldDataParam>>> getdataParamMapList() {
        //返回数据
        List<Map<String, List<FieldDataParam>>> dataParamMapList = Lists.newArrayList();

        Map<String, List<FieldDataParam>> map = Maps.newHashMap();
        map.put("0", getFieldDataParam());
        dataParamMapList.add(map);
        return dataParamMapList;
    }

    private static List<Map<String, Object>> getdataParamMapList2() {
        List<Map<String, Object>> dataParamMapList = Lists.newArrayList();
        Map<String, Object> map = Maps.newHashMap();
        map.put("data_group", 0 + "");
        map.put("abc", 1);

        dataParamMapList.add(map);
        return dataParamMapList;
    }

}
