package com.insgeek.business.quote.common.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.common.annotation.ExcelAnnotation;
import com.insgeek.business.quote.common.service.impl.CommonValueServiceImpl;
import com.insgeek.business.quote.frontend.aggservice.QuoteManageAggService;
import com.insgeek.business.quote.frontend.dto.manage.QpQuoteDto;
import com.insgeek.protocol.platform.metadata.client.DictClient;
import com.insgeek.protocol.platform.metadata.dto.DictDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
class ExportUtilTest {

    MockMvc mockMvc;

    @Mock
    private QuoteManageAggService quoteManageAggService;

    @Mock
    private CommonValueServiceImpl commonValueService;

    @Mock
    private DictClient dictClient;

    @InjectMocks
    ExportUtil exportUtil;


    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(exportUtil).build();
    }

    @Test
    void getTemplateResource() throws IllegalAccessException {
        String dictJson = "{\n" +
                "    \"qp_quote\":{\n" +
                "        \"business_source\":[\n" +
                "            {\n" +
                "                \"id\":\"114561236\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"1\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"电话团队线索\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"114561237\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"2\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"HR转介绍\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"114561238\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"3\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"自己获取（电话/微信/活动）\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"114561239\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"4\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"保险公司业务员推荐\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"114561240\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"5\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"经纪公司/代理公司人员推荐\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"114561241\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"6\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"第三方人员推荐（员福/易才/豆包）\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"114561242\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"7\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"离职销售推荐\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"114561243\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"8\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"其它\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        Map<String, Map<String, List<DictDto>>> dictMap = JacksonUtils.readValue(dictJson, new TypeReference<Map<String, Map<String, List<DictDto>>>>() {
        });
        Mockito.when(dictClient.listDictByEntityKeys(Collections.singletonList("qp_quote")))
                .thenReturn(ResponseVO.<Map<String, Map<String, List<DictDto>>>>builder().data(dictMap).build());


        String quoteDetail = "{\n" +
                "    \"qp_quote_dto\":{\n" +
                "        \"id\":930281257659593384,\n" +
                "        \"link_quote_id\":0,\n" +
                "        \"avg_age\":40,\n" +
                "        \"age_range\":\"无\",\n" +
                "        \"quote_count\":2,\n" +
                "        \"bill_period\":3,\n" +
                "        \"bill_unit\":2,\n" +
                "        \"customer_id\":114573864,\n" +
                "        \"customer_name\":\"厦门鲸鹳科技有限公司\",\n" +
                "        \"group_name\":\"极客邦控股（北京）有限公司\",\n" +
                "        \"quote_price\":236.8,\n" +
                "        \"expected_max_price\":1000,\n" +
                "        \"expected_min_price\":1,\n" +
                "        \"female_count\":5,\n" +
                "        \"first_payment_time\":\"\",\n" +
                "        \"health_report\":0,\n" +
                "        \"history_insurance_consumer\":\"极客版承包公司\",\n" +
                "        \"history_loss_ratio\":11,\n" +
                "        \"insured_count\":10000,\n" +
                "        \"insured_years\":3,\n" +
                "        \"major_past_illness_count\":1,\n" +
                "        \"max_age\":100,\n" +
                "        \"medicare_type\":1,\n" +
                "        \"min_age\":1,\n" +
                "        \"name\":\"极客邦控股（北京）有限公司2022-03-28报价\",\n" +
                "        \"occupation\":1,\n" +
                "        \"owner_id\":114511122,\n" +
                "        \"pay_period\":3,\n" +
                "        \"pay_type\":1,\n" +
                "        \"pay_unit\":2,\n" +
                "        \"plan_id\":0,\n" +
                "        \"quote_confirm_time\":\"\",\n" +
                "        \"quote_pass_time\":\"\",\n" +
                "        \"quote_time\":\"2022-03-28 11:27:27\",\n" +
                "        \"target_person\":[\n" +
                "            \"1\"\n" +
                "        ],\n" +
                "        \"sales_staff\":\"晓东测试\",\n" +
                "        \"staff_count\":0,\n" +
                "        \"status\":2,\n" +
                "        \"yjy_code\":\"\",\n" +
                "        \"start_date\":\"2022-03-01 00:00:00\",\n" +
                "        \"end_date\":\"2023-02-28 23:59:59\",\n" +
                "        \"comments\":\"\",\n" +
                "        \"business_source\":3,\n" +
                "        \"visit_times\":15,\n" +
                "        \"insured_type\":13,\n" +
                "        \"total_persons\":10000,\n" +
                "        \"medical_insured_persons\":1000,\n" +
                "        \"female_proportion\":0.05,\n" +
                "        \"commission_rate\":10\n" +
                "    },\n" +
                "    \"prod_list\":[\n" +
                "        {\n" +
                "            \"pkg_name\":\"准生产标品\",\n" +
                "            \"pkg_id\":104592090,\n" +
                "            \"quote_duty_list\":[\n" +
                "                {\n" +
                "                    \"id\":930281257659593440,\n" +
                "                    \"amount\":20000,\n" +
                "                    \"deduction\":0,\n" +
                "                    \"once_deduction\":1500,\n" +
                "                    \"once_quota\":400,\n" +
                "                    \"payment\":100,\n" +
                "                    \"product_id\":104592123,\n" +
                "                    \"qp_product_id\":0,\n" +
                "                    \"property\":2,\n" +
                "                    \"price\":100,\n" +
                "                    \"cost_price\":100,\n" +
                "                    \"agent_ratio\":10,\n" +
                "                    \"is_edit\":1,\n" +
                "                    \"product_type_name\":\"补充医疗门（急）诊\",\n" +
                "                    \"product_type\":\"1321220\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\":930281257659593441,\n" +
                "                    \"amount\":150000,\n" +
                "                    \"deduction\":0,\n" +
                "                    \"once_deduction\":0,\n" +
                "                    \"once_quota\":0,\n" +
                "                    \"payment\":0,\n" +
                "                    \"product_id\":104593230,\n" +
                "                    \"qp_product_id\":0,\n" +
                "                    \"property\":4,\n" +
                "                    \"price\":15,\n" +
                "                    \"cost_price\":15,\n" +
                "                    \"agent_ratio\":10,\n" +
                "                    \"is_edit\":1,\n" +
                "                    \"product_type_name\":\"重大疾病\",\n" +
                "                    \"product_type\":\"1311000\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\":930281257659593442,\n" +
                "                    \"amount\":100000,\n" +
                "                    \"deduction\":0,\n" +
                "                    \"once_deduction\":0,\n" +
                "                    \"once_quota\":0,\n" +
                "                    \"payment\":0,\n" +
                "                    \"product_id\":104597883,\n" +
                "                    \"qp_product_id\":0,\n" +
                "                    \"property\":4,\n" +
                "                    \"price\":70,\n" +
                "                    \"cost_price\":70,\n" +
                "                    \"agent_ratio\":10,\n" +
                "                    \"is_edit\":1,\n" +
                "                    \"product_type_name\":\"自驾（乘）汽车意外伤害\",\n" +
                "                    \"product_type\":\"1420050\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\":930281257659593443,\n" +
                "                    \"amount\":500000,\n" +
                "                    \"deduction\":0,\n" +
                "                    \"once_deduction\":0,\n" +
                "                    \"once_quota\":0,\n" +
                "                    \"payment\":0,\n" +
                "                    \"product_id\":104822356,\n" +
                "                    \"qp_product_id\":0,\n" +
                "                    \"property\":4,\n" +
                "                    \"price\":50,\n" +
                "                    \"cost_price\":50,\n" +
                "                    \"agent_ratio\":10,\n" +
                "                    \"is_edit\":1,\n" +
                "                    \"product_type_name\":\"意外身故、残疾\",\n" +
                "                    \"product_type\":\"1112000\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\":930281257659593444,\n" +
                "                    \"amount\":300,\n" +
                "                    \"deduction\":180,\n" +
                "                    \"once_deduction\":0,\n" +
                "                    \"once_quota\":0,\n" +
                "                    \"payment\":20,\n" +
                "                    \"product_id\":114315973,\n" +
                "                    \"qp_product_id\":0,\n" +
                "                    \"property\":5,\n" +
                "                    \"price\":1.8,\n" +
                "                    \"cost_price\":1.8,\n" +
                "                    \"agent_ratio\":10,\n" +
                "                    \"is_edit\":1,\n" +
                "                    \"product_type_name\":\"意外住院津贴\",\n" +
                "                    \"product_type\":\"1322200\"\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ],\n" +
                "    \"tenant_type\":1,\n" +
                "    \"special_rules\":{\n" +
                "        \"qoute\":[\n" +
                "            {\n" +
                "                \"id\":\"930281257659593452\",\n" +
                "                \"layout_template\":\"医院类型多选框：${customItem33} ；\",\n" +
                "                \"complete_layout_template\":\"医院类型多选框：私立医院 ；\",\n" +
                "                \"name\":\"报价特约\",\n" +
                "                \"scope\":1,\n" +
                "                \"rule_business_type\":\"defaultBusiType\",\n" +
                "                \"items\":[\n" +
                "                    {\n" +
                "                        \"id\":\"114511194\",\n" +
                "                        \"layout_item_type\":2,\n" +
                "                        \"item_type\":3,\n" +
                "                        \"item_type_name\":\"多选\",\n" +
                "                        \"item_title\":\"医院类型\",\n" +
                "                        \"item_key\":\"customItem33\",\n" +
                "                        \"item_name\":\"医院类型\",\n" +
                "                        \"is_must\":0,\n" +
                "                        \"read_only\":0,\n" +
                "                        \"assembly_style\":7,\n" +
                "                        \"item_value\":\"[2]\",\n" +
                "                        \"enable_flg\":true,\n" +
                "                        \"help_text\":\"\",\n" +
                "                        \"min_length\":0,\n" +
                "                        \"max_length\":2147483647,\n" +
                "                        \"visible_flg\":1,\n" +
                "                        \"must_flg\":0,\n" +
                "                        \"dict\":[\n" +
                "                            {\n" +
                "                                \"name\":\"医院类型\",\n" +
                "                                \"dict_key\":\"1\",\n" +
                "                                \"dict_value\":\"一级医院\",\n" +
                "                                \"default_flg\":false\n" +
                "                            },\n" +
                "                            {\n" +
                "                                \"name\":\"医院类型\",\n" +
                "                                \"dict_key\":\"2\",\n" +
                "                                \"dict_value\":\"私立医院\",\n" +
                "                                \"default_flg\":false\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"config_list\":[\n" +
                "\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"930281257659593453\",\n" +
                "                \"layout_template\":\"${customItem2}\",\n" +
                "                \"complete_layout_template\":\"aa112！#！\",\n" +
                "                \"name\":\"报价平台-自定义特约\",\n" +
                "                \"scope\":1,\n" +
                "                \"rule_business_type\":\"defaultBusiType\",\n" +
                "                \"items\":[\n" +
                "                    {\n" +
                "                        \"id\":\"114480147\",\n" +
                "                        \"layout_item_type\":2,\n" +
                "                        \"item_type\":15,\n" +
                "                        \"item_type_name\":\"文本域\",\n" +
                "                        \"item_title\":\"报价特别约定内容\",\n" +
                "                        \"item_key\":\"customItem2\",\n" +
                "                        \"item_name\":\"方案特别约定内容\",\n" +
                "                        \"is_must\":0,\n" +
                "                        \"read_only\":0,\n" +
                "                        \"assembly_style\":3,\n" +
                "                        \"item_value\":\"aa112！#！\",\n" +
                "                        \"enable_flg\":true,\n" +
                "                        \"help_text\":\"\",\n" +
                "                        \"min_length\":0,\n" +
                "                        \"max_length\":5000,\n" +
                "                        \"visible_flg\":1,\n" +
                "                        \"must_flg\":0\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"config_list\":[\n" +
                "\n" +
                "                ]\n" +
                "            }\n" +
                "        ],\n" +
                "        \"duty\":[\n" +
                "            {\n" +
                "                \"id\":\"930281257659593493\",\n" +
                "                \"layout_template\":\"扩展${customItem6}社保外药品及诊疗项目，赔付比例为${customItem30}\",\n" +
                "                \"complete_layout_template\":\"扩展丙类社保外药品及诊疗项目，赔付比例为10%\",\n" +
                "                \"name\":\"报价测试-测试扩展药品\",\n" +
                "                \"scope\":2,\n" +
                "                \"rule_business_type\":\"g3whsz4mpu\",\n" +
                "                \"duty_business_type\":\"1321220\",\n" +
                "                \"items\":[\n" +
                "                    {\n" +
                "                        \"id\":\"112004078\",\n" +
                "                        \"layout_item_type\":2,\n" +
                "                        \"item_type\":2,\n" +
                "                        \"item_type_name\":\"单选\",\n" +
                "                        \"item_title\":\"药品及诊疗项目报销类别\",\n" +
                "                        \"item_key\":\"customItem6\",\n" +
                "                        \"item_name\":\"药品及诊疗项目报销类别\",\n" +
                "                        \"is_must\":1,\n" +
                "                        \"read_only\":0,\n" +
                "                        \"assembly_style\":6,\n" +
                "                        \"item_value\":\"3\",\n" +
                "                        \"enable_flg\":true,\n" +
                "                        \"help_text\":\"\",\n" +
                "                        \"min_length\":0,\n" +
                "                        \"max_length\":2147483647,\n" +
                "                        \"visible_flg\":1,\n" +
                "                        \"must_flg\":1,\n" +
                "                        \"dict\":[\n" +
                "                            {\n" +
                "                                \"name\":\"药品及诊疗项目报销类别\",\n" +
                "                                \"dict_key\":\"2\",\n" +
                "                                \"dict_value\":\"乙类\",\n" +
                "                                \"default_flg\":false\n" +
                "                            },\n" +
                "                            {\n" +
                "                                \"name\":\"药品及诊疗项目报销类别\",\n" +
                "                                \"dict_key\":\"3\",\n" +
                "                                \"dict_value\":\"丙类\",\n" +
                "                                \"default_flg\":true\n" +
                "                            }\n" +
                "                        ]\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\":\"112004079\",\n" +
                "                        \"layout_item_type\":2,\n" +
                "                        \"item_type\":29,\n" +
                "                        \"item_type_name\":\"百分比\",\n" +
                "                        \"item_title\":\"实际赔付比例\",\n" +
                "                        \"item_key\":\"customItem30\",\n" +
                "                        \"item_name\":\"实际赔付比例\",\n" +
                "                        \"is_must\":1,\n" +
                "                        \"read_only\":0,\n" +
                "                        \"assembly_style\":4,\n" +
                "                        \"item_value\":\"10\",\n" +
                "                        \"enable_flg\":true,\n" +
                "                        \"help_text\":\"\",\n" +
                "                        \"min_length\":0,\n" +
                "                        \"max_length\":100,\n" +
                "                        \"visible_flg\":1,\n" +
                "                        \"must_flg\":1\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"config_list\":[\n" +
                "                    {\n" +
                "                        \"rule_id\":\"930281257659593493\",\n" +
                "                        \"plan_config_id\":\"930281257659593421\",\n" +
                "                        \"duty_id\":\"930281257659593440\",\n" +
                "                        \"rule_model_id\":\"112004074\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"930281257659593492\",\n" +
                "                \"layout_template\":\"补充医疗门（急）诊的就诊医院限中国大陆公立二级及以上医院普通部（不含特需、外宾，国际医疗部及高端病房）。以上医院约定均不包括精神病医院、职工医院、联合诊所及专供康复、休养、戒毒、戒酒、护理、养老等非以直接诊治病人为目的的医疗机构；北京市平谷区、怀柔区、密云县、顺义区除A类医院以外的所有医院均不在赔付范围内\",\n" +
                "                \"complete_layout_template\":\"补充医疗门（急）诊的就诊医院限中国大陆公立二级及以上医院普通部（不含特需、外宾，国际医疗部及高端病房）。以上医院约定均不包括精神病医院、职工医院、联合诊所及专供康复、休养、戒毒、戒酒、护理、养老等非以直接诊治病人为目的的医疗机构；北京市平谷区、怀柔区、密云县、顺义区除A类医院以外的所有医院均不在赔付范围内\",\n" +
                "                \"name\":\"报价平台-就诊医院1\",\n" +
                "                \"scope\":2,\n" +
                "                \"rule_business_type\":\"u9s5lp76xgd\",\n" +
                "                \"duty_business_type\":\"1321220\",\n" +
                "                \"items\":[\n" +
                "\n" +
                "                ],\n" +
                "                \"config_list\":[\n" +
                "                    {\n" +
                "                        \"rule_id\":\"930281257659593492\",\n" +
                "                        \"plan_config_id\":\"930281257659593421\",\n" +
                "                        \"duty_id\":\"930281257659593440\",\n" +
                "                        \"rule_model_id\":\"104674634\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"930281257659593491\",\n" +
                "                \"layout_template\":\"补充医疗门（急）诊不承担生效前已患重大既往症及其并发症导致的赔付责任\",\n" +
                "                \"complete_layout_template\":\"补充医疗门（急）诊不承担生效前已患重大既往症及其并发症导致的赔付责任\",\n" +
                "                \"name\":\"报价平台-既往症1\",\n" +
                "                \"scope\":2,\n" +
                "                \"rule_business_type\":\"4abbhxtywff\",\n" +
                "                \"duty_business_type\":\"1321220\",\n" +
                "                \"items\":[\n" +
                "\n" +
                "                ],\n" +
                "                \"config_list\":[\n" +
                "                    {\n" +
                "                        \"rule_id\":\"930281257659593491\",\n" +
                "                        \"plan_config_id\":\"930281257659593421\",\n" +
                "                        \"duty_id\":\"930281257659593440\",\n" +
                "                        \"rule_model_id\":\"104674285\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"930281257659593490\",\n" +
                "                \"layout_template\":\"补充医疗门（急）诊不承担任何原因产生的牙科相关赔付责任\",\n" +
                "                \"complete_layout_template\":\"补充医疗门（急）诊不承担任何原因产生的牙科相关赔付责任\",\n" +
                "                \"name\":\"报价平台-牙科1\",\n" +
                "                \"scope\":2,\n" +
                "                \"rule_business_type\":\"28thsqmar1y\",\n" +
                "                \"duty_business_type\":\"1321220\",\n" +
                "                \"items\":[\n" +
                "\n" +
                "                ],\n" +
                "                \"config_list\":[\n" +
                "                    {\n" +
                "                        \"rule_id\":\"930281257659593490\",\n" +
                "                        \"plan_config_id\":\"930281257659593421\",\n" +
                "                        \"duty_id\":\"930281257659593440\",\n" +
                "                        \"rule_model_id\":\"104668872\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"930281257659593489\",\n" +
                "                \"layout_template\":\"补充医疗门（急）诊需要社保先行结算\",\n" +
                "                \"complete_layout_template\":\"补充医疗门（急）诊需要社保先行结算\",\n" +
                "                \"name\":\"报价平台-社保结算1\",\n" +
                "                \"scope\":2,\n" +
                "                \"rule_business_type\":\"2gtcb1btvuj\",\n" +
                "                \"duty_business_type\":\"1321220\",\n" +
                "                \"items\":[\n" +
                "\n" +
                "                ],\n" +
                "                \"config_list\":[\n" +
                "                    {\n" +
                "                        \"rule_id\":\"930281257659593489\",\n" +
                "                        \"plan_config_id\":\"930281257659593421\",\n" +
                "                        \"duty_id\":\"930281257659593440\",\n" +
                "                        \"rule_model_id\":\"104667278\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"930281257659593488\",\n" +
                "                \"layout_template\":\"扩展社保范围外费用，社保范围外乙类按照${customItem34}赔付，每人赔付限额${customItem36}元；社保范围外丙类按照${customItem35}赔付，每人赔付限额${customItem37}元\",\n" +
                "                \"complete_layout_template\":\"扩展社保范围外费用，社保范围外乙类按照15.0%赔付，每人赔付限额16元；社保范围外丙类按照17.0%赔付，每人赔付限额18元\",\n" +
                "                \"name\":\"报价平台-扩展药品\",\n" +
                "                \"scope\":2,\n" +
                "                \"rule_business_type\":\"6inzgpe87bg\",\n" +
                "                \"duty_business_type\":\"1321220\",\n" +
                "                \"items\":[\n" +
                "                    {\n" +
                "                        \"id\":\"104667275\",\n" +
                "                        \"layout_item_type\":2,\n" +
                "                        \"item_type\":29,\n" +
                "                        \"item_type_name\":\"百分比\",\n" +
                "                        \"item_title\":\"丙类赔付比例\",\n" +
                "                        \"item_key\":\"customItem35\",\n" +
                "                        \"item_name\":\"丙类赔付比例\",\n" +
                "                        \"is_must\":1,\n" +
                "                        \"read_only\":0,\n" +
                "                        \"assembly_style\":4,\n" +
                "                        \"item_value\":\"17.0\",\n" +
                "                        \"enable_flg\":true,\n" +
                "                        \"help_text\":\"\",\n" +
                "                        \"min_length\":0,\n" +
                "                        \"max_length\":100,\n" +
                "                        \"visible_flg\":1,\n" +
                "                        \"must_flg\":1\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\":\"104667274\",\n" +
                "                        \"layout_item_type\":2,\n" +
                "                        \"item_type\":4,\n" +
                "                        \"item_type_name\":\"整数\",\n" +
                "                        \"item_title\":\"乙类赔付限额\",\n" +
                "                        \"item_key\":\"customItem36\",\n" +
                "                        \"item_name\":\"乙类赔付限额\",\n" +
                "                        \"is_must\":1,\n" +
                "                        \"read_only\":0,\n" +
                "                        \"assembly_style\":4,\n" +
                "                        \"item_value\":\"16\",\n" +
                "                        \"enable_flg\":true,\n" +
                "                        \"help_text\":\"\",\n" +
                "                        \"min_length\":0,\n" +
                "                        \"max_length\":99999999,\n" +
                "                        \"visible_flg\":1,\n" +
                "                        \"must_flg\":1\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\":\"104667273\",\n" +
                "                        \"layout_item_type\":2,\n" +
                "                        \"item_type\":29,\n" +
                "                        \"item_type_name\":\"百分比\",\n" +
                "                        \"item_title\":\"乙类赔付比例\",\n" +
                "                        \"item_key\":\"customItem34\",\n" +
                "                        \"item_name\":\"乙类赔付比例\",\n" +
                "                        \"is_must\":1,\n" +
                "                        \"read_only\":0,\n" +
                "                        \"assembly_style\":4,\n" +
                "                        \"item_value\":\"15.0\",\n" +
                "                        \"enable_flg\":true,\n" +
                "                        \"help_text\":\"\",\n" +
                "                        \"min_length\":0,\n" +
                "                        \"max_length\":100,\n" +
                "                        \"visible_flg\":1,\n" +
                "                        \"must_flg\":1\n" +
                "                    },\n" +
                "                    {\n" +
                "                        \"id\":\"104667276\",\n" +
                "                        \"layout_item_type\":2,\n" +
                "                        \"item_type\":4,\n" +
                "                        \"item_type_name\":\"整数\",\n" +
                "                        \"item_title\":\"丙类赔付限额\",\n" +
                "                        \"item_key\":\"customItem37\",\n" +
                "                        \"item_name\":\"丙类赔付限额\",\n" +
                "                        \"is_must\":1,\n" +
                "                        \"read_only\":0,\n" +
                "                        \"assembly_style\":4,\n" +
                "                        \"item_value\":\"18\",\n" +
                "                        \"enable_flg\":true,\n" +
                "                        \"help_text\":\"\",\n" +
                "                        \"min_length\":0,\n" +
                "                        \"max_length\":99999999,\n" +
                "                        \"visible_flg\":1,\n" +
                "                        \"must_flg\":1\n" +
                "                    }\n" +
                "                ],\n" +
                "                \"config_list\":[\n" +
                "                    {\n" +
                "                        \"rule_id\":\"930281257659593488\",\n" +
                "                        \"plan_config_id\":\"930281257659593421\",\n" +
                "                        \"duty_id\":\"930281257659593440\",\n" +
                "                        \"rule_model_id\":\"104667264\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"930281257659593494\",\n" +
                "                \"layout_template\":\"重大疾病的等待期为30天\",\n" +
                "                \"complete_layout_template\":\"重大疾病的等待期为30天\",\n" +
                "                \"name\":\"报价-重大疾病\",\n" +
                "                \"scope\":2,\n" +
                "                \"rule_business_type\":\"1gnq95ls3oi\",\n" +
                "                \"duty_business_type\":\"1311000\",\n" +
                "                \"items\":[\n" +
                "\n" +
                "                ],\n" +
                "                \"config_list\":[\n" +
                "                    {\n" +
                "                        \"rule_id\":\"930281257659593494\",\n" +
                "                        \"plan_config_id\":\"930281257659593421\",\n" +
                "                        \"duty_id\":\"930281257659593441\",\n" +
                "                        \"rule_model_id\":\"104621662\"\n" +
                "                    }\n" +
                "                ]\n" +
                "            }\n" +
                "        ]\n" +
                "    },\n" +
                "    \"extend\":{\n" +
                "        \"owner_name\":\"保险极客官方账号\"\n" +
                "    }\n" +
                "}";
        // TODO @李阳阳 一个报价多个方案合并后以下代码报错，先临时注释
//        QpQuoteDetailDto qpQuoteDetailDto = JacksonUtils.readValue(quoteDetail, QpQuoteDetailDto.class);
//        Mockito.when(quoteManageAggService.queryQuoteDetail(anyLong())).thenReturn(qpQuoteDetailDto);
//        Map<String, Object> templateResource = freemarkerUtil.getTemplateResource(qpQuoteDetailDto);
//        assertFalse(CollectionUtils.isEmpty(templateResource));
//        assertEquals("极客邦控股（北京）有限公司null保障方案",templateResource.get("planName"));
//        List<Map<String, Object>> basicList = (List<Map<String, Object>>) templateResource.get("basicInfo");
//        assertEquals(15,basicList.size());
//        List<String> dutyList = (List<String>) templateResource.get("duty");
//        assertEquals(7,dutyList.size());

    }

    /*@Test
    void excelAnnotation() {
        String qpQuoteDtoJson = "{\"id\":114667351,\"link_quote_id\":0,\"avg_age\":35,\"quote_count\":26,\"bill_period\":3,\"bill_unit\":2,\"customer_id\":114513742,\"customer_name\":\"厦门鲸鹳科技有限公司\",\"group_name\":\"北京鲸鹳科技有限公司陕西分公司\",\"quote_price\":424.25,\"expected_max_price\":100.0,\"expected_min_price\":1.0,\"female_count\":80,\"first_payment_time\":\"\",\"health_report\":0,\"history_insurance_consumer\":\"\",\"history_loss_ratio\":0.0,\"insured_count\":80,\"insured_years\":0,\"major_past_illness_count\":0,\"max_age\":100,\"medicare_type\":1,\"min_age\":1,\"name\":\"北京鲸鹳科技有限公司陕西分公司2022-03-18报价\",\"occupation\":4,\"owner_id\":114513377,\"pay_period\":3,\"pay_type\":1,\"pay_unit\":2,\"plan_id\":0,\"quote_confirm_time\":\"\",\"quote_pass_time\":\"\",\"quote_time\":\"2022-03-18 18:14:10\",\"target_person\":[\"1\"],\"sales_staff\":\"大流程\",\"staff_count\":100,\"status\":3,\"yjy_code\":\"\",\"start_date\":\"2022-03-01 00:00:00\",\"end_date\":\"2023-02-28 23:59:59\",\"comments\":\"\",\"business_source\":1,\"visit_times\":1,\"insured_type\":11,\"total_persons\":99999,\"medical_insured_persons\":55,\"female_proportion\":100.0,\"commission_rate\":100.0}";
        QpQuoteDto qpQuoteDto = JacksonUtils.readValue(qpQuoteDtoJson, QpQuoteDto.class);
        Field[] declaredFields = qpQuoteDto.getClass().getDeclaredFields();
        List<Field> fieldList = Arrays.stream(declaredFields).filter(item -> item.isAnnotationPresent(ExcelAnnotation.class)).collect(Collectors.toList());
        for (Field declaredField : fieldList) {
            String fieldName = declaredField.getName();
            ExcelAnnotation annotation = declaredField.getAnnotation(ExcelAnnotation.class);
            if ("avgAge".equals(fieldName)) {
                assertEquals("平均年龄", annotation.meaning());
            } else if ("ageRange".equals(fieldName)) {
                assertEquals("9", annotation.sort());
            } else if ("historyInsuranceConsumer".equals(fieldName)) {
                assertEquals(Boolean.FALSE, annotation.isRequired());
            } else if ("historyLossRatio".equals(fieldName)) {
                assertEquals(Boolean.TRUE, annotation.algorithm());
            } else if ("businessSource".equals(fieldName)) {
                assertEquals("1", annotation.tenantTag());
            }
        }
    }*/
}