package com.insgeek.business.quote.common.service.impl;

import com.google.common.collect.Lists;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.entity.QpFileDetails;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * @ClassName: RateAbstract
 * @Description:
 * @Author: YYY
 * @Date: 2022/7/15 9:27
 **/
@Slf4j
@RunWith(MockitoJUnitRunner.class)
class FileDetailsServiceTest {

    MockMvc mockMvc;

    @InjectMocks
    private QuoteFileServiceImpl fileService;

    @Mock
    private DataMapper<QpFileDetails> qpFileDetailsMapper;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(fileService).build();
    }

    @Test
    void executeRate() {
        try {
            Mockito.doReturn(qpFileDetailsMapper).when(qpFileDetailsMapper)
                    .entity(QpFileDetails.class);
            Mockito.when(qpFileDetailsMapper.insertAll(any(List.class))).thenReturn(getQpFileDetailsList());
            //Mockito.when(qpFileDetailsMapper.deleteListByIds(Lists.newArrayList(1L)));
            System.out.println("saveFiles---------->"+fileService.saveFiles(null,null,null,getQpFileDetailsList()));
            System.out.println("save---------->"+fileService.save(getQpFileDetailsList()));
            System.out.println("deleteFiles---------->"+fileService.deleteFiles(Lists.newArrayList(1L)));
            System.out.println("deleteFiles---------->"+fileService.getFile(1L));
            System.out.println("deleteFiles---------->"+fileService.deleteFiles(1,1L,-1));
        } catch (Exception e) {
            System.out.println();
        }

    }

    private List<QpFileDetails> getQpFileDetailsList() {
        List<QpFileDetails> list = Lists.newArrayList();
        QpFileDetails qpFileDetails = new QpFileDetails();
        qpFileDetails.setBusType(1)
                .setBusId(1L)
                .setBusClassify(1L)
                .setFastUrl("FastUrl")
                .setFileName("name")
                .setFileSize(88888L)
                .setFileType("PDF")
                .setOssKey("44444444");
        list.add(qpFileDetails);
        return list;
    }

    private List<QpFileDetails> getOldQpFileDetailsList() {
        List<QpFileDetails> list = Lists.newArrayList();
        QpFileDetails qpFileDetails = new QpFileDetails();
        qpFileDetails.setId(2L)
                .setBusType(1)
                .setBusId(1L)
                .setBusClassify(1L)
                .setFastUrl("FastUrl")
                .setFileName("name")
                .setFileSize(99999L)
                .setFileType("PDF")
                .setOssKey("44444444");
        list.add(qpFileDetails);
        return list;
    }

}
