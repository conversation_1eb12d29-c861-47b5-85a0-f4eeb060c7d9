package com.insgeek.business.quote.common.service.impl;

import com.google.common.collect.Lists;
import com.insgeek.business.quote.common.enums.CurrencyStateEnum;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.entity.QpTableDefinition;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @title: DynamicDataTest
 * @projectName insgeek-business-quote
 * @description: 1111
 * @date 2023/4/49:49
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
class DefinitionTableTest {
    MockMvc mockMvc;

    @InjectMocks
    private DefinitionTableServiceImpl definitionTableService;

    @Mock
    private DataMapper<QpTableDefinition> tableDefinitionDataMapper;


    private static List<QpTableDefinition> tableDefinitions = getQpTableDefinition();
    private static QpTableDefinition tableDefinition = tableDefinitions.get(0);

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(definitionTableService).build();
    }

    @Test
    void save() {
        initData();
        try {
            definitionTableService.save(getQpTableDefinition().get(0));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    void modify() {
        initData();
        try {
            definitionTableService.modify(getQpTableDefinition().get(0));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    void query() {
        initData();
        try {
            definitionTableService.query(getQpTableDefinition().get(0));
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    void findByTableCode() {
        initData();
        try {
            definitionTableService.findByTableCode(getQpTableDefinition().get(0).getTableCode());
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    void findById() {
        initData();
        try {
            definitionTableService.findById(getQpTableDefinition().get(0).getId());
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    private void initData() {
        Mockito.doReturn(tableDefinitionDataMapper).when(tableDefinitionDataMapper).entity(QpTableDefinition.class);
        Mockito.when(tableDefinitionDataMapper.insertOne(tableDefinition, true)).thenReturn(tableDefinition);
        Mockito.when(tableDefinitionDataMapper.updateOne(tableDefinition, true)).thenReturn(1);
        Mockito.when(tableDefinitionDataMapper.selectOne(tableDefinition.getId(), true)).thenReturn(tableDefinition);
        Mockito.when(tableDefinitionDataMapper.select(any(QpTableDefinition.class), any(Boolean.class))).thenReturn(tableDefinitions);
    }

    private static List<QpTableDefinition> getQpTableDefinition() {
        QpTableDefinition tableDefinition = new QpTableDefinition();
        tableDefinition.setId(1L)
                .setTableCode("1")
                .setTableStatus(Integer.valueOf(CurrencyStateEnum.YES.getCode()))
                .setTableType(1)
                .setTableTitle("ceshi");
        return Lists.newArrayList(tableDefinition);
    }
}
