package com.insgeek.business.quote.common.service.impl;

import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.backend.aggservice.QuoteDutyService;
import com.insgeek.business.quote.common.dao.constant.QpFileDetailsConst;
import com.insgeek.business.quote.common.dto.param.H5ChannelSubmitParam;
import com.insgeek.business.quote.common.dto.QpQuoteContractCustomerDto;
import com.insgeek.business.quote.common.enums.QuoteSourceEnum;
import com.insgeek.business.quote.common.service.QpQuoteService;
import com.insgeek.business.quote.frontend.aggservice.impl.QuoteManageAggServiceImpl;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.dto.SaveDutyDto;
import com.insgeek.protocol.data.client.entity.QpCustomer;
import com.insgeek.protocol.data.client.entity.QpCustomerOther;
import com.insgeek.protocol.data.client.entity.QpCustomerQuoteRelation;
import com.insgeek.protocol.data.client.entity.QpFileDetails;
import com.insgeek.protocol.data.client.entity.QpQuote;
import com.insgeek.protocol.data.client.entity.QpQuoteConfig;
import com.insgeek.protocol.data.client.entity.QpQuoteContract;
import com.insgeek.protocol.data.client.entity.QpQuoteInfo;
import com.insgeek.protocol.data.client.entity.QpTenantChannelProduct;
import com.insgeek.protocol.flow.dto.TaskActionResponseDto;
import com.insgeek.protocol.insurance.client.PlanConfigClient;
import com.insgeek.protocol.insurance.client.PlanConfigRuleClient;
import com.insgeek.protocol.insurance.client.QuoteClient;
import com.insgeek.protocol.insurance.dto.config.PlanConfigRulesDTO;
import com.insgeek.protocol.insurance.dto.config.PlanConfigStandardDTO;
import com.insgeek.protocol.insurance.dto.geekplus.group.GroupDetailDTO;
import com.insgeek.protocol.platform.common.dto.insurance.config.DutyDTO;
import com.insgeek.protocol.platform.common.dto.insurance.config.PlanConfigDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.assertj.core.util.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * @ClassName: RateAbstract
 * @Description:
 * @Author: YYY
 * @Date: 2022/7/15 9:27
 **/
@Slf4j
@RunWith(MockitoJUnitRunner.class)
class ChannelServiceTest {

    MockMvc mockMvc;

    @InjectMocks
    private ChannelServiceImpl channelService;

    @Mock
    private DataMapper<QpQuoteInfo> quoteInfoDataMapper;

    @Mock
    private DataMapper<QpQuote> quoteDataMapper;

    @Mock
    private DataMapper<QpQuoteConfig> quoteConfigDataMapper;
    @Mock
    private QuoteManageAggServiceImpl quoteManageAggService;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(channelService).build();
    }

    @Test
    void preBuildQuoteInfoId() {
        try {
            Mockito.doReturn(quoteInfoDataMapper).when(quoteInfoDataMapper)
                    .entity(QpQuoteInfo.class);
            //Mockito.when(qpTenantUserDataMapper.select(any(QpTenantUser.class))).thenReturn(getQpTenantUserList());
            //Mockito.when(qpTenantUserDataMapper.selectOne(getQpTenantUser().getId(), true)).thenReturn(getQpTenantUser());
            Mockito.when(quoteInfoDataMapper.insertOne(any(QpQuoteInfo.class))).thenReturn(getQpQuoteInfo());
            // Mockito.when(qpTenantUserDataMapper.updateOne(any(QpTenantUser.class))).thenReturn(1);
            //Mockito.when(qpTenantUserDataMapper.deleteAll(any(List.class), true)).thenReturn(1);
            channelService.preBuildQuoteInfoId(getQpQuoteInfo().getTenantChannelProductId());
        } catch (Exception e) {
            System.out.println();
        }

    }


    private QpQuoteInfo getQpQuoteInfo() {
        QpQuoteInfo info = new QpQuoteInfo();
        info.setQuoteSourceFlag(QuoteSourceEnum.CHANNEL.getCode());
        info.setCustomerId(NumberUtils.LONG_ZERO);
        info.setTenantChannelProductId(111111L);
        return info;
    }

    @Mock
    private DataMapper<QpTenantChannelProduct> channelProductDataMapper;
    @Mock
    private DataMapper<QpCustomer> customerDataMapper;
    @Mock
    private DataMapper<QpCustomerOther> customerOtherDataMapper;
    @Mock
    private DataMapper<QpCustomerQuoteRelation> customerQuoteRelationDataMapper;

    @Mock
    private QuoteContractCustomerServiceImpl quoteContractCustomerService;
    @Mock
    private QuoteContractServiceImpl quoteContractService;
    @Mock
    private QuoteClient quoteClient;
    @Mock
    private QuoteContractCustomerServiceImpl contractCustomerService;
    @Mock
    private QuoteFileServiceImpl quoteFileService;
    @Mock
    private QuoteRenewServiceImpl quoteRenewService;
    @Mock
    private QuoteFlowTaskServiceImpl quoteFlowTaskService;
    @Mock
    private QpQuoteService quoteService;

    @Mock
    private PlanConfigClient planConfigClient;

    @Mock
    private PlanConfigRuleClient planConfigRuleClient;

    @Mock
    private QuoteDutyService quoteDutyService;

    @Test
    void h5Submit() {
        try {

            H5ChannelSubmitParam param = getH5ChannelSubmitParam();
            //createCustomer
            Mockito.doReturn(channelProductDataMapper).when(channelProductDataMapper).entity(QpTenantChannelProduct.class);
            Mockito.when(channelProductDataMapper.selectOne(getQpTenantChannelProduct().getId(), true)).thenReturn(getQpTenantChannelProduct());
            Mockito.doReturn(customerDataMapper).when(customerDataMapper).entity(QpCustomer.class);
            Mockito.when(customerDataMapper.select(any(QpCustomer.class))).thenReturn(Lists.newArrayList(getCustomer()));
            Mockito.when(customerDataMapper.updateOne(any(QpCustomer.class), any(Boolean.class))).thenReturn(1);
            Mockito.when(customerDataMapper.updateOne(any(QpCustomer.class))).thenReturn(1);
            Mockito.when(customerDataMapper.selectOne(any(), any())).thenReturn(getCustomer());
            Mockito.when(customerDataMapper.insertOne(any(QpCustomer.class))).thenReturn(getCustomer());

            Mockito.doReturn(quoteInfoDataMapper).when(quoteInfoDataMapper).entity(QpQuoteInfo.class);
            Mockito.when(quoteInfoDataMapper.updateOne(any(QpQuoteInfo.class), any(Boolean.class))).thenReturn(1);
            Mockito.when(quoteInfoDataMapper.updateOne(any(QpQuoteInfo.class))).thenReturn(1);
            Mockito.when(quoteInfoDataMapper.insertOne(any(QpQuoteInfo.class))).thenReturn(getQpQuoteInfo());
            Mockito.when(quoteInfoDataMapper.insertOne(any(QpQuoteInfo.class), any(Boolean.class))).thenReturn(getQpQuoteInfo());
            Mockito.when(quoteInfoDataMapper.selectOne(any(), any())).thenReturn(getQpQuoteInfo());
            Mockito.when(quoteInfoDataMapper.select(any(QpQuoteInfo.class), any(Boolean.class))).thenReturn(Lists.newArrayList(getQpQuoteInfo()));


            Mockito.doReturn(quoteDataMapper).when(quoteDataMapper).entity(QpQuote.class);
            Mockito.when(quoteDataMapper.updateOne(any(QpQuote.class), any(Boolean.class))).thenReturn(1);
            Mockito.when(quoteDataMapper.updateOne(any(QpQuote.class))).thenReturn(1);
            Mockito.when(quoteDataMapper.insertOne(any(QpQuote.class))).thenReturn(getQpQuote());
            Mockito.when(quoteDataMapper.insertOne(any(QpQuote.class), any(Boolean.class))).thenReturn(getQpQuote());
            Mockito.when(quoteDataMapper.selectOne(any(), any())).thenReturn(getQpQuote());
            Mockito.when(quoteDataMapper.select(any(QpQuote.class), any(Boolean.class))).thenReturn(Lists.newArrayList(getQpQuote()));


            Mockito.doReturn(quoteConfigDataMapper).when(quoteConfigDataMapper).entity(QpQuoteConfig.class);
            Mockito.when(quoteConfigDataMapper.updateOne(any(QpQuoteConfig.class), any(Boolean.class))).thenReturn(1);
            Mockito.when(quoteConfigDataMapper.updateOne(any(QpQuoteConfig.class))).thenReturn(1);
            Mockito.when(quoteConfigDataMapper.insertOne(any(QpQuoteConfig.class))).thenReturn(getQpQuoteConfig());
            Mockito.when(quoteConfigDataMapper.insertOne(any(QpQuoteConfig.class), any(Boolean.class))).thenReturn(getQpQuoteConfig());
            Mockito.when(quoteConfigDataMapper.selectOne(any(), any())).thenReturn(getQpQuoteConfig());
            Mockito.when(quoteConfigDataMapper.select(any(QpQuoteConfig.class), any(Boolean.class))).thenReturn(Lists.newArrayList(getQpQuoteConfig()));


            Mockito.doReturn(getQpQuoteInfo()).when(quoteInfoDataMapper).selectOne(any(), any());
            Mockito.when(customerDataMapper.updateOne(any(QpCustomer.class), any(Boolean.class))).thenReturn(1);
            Mockito.when(customerDataMapper.updateOne(any(QpCustomer.class))).thenReturn(1);


            Mockito.doReturn(customerOtherDataMapper).when(customerOtherDataMapper).entity(QpCustomerOther.class);
            Mockito.when(customerOtherDataMapper.select(any(QpCustomerOther.class))).thenReturn(Lists.newArrayList(getCustomerOther()));
            Mockito.when(customerOtherDataMapper.updateOne(any(QpCustomerOther.class), any(Boolean.class))).thenReturn(1);
            Mockito.when(customerOtherDataMapper.selectOne(any(), any())).thenReturn(getCustomerOther());
            Mockito.when(customerOtherDataMapper.insertOne(any(QpCustomerOther.class))).thenReturn(getCustomerOther());

            //premectQuote
            ResponseVO responseVO_0 = new ResponseVO();
            responseVO_0.setCode(0);
            responseVO_0.setData(getConfigTemplateList());
            Mockito.when(planConfigClient.getConfigTemplateList(any())).thenReturn(responseVO_0);

            ResponseVO responseVO_1 = new ResponseVO();
            responseVO_1.setCode(0);
            responseVO_1.setData(getPlanConfigRulesDTO());
            Mockito.when(planConfigRuleClient.getConfigRule(any())).thenReturn(responseVO_1);

            ResponseVO responseVO_2 = new ResponseVO();
            responseVO_2.setCode(0);
            responseVO_2.setData(getConfigTemplateList());
            Mockito.when(quoteDutyService.saveDutys(any(), any(), new SaveDutyDto())).thenReturn(Lists.newArrayList(1L));

            ResponseVO responseVO_21 = new ResponseVO();
            responseVO_21.setCode(0);
            responseVO_21.setData(getPlanConfigStandardDTOList());
            Mockito.when(planConfigClient.getConfigStandard(any())).thenReturn(responseVO_21);


            //advanceQuote
            ResponseVO responseVO = new ResponseVO();
            responseVO.setCode(0);
            responseVO.setData(1L);
            Mockito.when(quoteContractCustomerService.addOrganization(any(), any(), any(), any())).thenReturn(responseVO);

            //createContract
            Mockito.when(customerDataMapper.selectOne(any(), any())).thenReturn(getCustomer());
            Mockito.when(quoteContractService.save(getQpQuoteContract())).thenReturn(1L);
            ResponseVO responseVO2 = new ResponseVO();
            responseVO2.setCode(0);
            responseVO2.setData(Lists.newArrayList(getGroupDetailDTO()));
            Mockito.when(quoteClient.groupInfo(any(List.class))).thenReturn(responseVO2);
            Mockito.when(contractCustomerService.save(any(List.class))).thenReturn(Lists.newArrayList(1L));
            Mockito.when(contractCustomerService.queryByContractId(any(), any())).thenReturn(Lists.newArrayList(getQpQuoteContractCustomerDto()));
            Mockito.when(quoteFileService.queryFile(any(QpFileDetails.class))).thenReturn(Lists.newArrayList(getFileDetails()));
            Mockito.when(quoteFileService.save(any(List.class))).thenReturn(Lists.newArrayList(1L));

            //createPlan
            Mockito.when(quoteRenewService.autoPlan(any(List.class))).thenReturn(Lists.newArrayList());

            //checkAfterData
            Mockito.doReturn(customerQuoteRelationDataMapper).when(customerQuoteRelationDataMapper).entity(QpCustomerQuoteRelation.class);
            Mockito.when(customerQuoteRelationDataMapper.select(any(QpCustomerQuoteRelation.class))).thenReturn(Lists.newArrayList(getQpCustomerQuoteRelation()));
            Mockito.when(customerQuoteRelationDataMapper.updateOne(any(QpCustomerQuoteRelation.class), any(Boolean.class))).thenReturn(1);

            //advanceContrace
            Mockito.when(quoteFlowTaskService.submit(any(), any())).thenReturn(ResponseVO.data(new TaskActionResponseDto()));
            Mockito.when(quoteContractService.modify(any(QpQuoteContract.class))).thenReturn(1L);

            Mockito.doNothing().when(quoteContractService).deleteRelation(any(), any());
            Mockito.doNothing().when(quoteService).deleteRelation(any(), any());

            channelService.h5Submit(param);

        } catch (Exception e) {

        }
    }

    private List<PlanConfigDTO> getConfigTemplateList() {
        PlanConfigDTO dto = new PlanConfigDTO();
        dto.setCode(0 + "")
                .setId(1L)
                .setBenefit("1")
                .setName("dfdsfdsf")
                .setCategory(null)
                .setClientType(null)
                .setComments(1 + "")
                .setCostPrice(null)
                .setIsHaveMillion(null)
                .setName("rtrt")
                .setTemplate(null)
                .setRelation(null)
                .setRealMandayPrice(null)
                .setPlatform(null)
                .setPlanConfigType(null)
                .setIsSalary(null);
        DutyDTO dutyDTO = new DutyDTO();
        dutyDTO.setId(103339179L)
                .setAmount(null)
                .setDeduction(null)
                .setOnceDeduction(null)
                .setOnceQuota(null)
                .setOtcRange(null)
                .setPayment(100)
                .setPayment(103338066);
        dutyDTO.setPkgId(1L);
        dutyDTO.setPkgName("phg_namw");
        dto.setDuties(Lists.newArrayList(dutyDTO));
        return Lists.newArrayList(dto);
    }

    private QpCustomerQuoteRelation getQpCustomerQuoteRelation() {
        QpCustomerQuoteRelation quoteRelation = new QpCustomerQuoteRelation();
        quoteRelation.setId(1L)
                .setBizModeFlag(1)
                .setContractCustomerId(1L);
        return quoteRelation;
    }

    private GroupDetailDTO getGroupDetailDTO() {
        GroupDetailDTO groupDetailDTO = new GroupDetailDTO();
        groupDetailDTO.setGroupAccount("1");
        groupDetailDTO.setContactMail("1");
        groupDetailDTO.setContactMobile("1");
        groupDetailDTO.setAddress("1");
        groupDetailDTO.setParentId("1");
        groupDetailDTO.setContactPerson("1");
        return groupDetailDTO;
    }

    private QpQuoteContractCustomerDto getQpQuoteContractCustomerDto() {
        QpQuoteContractCustomerDto dto = new QpQuoteContractCustomerDto();
        dto.setMainEnterpriseFlag(null)
                .setCustomerId(getCustomer().getId())
                .setContacts("1")
                .setQuoteContractId(getQpQuoteContract().getId())
                .setEnterpriseId(1L)
                .setEnterpriseName("33")
                .setTenantId(11L)
                .setSuperiorEnterpriseId(1L)
                .setMobilePhone("44")
                .setOfficeAddress("4556")
                .setMailbox("<EMAIL>");
        return dto;

    }

    private QpQuoteContract getQpQuoteContract() {
        return new QpQuoteContract()
                .setId(1L)
                .setQuoteInfoId(getQpQuoteInfo().getId())
                .setQuoteName(getQpQuoteInfo().getName())
                .setQuoteSourceFlag(QuoteSourceEnum.CHANNEL.getCode());
    }

    private H5ChannelSubmitParam getH5ChannelSubmitParam() {
        H5ChannelSubmitParam param = new H5ChannelSubmitParam();
        // param.setCompanyId(96L);
        param.setQuoteInfoId(1L);
        param.setTenantChannelProductId(1239377753167296926L);
        param.setStartData(null);
        param.setEndData(null);
        param.setSuration(12);
        param.setFileDetails(Lists.newArrayList(getFileDetails()));
        param.setCustomer(getCustomer());
        param.setCustomerOther(getCustomerOther());
        return param;
    }

    private QpTenantChannelProduct getQpTenantChannelProduct() {
        return new QpTenantChannelProduct()
                .setId(1239377753167296926L)
                .setChannelName("newName")
                .setStatusFlag(null)
                .setChannelConfigId(123L)
                .setCommissionRate(BigDecimal.ONE)
                .setPtTenantId(1239378113944549447L)
                .setSecretAddress("N+VfyX3CWiKWlxeCBDPRFU7PEGrEMe93OxiquLaf21uCR9wUvB0pCdGUHzLVmdqr");
    }

    private QpCustomer getCustomer() {
        return new QpCustomer()
                .setTopLevelId(1L)
                .setId(1L)
                .setQuoteSourceFlag(1)
                .setCustomerId(1L)
                .setContacts("公司联系人名称")
                .setEnterpriseName("公司名称")
                .setMailbox("<EMAIL>")
                .setMobilePhone("13654567890")
                .setOfficeAddress("XXXXXluldld")
                .setSuperiorId(0L)
                .setTenantId(1239378113944549447L)
                .setOwnerId(1L);
    }

    private QpCustomerOther getCustomerOther() {
        return new QpCustomerOther()
                .setId(1L)
                .setCustomerId(getCustomer().getId())
                .setChannelFlag(null)
                .setTenantId(1239378113944549447L)
                .setRenewalFlag(null)
                .setReputationFlag(null)
                .setCountyCode(1 + "")
                .setCountryName("china")
                .setProvinceCode("11")
                .setProvinceName("XX省")
                .setCityCode("111")
                .setCityName("市")
                .setCountyCode("1111")
                .setCountyName("旗县")
                .setCredentialsType(1 + "")
                .setCredentialsNo(1 + "");
    }

    private QpFileDetails getFileDetails() {
        return new QpFileDetails()
                .setId(1L)
                .setBusId(1L)
                .setBusType(QpFileDetailsConst.BusType.QUOTE_CHANNEL.getCode())
                .setBusClassify(1L)
                .setOssKey("4444444hhh");
    }

    public PlanConfigRulesDTO getPlanConfigRulesDTO() {
        PlanConfigRulesDTO planConfigRulesDTO = new PlanConfigRulesDTO();
        planConfigRulesDTO.setId(1L);
        planConfigRulesDTO.setAfterRelativeType(Lists.newArrayList("1"));
        planConfigRulesDTO.setChooseConfigRule(null);
        planConfigRulesDTO.setPlanConfigId(1L);
        planConfigRulesDTO.setAgeCheckMode(null);
        planConfigRulesDTO.setAgeSwitch(Lists.newArrayList(1 + ""));
        planConfigRulesDTO.setAvgAge(1);
        planConfigRulesDTO.setAvgAgeDefault(1);
        planConfigRulesDTO.setCanAddUser(null);
        planConfigRulesDTO.setCanDelUser(null);
        planConfigRulesDTO.setUserRefundWithClaim(null);
        planConfigRulesDTO.setSpouseRule(null);
        planConfigRulesDTO.setRelativeDelayDays(1);
        planConfigRulesDTO.setParentsRule(null);
        planConfigRulesDTO.setMaxPerson(2);
        planConfigRulesDTO.setRiskPersonProportion(1D);
        planConfigRulesDTO.setMinInsureDefault(1);
        planConfigRulesDTO.setJobCategoryRule(null);
        return planConfigRulesDTO;
    }

    public List<PlanConfigStandardDTO> getPlanConfigStandardDTOList() {
        PlanConfigStandardDTO dto = new PlanConfigStandardDTO();
        dto.setId(1L)
                .setPlanConfigId(1L)
                .setCompanyId(96L)
                .setBillType(null)
                .setHealthNotify("1")
                .setInsuranceRule("1")
                .setStandardType(null);
        return Lists.newArrayList(dto);
    }

    public QpQuote getQpQuote() {
        return new QpQuote()
                .setId(1L)
                .setQuoteInfoId(getQpQuoteInfo().getId());
    }

    public QpQuoteConfig getQpQuoteConfig() {
        return new QpQuoteConfig()
                .setId(1L)
                .setQuoteId(getQpQuote().getId());
    }
}
