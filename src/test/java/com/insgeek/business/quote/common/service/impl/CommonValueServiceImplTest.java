package com.insgeek.business.quote.common.service.impl;

import com.fasterxml.jackson.core.type.TypeReference;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.common.enums.ProductPropertyEnum;
import com.insgeek.business.quote.frontend.dto.excel.ExcelDutyDto;
import com.insgeek.business.quote.frontend.dto.manage.QpQuoteDto;
import com.insgeek.business.quote.frontend.dto.manage.QpQuoteDutyDto;
import com.insgeek.business.quote.frontend.dto.manage.QpQuoteProdDto;
import com.insgeek.protocol.platform.metadata.client.DictClient;
import com.insgeek.protocol.platform.metadata.dto.DictDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;

@Slf4j
@RunWith(MockitoJUnitRunner.class)
class CommonValueServiceImplTest {

    MockMvc mockMvc;

    @Mock
    private DictClient dictClient;

    @InjectMocks
    CommonValueServiceImpl commonValueService;


    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(commonValueService).build();
    }

    @Test
    void getExcelValue() {
        String dictJson = "{\n" +
                "    \"qp_quote\":{\n" +
                "        \"business_source\":[\n" +
                "            {\n" +
                "                \"id\":\"114561236\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"1\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"电话团队线索\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"114561237\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"2\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"HR转介绍\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"114561238\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"3\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"自己获取（电话/微信/活动）\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"114561239\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"4\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"保险公司业务员推荐\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"114561240\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"5\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"经纪公司/代理公司人员推荐\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"114561241\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"6\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"第三方人员推荐（员福/易才/豆包）\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"114561242\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"7\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"离职销售推荐\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            },\n" +
                "            {\n" +
                "                \"id\":\"114561243\",\n" +
                "                \"name\":\"业务来源\",\n" +
                "                \"api_key\":\"8\",\n" +
                "                \"standard_flg\":false,\n" +
                "                \"dict_value\":\"其它\",\n" +
                "                \"default_flg\":false,\n" +
                "                \"enable_flg\":true,\n" +
                "                \"order_num\":0,\n" +
                "                \"refer_dict_id\":null,\n" +
                "                \"children\":null\n" +
                "            }\n" +
                "        ]\n" +
                "    }\n" +
                "}";
        Map<String, Map<String, List<DictDto>>> dictMap = JacksonUtils.readValue(dictJson, new TypeReference<Map<String, Map<String, List<DictDto>>>>() {
        });
        Mockito.when(dictClient.listDictByEntityKeys(Collections.singletonList("qp_quote")))
                .thenReturn(ResponseVO.<Map<String, Map<String, List<DictDto>>>>builder().data(dictMap).build());
        Map<String,Map<String, Map<String, List<DictDto>>>> dictMapNew  = new HashMap<>();
        dictMapNew.put("qp_quote",dictMap);

        String qpQuoteDtoJson = "{\"id\":114667351,\"link_quote_id\":0,\"avg_age\":35,\"quote_count\":26,\"bill_period\":3,\"bill_unit\":2,\"customer_id\":114513742,\"customer_name\":\"厦门鲸鹳科技有限公司\",\"group_name\":\"北京鲸鹳科技有限公司陕西分公司\",\"quote_price\":424.25,\"expected_max_price\":100.0,\"expected_min_price\":1.0,\"female_count\":80,\"first_payment_time\":\"\",\"health_report\":0,\"history_insurance_consumer\":\"\",\"history_loss_ratio\":0.0,\"insured_count\":80,\"insured_years\":0,\"major_past_illness_count\":0,\"max_age\":100,\"medicare_type\":1,\"min_age\":1,\"name\":\"北京鲸鹳科技有限公司陕西分公司2022-03-18报价\",\"occupation\":4,\"owner_id\":114513377,\"pay_period\":3,\"pay_type\":1,\"pay_unit\":2,\"plan_id\":0,\"quote_confirm_time\":\"\",\"quote_pass_time\":\"\",\"quote_time\":\"2022-03-18 18:14:10\",\"target_person\":[\"1\"],\"sales_staff\":\"大流程\",\"staff_count\":100,\"status\":3,\"yjy_code\":\"\",\"start_date\":\"2022-03-01 00:00:00\",\"end_date\":\"2023-02-28 23:59:59\",\"comments\":\"\",\"business_source\":1,\"visit_times\":1,\"insured_type\":11,\"total_persons\":99999,\"medical_insured_persons\":55,\"female_proportion\":100.0,\"commission_rate\":100.0}";
        /*QpQuoteDto qpQuoteDto = JacksonUtils.readValue(qpQuoteDtoJson, QpQuoteDto.class);

        String femaleProportion = commonValueService.getExcelValue(dictMapNew,qpQuoteDto, "femaleProportion", "femaleProportion","qp_quote",false);
        assertEquals("100%", femaleProportion);

        String historyLossRatio = commonValueService.getExcelValue(dictMapNew,qpQuoteDto, "historyLossRatio", "historyLossRatio","qp_quote",false);
        assertEquals("0%", historyLossRatio);

        String ageRange = commonValueService.getExcelValue(dictMapNew,qpQuoteDto, "ageRange", "ageRange","qp_quote",false);
        assertEquals("1-100", ageRange);


        String businessSource = commonValueService.getExcelValue(dictMapNew,qpQuoteDto, "businessSource", 1,"qp_quote",false);
        assertEquals("电话团队线索", businessSource);*/
    }

    @Test
    void getDutyExcelValue() {
        String prodJson = "[\n" +
                "        {\n" +
                "            \"pkg_name\":\"准生产标品\",\n" +
                "            \"pkg_id\":104592090,\n" +
                "            \"quote_duty_list\":[\n" +
                "                {\n" +
                "                    \"id\":114667415,\n" +
                "                    \"amount\":50000,\n" +
                "                    \"deduction\":100,\n" +
                "                    \"once_deduction\":0,\n" +
                "                    \"once_quota\":0,\n" +
                "                    \"payment\":0,\n" +
                "                    \"product_id\":104592124,\n" +
                "                    \"qp_product_id\":0,\n" +
                "                    \"property\":3,\n" +
                "                    \"price\":0,\n" +
                "                    \"cost_price\":0,\n" +
                "                    \"agent_ratio\":0,\n" +
                "                    \"is_edit\":1,\n" +
                "                    \"duty_split\":[\n" +
                "                        {\n" +
                "                            \"id\":114667430,\n" +
                "                            \"end_amount\":5000,\n" +
                "                            \"payment\":100,\n" +
                "                            \"quote_duty_id\":114667415,\n" +
                "                            \"start_amount\":0\n" +
                "                        },\n" +
                "                        {\n" +
                "                            \"id\":114667431,\n" +
                "                            \"end_amount\":50000,\n" +
                "                            \"payment\":100,\n" +
                "                            \"quote_duty_id\":114667415,\n" +
                "                            \"start_amount\":5000\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    \"product_type_name\":\"补充医疗门（急）诊\",\n" +
                "                    \"product_type\":\"1321220\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\":114667427,\n" +
                "                    \"amount\":50000,\n" +
                "                    \"deduction\":5000,\n" +
                "                    \"once_deduction\":0,\n" +
                "                    \"once_quota\":540,\n" +
                "                    \"payment\":100,\n" +
                "                    \"product_id\":104592126,\n" +
                "                    \"qp_product_id\":0,\n" +
                "                    \"property\":1,\n" +
                "                    \"price\":0,\n" +
                "                    \"cost_price\":0,\n" +
                "                    \"agent_ratio\":0,\n" +
                "                    \"is_edit\":1,\n" +
                "                    \"product_type_name\":\"补充医疗住院\",\n" +
                "                    \"product_type\":\"1321230\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\":114667416,\n" +
                "                    \"amount\":150000,\n" +
                "                    \"deduction\":0,\n" +
                "                    \"once_deduction\":0,\n" +
                "                    \"once_quota\":0,\n" +
                "                    \"payment\":0,\n" +
                "                    \"product_id\":104593230,\n" +
                "                    \"qp_product_id\":0,\n" +
                "                    \"property\":4,\n" +
                "                    \"price\":15,\n" +
                "                    \"cost_price\":15,\n" +
                "                    \"agent_ratio\":0,\n" +
                "                    \"is_edit\":1,\n" +
                "                    \"product_type_name\":\"重大疾病\",\n" +
                "                    \"product_type\":\"1311000\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\":114667425,\n" +
                "                    \"amount\":100,\n" +
                "                    \"deduction\":100,\n" +
                "                    \"once_deduction\":0,\n" +
                "                    \"once_quota\":0,\n" +
                "                    \"payment\":100,\n" +
                "                    \"product_id\":104593264,\n" +
                "                    \"qp_product_id\":0,\n" +
                "                    \"property\":5,\n" +
                "                    \"price\":1,\n" +
                "                    \"cost_price\":1,\n" +
                "                    \"agent_ratio\":0,\n" +
                "                    \"is_edit\":1,\n" +
                "                    \"product_type_name\":\"住院津贴\",\n" +
                "                    \"product_type\":\"1322100\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\":114667424,\n" +
                "                    \"amount\":1000,\n" +
                "                    \"deduction\":0,\n" +
                "                    \"once_deduction\":0,\n" +
                "                    \"once_quota\":0,\n" +
                "                    \"payment\":0,\n" +
                "                    \"product_id\":104593281,\n" +
                "                    \"qp_product_id\":0,\n" +
                "                    \"property\":4,\n" +
                "                    \"price\":0,\n" +
                "                    \"cost_price\":0,\n" +
                "                    \"agent_ratio\":0,\n" +
                "                    \"is_edit\":1,\n" +
                "                    \"product_type_name\":\"女性生育\",\n" +
                "                    \"product_type\":\"1321600\"\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\":114667422,\n" +
                "                    \"amount\":200000,\n" +
                "                    \"deduction\":0,\n" +
                "                    \"once_deduction\":0,\n" +
                "                    \"once_quota\":0,\n" +
                "                    \"payment\":0,\n" +
                "                    \"product_id\":104593284,\n" +
                "                    \"qp_product_id\":0,\n" +
                "                    \"property\":4,\n" +
                "                    \"price\":0,\n" +
                "                    \"cost_price\":0,\n" +
                "                    \"agent_ratio\":0,\n" +
                "                    \"is_edit\":1,\n" +
                "                    \"product_type_name\":\"疾病身故或全残\",\n" +
                "                    \"product_type\":\"1111000\"\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    ]";
        //责任信息
        /*List<QpQuoteProdDto> quoteProdDtoList = JacksonUtils.readValue(prodJson, new TypeReference<List<QpQuoteProdDto>>() {
        });
        for (QpQuoteProdDto qpQuoteProdDto : quoteProdDtoList) {
            for (QpQuoteDutyDto qpQuoteDutyDto : qpQuoteProdDto.getQuoteDutyList()) {
                ExcelDutyDto excelDutyDto = new ExcelDutyDto()
                        .setDutyName(qpQuoteDutyDto.getProductTypeName())
                        .setAmount(String.valueOf(qpQuoteDutyDto.getAmount()))
                        .setDutyType(ProductPropertyEnum.getNameByCode(qpQuoteDutyDto.getProperty()))
                        .setPrice(null);
                if (ProductPropertyEnum.YEARLY_DEDUCTION.getCode().equals(qpQuoteDutyDto.getProperty())) {
                    excelDutyDto.setDeduction(null);
                    excelDutyDto.setPayment(qpQuoteDutyDto.getPayment());
                } else if ((ProductPropertyEnum.SEGMENT.getCode()).equals(qpQuoteDutyDto.getProperty())) {
                    excelDutyDto.setDeduction(null);
                    excelDutyDto.setPayment(-1);
                } else if (((ProductPropertyEnum.FIXED_AMOUNT.getCode()).equals(qpQuoteDutyDto.getProperty())) || (ProductPropertyEnum.ALLOWANCE.getCode()).equals(qpQuoteDutyDto.getProperty())) {
                    excelDutyDto.setDeduction(-1);
                    excelDutyDto.setPayment(-1);
                }
                commonValueService.getDutyExcelValue(excelDutyDto,qpQuoteDutyDto);

                if (ProductPropertyEnum.YEARLY_DEDUCTION.getCode().equals(qpQuoteDutyDto.getProperty())) {
                    assertFalse(CollectionUtils.isEmpty(excelDutyDto.getOther()));
                    //assertEquals(CommonValueServiceImpl.DAY_QUOTA + qpQuoteDutyDto.getOnceQuota(),excelDutyDto.getOther().get(0));
                } else if ((ProductPropertyEnum.ONCE_DEDUCTION.getCode()).equals(qpQuoteDutyDto.getProperty())) {
                    assertFalse(CollectionUtils.isEmpty(excelDutyDto.getOther()));
//                    assertEquals(3,excelDutyDto.getOther().size());
                } else if ((ProductPropertyEnum.SEGMENT.getCode()).equals(qpQuoteDutyDto.getProperty())) {
                    assertFalse(CollectionUtils.isEmpty(excelDutyDto.getOther()));
//                    assertEquals(3 * qpQuoteDutyDto.getDutySplit().size(),excelDutyDto.getOther().size());
                } else if ((ProductPropertyEnum.ALLOWANCE.getCode()).equals(qpQuoteDutyDto.getProperty())) {
                    assertFalse(CollectionUtils.isEmpty(excelDutyDto.getOther()));
//                    assertEquals(2,excelDutyDto.getOther().size());
                }
            }
        }*/
    }

    /*@Test
    void getConfigExcelValue(){
        String dictJson = "{\n" +
                "        \"qp_quote_config\": {\n" +
                "            \"occupation\": [\n" +
                "                {\n" +
                "                    \"id\": \"1052340637619884671\",\n" +
                "                    \"name\": \"职业类别\",\n" +
                "                    \"api_key\": \"1\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"1-3类\",\n" +
                "                    \"default_flg\": true,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"1052340637619884674\",\n" +
                "                    \"name\": \"职业类别\",\n" +
                "                    \"api_key\": \"4\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"4类\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"1052340637619884675\",\n" +
                "                    \"name\": \"职业类别\",\n" +
                "                    \"api_key\": \"5\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"5类\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"1052340637619884676\",\n" +
                "                    \"name\": \"职业类别\",\n" +
                "                    \"api_key\": \"6\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"6类\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                }\n" +
                "            ],\n" +
                "            \"medicare_type\": [\n" +
                "                {\n" +
                "                    \"id\": \"1040467887724576800\",\n" +
                "                    \"name\": \"医保类型\",\n" +
                "                    \"api_key\": \"0\",\n" +
                "                    \"standard_flg\": null,\n" +
                "                    \"dict_value\": \"无社保\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"977948642956607752\",\n" +
                "                    \"name\": \"医保类型\",\n" +
                "                    \"api_key\": \"1\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"有社保\",\n" +
                "                    \"default_flg\": true,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                }\n" +
                "            ],\n" +
                "            \"business_type\": [\n" +
                "                {\n" +
                "                    \"id\": \"103333754\",\n" +
                "                    \"name\": \"默认业务类型\",\n" +
                "                    \"api_key\": \"defaultBusiType\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"默认业务类型\",\n" +
                "                    \"default_flg\": true,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                }\n" +
                "            ],\n" +
                "            \"insured_type\": [\n" +
                "                {\n" +
                "                    \"id\": \"1052340637619884699\",\n" +
                "                    \"name\": \"参保类型\",\n" +
                "                    \"api_key\": \"10\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"企业统一参保\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"1052340637619884700\",\n" +
                "                    \"name\": \"参保类型\",\n" +
                "                    \"api_key\": \"11\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"个人选择、企业代扣费\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"1052340637619884701\",\n" +
                "                    \"name\": \"参保类型\",\n" +
                "                    \"api_key\": \"12\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"企业统一支付基础保障费用，其它个人选择、企业代扣费\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"1052340637619884702\",\n" +
                "                    \"name\": \"参保类型\",\n" +
                "                    \"api_key\": \"14\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"企业按入职年限、部门或其他条件部分参保\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                }\n" +
                "            ],\n" +
                "            \"client_type\": [\n" +
                "                {\n" +
                "                    \"id\": \"103334125\",\n" +
                "                    \"name\": \"适用类型\",\n" +
                "                    \"api_key\": \"1\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"企业增员\",\n" +
                "                    \"default_flg\": true,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"103334126\",\n" +
                "                    \"name\": \"适用类型\",\n" +
                "                    \"api_key\": \"2\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"个人推荐\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"103334127\",\n" +
                "                    \"name\": \"适用类型\",\n" +
                "                    \"api_key\": \"3\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"个人自选\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"103334128\",\n" +
                "                    \"name\": \"适用类型\",\n" +
                "                    \"api_key\": \"4\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"自选升级配置\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"103334129\",\n" +
                "                    \"name\": \"适用类型\",\n" +
                "                    \"api_key\": \"5\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"拼单配置\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                }\n" +
                "            ],\n" +
                "            \"relation\": [\n" +
                "                {\n" +
                "                    \"id\": \"103334130\",\n" +
                "                    \"name\": \"适用人员\",\n" +
                "                    \"api_key\": \"0\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"本人\",\n" +
                "                    \"default_flg\": true,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"103334131\",\n" +
                "                    \"name\": \"适用人员\",\n" +
                "                    \"api_key\": \"1\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"子女\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"103334132\",\n" +
                "                    \"name\": \"适用人员\",\n" +
                "                    \"api_key\": \"2\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"配偶\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"103334133\",\n" +
                "                    \"name\": \"适用人员\",\n" +
                "                    \"api_key\": \"3\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"父母\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                }\n" +
                "            ],\n" +
                "            \"status\": [\n" +
                "                {\n" +
                "                    \"id\": \"103334134\",\n" +
                "                    \"name\": \"状态\",\n" +
                "                    \"api_key\": \"1\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"有效\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                },\n" +
                "                {\n" +
                "                    \"id\": \"103334135\",\n" +
                "                    \"name\": \"状态\",\n" +
                "                    \"api_key\": \"0\",\n" +
                "                    \"standard_flg\": false,\n" +
                "                    \"dict_value\": \"无效\",\n" +
                "                    \"default_flg\": false,\n" +
                "                    \"enable_flg\": true,\n" +
                "                    \"order_num\": 0,\n" +
                "                    \"refer_dict_id\": null,\n" +
                "                    \"children\": null\n" +
                "                }\n" +
                "            ]\n" +
                "        }\n" +
                "    }";

        Map<String, Map<String, List<DictDto>>> dictMap = JacksonUtils.readValue(dictJson, new TypeReference<Map<String, Map<String, List<DictDto>>>>() {
        });
        Mockito.when(dictClient.listDictByEntityKeys(Collections.singletonList("qp_quote_config")))
                .thenReturn(ResponseVO.<Map<String, Map<String, List<DictDto>>>>builder().data(dictMap).build());
        Map<String,Map<String, Map<String, List<DictDto>>>> dictMapNew  = new HashMap<>();
        dictMapNew.put("qp_quote_config",dictMap);

        String relation = commonValueService.getConfigExcelValue(dictMapNew, null, "relation", 1);
        assertEquals("子女", relation);


    }*/
}