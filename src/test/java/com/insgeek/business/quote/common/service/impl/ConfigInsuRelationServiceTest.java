package com.insgeek.business.quote.common.service.impl;

import com.google.common.collect.Lists;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.common.dao.mapper.QuoteMapper;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.entity.QpCustomerQuoteRelation;
import com.insgeek.protocol.data.client.entity.QpQuote;
import com.insgeek.protocol.data.client.entity.QpQuoteContractCustomer;
import com.insgeek.protocol.insurance.client.QuoteClient;
import com.insgeek.protocol.insurance.dto.quote.PlanSettleInfo;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * @ClassName: RateAbstract
 * @Description:
 * @Author: YYY
 * @Date: 2022/7/15 9:27
 **/
@Slf4j
@RunWith(MockitoJUnitRunner.class)
class ConfigInsuRelationServiceTest {

    MockMvc mockMvc;

    @InjectMocks
    private CustomerQuoteRelationServiceImpl customerQuoteRelationService;

    @Mock
    private DataMapper<QpCustomerQuoteRelation> customerQuoteRelationMapper;

    @Mock
    private DataMapper<QpQuoteContractCustomer> quoteContractCustomerMapper;

    @Mock
    private QuoteMapper quoteMapper;
    @Mock
    private QuoteClient quoteClient;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(customerQuoteRelationService).build();
    }

    @Test
    @Disabled
    void execute() {
        try {
            Mockito.doReturn(customerQuoteRelationMapper).when(customerQuoteRelationMapper)
                    .entity(QpCustomerQuoteRelation.class);
            Mockito.when(customerQuoteRelationMapper.insertAll(any())).thenReturn(getCustomerQuoteRelations());
            Mockito.doReturn(getQpQuoteContractCustomer().get(0)).when(quoteContractCustomerMapper).selectOne(any(), any());
            Mockito.doReturn(getQpQuote().get(0)).when(quoteMapper).find(any());
            Mockito.when(quoteClient.getPlanList(any())).thenReturn(getIgPlan());
            //Mockito.doReturn(getQpQuoteContractCustomer()).when(quoteContractCustomerMapper).select(,any());
            Mockito.doReturn(getQpQuote()).when(quoteMapper).selectListByCondition(any(), false);

        } catch (Exception e) {
        }
        try {
            customerQuoteRelationService.save(getCustomerQuoteRelations());
        } catch (Exception e) {
        }

        try {
            customerQuoteRelationService.add(getCustomerQuoteRelations().get(0));
        } catch (Exception e) {
        }
        try {
            customerQuoteRelationService.removes(Lists.newArrayList(1L));
        } catch (Exception e) {
        }
        try {
            customerQuoteRelationService.modify(getCustomerQuoteRelations().get(0));
        } catch (Exception e) {
        }
        try {
            customerQuoteRelationService.queryByContractCustomerId(1L);
        } catch (Exception e) {
        }
        try {
            customerQuoteRelationService.queryByContractCustomerIds(Lists.newArrayList(1L));
        } catch (Exception e) {
        }
        try {
            List<Long> longs = customerQuoteRelationService.removeByContractCustramerId(1L);
        } catch (Exception e) {
        }
        try {
            customerQuoteRelationService.bindInsurancePlan(1L, 1L, 1L);
        } catch (Exception e) {
        }


    }

    private List<QpCustomerQuoteRelation> getCustomerQuoteRelations() {
        List<QpCustomerQuoteRelation> list = Lists.newArrayList();
        QpCustomerQuoteRelation quoteRelation = new QpCustomerQuoteRelation();
        quoteRelation.setQuoteId(1L)
                .setInsuranceQuoteId(1L)
                .setContractCustomerId(1L)
                .setId(0L);
        list.add(quoteRelation);
        return list;
    }

    private List<QpQuoteContractCustomer> getQpQuoteContractCustomer() {
        List<QpQuoteContractCustomer> list = Lists.newArrayList();
        QpQuoteContractCustomer qpFileDetails = new QpQuoteContractCustomer();
        qpFileDetails.setId(1L)
                .setCustomerId(1L)
                .setQuoteContractId(1L);
        list.add(qpFileDetails);
        return list;
    }

    private List<QpQuote> getQpQuote() {
        List<QpQuote> list = Lists.newArrayList();
        QpQuote qpFileDetails = new QpQuote();
        qpFileDetails.setId(1L)
                .setCustomerId(1L);
        list.add(qpFileDetails);
        return list;
    }

    private ResponseVO<List<PlanSettleInfo>> getIgPlan() {
        ResponseVO<List<PlanSettleInfo>> groupPlans = new ResponseVO<>();
        List<PlanSettleInfo> list = Lists.newArrayList();
        PlanSettleInfo plan = new PlanSettleInfo();
        plan.setId(1L);
        list.add(plan);
        groupPlans.setData(list);
        return groupPlans;
    }

}
