package com.insgeek.business.quote.common.service.impl;

import com.google.common.collect.Lists;
import com.insgeek.business.quote.common.dto.param.FieldDefinitionParam;
import com.insgeek.business.quote.common.enums.CurrencyStateEnum;
import com.insgeek.business.quote.common.service.DefinitionTableService;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.entity.QpFieldDefinition;
import com.insgeek.protocol.data.client.entity.QpTableDefinition;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;

/**
 * <AUTHOR>
 * @title: DynamicDataTest
 * @projectName insgeek-business-quote
 * @description: 1111
 * @date 2023/4/49:49
 */
@Slf4j
@RunWith(MockitoJUnitRunner.class)
class DefinitionFieldTest {
    MockMvc mockMvc;

    @InjectMocks
    private DefinitionFieldServiceImpl definitionFieldService;

    @Mock
    private DataMapper<QpFieldDefinition> fieldDefinitionDataMapper;

    @Mock
    private DefinitionTableService definitionTableService;


    private static List<QpTableDefinition> tableDefinitions = getQpTableDefinition();
    private static QpTableDefinition tableDefinition = tableDefinitions.get(0);
    private static List<QpFieldDefinition> fieldDefinitions = getQpFieldDefinition();
    private static QpFieldDefinition fieldDefinition = fieldDefinitions.get(0);

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(definitionFieldService).build();
    }

    @Test
    void save() {
        initData();
        try {
            definitionFieldService.save(1L, getFieldDefinitionParam());
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    void savePlugs() {
        initData();
        try {
            definitionFieldService.savePlugs(1L, getFieldDefinitionParam());
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    void modify() {
        initData();
        try {
            definitionFieldService.modify(fieldDefinition);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }

    @Test
    void queryByTableDefinitionId() {
        initData();
        try {
            definitionFieldService.queryByTableDefinitionId(1L, true);
        } catch (Exception e) {
            System.out.println(e.getMessage());
        }
    }


    private void initData() {
        Mockito.doReturn(fieldDefinitionDataMapper).when(fieldDefinitionDataMapper).entity(QpFieldDefinition.class);
        Mockito.when(fieldDefinitionDataMapper.insertOne(fieldDefinition, true)).thenReturn(fieldDefinition);
        Mockito.when(fieldDefinitionDataMapper.insertAll(fieldDefinitions, true)).thenReturn(fieldDefinitions);
        Mockito.when(fieldDefinitionDataMapper.updateOne(fieldDefinition, true)).thenReturn(1);
        Mockito.when(fieldDefinitionDataMapper.selectOne(tableDefinition.getId(), true)).thenReturn(fieldDefinition);
        Mockito.when(fieldDefinitionDataMapper.select(any(QpFieldDefinition.class), any(Boolean.class))).thenReturn(fieldDefinitions);
        Mockito.when(definitionTableService.findById(tableDefinition.getId())).thenReturn(tableDefinition);
        Mockito.when(definitionTableService.findByTableCode(tableDefinition.getTableCode())).thenReturn(tableDefinition);
        Mockito.when(definitionTableService.query(tableDefinition)).thenReturn(tableDefinitions);
        Mockito.when(definitionTableService.modify(tableDefinition)).thenReturn(tableDefinition);
    }

    private static List<QpTableDefinition> getQpTableDefinition() {
        QpTableDefinition tableDefinition = new QpTableDefinition();
        tableDefinition.setId(1L)
                .setTableCode("1")
                .setTableStatus(Integer.valueOf(CurrencyStateEnum.YES.getCode()))
                .setTableType(1)
                .setTableTitle("ceshi");
        return Lists.newArrayList(tableDefinition);
    }

    private static List<QpFieldDefinition> getQpFieldDefinition() {
        QpFieldDefinition fieldDefinition = new QpFieldDefinition();
        fieldDefinition.setId(1L)
                .setTableDefinitionId(getQpTableDefinition().get(0).getId())
                .setFieldCode("abc")
                .setFieldName("ceshi")
                .setFieldStatus(1)
                .setFieldVal("3")
                .setFieldRequired(2)
                .setEditCondition("");
        return Lists.newArrayList(fieldDefinition);
    }

    private static List<FieldDefinitionParam> getFieldDefinitionParam() {
        FieldDefinitionParam param = new FieldDefinitionParam();
        param.setFieldCode("abc")
                .setValType(1)
                .setFieldName("ceshi")
                .setTableDefinitionId(1L)
                .setFieldStatus(1);
        return Lists.newArrayList(param);
    }
}
