package com.insgeek.business.quote.common.service.impl;

import com.google.common.collect.Lists;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.backend.dto.quote.QpQuoteConfigDTO;
import com.insgeek.business.quote.common.dao.mapper.QpCustomerMapper;
import com.insgeek.business.quote.common.enums.SourceTabFlagEnum;
import com.insgeek.business.quote.common.enums.SystemTabFlagEnum;
import com.insgeek.business.quote.util.DateFormater;
import com.insgeek.business.quote.util.QuoteUtil;
import com.insgeek.protocol.data.client.entity.QpCustomer;
import com.insgeek.protocol.insurance.client.QuoteClient;
import com.insgeek.protocol.insurance.dto.geekplus.group.GroupDetailDTO;
import com.insgeek.business.quote.util.Utils;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;

/**
 * @ClassName: RateAbstract
 * @Description:
 * @Author: YYY
 * @Date: 2022/7/15 9:27
 **/
@Slf4j
@RunWith(MockitoJUnitRunner.class)
class CustomerServiceTest {

    MockMvc mockMvc;

    @InjectMocks
    private QpCustomerServiceImpl customerService;

    @Mock
    private QpCustomerMapper qpCustomerMapper;    
    
    @Mock
    private QuoteClient quoteClient;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(customerService).build();
    }

    @Test
    void queryRelationCustomer() {
        try {
            test();
            Mockito.when(qpCustomerMapper.find(any())).thenReturn(getQpCustomer().get(0));
            Mockito.when(qpCustomerMapper.selectListByCondition(any(), anyBoolean())).thenReturn(getQpCustomer());
            Mockito.when(quoteClient.groupInfo(any(ArrayList.class))).thenReturn(getGroupList());
            customerService.queryRelationCustomer(1L, true,true,true,true);
        } catch (Exception e) {
            System.out.println();
        }

    }
    
    public static void test(){
        QpQuoteConfigDTO dto = new QpQuoteConfigDTO();
        dto.setOperate(QuoteUtil.UNCHANGED);
        dto.setSystemTabFlag(SystemTabFlagEnum.QUOTATION.getCode());
        dto.setSourceTabFlag(SourceTabFlagEnum.CREATE.getCode());
        
        QuoteUtil.getOperate(SystemTabFlagEnum.QUOTATION.getCode(), dto);
        QuoteUtil.getOperate(SystemTabFlagEnum.CENTER_QUOTATION.getCode(), dto);

        QpQuoteConfigDTO dto2 = new QpQuoteConfigDTO();
        dto2.setOperate(QuoteUtil.UNCHANGED);
        dto2.setSystemTabFlag(SystemTabFlagEnum.CENTER_QUOTATION.getCode());
        dto2.setSourceTabFlag(SourceTabFlagEnum.CONSULT.getCode());

        QuoteUtil.getOperate(SystemTabFlagEnum.QUOTATION.getCode(), dto2);
        QuoteUtil.getOperate(SystemTabFlagEnum.CENTER_QUOTATION.getCode(), dto2);
        
        Date date = new Date();;
        DateFormater.getInstance(date).setTimeZone(DateFormater.DEFAULT_TIME_ZONE).getBegin();
        /*boolean qwe = DateFormater.getInstance(date.getTime()).getOffset(1).getOffsetMonth(1).getOffsetSecond(1).getFirstDateOfYear().setDay(1)
                .equals(DateFormater.getInstance().setTimeZone("GMT+08:00").getDate());*/
        DateFormater instance = DateFormater.getInstance("2021-12-21");
        instance.getDaysOfWeek();
        instance.getDaysBetween(DateFormater.getInstance("2021-12-21 00:00:00"));
        instance.getEnd();
        instance.getYearWeek();
        instance.getYearMonth();
        instance.getHour24();
        instance.getTimeZone();
        DateFormater.getInstance("2021-12");

        CharSequence str = "hello world";
        
        Utils.decode(Utils.encode(123));
        Utils.implode(new Object[]{"a"});
        Utils.implode(Utils.asList(str));
        Utils.implode(Utils.asList(str),"cao ");
        Utils.decode64(Utils.encode64(123+""));
        Utils.deepCopy(new Date());
        Utils.fill("123", 1);
        Utils.randString(1);
        Utils.randString(1,true);
        Utils.randString(1,true,true);
        Utils.formatMoney(new BigDecimal("123"),1);
        Utils.getCurrentProcessId();
        Utils.getAllLocalHostIpv4s();
        Utils.getCounter();
        Utils.getAllLocalHostIpv6s();
        Utils.getLocalHostIp();
        
    }

    private  ResponseVO<List<GroupDetailDTO>> getGroupList(){
        List<GroupDetailDTO> list = Lists.newArrayList();
        GroupDetailDTO groupDetailDTO = new GroupDetailDTO();
        groupDetailDTO.setId(1L);
        groupDetailDTO.setGroupAccount("11");
        groupDetailDTO.setContactMail("11");
        groupDetailDTO.setContactMobile("11");
        groupDetailDTO.setAddress("11");
        groupDetailDTO.setParentId("0");
        groupDetailDTO.setParentName("11");
        groupDetailDTO.setType(1);
        groupDetailDTO.setContactPerson("11");
        list.add(groupDetailDTO);

        GroupDetailDTO groupDetailDTO2 = new GroupDetailDTO();
        groupDetailDTO2.setId(2L);
        groupDetailDTO2.setGroupAccount("22");
        groupDetailDTO2.setContactMail("22");
        groupDetailDTO2.setContactMobile("22");
        groupDetailDTO2.setAddress("22");
        groupDetailDTO2.setParentId("0");
        groupDetailDTO2.setParentName("22");
        groupDetailDTO2.setType(1);
        groupDetailDTO2.setContactPerson("22");
        list.add(groupDetailDTO2);
        return ResponseVO.data(list);
    }
    private List<QpCustomer> getQpCustomer() {
        List<QpCustomer> list = Lists.newArrayList();
        QpCustomer qpFileDetails = new QpCustomer();
        qpFileDetails.setId(1L)
                .setCustomerId(1L)
                .setEnterpriseName("2222")
                .setSuperiorId(0L)
                .setTopLevelId(1L);
        list.add(qpFileDetails);
        QpCustomer qpFileDetails2 = new QpCustomer();
        qpFileDetails2.setId(2L)
                .setCustomerId(2L)
                .setEnterpriseName("2222")
                .setSuperiorId(1L)
                .setTopLevelId(1L);
        list.add(qpFileDetails2);
        return list;
    }


}
