package com.insgeek.business.quote.feilv;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.components.orm.model.querydsl.BQLQuery;
import com.insgeek.components.orm.model.querydsl.BQLQueryFactory;
import com.insgeek.protocol.data.client.entity.*;
import com.insgeek.protocol.platform.rule.client.RuleClient;
import com.insgeek.protocol.platform.rule.client.RuleExecuteClient;
import com.insgeek.protocol.platform.rule.dto.RuleInstanceDto;
import com.querydsl.core.Tuple;
import com.querydsl.core.support.QueryBase;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.querydsl.sql.ProjectableSQLQuery;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;
/**
 * @Description:
 * @Date: 2025-06-04  14:58
 * @Author: YuanSiYuan
 */
@ExtendWith(MockitoExtension.class)
public class FeilvEmployerQuoteServiceTest {

    @InjectMocks
    private FeilvEmployerQuoteService feilvEmployerQuoteService;

    @Mock
    private RuleExecuteClient ruleExecuteClient;

    @Mock
    private RuleClient ruleClient;

    @Mock
    private DataMapper<QpQuoteInfo> qpQuoteInfoDataMapper;

    @Mock
    private DataMapper<QpQuote> qpQuoteDataMapper;

    @Mock
    private DataMapper<QpQuoteConfig> qpQuoteConfigDataMapper;

    @Mock
    private DataMapper<QpQuoteDuty> qpQuoteDutyDataMapper;

    @Mock
    private BQLQueryFactory bqlQueryFactory;

    private Long quoteId = 1L;

    @Test
    void testGetFeilvReturnDtos_Basic() {
        // 准备测试数据
        List<QpQuoteConfig> configs = new ArrayList<>();
        QpQuoteConfig config = new QpQuoteConfig();
        config.setId(100L);
        config.setName("Test Plan");
        configs.add(config);

        QpQuote qpQuote = new QpQuote();
        qpQuote.setId(quoteId);
        qpQuote.setQuoteInfoId(200L);
        qpQuote.setOccupation("1");
        qpQuote.setExpandHospitalization("Yes");
        qpQuote.setAddGrant("Yes");
        qpQuote.setOpeningInsuredCount(10);
        qpQuote.setOpeningAvgAge(35);

        QpQuoteInfo qpQuoteInfo = new QpQuoteInfo();
        qpQuoteInfo.setId(200L);
        qpQuoteInfo.setEnterpriseNature("Private");

        List<QpQuoteDuty> duties = new ArrayList<>();
        QpQuoteDuty duty = new QpQuoteDuty();
        duty.setBusinessType("2811000");
        duty.setQuoteConfigId(100L);
        duties.add(duty);

        // 模拟依赖行为
        when(qpQuoteConfigDataMapper.entity(any(Class.class))).thenReturn(qpQuoteConfigDataMapper);
        when(qpQuoteConfigDataMapper.select(any(QpQuoteConfig.class), eq(true))).thenReturn(configs);

        when(qpQuoteDataMapper.entity(any(Class.class))).thenReturn(qpQuoteDataMapper);
        when(qpQuoteDataMapper.selectOne(eq(quoteId), eq(true))).thenReturn(qpQuote);

        when(qpQuoteDutyDataMapper.entity(any(Class.class))).thenReturn(qpQuoteDutyDataMapper);
        when(qpQuoteDutyDataMapper.select(any(DataCondition.class), eq(true))).thenReturn(duties);

        when(qpQuoteInfoDataMapper.entity(any(Class.class))).thenReturn(qpQuoteInfoDataMapper);
        when(qpQuoteInfoDataMapper.selectOne(eq(200L), eq(true))).thenReturn(qpQuoteInfo);


        List<SameOccNumDto> sameOccNumDtoList = new ArrayList<>();
//        sameOccNumDtoList.add(new SameOccNumDto(1L, "Engineer"));
//        sameOccNumDtoList.add(new SameOccNumDto(2L, "Engineer"));
        BQLQuery<Tuple> bqlQuery = mock(BQLQuery.class);
        when(bqlQueryFactory.select(any(), any())).thenReturn(bqlQuery);
        when(bqlQuery.from((Expression<?>) any())).thenReturn(bqlQuery);
        when(bqlQuery.leftJoin(any())).thenReturn(bqlQuery);
        when(bqlQuery.on(any(Predicate.class))).thenReturn(bqlQuery);
        when(bqlQuery.where(any(Predicate.class))).thenReturn(bqlQuery);
        when(bqlQuery.findList(SameOccNumDto.class)).thenReturn(sameOccNumDtoList);



        ResponseVO<List<RuleInstanceDto>> executeResponse = new ResponseVO<>();
        executeResponse.setData(Collections.singletonList(new RuleInstanceDto()));

//        when(ruleExecuteClient.execute(anyLong(), any())).thenReturn(executeResponse);

        ResponseVO<Map<Long, Map<String, String>>> envResponse = new ResponseVO<>();
        envResponse.setData(new HashMap<>());
//        when(ruleClient.findEnvironmentVariablesValuePostSecond(anyList())).thenReturn(envResponse);

        // 执行方法
        feilvEmployerQuoteService.getFeilvReturnDtos(quoteId, null);

        // 验证行为
        verify(qpQuoteConfigDataMapper, times(1)).select(any(QpQuoteConfig.class), eq(true));
        verify(qpQuoteDutyDataMapper, times(1)).select(any(DataCondition.class), eq(true));
//        verify(ruleExecuteClient, times(1)).execute(anyLong(), any());
//        verify(qpQuoteDutyDataMapper, times(1)).updateAll(anyList(), eq(true));
    }




}
