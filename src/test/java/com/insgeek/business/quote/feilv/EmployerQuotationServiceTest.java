package com.insgeek.business.quote.feilv;

import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.components.orm.model.querydsl.BQLQueryFactory;
import com.insgeek.protocol.data.client.entity.QpQuotation;
import com.insgeek.protocol.data.client.entity.QpQuotationConfig;
import com.insgeek.protocol.data.client.entity.QpQuotationDuty;
import com.insgeek.protocol.data.client.entity.QpQuotationInfo;
import com.insgeek.protocol.platform.rule.client.RuleClient;
import com.insgeek.protocol.platform.rule.client.RuleExecuteClient;
import com.insgeek.protocol.platform.rule.dto.RuleInstanceDto;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.fail;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class EmployerQuotationServiceTest {

    @InjectMocks
    private FeilvEmployerQuotationService service;

    @Mock
    private RuleExecuteClient ruleExecuteClient;

    @Mock
    private RuleClient ruleClient;

    @Mock
    private DataMapper<QpQuotationConfig> qpQuotationConfigDataMapper;

    @Mock
    private DataMapper<QpQuotation> qpQuotationDataMapper;

    @Mock
    private DataMapper<QpQuotationInfo> qpQuotationInfoDataMapper;

    @Mock
    private DataMapper<QpQuotationDuty> qpQuotationDutyDataMapper;

    @Mock
    private BQLQueryFactory bqlQueryFactory;

    @Test
    void testGetDutyType() throws Exception {
        // Arrange
        FeilvEmployerQuotationService service = new FeilvEmployerQuotationService();
        QpQuotationDuty duty1 = mock(QpQuotationDuty.class);
        QpQuotationDuty duty2 = mock(QpQuotationDuty.class);
        when(duty1.getBusinessType()).thenReturn("2813000");
        when(duty2.getBusinessType()).thenReturn("2818000");
        List<QpQuotationDuty> duties = Arrays.asList(duty1, duty2);
        // 使用反射获取私有方法
        Method method = FeilvEmployerQuotationService.class.getDeclaredMethod("getDutyType", List.class);
        method.setAccessible(true); // 设置为可访问
        // Act
        Integer result = (Integer) method.invoke(service, duties);
        // Assert
        assertEquals(2, result);
    }
}