package com.insgeek.business.quote.feilv;

/**
 * @Description:
 * @Date: 2025-06-09  15:26
 * @Author: Yuan<PERSON>i<PERSON>uan
 */

import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.entity.QpQuote;
import com.insgeek.protocol.data.client.entity.QpQuoteInfo;
import com.insgeek.protocol.platform.common.dto.entity.IgDuty;
import com.insgeek.protocol.platform.common.dto.entity.IgPlanConfig;
import com.insgeek.protocol.platform.rule.client.RuleClient;
import com.insgeek.protocol.platform.rule.client.RuleExecuteClient;
import com.insgeek.protocol.platform.rule.dto.RuleInstanceDto;
import com.insgeek.protocol.platform.rule.dto.RuleStreamRequest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class FeilvEmployerConfigServiceTest {

    @InjectMocks
    private FeilvEmployerConfigService feilvEmployerConfigService;
    @Mock
    RuleExecuteClient ruleExecuteClient;
    @Mock
    RuleClient ruleClient;
    @Mock
    private DataMapper<QpQuoteInfo> qpQuoteInfoDataMapper;
    @Mock
    private DataMapper<QpQuote> qpQuoteDataMapper;
    @Mock
    private DataMapper<IgPlanConfig> igPlanConfigDataMapper;
    @Mock
    private DataMapper<IgDuty> igDutyDataMapper;

    private static final Long TEST_QUOTE_ID = 1L;
    private static final Long CONFIG_ID = 100L;

    @BeforeEach
    void setUp() {
        // 初始化通用mock行为
        when(qpQuoteDataMapper.entity(QpQuote.class)).thenReturn(qpQuoteDataMapper);
        when(igPlanConfigDataMapper.entity(IgPlanConfig.class)).thenReturn(igPlanConfigDataMapper);
        when(igDutyDataMapper.entity(IgDuty.class)).thenReturn(igDutyDataMapper);
        when(qpQuoteInfoDataMapper.entity(QpQuoteInfo.class)).thenReturn(qpQuoteInfoDataMapper);
    }

    @Test
    @DisplayName("当正常流程时返回费率计算结果")
    void testGetFeilvReturnDtos_normalCase() {
        // 准备测试数据
        List<Long> configLists = Collections.singletonList(CONFIG_ID);

        // 创建模拟的IgPlanConfig对象
        IgPlanConfig planConfig = new IgPlanConfig();
        planConfig.setId(CONFIG_ID);
        planConfig.setName("测试计划");

        // 创建模拟的QpQuote对象
        QpQuote qpQuote = new QpQuote();
        qpQuote.setId(TEST_QUOTE_ID);
        qpQuote.setQuoteInfoId(1000L);
        qpQuote.setOccupation("1");
        qpQuote.setExpandHospitalization("1");
        qpQuote.setAddGrant("1");
        qpQuote.setOpeningInsuredCount(10);
        qpQuote.setOpeningAvgAge(35);
        qpQuote.setOpeningFemaleProportion(new BigDecimal("0.5"));
        qpQuote.setExpandTwentyFour("1");

        // 创建模拟的QpQuoteInfo对象
        QpQuoteInfo qpQuoteInfo = new QpQuoteInfo();
        qpQuoteInfo.setId(1000L);
        qpQuoteInfo.setEnterpriseNature("私营");

        // 创建模拟的IgDuty对象
        IgDuty igDuty = new IgDuty();
        igDuty.setPlanConfigId(CONFIG_ID);

        // 配置mock行为
        when(igPlanConfigDataMapper.select(configLists, true)).thenReturn(Collections.singletonList(planConfig));
        when(qpQuoteDataMapper.selectOne(TEST_QUOTE_ID, true)).thenReturn(qpQuote);
        when(qpQuoteInfoDataMapper.selectOne(1000L, true)).thenReturn(qpQuoteInfo);
        when(igDutyDataMapper.select(any(DataCondition.class), eq(true))).thenReturn(Collections.singletonList(igDuty));

        // 模拟规则执行结果
        RuleInstanceDto ruleInstanceDto = new RuleInstanceDto();
        ruleInstanceDto.setId(2629745579843800763L);

        ResponseVO responseVO = new ResponseVO();
        responseVO.setData(Collections.singletonList(ruleInstanceDto));
        when(ruleExecuteClient.execute(anyLong(), any(RuleStreamRequest.class))).thenReturn(responseVO);

        // 模拟规则结果查询
        Map<Long, Map<String, String>> mockResult = new HashMap<>();
        mockResult.put(2629745579843800763L, Collections.singletonMap("factor", "1.2"));

        ResponseVO responseVO1 = new ResponseVO<>();
        responseVO1.setData(mockResult);
        when(ruleClient.findEnvironmentVariablesValuePostSecond(anyList()))
                .thenReturn(responseVO1);

        // 执行测试
        Map<Long, Map<String, String>> result = feilvEmployerConfigService.getFeilvReturnDtos(TEST_QUOTE_ID, configLists);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result).hasSize(1);
        assertThat(result.get(2629745579843800763L)).containsEntry("factor", "1.2");

        // 验证交互次数
        verify(igPlanConfigDataMapper, times(1)).select(configLists, true);
        verify(qpQuoteDataMapper, times(1)).selectOne(TEST_QUOTE_ID, true);
        verify(ruleExecuteClient, times(1)).execute(anyLong(), any(RuleStreamRequest.class));
    }


}
