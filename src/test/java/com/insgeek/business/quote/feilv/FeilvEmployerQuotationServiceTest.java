package com.insgeek.business.quote.feilv;

import com.insgeek.protocol.platform.rule.client.RuleClient;
import com.insgeek.protocol.platform.rule.client.RuleExecuteClient;
import com.querydsl.core.types.Expression;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import static org.mockito.Mockito.*;
import static org.assertj.core.api.Assertions.*;
import java.util.*;
import java.util.stream.Collectors;
import com.insgeek.protocol.platform.rule.dto.*;
import com.insgeek.protocol.data.client.entity.*;
import com.insgeek.boot.web.vo.*;
import com.insgeek.components.orm.model.impl.data.*;
import com.insgeek.components.orm.model.querydsl.*;
import com.insgeek.business.quote.common.enums.*;
import com.insgeek.business.quote.feilv.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.Tuple;
import org.springframework.beans.factory.annotation.Autowired;

@ExtendWith(MockitoExtension.class)
class FeilvEmployerQuotationServiceTest {
    @InjectMocks
    private FeilvEmployerQuotationService feilvEmployerQuotationService;
    
    @Mock
    private DataMapper<QpQuotationConfig> qpQuotationConfigDataMapper;
    @Mock
    private DataMapper<QpQuotation> qpQuotationDataMapper;
    @Mock
    private DataMapper<QpQuotationDuty> qpQuotationDutyDataMapper;
    @Mock
    private BQLQueryFactory bqlQueryFactory;
    @Mock
    private RuleExecuteClient ruleExecuteClient;
    @Mock
    private RuleClient ruleClient;

    @Mock
    private DataMapper<QpQuotationInfo> qpQuotationInfoDataMapper;
    
    // 测试getFeilvReturnDtos正常流程
    @Test
    void getFeilvReturnDtos_normalFlow_verifyRuleExecution() {
        // 准备测试数据
        Long quotationId = 1L;
        List<Long> configLists = Arrays.asList(100L, 200L);
        
        // 模拟配置查询结果
        QpQuotationConfig config1 = new QpQuotationConfig();
        config1.setId(1L);
        config1.setName("雇主标品方案一");
        QpQuotationConfig config2 = new QpQuotationConfig();
        config2.setId(2L);
        config2.setName("雇主标品方案二");
        when(qpQuotationConfigDataMapper.entity(any())).thenReturn(qpQuotationConfigDataMapper);
        when(qpQuotationConfigDataMapper.select(any(QpQuotationConfig.class), eq(true))).thenReturn(Arrays.asList(config1, config2));
        
        // 模拟报价信息查询
        QpQuotation qpQuotation = mock(QpQuotation.class);
        when(qpQuotation.getOccupation()).thenReturn("1");
        when(qpQuotation.getExpandHospitalization()).thenReturn("YES");
        when(qpQuotationDataMapper.entity(any(Class.class))).thenReturn(qpQuotationDataMapper);
        when(qpQuotationDataMapper.selectOne(quotationId, true)).thenReturn(qpQuotation);
        
        // 模拟责任条款查询
        QpQuotationDuty duty1 =new QpQuotationDuty();
        duty1.setBusinessType("2811000");
        duty1.setQuotationConfigId(1L);
        when(qpQuotationDutyDataMapper.entity(any(Class.class))).thenReturn(qpQuotationDutyDataMapper);
        when(qpQuotationDutyDataMapper.select(any(DataCondition.class), eq(true))).thenReturn(Arrays.asList(duty1));


        QpQuotationInfo qpQuotationInfo = new QpQuotationInfo();
        qpQuotationInfo.setEnterpriseNature("PRIVATE");
        qpQuotationInfo.setId(1L);
        qpQuotationInfo.setStandardFlag("1");

        when(qpQuotationInfoDataMapper.entity(any(Class.class))).thenReturn(qpQuotationInfoDataMapper);
        when(qpQuotationInfoDataMapper.selectOne(any(), eq(true))).thenReturn(qpQuotationInfo);
        // 模拟职业类别因子
        Map<Long, Integer> sameOccNumMap = new HashMap<>();
        sameOccNumMap.put(100L, 2);
        List<SameOccNumDto> sameOccNumDtoList = new ArrayList<>();
        sameOccNumDtoList.add(new SameOccNumDto(1L, "1"));
        sameOccNumDtoList.add(new SameOccNumDto(2L, "1"));
        BQLQuery<Tuple> bqlQuery = mock(BQLQuery.class);
        when(bqlQueryFactory.select(any(), any())).thenReturn(bqlQuery);
        when(bqlQuery.from((Expression<?>) any())).thenReturn(bqlQuery);
        when(bqlQuery.leftJoin(any())).thenReturn(bqlQuery);
        when(bqlQuery.on(any(Predicate.class))).thenReturn(bqlQuery);
        when(bqlQuery.where(any(Predicate.class))).thenReturn(bqlQuery);
        when(bqlQuery.findList(SameOccNumDto.class)).thenReturn(sameOccNumDtoList);
        
        // 模拟规则流执行
        RuleStreamRequest expectedRequest = new RuleStreamRequest();
        expectedRequest.setDataIds(Arrays.asList(100L, 200L));
        
        ResponseVO<List<RuleInstanceDto>> responseVO =new ResponseVO<>();
        responseVO.setData(Collections.singletonList(new RuleInstanceDto()));
        when(ruleExecuteClient.execute(anyLong(), any())).thenReturn(responseVO);

        ResponseVO<Map<Long, Map<String, String>>> envResponse = new ResponseVO<>();
        envResponse.setData(new HashMap<>());
        when(ruleClient.findEnvironmentVariablesValuePostSecond(anyList())).thenReturn(envResponse);
        ResponseVO<List<RuleInstanceDto>>  ruleExecuteResponse = new ResponseVO<>();

        List<RuleInstanceDto>  ruleInstanceDtos = new ArrayList<>();
        ruleInstanceDtos.add(new RuleInstanceDto().setId(1L));
        ruleExecuteResponse.setData(ruleInstanceDtos);
        when(ruleExecuteClient.execute(anyLong(), any(RuleStreamRequest.class)))
                .thenReturn(ruleExecuteResponse); // 确保模拟行为正确
        // 执行方法
        feilvEmployerQuotationService.getFeilvReturnDtos(quotationId, configLists);

        // 验证规则流调用
        verify(ruleExecuteClient).execute(anyLong(), any(RuleStreamRequest.class));
    }
}