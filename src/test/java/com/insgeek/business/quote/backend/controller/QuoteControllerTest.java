package com.insgeek.business.quote.backend.controller;

import com.insgeek.boot.web.vo.ResponseVO;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;


@Slf4j
@RunWith(MockitoJUnitRunner.class)
class QuoteControllerTest {

    MockMvc mockMvc;


    @InjectMocks
    QpQuoteController quoteController;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(quoteController).build();
    }

    @Test
    void getFemaleProportion() {
        ResponseVO<String> femaleProportion = quoteController.getFemaleProportion(100, 20);

        assertEquals(new BigDecimal(femaleProportion.getData()).compareTo(BigDecimal.valueOf(20)), 0);
    }

/*    @Test
    void getTenantUserList() {
        List<Long> tenantIds = new ArrayList<>();
        tenantIds.add(1L);
        ResponseVO<Object> femaleProportion = quoteV2Controller.getTenantUserList(tenantIds);
    }*/

}