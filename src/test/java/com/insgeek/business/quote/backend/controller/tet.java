package com.insgeek.business.quote.backend.controller;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

import com.alibaba.fastjson.JSONObject;
import com.insgeek.business.quote.backend.dto.rate.RateOneDimensionDto;
import com.insgeek.business.quote.backend.dto.rate.RateTwoDimensionDto;

/**
 * @ClassName: tet
 * @Description:
 * @Author: YYY
 * @Date: 2022/6/29 9:26
 **/
public class tet {

    public static void main(String[] args) {
        String ss="0";
        String ss1="1";
        String ss2="2";
        String s3="3";
        String s4="4";
        String s5="5";

        Object[] rr=new Object[3];
        rr[0] = ss;
        rr[1] = ss1;
        rr[2] = ss2;
        fff(rr,s3);
    }

    private static void fff(Object...obj){
        for (Object o : obj) {
            if(getObject(o)){
                Object[] o2=(Object[])o;
                for (Object o1 : o2) {
                    System.out.println(o1);
                }
            }else
            System.out.println(o);
        }
        System.out.println();
    }
    private static boolean getObject(Object...obj){
        if(obj instanceof Object[]){
            return true;
        }
        return false;
    }



    public static BigDecimal incremental(BigDecimal base, BigDecimal incremental,
                                         BigDecimal divisor, BigDecimal value) {
        BigDecimal[] bigDecimals = value.divideAndRemainder(divisor);
        BigDecimal res = bigDecimals[0].multiply(incremental).add(base).add(incremental);
        if (bigDecimals[1].compareTo(BigDecimal.ZERO) == 0) {
            return res.subtract(incremental);
        }
        return res;
    }

    private static void one(){
        String str="[\n" +
                "  {\n" +
                "    \"order\": 1,\n" +
                "    \"result_type\": 1,\n" +
                "    \"result\": \"\",\n" +
                "    \"resultHandleType\": 1,\n" +
                "    \"variable\": \"\",\n" +
                "    \"rateDtos\": [\n" +
                "      {\n" +
                "        \"filedType\": 1,\n" +
                "        \"filed\": \"qpQuoteDuty.amount\",\n" +
                "        \"leftBoundary\": \"[\",\n" +
                "        \"rightBoundary\": \"]\",\n" +
                "        \"handleField\": \"\",\n" +
                "        \"startVal\": 0,\n" +
                "        \"endVal\": 100000,\n" +
                "        \"order\": 1\n" +
                "      }\n" +
                "    ]\n" +
                "  }\n" +
                "]";

        List<RateOneDimensionDto> ddd= JSONObject.parseArray(str,RateOneDimensionDto.class);
        System.out.println(ddd);
    }

    private static void two(){
        String str="{\n" +
                "  \"filedY\": \"qpQuoteDuty.amount\",\n" +
                "  \"filedX\": \"qpQuoteDuty.payment\",\n" +
                "  \"xValType\": \"\",\n" +
                "  \"yValType\": \"\",\n" +
                "  \"hitType\": 2,\n" +
                "  \"rateTwoDtos\": [\n" +
                "    {\n" +
                "      \"yVal\": 1000,\n" +
                "      \"leftBoundary\": \"[\",\n" +
                "      \"rightBoundary\": \"]\",\n" +
                "      \"type\": 1,\n" +
                "      \"xs\": [\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 50,\n" +
                "          \"xVal\": 35,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"50%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 60,\n" +
                "          \"xVal\": 36,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"60%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 70,\n" +
                "          \"xVal\": 37,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"70%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 80,\n" +
                "          \"xVal\": 38,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"80%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 90,\n" +
                "          \"xVal\": 39,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"90%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 100,\n" +
                "          \"xVal\": 40,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"100%\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"order\": 1\n" +
                "    },\n" +
                "    {\n" +
                "      \"yVal\": 2000,\n" +
                "      \"leftBoundary\": \"[\",\n" +
                "      \"rightBoundary\": \"]\",\n" +
                "      \"type\": 1,\n" +
                "      \"xs\": [\n" +
                "        {\n" +
                "           \"leftBoundary\": \"[\",\n" +
                "           \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 50,\n" +
                "          \"xVal\": 50,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"50%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 60,\n" +
                "          \"xVal\": 52,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"60%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 70,\n" +
                "          \"xVal\": 54,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"70%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 80,\n" +
                "          \"xVal\": 56,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"80%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 90,\n" +
                "          \"xVal\": 58,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"90%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 100,\n" +
                "          \"xVal\": 60,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"100%\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"order\": 2\n" +
                "    },\n" +
                "    {\n" +
                "      \"yVal\": 3000,\n" +
                "      \"leftBoundary\": \"[\",\n" +
                "      \"rightBoundary\": \"]\",\n" +
                "      \"type\": 1,\n" +
                "      \"xs\": [\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 50,\n" +
                "          \"xVal\": 50,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"50%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 60,\n" +
                "          \"xVal\": 54,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"60%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 70,\n" +
                "          \"xVal\": 58,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"70%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 80,\n" +
                "          \"xVal\": 62,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"80%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 90,\n" +
                "          \"xVal\": 66,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"90%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 100,\n" +
                "          \"xVal\": 70,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"100%\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"order\": 3\n" +
                "    },\n" +
                "    {\n" +
                "      \"yVal\": 5000,\n" +
                "      \"leftBoundary\": \"[\",\n" +
                "      \"rightBoundary\": \"]\",\n" +
                "      \"type\": 1,\n" +
                "      \"xs\": [\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 50,\n" +
                "          \"xVal\": 60,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"50%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 60,\n" +
                "          \"xVal\": 65,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"60%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 70,\n" +
                "          \"xVal\": 70,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"70%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 80,\n" +
                "          \"xVal\": 75,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"80%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 90,\n" +
                "          \"xVal\": 80,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"90%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 100,\n" +
                "          \"xVal\": 85,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"100%\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"order\": 4\n" +
                "    },\n" +
                "    {\n" +
                "      \"yVal\": 8000,\n" +
                "      \"leftBoundary\": \"[\",\n" +
                "      \"rightBoundary\": \"]\",\n" +
                "      \"type\": 1,\n" +
                "      \"xs\": [\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 50,\n" +
                "          \"xVal\": 70,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"50%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 60,\n" +
                "          \"xVal\": 76,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"60%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 70,\n" +
                "          \"xVal\": 82,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"70%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 80,\n" +
                "          \"xVal\": 88,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"80%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 90,\n" +
                "          \"xVal\": 94,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"90%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 100,\n" +
                "          \"xVal\": 100,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"100%\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"order\": 5\n" +
                "    },\n" +
                "    {\n" +
                "      \"yVal\": 10000,\n" +
                "      \"leftBoundary\": \"[\",\n" +
                "      \"rightBoundary\": \"]\",\n" +
                "      \"type\": 1,\n" +
                "      \"xs\": [\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 50,\n" +
                "          \"xVal\": 88,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"50%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 60,\n" +
                "          \"xVal\": 96,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"60%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 70,\n" +
                "          \"xVal\": 104,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"70%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 80,\n" +
                "          \"xVal\": 112,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"80%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 90,\n" +
                "          \"xVal\": 120,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"90%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 100,\n" +
                "          \"xVal\": -1,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"100%\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"order\": 6\n" +
                "    },\n" +
                "    {\n" +
                "      \"yVal\": 12000,\n" +
                "      \"leftBoundary\": \"[\",\n" +
                "      \"rightBoundary\": \"]\",\n" +
                "      \"type\": 1,\n" +
                "      \"xs\": [\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 50,\n" +
                "          \"xVal\": 100,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"50%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 60,\n" +
                "          \"xVal\": 108,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"60%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 70,\n" +
                "          \"xVal\": 116,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"70%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 80,\n" +
                "          \"xVal\": 125,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"80%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 90,\n" +
                "          \"xVal\": -1,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"90%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 100,\n" +
                "          \"xVal\": -1,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"100%\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"order\": 7\n" +
                "    },\n" +
                "    {\n" +
                "      \"yVal\": 15000,\n" +
                "      \"leftBoundary\": \"[\",\n" +
                "      \"rightBoundary\": \"]\",\n" +
                "      \"type\": 1,\n" +
                "      \"xs\": [\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 50,\n" +
                "          \"xVal\": 120,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"50%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 60,\n" +
                "          \"xVal\": 130,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"60%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 70,\n" +
                "          \"xVal\": 140,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"70%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 80,\n" +
                "          \"xVal\": -1,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"80%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 90,\n" +
                "          \"xVal\": -1,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"90%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 100,\n" +
                "          \"xVal\": -1,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"100%\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"order\": 8\n" +
                "    },\n" +
                "    {\n" +
                "      \"yVal\": 20000,\n" +
                "      \"leftBoundary\": \"[\",\n" +
                "      \"rightBoundary\": \"]\",\n" +
                "      \"type\": 1,\n" +
                "      \"xs\": [\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 50,\n" +
                "          \"xVal\": 150,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"50%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 60,\n" +
                "          \"xVal\": 165,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"60%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 70,\n" +
                "          \"xVal\": -1,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"70%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 80,\n" +
                "          \"xVal\": -1,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"80%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 90,\n" +
                "          \"xVal\": -1,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"90%\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"leftBoundary\": \"[\",\n" +
                "          \"rightBoundary\": \"]\",\n" +
                "          \"xKey\": 100,\n" +
                "          \"xVal\": -1,\n" +
                "          \"type\": 1,\n" +
                "          \"xName\": \"100%\"\n" +
                "        }\n" +
                "      ],\n" +
                "      \"order\": 8\n" +
                "    }\n" +
                "  ]\n" +
                "}";
        RateTwoDimensionDto ddd= JSONObject.parseObject(str,RateTwoDimensionDto.class);
        System.out.println(ddd);
    }
}
