package com.insgeek.business.quote.backend.controller;

import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.backend.Enums.QuoteTypeEnum;
import com.insgeek.business.quote.common.dao.mapper.QuotationInfoMapper;
import com.insgeek.business.quote.common.enums.dict.RenewFlagEnum;
import com.insgeek.business.quote.risk.InsureRulesDto;
import com.insgeek.business.quote.risk.RiskService;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.dto.IgStandardOrderDetail;
import com.insgeek.protocol.data.client.entity.*;
import com.insgeek.protocol.dataapp.bi.client.BusinessClient;
import com.insgeek.protocol.dataapp.bi.dto.RenewCountDto;
import com.insgeek.protocol.insurance.enums.RiskRuleTypeEnum;
import com.insgeek.protocol.insurance.entity.IgSpecial;
import com.insgeek.protocol.insurance.entity.InsuranceRuleVo;
import com.insgeek.protocol.insurance.entity.RuleItem;
import com.insgeek.protocol.insurance.client.RiskCtrlFactoryClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RiskServiceTest {

    @InjectMocks
    private RiskService riskService;

    @Mock
    private DataMapper<QpQuotation> quotationMapper;

    @Mock
    private QuotationInfoMapper quotationInfoMapper;

    @Mock
    private BusinessClient businessClient;

    @Mock
    private DataMapper<QpCustomer> qpCustomerDataMapper;

    @Mock
    private RiskCtrlFactoryClient riskCtrlFactoryClient;

    @Mock
    private DataMapper<QpQuotationHistoryCustomer> qpQuotationHistoryCustomerDataMapper;

    @Mock
    private DataMapper<IgStandardOrderDetail> igStandardOrderDetailDataMapper;

    @Mock
    private DataMapper<QpQuotationConfig> quotationConfigMapper;

    @Mock
    private DataMapper<QpQuotationDuty> quotationDutyMapper;

    @Mock
    private DataMapper<IgSpecial> igSpecialDataMapper;

    private static final Long QUOTATION_INFO_ID = 1001L;

    @BeforeEach
    void setUp() {
        // 初始化通用mock行为
        when(quotationMapper.entity(QpQuotation.class)).thenReturn(quotationMapper);
        // 以下桩定义仅在特定测试中需要，移除避免UnnecessaryStubbingException
        when(quotationConfigMapper.entity(QpQuotationConfig.class)).thenReturn(quotationConfigMapper);
//        when(quotationDutyMapper.entity(QpQuotationDuty.class)).thenReturn(quotationDutyMapper);
        when(qpCustomerDataMapper.entity(QpCustomer.class)).thenReturn(qpCustomerDataMapper);
//        when(igSpecialDataMapper.entity(IgSpecial.class)).thenReturn(igSpecialDataMapper);
//        when(igStandardOrderDetailDataMapper.entity(IgStandardOrderDetail.class)).thenReturn(igStandardOrderDetailDataMapper);
        when(qpQuotationHistoryCustomerDataMapper.entity(QpQuotationHistoryCustomer.class)).thenReturn(qpQuotationHistoryCustomerDataMapper);
    }

    @Test
    @DisplayName("当非续期且非雇主报价时，正确生成保险规则")
    void testGetInsureRiskdata_nonRenew_nonEmployer() {
        // 准备测试数据
        QpQuotationInfo qpQuotationInfo = new QpQuotationInfo();
        qpQuotationInfo.setId(QUOTATION_INFO_ID);
        qpQuotationInfo.setRenewFlag(RenewFlagEnum.RENEW.getValue());
        qpQuotationInfo.setQuoteType(QuoteTypeEnum.EMPLOYER.getCode());
        qpQuotationInfo.setMaxAge(60);
        qpQuotationInfo.setMinAge(18);
        qpQuotationInfo.setOpeningAvgAge(35);
        qpQuotationInfo.setInterimAvgAge(40);
        qpQuotationInfo.setCustomerId(100L);

        List<QpQuotation> quotations = createTestQuotations();

        // 配置mock行为
        when(quotationInfoMapper.find(QUOTATION_INFO_ID)).thenReturn(qpQuotationInfo);
        when(quotationMapper.select(any(DataCondition.class), eq(true))).thenReturn(quotations);

        // 模拟客户信息
        QpCustomer qpCustomer = new QpCustomer();
        qpCustomer.setCustomerId(100L);
        when(qpCustomerDataMapper.entity(QpCustomer.class)
                .selectOne(qpQuotationInfo.getCustomerId(), true)).thenReturn(qpCustomer);

        // 模拟续期次数
        RenewCountDto renewCountDto = new RenewCountDto();
        renewCountDto.setRenewCt(2);
        ResponseVO<List<RenewCountDto>> responseVO = ResponseVO.data(Collections.singletonList(renewCountDto));
        when(businessClient.getRenewCount(anyList())).thenReturn(responseVO);

        // 执行测试
        InsureRulesDto result = riskService.getInsureRiskdata(QUOTATION_INFO_ID);

        // 验证结果
        assertThat(result).isNotNull();
        // 验证交互次数

    }


    private List<QpQuotation> createTestQuotations() {
        List<QpQuotation> quotations = new ArrayList<>();

        // 创建子女
        QpQuotation child = new QpQuotation();
        child.setRelation("1");
        child.setMaxAge(25);
        child.setMinAge(10);

        // 创建配偶
        QpQuotation spouse = new QpQuotation();
        spouse.setRelation("2");
        spouse.setMaxAge(55);
        spouse.setMinAge(25);

        // 创建父母
        QpQuotation parent = new QpQuotation();
        parent.setRelation("3");
        parent.setMaxAge(70);
        parent.setMinAge(30);

        quotations.add(child);
        quotations.add(spouse);
        quotations.add(parent);

        return quotations;
    }

    private InsuranceRuleVo createTestInsuranceRuleVo() {
        InsuranceRuleVo insuranceRuleVo = new InsuranceRuleVo();
        insuranceRuleVo.setRuleKey(RiskRuleTypeEnum.STAFF_AGE_RANGE.getKey());

        RuleItem minAgeItem = new RuleItem();
        minAgeItem.setItemKey("min_age");
        minAgeItem.setItemValue("25");

        RuleItem maxAgeItem = new RuleItem();
        maxAgeItem.setItemKey("max_age");
        maxAgeItem.setItemValue("55");

        insuranceRuleVo.setRuleItems(Arrays.asList(minAgeItem, maxAgeItem));

        return insuranceRuleVo;
    }

    private void mockFamilyExtensionBehavior() {
        // 模拟方案配置
        List<Long> quotationIds = Collections.singletonList(1L);
        DataCondition<QpQuotationConfig> configDataCondition = new DataCondition<>();
        when(quotationConfigMapper.select(any(DataCondition.class), eq(true))).thenReturn(Collections.singletonList(new QpQuotationConfig()));

        // 模拟责任
        DataCondition<QpQuotationDuty> dutydataCondition = new DataCondition<>();
        when(quotationDutyMapper.select(any(DataCondition.class), eq(true))).thenReturn(Collections.singletonList(new QpQuotationDuty()));

        // 模拟特殊条款
        DataCondition<IgSpecial> meSpecialCondition = new DataCondition<>();
        when(igSpecialDataMapper.select(any(DataCondition.class), eq(true))).thenReturn(Collections.singletonList(new IgSpecial()));
    }
}
