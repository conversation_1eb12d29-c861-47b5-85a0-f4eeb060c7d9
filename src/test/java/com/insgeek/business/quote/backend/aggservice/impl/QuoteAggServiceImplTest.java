package com.insgeek.business.quote.backend.aggservice.impl;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.insgeek.boot.commons.exception.InsgeekException;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.backend.dto.quote.QpQuoteDutyDTO;
import com.insgeek.business.quote.backend.dto.quote.QuoteConfigDto;
import com.insgeek.business.quote.backend.dto.quote.QuoteDTO;
import com.insgeek.business.quote.backend.exception.QuoteErrorCode;
import com.insgeek.business.quote.common.dao.mapper.QuoteMapper;
import com.insgeek.business.quote.common.feign.api.QueryFeginService;
import com.insgeek.business.quote.frontend.enums.sql.table.TableNameEnums;
import com.insgeek.insurance.meta.utils.business.duty.DutyCodeConfig;
import com.insgeek.protocol.data.client.dto.IgProductDutySplitDto;
import com.insgeek.protocol.data.client.dto.QuoteProductDTO;
import com.insgeek.protocol.data.client.entity.QpQuote;
import com.insgeek.protocol.platform.common.client.BqlPermissionClient;
import com.insgeek.protocol.platform.common.client.PlatformDataClient;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;
import org.springframework.util.CollectionUtils;

import java.time.ZonedDateTime;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;


@Slf4j
@RunWith(MockitoJUnitRunner.class)
@Disabled
class QuoteAggServiceImplTest {

    MockMvc mockMvc;

    @Mock
    QuoteMapper quoteMapper;

    @Mock
    BqlPermissionClient bqlPermissionClient;

    @Mock
    private QueryFeginService queryFeginService;

    @Mock
    private PlatformDataClient platformDataClient;

    @InjectMocks
    QuoteAggServiceImpl quoteAggService;

    @Mock
    private DutyCodeConfig dutyCodeConfig;


    @BeforeEach
    public void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(quoteAggService).build();

    }


    /**
     * 人员类型为非本人时，报价的保险期间应在所选的本人保险期间内
     */
    @Test
    void createQuote() throws Exception {
        //关联主被报价
        Mockito.doReturn(new QpQuote().setId(1L)
                //.setStartTime(DateUtil.parseDateTime("2021-01-01 00:00:00").getTime())
               // .setEndTime(DateUtil.parseDateTime("2021-12-31 23:59:59").getTime())
        ).when(quoteMapper).selectOneByEntity(new QpQuote().setId(1L));
        QuoteDTO quoteDTO = (QuoteDTO) new QuoteDTO().setStartDate(ZonedDateTime.now()).setEndDate(ZonedDateTime.now()).setLinkQuoteId(1L);
        assertThrows(InsgeekException.class, () -> quoteAggService.createQuote(quoteDTO), QuoteErrorCode.LINK_QUOTE_INSURANCE_PERIOD.getMessage());

    }

    @Test
    void getTenantUserList() {
        try {
            List<Long> tenantId = new ArrayList<>();
            tenantId.add(2405640542886864576L);
            quoteAggService.getTenantUserList(tenantId);
        } catch (Exception e) {
            System.out.println(e);
        }
    }


    @Test
    void createQuoteConfig() throws Exception {
        try {
            quoteAggService.createQuoteConfig(null, null);
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    @Test
    void createQuoteConfigInfo() throws Exception {
        try {
            quoteAggService.createQuoteConfigInfo(null, null);
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    @Test
    void getQuoteDutyTerms1() throws Exception {
        try {
            quoteAggService.getQuoteDutyTerms(null);
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    @Test
    void getQuoteDutyTerms2() throws Exception {
        try {
            quoteAggService.getQuoteDutyTerms(null, null);
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    @Test
    void requestCompanyRisk() throws Exception {
        try {
            quoteAggService.requestCompanyRisk(null, null, null);
        } catch (Exception e) {
            System.out.println(e);
        }
    }


    @Test
    void verifyQuoteCreated() throws Exception {
        try {
            quoteAggService.verifyQuoteCreated(null);
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    @Test
    void autoCreate() throws Exception {
        try {
            quoteAggService.autoCreate(null);
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    /**
     * 人员类型为非本人时，报价的保险期间应在所选的本人保险期间内
     */
    @Test
    void updateQuote() throws Exception {
        //关联主被报价
//        Mockito.doReturn(new QpQuote().setId(1L)
//                .setStartTime(DateUtil.parseDateTime("2021-01-01 00:00:00").getTime())
//                .setEndTime(DateUtil.parseDateTime("2021-12-31 23:59:59").getTime())
//        ).when(quoteMapper).selectOneByEntity(new QpQuote().setId(1L));
//        QpQuote quoteDTO = (QuoteDTO) new QpQuote().setStartDate("2020-01-01 00:00:00").setEndDate("2021-12-30 23:59:59").setLinkQuoteId(1L);
//        assertThrows(InsgeekException.class,() -> quoteAggService.updateQuote(1L, quoteDTO), QuoteErrorCode.LINK_QUOTE_INSURANCE_PERIOD.getMessage());
    }

    @Test
    void verifyQpConfig() {
        String json = "[\n" +
                "    {\n" +
                "        \"product_id\":\"573475863\",\n" +
                "        \"product_duty_id\":\"573475896\",\n" +
                "        \"custom_duty\":{\n" +
                "            \"qp_product_id\":\"\",\n" +
                "            \"product_id\":\"573475863\",\n" +
                "            \"product_duty_id\":\"573475896\",\n" +
                "            \"pkg_id\":\"573475862\",\n" +
                "            \"type\":\"1321600\"\n" +
                "        }\n" +
                "    }\n" +
                "]";
        List<QuoteConfigDto> configDtoList = JacksonUtils.readValue(json, new TypeReference<List<QuoteConfigDto>>() {
        });

        String sqlResp = "[\n" +
                "    {\n" +
                "        \"type\":\"1321600\"\n" +
                "    },\n" +
                "    {\n" +
                "        \"type\":\"1321600\"\n" +
                "    }\n" +
                "]";
        List<QuoteProductDTO> productDTOList = JacksonUtils.readValue(sqlResp, new TypeReference<List<QuoteProductDTO>>() {
        });
        Mockito.when(bqlPermissionClient.query(anyString())).thenReturn(ResponseVO.builder().data(productDTOList).build());

        Mockito.when(queryFeginService.getQuoteProductList(anyList())).thenReturn(productDTOList);

        assertThrows(InsgeekException.class, () -> quoteAggService.verifyQpConfig(1L, configDtoList));
    }


    @Test
    void getQpQuoteDuties() {
        try {


            String emptyOptionalJson = "[\n" +
                    "    {\n" +
                    "        \"product_id\":\"444292420\",\n" +
                    "        \"product_duty_id\":\"444335163\",\n" +
                    "        \"custom_duty\":{\n" +
                    "            \"qp_product_id\":\"\",\n" +
                    "            \"amount\":\"10000\",\n" +
                    "            \"deduction\":\"0\",\n" +
                    "            \"product_id\":\"444292420\",\n" +
                    "            \"product_duty_id\":\"444335163\",\n" +
                    "            \"once_deduction\":\"\",\n" +
                    "            \"once_quota\":\"0\",\n" +
                    "            \"payment\":\"90\",\n" +
                    "            \"product_name\":\"补充医疗门诊\",\n" +
                    "            \"property\":1,\n" +
                    "            \"pkg_name\":\"补充医疗门诊\",\n" +
                    "            \"pkg_id\":\"444286937\",\n" +
                    "            \"type\":\"1321220\",\n" +
                    "            \"product_type_name\":\"补充医疗门（急）诊\",\n" +
                    "            \"product_type_id\":\"5\",\n" +
                    "            \"is_disabled\":0,\n" +
                    "            \"source_code\":\"B\",\n" +
                    "            \"otc_range\":0,\n" +
                    "            \"edit_status\":1\n" +
                    "        }\n" +
                    "    }\n" +
                    "]";
            List<QuoteConfigDto> emptyQuoteConfigDtoList = JacksonUtils.readValue(emptyOptionalJson, new TypeReference<List<QuoteConfigDto>>() {
            });
            String json = "[\n" +
                    "    {\n" +
                    "        \"qp_product_id\":114330354,\n" +
                    "        \"amount\":20000,\n" +
                    "        \"deduction\":0,\n" +
                    "        \"product_id\":104592123,\n" +
                    "        \"product_duty_id\":104598886,\n" +
                    "        \"once_deduction\":0,\n" +
                    "        \"once_quota\":0,\n" +
                    "        \"payment\":100,\n" +
                    "        \"product_name\":\"补充医疗门诊-次免\",\n" +
                    "        \"property\":2,\n" +
                    "        \"pkg_name\":\"准生产标品\",\n" +
                    "        \"pkg_id\":104592090,\n" +
                    "        \"type\":\"1321220\",\n" +
                    "        \"product_type_name\":\"补充医疗门（急）诊\",\n" +
                    "        \"product_type_id\":5,\n" +
                    "        \"is_disabled\":0,\n" +
                    "        \"source_code\":\"BR\",\n" +
                    "        \"otc_range\":0,\n" +
                    "        \"edit_status\":1\n" +
                    "    }\n" +
                    "]";
            List<QuoteProductDTO> quoteProductDTOS = JacksonUtils.readValue(json, new TypeReference<List<QuoteProductDTO>>() {
            });
            Mockito.doReturn(quoteProductDTOS).when(queryFeginService).getQuoteProductList();
            List<QpQuoteDutyDTO> emptyQpQuoteDuties = quoteAggService.getQpQuoteDuties(0L, 1L, emptyQuoteConfigDtoList);
            // assertTrue(() -> CollectionUtils.isEmpty(emptyQpQuoteDuties))

            Mockito.when(dutyCodeConfig.createCode(anyString(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyInt(), anyList(), anyInt())).thenReturn("TEST");

            String qpOptionalJson = "[\n" +
                    "    {\n" +
                    "        \"qp_product_id\":\"444292420\"\n" +
                    "    }\n" +
                    "]";
            List<QuoteConfigDto> qpQuoteConfigDtoList = JacksonUtils.readValue(qpOptionalJson, new TypeReference<List<QuoteConfigDto>>() {
            });
            Mockito.when(queryFeginService.getQuoteProductList(anyList())).thenReturn(quoteProductDTOS);
            List<QpQuoteDutyDTO> qpQuoteDuties = quoteAggService.getQpQuoteDuties(0L, 1L, qpQuoteConfigDtoList);
            assertTrue(() -> !CollectionUtils.isEmpty(qpQuoteDuties));
            assertEquals(20000, qpQuoteDuties.get(0).getAmount());
            assertEquals("TEST", qpQuoteDuties.get(0).getDutyCode());


            String customOptionalJson = "[\n" +
                    "    {\n" +
                    "        \"product_id\":\"104592123\",\n" +
                    "        \"product_duty_id\":\"444335163\",\n" +
                    "        \"custom_duty\":{\n" +
                    "            \"qp_product_id\":\"\",\n" +
                    "            \"amount\":\"10000\",\n" +
                    "            \"deduction\":\"0\",\n" +
                    "            \"product_id\":\"444292420\",\n" +
                    "            \"product_duty_id\":\"444335163\",\n" +
                    "            \"once_deduction\":\"\",\n" +
                    "            \"once_quota\":\"0\",\n" +
                    "            \"payment\":\"90\",\n" +
                    "            \"product_name\":\"补充医疗门诊\",\n" +
                    "            \"property\":2,\n" +
                    "            \"pkg_name\":\"补充医疗门诊\",\n" +
                    "            \"pkg_id\":\"444286937\",\n" +
                    "            \"type\":\"1321220\",\n" +
                    "            \"product_type_name\":\"补充医疗门（急）诊\",\n" +
                    "            \"product_type_id\":\"5\",\n" +
                    "            \"is_disabled\":0,\n" +
                    "            \"source_code\":\"B\",\n" +
                    "            \"otc_range\":0,\n" +
                    "            \"edit_status\":1\n" +
                    "        }\n" +
                    "    }\n" +
                    "]";
            List<QuoteConfigDto> customQuoteConfigDtoList = JacksonUtils.readValue(customOptionalJson, new TypeReference<List<QuoteConfigDto>>() {
            });

            List<QpQuoteDutyDTO> customQuoteDuties = quoteAggService.getQpQuoteDuties(0L, 1L, customQuoteConfigDtoList);
            assertTrue(() -> !CollectionUtils.isEmpty(customQuoteDuties));
            assertEquals(10000, customQuoteDuties.get(0).getAmount());
            assertEquals("TEST", customQuoteDuties.get(0).getDutyCode());
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    @Test
    void setQuoteDutySplit() {
        QpQuoteDutyDTO qpQuoteDutyDTO1 = new QpQuoteDutyDTO();
        quoteAggService.setQuoteDutySplit(qpQuoteDutyDTO1, null);
        assertNull(qpQuoteDutyDTO1.getQuoteDutySplit());

        List<IgProductDutySplitDto> dutySplitDtoList = new ArrayList<>();
        dutySplitDtoList.add(new IgProductDutySplitDto().setStartAmount(null).setEndAmount(null).setPayment(100));
        QpQuoteDutyDTO qpQuoteDutyDTO2 = new QpQuoteDutyDTO();
        quoteAggService.setQuoteDutySplit(qpQuoteDutyDTO2, dutySplitDtoList);

        assertTrue(() -> !CollectionUtils.isEmpty(qpQuoteDutyDTO2.getQuoteDutySplit()));
        assertEquals(100, qpQuoteDutyDTO2.getQuoteDutySplit().get(0).getStartAmount());
        assertEquals(100, qpQuoteDutyDTO2.getQuoteDutySplit().get(0).getEndAmount());
        assertEquals(100, qpQuoteDutyDTO2.getQuoteDutySplit().get(0).getPayment());
    }


    @Test
    void getSaleName() {
        Map<String, String> map = new HashMap<>();
        map.put("name", "test");
        Mockito.doReturn(ResponseVO.builder().data(map).build()).when(platformDataClient).getPlatformDataInfo(eq(TableNameEnums.USER.getTableName()), eq(1L));
        Mockito.doReturn(ResponseVO.builder().data(Collections.emptyMap()).build()).when(platformDataClient).getPlatformDataInfo(eq(TableNameEnums.USER.getTableName()), eq(2L));

        assertEquals(quoteAggService.getSaleName(1L), "test");
        assertNull(quoteAggService.getSaleName(2L));
    }

}