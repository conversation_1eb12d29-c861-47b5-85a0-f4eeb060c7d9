package com.insgeek.business.quote.backend.aggservice.impl;

import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.backend.dto.quote.BrokerDto;
import com.insgeek.business.quote.common.exception.QuoteException;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.PlatformDataClient;
import com.insgeek.protocol.data.client.entity.QpBrokerInfo;
import com.insgeek.protocol.platform.user.client.UsersClient;
import com.insgeek.protocol.platform.user.dto.UserDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class BrokerServiceImplTest {

    @InjectMocks
    private BrokerServiceImpl brokerService;

    @Mock
    private UsersClient usersClient;

    @Mock
    private PlatformDataClient platformDataClient;

    @Mock
    private DataMapper<QpBrokerInfo> qpBrokerInfoDataMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // ------------------ modify 方法测试 ------------------

    /*@Test
    void testModify_UserUpdateFailed_ThrowsQuoteException() {
        Long userId = 1L;
        BrokerDto dto = new BrokerDto();
        dto.setEmail("<EMAIL>");
        dto.setLicenseType(Arrays.asList("0","1"));

        ResponseVO errorResponse = new ResponseVO();
        errorResponse.setCode(500);
        errorResponse.setMessage("Update failed");

        when(usersClient.updateUser(eq(userId), any(UserDto.class))).thenReturn(errorResponse);

        QuoteException exception = assertThrows(QuoteException.class, () -> brokerService.modify(userId, dto));
        assertNotNull(exception.getMessage());
    }

    @Test
    void testModify_Successful_UpdateUserInfoByEmailReturnsZero() {
        Long userId = 1L;
        BrokerDto dto = new BrokerDto();
        dto.setEmail("<EMAIL>");
        dto.setLicenseType(Arrays.asList("0","1"));

        ResponseVO successResponse = new ResponseVO();
        successResponse.setCode(0);
        successResponse.setMessage("OK");

        when(usersClient.updateUser(eq(userId), any(UserDto.class))).thenReturn(successResponse);
        when(platformDataClient.findByConditionWithoutPermission(anyString(), anyMap(), anyInt(), anyInt()))
                .thenReturn(ResponseVO.builder().data(new ArrayList<>()).build());

        DataCondition<QpBrokerInfo> condition = new DataCondition<>();
        condition.eq("user_id",userId);
        when(qpBrokerInfoDataMapper.entity(QpBrokerInfo.class)).thenReturn(qpBrokerInfoDataMapper);
        when(qpBrokerInfoDataMapper.selectAll(condition,true)).thenReturn(new ArrayList<>());

        assertThrows(NullPointerException.class, () -> brokerService.modify(userId, dto));
    }

    // ------------------ updateUserInfoByEmail 方法测试 ------------------

    @Test
    void testUpdateUserInfoByEmail_NoUserFound_ReturnsZero() {
        BrokerDto dto = new BrokerDto();
        dto.setEmail("<EMAIL>");
        dto.setLicenseType(Arrays.asList("0","1"));

        when(platformDataClient.findByConditionWithoutPermission(anyString(), anyMap(), anyInt(), anyInt()))
                .thenReturn(new ResponseVO());

        Long result = (Long) ReflectionTestUtils.invokeMethod(brokerService, "updateUserInfoByEmail", "<EMAIL>", dto);
        assertEquals(0L, result);
    }

    @Test
    void testUpdateUserInfoByEmail_ExceptionOccurs_LogAndReturnZero() throws Exception {
        BrokerDto dto = new BrokerDto();
        dto.setEmail("<EMAIL>");
        dto.setLicenseType(Arrays.asList("0","1"));

        doThrow(new RuntimeException("DB Error")).when(platformDataClient).findByConditionWithoutPermission(anyString(), anyMap(), anyInt(), anyInt());

        Long result = (Long) ReflectionTestUtils.invokeMethod(brokerService, "updateUserInfoByEmail", "<EMAIL>", dto);
        assertEquals(0L, result);
    }


    // ------------------ add 方法测试 ------------------

    @Test
    void testAdd_CreateUserFailed_ThrowsQuoteException() {
        BrokerDto dto = new BrokerDto();
        dto.setEmail("<EMAIL>");
        dto.setLicenseType(Arrays.asList("0","1"));

        ResponseVO errorResponse = new ResponseVO();
        errorResponse.setCode(500);
        errorResponse.setMessage("Create failed");

        when(usersClient.creatUser(any(UserDto.class))).thenReturn(errorResponse);

        QuoteException exception = assertThrows(QuoteException.class, () -> brokerService.add(dto));
        assertNotNull(exception.getMessage());
    }

    @Test
    void testAdd_Successful_CallsUpdateUserInfoByEmail() {
        BrokerDto dto = new BrokerDto();
        dto.setEmail("<EMAIL>");
        dto.setLicenseType(Arrays.asList("0","1"));

        ResponseVO successResponse = new ResponseVO();
        successResponse.setCode(0);
        successResponse.setMessage("OK");

        when(usersClient.creatUser(any(UserDto.class))).thenReturn(successResponse);
        when(platformDataClient.findByConditionWithoutPermission(anyString(), anyMap(), anyInt(), anyInt()))
                .thenReturn(new ResponseVO());


        assertEquals(0L, brokerService.add(dto));
    }*/
}
