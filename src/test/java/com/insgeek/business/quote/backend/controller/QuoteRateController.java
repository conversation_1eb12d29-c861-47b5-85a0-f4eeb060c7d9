package com.insgeek.business.quote.backend.controller;


import com.google.common.collect.Lists;
import com.insgeek.boot.web.context.IdentityContext;
import com.insgeek.business.quote.common.dao.condition.DefaultBaseCondition;
import com.insgeek.business.quote.common.dao.condition.interfaces.BaseCondition;
import com.insgeek.business.quote.common.service.impl.RateServiceImpl;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.insurance.meta.utils.context.IdentityUtil;
import com.insgeek.protocol.data.client.entity.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import java.time.ZonedDateTime;
import java.util.List;


/**
 * @ClassName: QuoteRateController
 * @Description:
 * @Author: YYY
 * @Date: 2022/6/27 20:04
 **/
@Slf4j
//@RunWith(MockitoJUnitRunner.class)
public class QuoteRateController {

    MockMvc mockMvc;


    @InjectMocks
    private RateServiceImpl rateService;

    @Mock
    private DataMapper<QpFactorRuleLog> factorRuleLogMapper;
    @Mock
    private DataMapper<QpFactor> factorMapper;

    @Mock
    private DataMapper<QpFactorRule> factorRuleMapper;

    @BeforeAll
    static void beforeAll() {
        IdentityUtil.setRobotAuth();
        IdentityContext.getIdentity();
        //设置登录信息
        log.info("-------------------BeforeAll");
    }


    @BeforeEach
    void setup() {
        MockitoAnnotations.initMocks(this);
        mockMvc = MockMvcBuilders.standaloneSetup(rateService).build();
    }


    @Test
    void excuteDuty() {
        try {
      /*  Mockito.when(quoteDutyDataMapper.entity(QpQuoteDuty.class).select(getDuty(),true))
                .thenReturn(Lists.newArrayList(getDuty()));*/
       /* DataCondition<QpProductFactorRelation> condition = new DataCondition();
        condition.in("product_type", new Object[]{Lists.newArrayList("1321300")});*/
            BaseCondition<QpProductFactorRelation> relationBaseCondition = new DefaultBaseCondition<>();
            relationBaseCondition.in("product_type", Lists.newArrayList("1321300"));
       /* Mockito.when(getFactorRelations())
                .thenReturn(factorRelationMapper.selectListByCondition(relationBaseCondition,true));
*/

            Mockito.when(factorMapper.entity(QpFactor.class).select(Lists.newArrayList(1L), true))
                    .thenReturn(getFactors());

            DataCondition<QpFactorRule> condition2 = new DataCondition();
            condition2.in("factor_id", new Object[]{Lists.newArrayList(1L, 2L)});
            Mockito.when(factorRuleMapper.entity(QpFactorRule.class).select(condition2, true))
                    .thenReturn(getFactorRules());

            Mockito.when(factorRuleLogMapper.entity(QpFactorRuleLog.class).insertAll(null, true))
                    .thenReturn(Lists.newArrayList(new QpFactorRuleLog().setFactorId(1L), new QpFactorRuleLog().setFactorId(2L)));
            rateService.executeRate(Lists.newArrayList(getDuty()));
        } catch (Exception e) {
        }
    }


    private QpQuoteDuty getDuty() {
        QpQuoteDuty qpQuoteDuty = new QpQuoteDuty();
        qpQuoteDuty.setQuoteConfigId(11111L)
                .setAmount(null)
                .setPayment(5555)
                .setDeduction(null)
                .setOnceDeduction(null)
                .setOnceQuota(null)
                .setUpdatedBy(1997L)
                .setCreatedAt(ZonedDateTime.now());
        qpQuoteDuty.setBusinessType("1321300");
        return qpQuoteDuty;
    }

    //@Test
    void excuteDuty2() {
        Mockito.doReturn(getFactorRelations()).when(rateService)
                .queryRelationByProductTypes(Lists.newArrayList("1321300"));
        System.out.println();
    }

    /**
     * 获取规则关系
     *
     * @return
     */
    private List<QpProductFactorRelation> getFactorRelations() {
        List<QpProductFactorRelation> list = Lists.newArrayList();
        QpProductFactorRelation qpProductFactorRelation = new QpProductFactorRelation();
        qpProductFactorRelation.setProductType("1321300")
                .setFactorId(1L)
                .setRelationState(1)
                .setRelationProductType("");
        QpProductFactorRelation qpProductFactorRelation2 = new QpProductFactorRelation();
        qpProductFactorRelation2.setProductType("1321300")
                .setFactorId(2L)
                .setRelationState(1)
                .setRelationProductType("");
        list.add(qpProductFactorRelation);
        list.add(qpProductFactorRelation2);
        return list;
    }

    /**
     * 获取规则
     *
     * @return
     */
    private List<QpFactor> getFactors() {
        QpFactor qpFactor = new QpFactor();
        qpFactor.setId(1L)
                .setFactorName("意外医疗基础保费")
                .setFactorCode("1321300-01")
                .setResultMsg("{\"1\":\"成功\",\"2\":\"警告\",\"3\":\"失败\"}")
                .setResultVal("保费")
                .setHitSymbol(1)
                .setFactorOrder(1);
        QpFactor qpFactor2 = new QpFactor();
        qpFactor2.setId(2L)
                .setFactorName("意外医疗基础费率")
                .setFactorCode("1321300-02")
                .setResultMsg("{\"1\":\"成功\",\"2\":\"警告\",\"3\":\"失败\"}")
                .setResultVal("A基础费率")
                .setHitSymbol(1)
                .setFactorOrder(2);

        return Lists.newArrayList(qpFactor, qpFactor2);
    }

    /**
     * 获取规则
     *
     * @return
     */
    private List<QpFactorRule> getFactorRules() {
        QpFactorRule qpFactorRule = new QpFactorRule();
        qpFactorRule.setFactorId(1L)
                .setResultValType(1)
                .setRuleOrder(1)
                .setHitResultType(1)
                .setRuleContent("[{\"order\": 1,\"resultType\":1,\"result\":\"\",\"resultHandleType\":3,\"variable\":\"\",\"rateDtos\":[{\"filedType\":1 ,\"filed\":\"\",\"leftBoundary\":\"\",\"rightBoundary\":\"\",\"handleField\":\"qpQuoteDuty.amount\",\"startVal\":\"0\",\"endVal\":\"0\",\"order\":1}]}]")
                .setResultVal("0")
                .setRuleType(1)
                .setHitRuleHandle(2)
                .setNotHitRuleHandle(1);

        QpFactorRule qpFactor2 = new QpFactorRule();
        qpFactor2.setFactorId(2L)
                .setResultValType(2)
                .setRuleOrder(1)
                .setHitResultType(1)
                .setRuleContent("[{\"order\": 1,\"resultType\":1,\"result\":\"\",\"resultHandleType\":1,\"variable\":\"\",\"rateDtos\":[{\"filedType\":1 ,\"filed\":\"qpQuoteDuty.amount\",\"leftBoundary\":\"[\",\"rightBoundary\":\"]\",\"handleField\":\"\",\"startVal\":\"0\",\"endVal\":\"100000\",\"order\":1}]}]")
                .setResultVal("0.0012")
                .setRuleType(1)
                .setHitRuleHandle(2)
                .setNotHitRuleHandle(1);

        QpFactorRule qpFactor3 = new QpFactorRule();
        qpFactor3.setFactorId(2L)
                .setResultValType(2)
                .setRuleOrder(2)
                .setHitResultType(2)
                .setRuleContent("[{\"order\": 1,\"resultType\":1,\"result\":\"\",\"resultHandleType\":1,\"variable\":\"\",\"rateDtos\":[{\"filedType\":1 ,\"filed\":\"qpQuoteDuty.amount\",\"leftBoundary\":\"(\",\"rightBoundary\":\"]\",\"handleField\":\"\",\"startVal\":100000,\"endVal\":999999999,\"order\":1}]}]")
                .setResultVal("0")
                .setRemarks("保额＞10万，费率按照0计算，提示保额超出最大值")
                .setRuleType(1)
                .setHitRuleHandle(2)
                .setNotHitRuleHandle(1);

        return Lists.newArrayList(qpFactorRule, qpFactor2, qpFactor3);
    }

}
