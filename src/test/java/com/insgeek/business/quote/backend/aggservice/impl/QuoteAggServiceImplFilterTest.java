package com.insgeek.business.quote.backend.aggservice.impl;

import com.insgeek.protocol.insurance.entity.IgPlanConfigSift;
import com.insgeek.protocol.platform.common.dto.insurance.config.PlanConfigDTO;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.insurance.meta.utils.context.IdentityUtil;
import com.insgeek.boot.web.auth.dto.IdentityDto;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class QuoteAggServiceImplFilterTest {

    @InjectMocks
    private QuoteAggServiceImpl quoteAggService;

    @Mock
    private DataMapper<IgPlanConfigSift> planConfigSiftDataMapper;

    @Mock
    private IdentityUtil identityUtil;

    @BeforeEach
    void setUp() {
        // 初始化mock对象
    }

   /* @Test
    void testFilterConfigsSift_withNullData() throws Exception {
        // Arrange
        List<PlanConfigDTO> data = null;

        // Act & Assert
        assertDoesNotThrow(() -> invokeFilterConfigsSift(data));
    }*/

    @Test
    void testFilterConfigsSift_withEmptyData() throws Exception {
        // Arrange
        List<PlanConfigDTO> data = new ArrayList<>();

        // Act
        //invokeFilterConfigsSift(data);

        // Assert
        assertTrue(data.isEmpty());
    }

    @Test
    void testContainsInCsv_nullInput() {
        // Act & Assert
        assertFalse(QuoteAggServiceImpl.containsInCsv(null, "test"));
        assertFalse(QuoteAggServiceImpl.containsInCsv("test", null));
    }

    @Test
    void testContainsInCsv_valueExists() {
        // Act
        boolean result = QuoteAggServiceImpl.containsInCsv("123, 456,789", "456");

        // Assert
        assertTrue(result);
    }

    @Test
    void testContainsInCsv_valueNotExists() {
        // Act
        boolean result = QuoteAggServiceImpl.containsInCsv("123, 456,789", "abc");

        // Assert
        assertFalse(result);
    }

    private void mockCommonBehavior(List<PlanConfigDTO> data,
                                    List<IgPlanConfigSift> siftList,
                                    Long tenantId,
                                    Long departId,
                                    Long userId) {
        // 创建DataCondition并验证其调用
        ArgumentCaptor<DataCondition<IgPlanConfigSift>> conditionCaptor = ArgumentCaptor.forClass(DataCondition.class);

        when(planConfigSiftDataMapper.entity(eq(IgPlanConfigSift.class))).thenReturn(planConfigSiftDataMapper);

        when(planConfigSiftDataMapper.select(conditionCaptor.capture(), eq(true))).thenReturn(siftList);

        // 模拟用户信息
        IdentityDto identityDto = new IdentityDto();
        identityDto.setUserId(userId);
        identityDto.setTenantId(tenantId);
        identityDto.setDepartId(departId);

        when(identityUtil.getUserInfo()).thenReturn(identityDto);
    }

    private List<PlanConfigDTO> createTestPlanConfigs(int count) {
        List<PlanConfigDTO> configs = new ArrayList<>();
        for (int i = 0; i < count; i++) {
            PlanConfigDTO config = new PlanConfigDTO();
            config.setId((long) (100 + i));
            config.setName("config" + (i + 1));
            configs.add(config);
        }
        return configs;
    }

    private List<IgPlanConfigSift> createTestSiftRecords(List<PlanConfigDTO> configs,
                                                       boolean withDeptMatch,
                                                       boolean withUserMatch) {
        List<IgPlanConfigSift> records = new ArrayList<>();

        for (int i = 0; i < configs.size(); i++) {
            IgPlanConfigSift record = new IgPlanConfigSift();
            record.setPlanConfigId(configs.get(i).getId());

            if (withDeptMatch && i == 0) {
                record.setInternalDeptId("1, 1001, 100"); // 匹配departId=1
            }

            if (withUserMatch && i == 1) {
                record.setInternalUserId("1001, 2000"); // 匹配userId=1001
                record.setExternalUserId("1001, 2000"); // 匹配userId=1001
            }

            if (i == 0 && !withDeptMatch && !withUserMatch) {
                record.setExternalTenantId("1000, 2000"); // 匹配tenantId=1000
            }

            if (i == 1 && !withDeptMatch && !withUserMatch) {
                record.setExternalUserId("1001, 2000"); // 匹配userId=1001
            }

            records.add(record);
        }
        return records;
    }

    /*private void invokeFilterConfigsSift(List<PlanConfigDTO> data) throws Exception {
        // 使用反射调用私有方法
        java.lang.reflect.Method method = QuoteAggServiceImpl.class.getDeclaredMethod("filterConfigsSift", List.class);
        method.setAccessible(true);
        method.invoke(quoteAggService, data);
    }*/
}
