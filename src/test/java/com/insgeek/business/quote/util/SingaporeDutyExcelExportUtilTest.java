package com.insgeek.business.quote.util;

import com.insgeek.business.quote.sgp.dto.*;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("新加坡保险责任导出工具测试")
class SingaporeDutyExcelExportUtilTest {

    private SingaporeMultiplePlansExcelDTO testData;

    @BeforeEach
    void setUp() {
        testData = createTestData();
    }

    @Test
    @DisplayName("测试正常导出Excel功能")
    void testExportToStream() throws IOException {
        // 准备输出流
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

        // 执行导出操作
        SingaporeDutyExcelExportUtil.exportToStream(testData, outputStream);

        // 验证输出不为空
        assertTrue(outputStream.size() > 0, "导出的Excel文件不应为空");

        // 验证输出可以被解析为有效的Excel文件
        ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        Workbook workbook = WorkbookFactory.create(inputStream);
        assertNotNull(workbook, "应能创建有效的Workbook对象");

        // 验证工作表数量
        assertEquals(1, workbook.getNumberOfSheets(), "应创建一个工作表");

        // 验证工作表名称
        assertEquals("plans", workbook.getSheetName(0), "工作表名称应为'Plan A|Level 1'");

        // 关闭资源
        workbook.close();
        inputStream.close();
        outputStream.close();
    }

    @Test
    @DisplayName("测试空数据导出")
    void testExportToStreamWithEmptyData() throws IOException {
        SingaporeMultiplePlansExcelDTO emptyData = new SingaporeMultiplePlansExcelDTO();
        emptyData.setPlanName(new String[]{"Plan A"});
        emptyData.setPremium(new String[]{"99999999"});
        emptyData.setConfigList(new ArrayList<>());

        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        assertDoesNotThrow(() -> SingaporeDutyExcelExportUtil.exportToStream(emptyData, outputStream));
        assertTrue(outputStream.size() > 0, "即使没有数据也应该生成Excel文件");

        ByteArrayInputStream inputStream = new ByteArrayInputStream(outputStream.toByteArray());
        Workbook workbook = WorkbookFactory.create(inputStream);
        assertEquals(1, workbook.getNumberOfSheets(), "没有计划时不应创建工作表");

        workbook.close();
        inputStream.close();
        outputStream.close();
    }

    @Test
    @DisplayName("测试获取保险类型详情名称")
    void testGetInsuranceTypeDetailName() throws Exception {
        // 使用反射访问私有方法
        java.lang.reflect.Method method = SingaporeDutyExcelExportUtil.class.getDeclaredMethod(
                "getInsuranceTypeDetailName", String.class);
        method.setAccessible(true);

        // 测试已知的枚举值
        String ghsName = (String) method.invoke(null, "GHS");
        assertEquals("Group Hospital & Surgical", ghsName, "GHS代码应返回正确的描述");

        // 测试未知代码
        String unknownName = (String) method.invoke(null, "UNKNOWN");
        assertEquals("UNKNOWN", unknownName, "未知代码应返回代码本身");

        // 测试null值
        String nullName = (String) method.invoke(null, (String) null);
        assertNull(nullName, "null值应返回null");
    }

    /**
     * 创建测试数据
     */
    private SingaporeMultiplePlansExcelDTO createTestData() {
        SingaporeMultiplePlansExcelDTO preDTO = new SingaporeMultiplePlansExcelDTO();
        preDTO.setPlanName(new String[]{"Plan A"});
        preDTO.setPremium(new String[]{"99999999"});
        preDTO.setConfigList(new ArrayList<>());
        return preDTO;
    }
}