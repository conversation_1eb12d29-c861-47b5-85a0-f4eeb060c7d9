package com.insgeek.business.quote.util;

import com.insgeek.business.quote.sgp.dto.SingaporeDutyExcelDutyDTO;
import com.insgeek.business.quote.sgp.dto.SingaporeDutyExcelDutyFieldDTO;
import com.insgeek.business.quote.sgp.dto.SingaporeDutyExcelPlanConfigDTO;
import com.insgeek.protocol.insurance.dto.product.request.front.*;
import org.apache.poi.ss.usermodel.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@DisplayName("新加坡保险责任解析工具类测试")
class SingaporeDutyExcelParserUtilTest {

    @Test
    @DisplayName("测试findHeaderIndex方法")
    void testFindHeaderIndex() throws Exception {
        // 使用反射访问私有方法
        java.lang.reflect.Method method = SingaporeDutyExcelParserUtil.class.getDeclaredMethod(
                "findHeaderIndex", Row.class, String.class);
        method.setAccessible(true);

        // 测试null行的情况
        int result = (int) method.invoke(null, null, "Test");
        assertEquals(-1, result, "行为空时应返回-1");

        // 创建模拟Row和Cell
        Row mockRow = Mockito.mock(Row.class);
        Cell mockCell1 = Mockito.mock(Cell.class);
        Cell mockCell2 = Mockito.mock(Cell.class);

        // 设置模拟行为
        when(mockRow.getLastCellNum()).thenReturn((short) 2);
        when(mockRow.getCell(0)).thenReturn(mockCell1);
        when(mockRow.getCell(1)).thenReturn(mockCell2);
        when(mockCell1.getStringCellValue()).thenReturn("PlanName");
        when(mockCell2.getStringCellValue()).thenReturn("Category");

        // 模拟getCellString方法返回值
        when(mockCell1.getCellType()).thenReturn(CellType.STRING);
        when(mockCell2.getCellType()).thenReturn(CellType.STRING);

        // 测试精确匹配
        int result1 = (int) method.invoke(null, mockRow, "PlanName");
        assertEquals(0, result1, "应找到PlanName列索引为0");

        // 测试容错匹配
        int result2 = (int) method.invoke(null, mockRow, "Category");
        assertEquals(1, result2, "应找到Category列索引为1");

        // 测试未找到匹配
        int result3 = (int) method.invoke(null, mockRow, "NotFound");
        assertEquals(-1, result3, "未找到匹配时应返回-1");
    }

    @Test
    @DisplayName("测试getCellString方法")
    void testGetCellString() throws Exception {
        // 使用反射访问私有方法
        java.lang.reflect.Method method = SingaporeDutyExcelParserUtil.class.getDeclaredMethod(
                "getCellString", Cell.class);
        method.setAccessible(true);

        // 测试null单元格
        String nullResult = (String) method.invoke(null, (Cell) null);
        assertEquals("", nullResult, "单元格为空时应返回空字符串");

        // 测试字符串类型单元格
        Cell mockStringCell = Mockito.mock(Cell.class);
        when(mockStringCell.getCellType()).thenReturn(CellType.STRING);
        when(mockStringCell.getStringCellValue()).thenReturn("Test Value");
        String stringResult = (String) method.invoke(null, mockStringCell);
        assertEquals("Test Value", stringResult, "字符串单元格应返回对应值");

        // 测试数值类型单元格（整数）
        Cell mockNumericCell = Mockito.mock(Cell.class);
        when(mockNumericCell.getCellType()).thenReturn(CellType.NUMERIC);
        when(mockNumericCell.getNumericCellValue()).thenReturn(123.0);
        String numericResult = (String) method.invoke(null, mockNumericCell);
        assertEquals("123", numericResult, "整数数值应返回整数字符串");

        // 测试数值类型单元格（小数）
        Cell mockDecimalCell = Mockito.mock(Cell.class);
        when(mockDecimalCell.getCellType()).thenReturn(CellType.NUMERIC);
        when(mockDecimalCell.getNumericCellValue()).thenReturn(123.45);
        String decimalResult = (String) method.invoke(null, mockDecimalCell);
        assertEquals("123.45", decimalResult, "小数数值应返回小数字符串");

        // 测试布尔类型单元格
        Cell mockBooleanCell = Mockito.mock(Cell.class);
        when(mockBooleanCell.getCellType()).thenReturn(CellType.BOOLEAN);
        when(mockBooleanCell.getBooleanCellValue()).thenReturn(true);
        String booleanResult = (String) method.invoke(null, mockBooleanCell);
        assertEquals("true", booleanResult, "布尔单元格应返回布尔值字符串");
    }

    @Test
    @DisplayName("测试norm方法")
    void testNorm() throws Exception {
        // 使用反射访问私有方法
        java.lang.reflect.Method method = SingaporeDutyExcelParserUtil.class.getDeclaredMethod(
                "norm", String.class);
        method.setAccessible(true);

        // 测试null值
        String nullResult = (String) method.invoke(null, (String) null);
        assertEquals("", nullResult, "null值应转换为空字符串");

        // 测试正常字符串
        String normalResult = (String) method.invoke(null, "Test String");
        assertEquals("test string", normalResult, "正常字符串应转为小写并去除多余空格");

        // 测试包含多个空格的字符串
        String multiSpaceResult = (String) method.invoke(null, "  Test   String  ");
        assertEquals("test string", multiSpaceResult, "多个空格应合并为单个空格");

        // 测试全大写字符串
        String upperResult = (String) method.invoke(null, "TEST STRING");
        assertEquals("test string", upperResult, "大写字符串应转为小写");
    }

    @Test
    @DisplayName("测试safeStr方法")
    @SuppressWarnings("unused")
    void testSafeStr() throws Exception {
        // 使用反射访问私有方法
        java.lang.reflect.Method method = SingaporeDutyExcelParserUtil.class.getDeclaredMethod(
                "safeStr", String.class);
        method.setAccessible(true);

        // 测试null值
        String nullResult = (String) method.invoke(null, (String) null);
        assertEquals("", nullResult, "null值应转换为空字符串");

        // 测试正常字符串
        String normalResult = (String) method.invoke(null, "Test String");
        assertEquals("Test String", normalResult, "正常字符串应保持不变");

        // 测试前后有空格的字符串
        String spaceResult = (String) method.invoke(null, "  Test String  ");
        assertEquals("Test String", spaceResult, "应去除前后空格");

        // 测试"As Charged"字符串
        String asChargedResult = (String) method.invoke(null, "as charged");
        assertEquals("99999999", asChargedResult, "as charged应转换为99999999");
    }

    @Test
    @DisplayName("测试convertItemValueToString方法")
    void testConvertItemValueToString() throws Exception {
        // 使用反射访问私有方法
        java.lang.reflect.Method method = SingaporeDutyExcelParserUtil.class.getDeclaredMethod(
                "convertItemValueToString", Object.class);
        method.setAccessible(true);

        // 测试null值
        String nullResult = (String) method.invoke(null, (Object) null);
        assertEquals("", nullResult, "null值应转换为空字符串");

        // 测试字符串值
        String stringResult = (String) method.invoke(null, "test string");
        assertEquals("test string", stringResult, "字符串值应保持不变");

        // 测试数字值
        String numberResult = (String) method.invoke(null, 123);
        assertEquals("123", numberResult, "数字值应转换为字符串");

        // 测试Map值（包含amount）
        Map<String, Object> mapWithAmount = new HashMap<>();
        mapWithAmount.put("amount", 50000);
        String mapResult = (String) method.invoke(null, mapWithAmount);
        assertEquals("50000", mapResult, "包含amount的Map应返回amount值");

        // 测试Map值（不包含amount）
        Map<String, Object> mapWithoutAmount = new HashMap<>();
        mapWithoutAmount.put("key", "value");
        String mapResult2 = (String) method.invoke(null, mapWithoutAmount);
        assertEquals(mapWithoutAmount.toString(), mapResult2, "不包含amount的Map应返回toString结果");

        // 测试其他对象类型
        Object testObject = new Object();
        String objectResult = (String) method.invoke(null, testObject);
        assertEquals(testObject.toString(), objectResult, "其他对象类型应返回toString结果");
    }

    @Test
    @DisplayName("测试setConfigSumAssured方法")
    void testSetConfigSumAssured() throws Exception {
        // 使用反射访问私有方法
        java.lang.reflect.Method method = SingaporeDutyExcelParserUtil.class.getDeclaredMethod(
                "setConfigSumAssured", SingaporeDutyExcelPlanConfigDTO.class);
        method.setAccessible(true);

        // 创建测试数据
        SingaporeDutyExcelPlanConfigDTO configDTO = new SingaporeDutyExcelPlanConfigDTO();
        List<SingaporeDutyExcelDutyDTO> dutyList = new ArrayList<>();

        // 添加普通责任
        SingaporeDutyExcelDutyDTO duty1 = new SingaporeDutyExcelDutyDTO();
        duty1.setProductName("住院医疗责任");
        List<SingaporeDutyExcelDutyFieldDTO> fieldList1 = new ArrayList<>();
        SingaporeDutyExcelDutyFieldDTO field1 = new SingaporeDutyExcelDutyFieldDTO();
        field1.setTitle("保额");
        field1.setValue("100000");
        fieldList1.add(field1);
        duty1.setFieldList(fieldList1);
        dutyList.add(duty1);

        // 添加Sum Assured责任
        SingaporeDutyExcelDutyDTO duty2 = new SingaporeDutyExcelDutyDTO();
        duty2.setProductName("Sum Assured");
        List<SingaporeDutyExcelDutyFieldDTO> fieldList2 = new ArrayList<>();
        SingaporeDutyExcelDutyFieldDTO field2 = new SingaporeDutyExcelDutyFieldDTO();
        field2.setTitle("保额");
        field2.setValue("500000");
        fieldList2.add(field2);
        duty2.setFieldList(fieldList2);
        dutyList.add(duty2);

        configDTO.setDutyList(dutyList);

        // 调用方法
        method.invoke(null, configDTO);

        // 验证结果
        assertEquals("500000", configDTO.getSumAssured(), "应正确设置总保额");
        assertEquals(1, configDTO.getDutyList().size(), "应移除Sum Assured责任");
        assertEquals("住院医疗责任", configDTO.getDutyList().get(0).getProductName(), "应保留其他责任");
    }

    @Test
    @DisplayName("测试setConfigRemarks方法")
    void testSetConfigRemarks() throws Exception {
        // 使用反射访问私有方法
        java.lang.reflect.Method method = SingaporeDutyExcelParserUtil.class.getDeclaredMethod(
                "setConfigRemarks", SingaporeDutyExcelPlanConfigDTO.class);
        method.setAccessible(true);

        // 创建测试数据
        SingaporeDutyExcelPlanConfigDTO configDTO = new SingaporeDutyExcelPlanConfigDTO();
        List<SingaporeDutyExcelDutyDTO> dutyList = new ArrayList<>();

        // 添加普通责任
        SingaporeDutyExcelDutyDTO duty1 = new SingaporeDutyExcelDutyDTO();
        duty1.setProductName("住院医疗责任");
        List<SingaporeDutyExcelDutyFieldDTO> fieldList1 = new ArrayList<>();
        SingaporeDutyExcelDutyFieldDTO field1 = new SingaporeDutyExcelDutyFieldDTO();
        field1.setTitle("保额");
        field1.setValue("100000");
        fieldList1.add(field1);
        duty1.setFieldList(fieldList1);
        dutyList.add(duty1);

        // 添加Remarks责任
        SingaporeDutyExcelDutyDTO duty2 = new SingaporeDutyExcelDutyDTO();
        duty2.setProductName("Remarks");
        List<SingaporeDutyExcelDutyFieldDTO> fieldList2 = new ArrayList<>();
        SingaporeDutyExcelDutyFieldDTO field2 = new SingaporeDutyExcelDutyFieldDTO();
        field2.setTitle("备注");
        field2.setValue("测试备注");
        fieldList2.add(field2);
        duty2.setFieldList(fieldList2);
        dutyList.add(duty2);

        configDTO.setDutyList(dutyList);

        // 调用方法
        method.invoke(null, configDTO);

        // 验证结果
        assertEquals("测试备注", configDTO.getRemarks(), "应正确设置备注");
        assertEquals(1, configDTO.getDutyList().size(), "应移除Remarks责任");
        assertEquals("住院医疗责任", configDTO.getDutyList().get(0).getProductName(), "应保留其他责任");
    }
}