package com.insgeek.business.quote.util;

import com.insgeek.boot.commons.exception.InsgeekException;
import com.insgeek.business.quote.frontend.exception.FrontendErrorMsgAndCode;
import com.insgeek.protocol.platform.fileservice.dto.UploadResultDto;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.MalformedURLException;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.anyInt;
import static org.mockito.Mockito.anyString;

class FileUtilTest {

    private File tempFile;
    private static final String TEST_FILE_CONTENT = "test content";

    @BeforeEach
    void setUp() throws IOException {
        // 创建临时文件用于测试
        tempFile = File.createTempFile("test", ".txt");
        try (FileWriter writer = new FileWriter(tempFile)) {
            writer.write(TEST_FILE_CONTENT);
        }
    }

    @AfterEach
    void tearDown() {
        // 清理临时文件
        if (tempFile != null && tempFile.exists()) {
            tempFile.delete();
        }
    }

    @Test
    @DisplayName("测试fileToMultipartFile方法 - 正常情况")
    void testFileToMultipartFile_NormalCase() {
        // 执行测试
        MultipartFile multipartFile = FileUtil.fileToMultipartFile(tempFile);

        // 验证结果
        assertThat(multipartFile).isNotNull();
        assertThat(multipartFile.getOriginalFilename()).isEqualTo(tempFile.getName());
        assertThat(multipartFile.getSize()).isEqualTo(TEST_FILE_CONTENT.length());
    }

    @Test
    @DisplayName("测试fileToMultipartFile方法 - 文件不存在")
    void testFileToMultipartFile_FileNotExists() {
        // 准备测试数据
        File nonExistentFile = new File("non-existent-file.txt");

        // 验证异常
        assertThatThrownBy(() -> FileUtil.fileToMultipartFile(nonExistentFile))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("Invalid file");
    }

    @Test
    @DisplayName("测试getFileExtension方法 - 正常情况")
    void testGetFileExtension_NormalCase() {
        // 测试数据
        String fileName1 = "test.pdf";
        String fileName2 = "document.file.txt";
        String fileName3 = "image.jpeg";

        // 执行测试
        String extension1 = FileUtil.getFileExtension(fileName1);
        String extension2 = FileUtil.getFileExtension(fileName2);
        String extension3 = FileUtil.getFileExtension(fileName3);

        // 验证结果
        assertThat(extension1).isEqualTo("pdf");
        assertThat(extension2).isEqualTo("txt");
        assertThat(extension3).isEqualTo("jpeg");
    }

    @Test
    @DisplayName("测试getFileExtension方法 - 边界情况")
    void testGetFileExtension_EdgeCases() {
        // 测试边界情况
        assertThat(FileUtil.getFileExtension(null)).isEqualTo("");
        assertThat(FileUtil.getFileExtension("")).isEqualTo("");
        assertThat(FileUtil.getFileExtension("file_without_extension")).isEqualTo("");
        assertThat(FileUtil.getFileExtension("file.")).isEqualTo("");
    }


    @Test
    @DisplayName("测试downloadFile方法 - URL为空")
    void testDownloadFile_UrlIsNull() {
        // 准备测试数据
        UploadResultDto uploadResultDto = new UploadResultDto();
        uploadResultDto.setLargeUrl(null);
        uploadResultDto.setFileName("test.pdf");

        HttpServletResponse response = mock(HttpServletResponse.class);

        // 验证异常
        assertThatThrownBy(() -> FileUtil.downloadFile(uploadResultDto, response))
                .isInstanceOf(InsgeekException.class)
                .hasMessage(FrontendErrorMsgAndCode.DOWNLOAD_ERR.msg());
    }

    @Test
    @DisplayName("测试downloadFile方法 - URL为空字符串")
    void testDownloadFile_UrlIsEmpty() {
        // 准备测试数据
        UploadResultDto uploadResultDto = new UploadResultDto();
        uploadResultDto.setLargeUrl("");
        uploadResultDto.setFileName("test.pdf");

        HttpServletResponse response = mock(HttpServletResponse.class);

        // 验证异常
        assertThatThrownBy(() -> FileUtil.downloadFile(uploadResultDto, response))
                .isInstanceOf(InsgeekException.class)
                .hasMessage(FrontendErrorMsgAndCode.DOWNLOAD_ERR.msg());
    }
}