package com.insgeek.business.quote.external.bi.service.impl;

import com.google.common.collect.Maps;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.common.dto.FloatPriceFactorDto;
import com.insgeek.business.quote.common.dto.result.QpConfigInsuRelationResult;
import com.insgeek.business.quote.common.enums.DentalAdjustmentCoefficientEnum;
import com.insgeek.business.quote.common.enums.HospitalAdjustmentCoefficientEnum;
import com.insgeek.business.quote.common.enums.PayMethodCoefficientEnum;
import com.insgeek.business.quote.common.service.QpCustomerService;
import com.insgeek.business.quote.common.service.impl.QpConfigInsuRelationServiceImpl;
import com.insgeek.business.quote.config.SpecialConfig;
import com.insgeek.protocol.data.client.entity.QpConfigInsuRelation;
import com.insgeek.protocol.data.client.entity.QpCustomer;
import com.insgeek.protocol.data.client.entity.QpQuote;
import com.insgeek.protocol.data.client.entity.QpQuoteInfo;
import com.insgeek.protocol.dataapp.bi.client.BusinessClient;
import com.insgeek.protocol.dataapp.bi.client.RenewalQuoteClient;
import com.insgeek.protocol.dataapp.bi.dto.ContractParamsDto;
import com.insgeek.protocol.dataapp.bi.dto.RenewalQuoteAdviceDTO;
import com.insgeek.protocol.insurance.client.SpecialRulesClient;
import com.insgeek.protocol.insurance.vo.specialrules.special.vo.SpecialDTO;
import com.insgeek.protocol.insurance.vo.specialrules.special.vo.SpecialItem;
import com.insgeek.protocol.insurance.vo.specialrules.special.vo.SpecialParamDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
@DisplayName("BiServiceImpl 测试")
class BiServiceImplTest {

    @InjectMocks
    private BiServiceImpl biService;

    @Mock
    private BusinessClient businessClient;

    @Mock
    private SpecialConfig specialConfig;

    @Mock
    private SpecialRulesClient specialRulesClient;

    @Mock
    private QpConfigInsuRelationServiceImpl qpConfigInsuRelationService;

    @Mock
    private RenewalQuoteClient renewalQuoteClient;

    @Mock
    private QpCustomerService customerService;

    @Nested
    @DisplayName("getOldEnterpriseData 方法测试")
    class GetOldEnterpriseDataTest {

        @Test
        @DisplayName("当客户ID列表为空时，应返回空Map")
        void testGetOldEnterpriseDataWithEmptyCustomerIds() {
            Map<Long, ContractParamsDto> result = biService.getOldEnterpriseData(Collections.emptyList(), true);
            assertThat(result).isNotNull();
            assertThat(result).isEmpty();
        }

        @Test
        @DisplayName("当客户列表为空时，应返回空Map")
        void testGetOldEnterpriseDataWithEmptyCustomerList() {
            when(customerService.selectByIds(any())).thenReturn(Collections.emptyList());

            Map<Long, ContractParamsDto> result = biService.getOldEnterpriseData(Arrays.asList(1L, 2L), true);

            assertThat(result).isNotNull();
            assertThat(result).isEmpty();
            verify(customerService).selectByIds(any());
        }

        @Test
        @DisplayName("当客户列表中没有有效的groupId时，应返回空Map")
        void testGetOldEnterpriseDataWithNoValidGroupIds() {
            List<QpCustomer> customerList = new ArrayList<>();
            QpCustomer customer = new QpCustomer();
            customer.setId(1L);
            customer.setCustomerId(null); // 无效的groupId
            customerList.add(customer);

            when(customerService.selectByIds(any())).thenReturn(customerList);

            Map<Long, ContractParamsDto> result = biService.getOldEnterpriseData(Arrays.asList(1L, 2L), true);

            assertThat(result).isNotNull();
            assertThat(result).isEmpty();
            verify(customerService).selectByIds(any());
        }

        @Test
        @DisplayName("正常情况下的测试")
        void testGetOldEnterpriseDataNormal() {
            // 准备测试数据
            List<QpCustomer> customerList = new ArrayList<>();
            QpCustomer customer1 = new QpCustomer();
            customer1.setId(1L);
            customer1.setCustomerId(100L);
            customerList.add(customer1);

            QpCustomer customer2 = new QpCustomer();
            customer2.setId(2L);
            customer2.setCustomerId(200L);
            customerList.add(customer2);

            List<ContractParamsDto> contractParamsDtoList = new ArrayList<>();
            ContractParamsDto dto1 = new ContractParamsDto();
            dto1.setCompanyId(100L);
            contractParamsDtoList.add(dto1);

            ContractParamsDto dto2 = new ContractParamsDto();
            dto2.setCompanyId(200L);
            contractParamsDtoList.add(dto2);

            ResponseVO<List<ContractParamsDto>> responseVO = new ResponseVO<>();
            responseVO.setCode(0);
            responseVO.setData(contractParamsDtoList);

            // 设置mock行为
            when(customerService.selectByIds(any())).thenReturn(customerList);
            when(businessClient.getContractParams(anyString(), any(Boolean.class))).thenReturn(responseVO);

            // 执行测试
            Map<Long, ContractParamsDto> result = biService.getOldEnterpriseData(Arrays.asList(1L, 2L), true);

            // 验证结果
            assertThat(result).isNotNull();
            assertThat(result).hasSize(2);
            assertThat(result).containsKey(100L);
            assertThat(result).containsKey(200L);

            // 验证调用
            verify(customerService).selectByIds(any());
            verify(businessClient).getContractParams(anyString(), any(Boolean.class));
        }
    }

    @Nested
    @DisplayName("getPriceFactorDto 方法测试")
    class GetPriceFactorDtoTest {

        @Test
        @DisplayName("当关联企业列表为空时，应返回null")
        void testGetPriceFactorDtoWithEmptyRelationList() {
            QpQuoteInfo info = new QpQuoteInfo();
            info.setId(1L);
            QpQuote quote = new QpQuote();

            when(qpConfigInsuRelationService.query(any(QpConfigInsuRelation.class))).thenReturn(Collections.emptyList());

            FloatPriceFactorDto result = biService.getPriceFactorDto(info, quote);

            assertThat(result).isNull();
            verify(qpConfigInsuRelationService).query(any(QpConfigInsuRelation.class));
        }
    }
}