package com.insgeek.business.quote.formock;

import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.entity.MetaEntity;
import com.insgeek.protocol.data.client.entity.QpBiData;
import com.insgeek.protocol.data.client.entity.QpConfigInsuRelation;
import com.insgeek.protocol.data.client.entity.QpConfigRateFloat;
import com.insgeek.protocol.data.client.entity.QpFileDetails;
import com.insgeek.protocol.data.client.entity.QpNonInsuranceServicesInfo;
import com.insgeek.protocol.data.client.entity.QpQuotation;
import com.insgeek.protocol.data.client.entity.QpQuotationBiData;
import com.insgeek.protocol.data.client.entity.QpQuotationBonus;
import com.insgeek.protocol.data.client.entity.QpQuotationConfig;
import com.insgeek.protocol.data.client.entity.QpQuotationConfigInsuRelation;
import com.insgeek.protocol.data.client.entity.QpQuotationConfigRateFloat;
import com.insgeek.protocol.data.client.entity.QpQuotationDuty;
import com.insgeek.protocol.data.client.entity.QpQuotationDutySplit;
import com.insgeek.protocol.data.client.entity.QpQuotationHistoryCustomer;
import com.insgeek.protocol.data.client.entity.QpQuotationInfo;
import com.insgeek.protocol.data.client.entity.QpQuotationInfoCustomer;
import com.insgeek.protocol.data.client.entity.QpQuotationInfoGroup;
import com.insgeek.protocol.data.client.entity.QpQuotationNonInsuranceServicesInfo;
import com.insgeek.protocol.data.client.entity.QpQuote;
import com.insgeek.protocol.data.client.entity.QpQuoteBonus;
import com.insgeek.protocol.data.client.entity.QpQuoteConfig;
import com.insgeek.protocol.data.client.entity.QpQuoteDuty;
import com.insgeek.protocol.data.client.entity.QpQuoteDutySplit;
import com.insgeek.protocol.data.client.entity.QpQuoteHistoryCustomer;
import com.insgeek.protocol.data.client.entity.QpQuoteInfo;
import com.insgeek.protocol.data.client.entity.QpQuoteInfoCustomer;
import com.insgeek.protocol.data.client.entity.QpQuoteInfoGroup;
import lombok.Data;
import org.mockito.ArgumentMatcher;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023年08月08日 17:56:00
 */
public class AbstractMockService {
    protected static final List<DataMapper<? extends MetaEntity>> list = new ArrayList<>();
    @Mock
    protected DataMapper<QpQuoteInfo> qpQuoteInfoDataMapper;
    @Mock
    protected DataMapper<QpQuotationInfo> qpQuotationInfoDataMapper;
    @Mock
    protected DataMapper<QpBiData> qpBiDataDataMapper;
    @Mock
    protected DataMapper<QpQuotationBiData> qpQuotationBiDataDataMapper;
    @Mock
    protected DataMapper<QpQuoteConfig> qpQuoteConfigDataMapper;
    @Mock
    protected DataMapper<QpQuotationConfig> qpQuotationConfigDataMapper;
    @Mock
    protected DataMapper<QpConfigInsuRelation> qpConfigInsuRelationDataMapper;
    @Mock
    protected DataMapper<QpConfigRateFloat> qpConfigRateFloatDataMapper;
    @Mock
    protected DataMapper<QpQuotationConfigRateFloat> qpQuotationConfigRateFloatDataMapper;
    @Mock
    protected DataMapper<QpQuotationConfigInsuRelation> qpQuotationConfigInsuRelationDataMapper;
    @Mock
    protected DataMapper<QpQuoteDuty> qpQuoteDutyDataMapper;
    @Mock
    protected DataMapper<QpQuotationDuty> qpQuotationDutyDataMapper;
    @Mock
    protected DataMapper<QpQuoteDutySplit> qpQuoteDutySplitDataMapper;
    @Mock
    protected DataMapper<QpQuotationDutySplit> qpQuotationDutySplitDataMapper;
    @Mock
    protected DataMapper<QpFileDetails> qpFileDetailsDataMapper;
    @Mock
    protected DataMapper<QpQuoteHistoryCustomer> qpQuoteHistoryCustomerDataMapper;
    @Mock
    protected DataMapper<QpQuotationHistoryCustomer> qpQuotationHistoryCustomerDataMapper;
    @Mock
    protected DataMapper<QpQuoteInfoCustomer> qpQuoteInfoCustomerDataMapper;
    @Mock
    protected DataMapper<QpQuotationInfoCustomer> qpQuotationInfoCustomerDataMapper;
    @Mock
    protected DataMapper<QpQuoteInfoGroup> qpQuoteInfoGroupDataMapper;
    @Mock
    protected DataMapper<QpQuotationInfoGroup> qpQuotationInfoGroupDataMapper;
    @Mock
    protected DataMapper<QpNonInsuranceServicesInfo> qpNonInsuranceServicesInfoDataMapper;
    @Mock
    protected DataMapper<QpQuotationNonInsuranceServicesInfo> qpQuotationNonInsuranceServicesInfoDataMapper;
    @Mock
    protected DataMapper<QpQuote> qpQuoteDataMapper;
    @Mock
    protected DataMapper<QpQuotation> qpQuotationDataMapper;
    @Mock
    private DataMapper<QpQuoteBonus> qpQuoteBonusDataMapper;

    @Mock
    private DataMapper<QpQuotationBonus> qpQuotationBonusDataMapper;


    public void initDataMapper() {
        list.clear();
        list.add(qpQuoteInfoDataMapper);
        list.add(qpQuotationInfoDataMapper);
        list.add(qpBiDataDataMapper);
        list.add(qpQuotationBiDataDataMapper);
        list.add(qpQuoteConfigDataMapper);
        list.add(qpQuotationConfigDataMapper);
        list.add(qpConfigInsuRelationDataMapper);
        list.add(qpConfigRateFloatDataMapper);
        list.add(qpQuotationConfigRateFloatDataMapper);
        list.add(qpQuotationConfigInsuRelationDataMapper);
        list.add(qpQuoteDutyDataMapper);
        list.add(qpQuotationDutyDataMapper);
        list.add(qpQuoteDutySplitDataMapper);
        list.add(qpQuotationDutySplitDataMapper);
        // list.add(qpFileDetailsDataMapper);
        list.add(qpQuoteHistoryCustomerDataMapper);
        list.add(qpQuotationHistoryCustomerDataMapper);
        list.add(qpQuoteInfoCustomerDataMapper);
        list.add(qpQuotationInfoCustomerDataMapper);
        list.add(qpQuoteInfoGroupDataMapper);
        list.add(qpQuotationInfoGroupDataMapper);
        list.add(qpNonInsuranceServicesInfoDataMapper);
        list.add(qpQuotationNonInsuranceServicesInfoDataMapper);
        list.add(qpQuoteDataMapper);
        list.add(qpQuotationDataMapper);
        list.add(qpQuoteBonusDataMapper);
        list.add(qpQuotationBonusDataMapper);
    }

    public void stubbing() {
        for (DataMapper<? extends MetaEntity> dataMapper : list) {
            mockEntity(dataMapper);
            mockInsertOne(dataMapper);
        }
        Mockito.lenient().doReturn(qpFileDetailsDataMapper).when(qpFileDetailsDataMapper).entity(any());
        QpFileDetailsArgumentMatcher matcher = new QpFileDetailsArgumentMatcher();
        Mockito.lenient().when(qpFileDetailsDataMapper.insertOne(argThat(matcher))).thenAnswer(invocation -> matcher.getT());
    }

    protected <T> void mockEntity(DataMapper<T> dataMapper) {
        Mockito.lenient().doReturn(dataMapper).when(dataMapper).entity(any());
    }

    protected <T extends MetaEntity> void mockInsertOne(DataMapper<T> dataMapper) {
        C<T> matcher = new C<>();
        Mockito.lenient().when(dataMapper.insertOne(argThat(matcher))).thenAnswer(invocation -> matcher.getT());
    }

    @Data
    public static class C<T extends MetaEntity> implements ArgumentMatcher<T> {
        public static final AtomicLong idGenerator = new AtomicLong(12300000L);

        private T t;

        @Override
        public boolean matches(T argument) {
            argument.setId(idGenerator.getAndIncrement());
            this.t = argument;
            return true;
        }
    }

    @Data
    public static class QpFileDetailsArgumentMatcher implements ArgumentMatcher<QpFileDetails> {
        public static final AtomicLong idGenerator = new AtomicLong(12300000L);

        private QpFileDetails t;

        @Override
        public boolean matches(QpFileDetails argument) {
            argument.setId(idGenerator.getAndIncrement());
            this.t = argument;
            return true;
        }
    }

}