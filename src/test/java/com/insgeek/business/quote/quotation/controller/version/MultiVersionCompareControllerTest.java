package com.insgeek.business.quote.quotation.controller.version;

import com.google.common.collect.Lists;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.formock.AbstractMockService;
import com.insgeek.business.quote.quotation.dto.version.QuotationMultiVersionCompareListDto;
import com.insgeek.business.quote.quotation.dto.version.QuoteMultiVersionCompareListDto;
import com.insgeek.business.quote.quotation.service.impl.version.MultiVersionCompareService;
import com.insgeek.business.quote.quotation.service.impl.version.MultiVersionCompareServiceImpl;
import com.insgeek.protocol.data.client.entity.QpQuotationInfo;
import com.insgeek.protocol.data.client.entity.QpQuotationInfoGroup;
import com.insgeek.protocol.data.client.entity.QpQuoteInfo;
import com.insgeek.protocol.data.client.entity.QpQuoteInfoGroup;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
@Slf4j
class MultiVersionCompareControllerTest extends AbstractMockService {

    @Spy
    @InjectMocks
    private MultiVersionCompareService multiVersionCompareService = Mockito.spy(new MultiVersionCompareServiceImpl());

    @Spy
    @InjectMocks
    private MultiVersionCompareController underTest;

    @BeforeEach
    void setup() {
        MockitoAnnotations.initMocks(this);
        initDataMapper();
    }

    @Nested
    class WhenListQuoteInfoGroupId {
        @BeforeEach
        void setup() {
            MockitoAnnotations.initMocks(this);
            initDataMapper();
        }

        @Test
        void name() {
            Long quoteInfoGroupId = 123L;

            //
            stubbing();
            QpQuoteInfoGroup qpQuoteInfoGroup = new QpQuoteInfoGroup();
            qpQuoteInfoGroup.setId(quoteInfoGroupId);

            Mockito.doReturn(qpQuoteInfoGroup).when(qpQuoteInfoGroupDataMapper).selectOne(any(), any());

            QpQuoteInfo qpQuoteInfo = new QpQuoteInfo();
            qpQuoteInfo.setId(1L);
            qpQuoteInfo.setPublishVersionFlag(1);
            qpQuoteInfo.setVersionNo(1);
            qpQuoteInfo.setDs(null);
            qpQuoteInfo.setCurrentTurns(1);
            QpQuoteInfo qpQuoteInfo2 = new QpQuoteInfo();
            qpQuoteInfo2.setId(2L);
            qpQuoteInfo2.setPublishVersionFlag(1);
            qpQuoteInfo2.setVersionNo(2);
            qpQuoteInfo2.setDs(null);
            qpQuoteInfo2.setCurrentTurns(1);
            Mockito.doReturn(Lists.newArrayList(qpQuoteInfo2, qpQuoteInfo)).when(qpQuoteInfoDataMapper).select(any(QpQuoteInfo.class), any());


            log.warn("入参为:");
            ResponseVO<QuoteMultiVersionCompareListDto> responseVO = underTest.listByQuoteInfoGroupId(quoteInfoGroupId);
            log.warn("响应为:" + JacksonUtils.writeAsString(responseVO));
            Mockito.verify(qpQuoteInfoGroupDataMapper).selectOne(any(), any());
        }
    }

    @Nested
    class WhenListQuotationInfoGroupId {
        @BeforeEach
        void setup() {
            MockitoAnnotations.initMocks(this);
            initDataMapper();
        }

        @Test
        void name() {
            Long quotationInfoGroupId = 123L;

            //
            stubbing();
            QpQuotationInfoGroup qpQuotationInfoGroup = new QpQuotationInfoGroup();
            qpQuotationInfoGroup.setId(quotationInfoGroupId);

            Mockito.doReturn(qpQuotationInfoGroup).when(qpQuotationInfoGroupDataMapper).selectOne(any(), any());

            QpQuotationInfo qpQuotationInfo = new QpQuotationInfo();
            qpQuotationInfo.setId(1L);
            qpQuotationInfo.setPublishVersionFlag(1);
            qpQuotationInfo.setVersionNo(1);
            qpQuotationInfo.setDs(null);
            qpQuotationInfo.setCurrentTurns(1);
            QpQuotationInfo qpQuotationInfo2 = new QpQuotationInfo();
            qpQuotationInfo2.setId(2L);
            qpQuotationInfo2.setPublishVersionFlag(1);
            qpQuotationInfo2.setVersionNo(2);
            qpQuotationInfo2.setDs(null);
            qpQuotationInfo2.setCurrentTurns(1);
            Mockito.doReturn(Lists.newArrayList(qpQuotationInfo2, qpQuotationInfo)).when(qpQuotationInfoDataMapper).select(any(QpQuotationInfo.class), any());


            log.warn("入参为:");
            ResponseVO<QuotationMultiVersionCompareListDto> responseVO = underTest.listByQuotationInfoGroupId(quotationInfoGroupId);
            log.warn("响应为:" + JacksonUtils.writeAsString(responseVO));
            Mockito.verify(qpQuotationInfoGroupDataMapper).selectOne(any(), any());
        }
    }
}