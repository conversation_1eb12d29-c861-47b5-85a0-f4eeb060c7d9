package com.insgeek.business.quote.convert.aggservice.impl.manage;

import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.D;
import com.insgeek.business.quote.convert.aggservice.ConvertContext;
import com.insgeek.business.quote.convert.aggservice.enums.ConvertOperationType;
import com.insgeek.business.quote.convert.aggservice.impl.BiDataConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.BonusConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.ConfigConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.ConfigInsuRelationConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.ConfigRateFloatConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.DutyConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.DutySplitConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.FileConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.HistoryCustomerConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.InfoConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.InfoCustomerConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.InfoGroupConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.NonInsuranceServicesInfoConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.QuoteConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.init.QuotationInitConvertContextServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.init.QuoteInitConvertContextServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.outer.CoreDutyConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.outer.SpecialRulesConvertServiceImpl;
import com.insgeek.business.quote.convert.aggservice.impl.outer.VersionHandlerConvertServiceImpl;
import com.insgeek.business.quote.formock.AbstractMockService;
import com.insgeek.protocol.platform.data.client.EntityRelationModelClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;

import static org.mockito.ArgumentMatchers.any;

@ExtendWith(MockitoExtension.class)
class ConvertServiceImplTest extends AbstractMockService {


    @Mock
    private QuoteInitConvertContextServiceImpl quoteInitConvertContextService;

    @Mock
    private QuotationInitConvertContextServiceImpl quotationInitConvertContextService;

    @Mock
    private CoreDutyConvertServiceImpl coreDutyConvertService;

    @Mock
    private SpecialRulesConvertServiceImpl specialRulesConvertService;

    @Mock
    private EntityRelationModelClient entityRelationModelClient;

    @Spy
    @InjectMocks
    private InfoConvertServiceImpl infoConvertService;
    @Spy
    @InjectMocks
    private QuoteConvertServiceImpl quoteConvertService;
    @Spy
    @InjectMocks
    private BonusConvertServiceImpl bonusConvertService;

    @Spy
    @InjectMocks
    private ConfigConvertServiceImpl configConvertService;
    @Spy
    @InjectMocks
    private ConfigInsuRelationConvertServiceImpl configInsuRelationConvertService;
    @Spy
    @InjectMocks
    private ConfigRateFloatConvertServiceImpl configRateFloatConvertService;
    @Spy
    @InjectMocks
    private DutyConvertServiceImpl dutyConvertService;
    @Spy
    @InjectMocks
    private DutySplitConvertServiceImpl dutySplitConvertService;
    @Spy
    @InjectMocks
    private BiDataConvertServiceImpl biDataConvertService;
    @Spy
    @InjectMocks
    private NonInsuranceServicesInfoConvertServiceImpl nonInsuranceServicesInfoConvertService;
    @Spy
    @InjectMocks
    private HistoryCustomerConvertServiceImpl historyCustomerConvertService;
    @Spy
    @InjectMocks
    private InfoCustomerConvertServiceImpl infoCustomerConvertService;
    @Spy
    @InjectMocks
    private InfoGroupConvertServiceImpl infoGroupConvertService;
    @Spy
    @InjectMocks
    private FileConvertServiceImpl fileConvertService;
    @Spy
    @InjectMocks
    private VersionHandlerConvertServiceImpl versionHandlerConvertService;
    @Spy
    @InjectMocks
    private ConvertServiceImpl underTest;

    @BeforeEach
    void setup() {
        MockitoAnnotations.initMocks(this);
        initDataMapper();
    }

    @Nested
    class WhenXjToBj {

        @BeforeEach
        void setup() {
            MockitoAnnotations.initMocks(this);
            initDataMapper();
        }

       /* @Test
        void name() {
            ConvertContext convertContext = D.ConvertContext();
            // stubbing
            stubbing();
            Mockito.lenient().doReturn(D.ConvertContext()).when(quotationInitConvertContextService).initQuotationContext(any());
            Mockito.lenient().doReturn(D.ConvertContext()).when(quoteInitConvertContextService).initQuoteContext(any());

            Mockito.lenient().doReturn(ResponseVO.<Long>builder().data(123434L).build()).when(entityRelationModelClient).upgradeData(any());

            // test
            underTest.xjToBj(convertContext);

            Mockito.verify(entityRelationModelClient).upgradeData(any());
        }

        @Test
        void name2() {
            ConvertContext convertContext = D.ConvertContext();
            convertContext.setConvertOperationType(ConvertOperationType.BJ_TO_XJ);
            // stubbing
            stubbing();
            Mockito.lenient().doReturn(D.ConvertContext()).when(quotationInitConvertContextService).initQuotationContext(any());
            Mockito.lenient().doReturn(D.ConvertContext()).when(quoteInitConvertContextService).initQuoteContext(any());

            Mockito.lenient().doReturn(ResponseVO.<Long>builder().data(123434L).build()).when(entityRelationModelClient).upgradeData(any());

            // test
            underTest.bjToXj(convertContext);

            Mockito.verify(entityRelationModelClient).upgradeData(any());

        }*/
    }


}