package com.insgeek.business.quote;

import com.fasterxml.jackson.core.type.TypeReference;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.business.quote.convert.aggservice.ConvertContext;

import static com.insgeek.business.quote.mockutils.MockFileUtils.readTestClassPathAsString;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @since 2023年08月08日 16:03:00
 */
public class D {

    public static ConvertContext ConvertContext() {
        String s = readTestClassPathAsString("mock/data/convertContext.json");
        return JacksonUtils.readValue(s, new TypeReference<ConvertContext>() {
        });
    }


}