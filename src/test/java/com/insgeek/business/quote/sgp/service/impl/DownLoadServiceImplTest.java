package com.insgeek.business.quote.sgp.service.impl;

import com.insgeek.boot.commons.exception.InsgeekException;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.frontend.exception.FrontendErrorMsgAndCode;
import com.insgeek.business.quote.sgp.dto.*;
import com.insgeek.business.quote.sgp.enums.IndustryNatureEnum;
import com.insgeek.business.quote.sgp.enums.PolicyTemplateTypeEnum;
import com.insgeek.components.orm.model.querydsl.BQLQuery;
import com.insgeek.components.orm.model.querydsl.BQLQueryFactory;
import com.insgeek.protocol.data.client.entity.*;
import com.insgeek.protocol.platform.fileservice.client.HtmlHandlerClient;
import com.insgeek.protocol.platform.fileservice.dto.UploadResultDto;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.Expressions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * @Description: DownLoadServiceImpl单元测试
 * @Date: 2025-09-16
 */
@ExtendWith(MockitoExtension.class)
class DownLoadServiceImplTest {

    @InjectMocks
    private DownLoadServiceImpl downLoadService;

    @Mock
    private BQLQueryFactory bqlQueryFactory;

    @Mock
    private HtmlHandlerClient htmlHandlerClient;

    private static final Long TEST_QUOTE_INFO_ID = 1000L;
    private static final Long TEST_QUOTATION_INFO_ID = 2000L;
    private static final String TEST_POLICY_NAME = "AIA";

    @BeforeEach
    void setUp() {
        // 初始化通用mock行为
    }

    @Test
    @DisplayName("测试getQuotationInfoId方法-正常情况")
    void testGetQuotationInfoId_NormalCase() {
        // 准备测试数据
        QQpQuoteInfo qQpQuoteInfoEntity = QQpQuoteInfo.qp_quote_info;
        QQpQuoteInfoGroup qQpQuoteInfoGroupEntity = QQpQuoteInfoGroup.qp_quote_info_group;
        QQpQuotationInfoGroup qQpQuotationInfoGroupEntity = QQpQuotationInfoGroup.qp_quotation_info_group;

        QpQuoteInfo qpQuoteInfo = new QpQuoteInfo();
        qpQuoteInfo.setId(TEST_QUOTE_INFO_ID);
        qpQuoteInfo.setGroupId(100L);

        QpQuoteInfoGroup qpQuoteInfoGroup = new QpQuoteInfoGroup();
        qpQuoteInfoGroup.setId(100L);
        qpQuoteInfoGroup.setQuotationInfoGroupId(200L);

        QpQuotationInfoGroup qpQuotationInfoGroup = new QpQuotationInfoGroup();
        qpQuotationInfoGroup.setId(200L);
        qpQuotationInfoGroup.setLatestQuotationInfoId(TEST_QUOTATION_INFO_ID);

        // 创建BQLQuery mock对象
        BQLQuery<QpQuoteInfo> quoteInfoQuery = mock(BQLQuery.class);
        BQLQuery<QpQuoteInfoGroup> quoteInfoGroupQuery = mock(BQLQuery.class);
        BQLQuery<QpQuotationInfoGroup> quotationInfoGroupQuery = mock(BQLQuery.class);


        // 配置mock行为
        // 正确配置 mock 行为
        // 1. 解决 select() 的多次存根问题
        when(bqlQueryFactory.select(any(Expression.class)))
                .thenReturn(quoteInfoQuery)        // 第一次调用返回 quoteInfoQuery
                .thenReturn(quoteInfoGroupQuery).thenReturn(quotationInfoGroupQuery);  // 第二次调用返回 quoteInfoGroupQuery

        // 2. 配置 quoteInfoQuery 的链式调用
        when(quoteInfoQuery.from(qQpQuoteInfoEntity)).thenReturn(quoteInfoQuery);
        when(quoteInfoQuery.where(any(Predicate.class))).thenReturn(quoteInfoQuery);
        when(quoteInfoQuery.findOne(eq(true), any())).thenReturn(qpQuoteInfo);

        // 3. 配置 quoteInfoGroupQuery 的链式调用
        when(quoteInfoGroupQuery.from(qQpQuoteInfoGroupEntity)).thenReturn(quoteInfoGroupQuery);
        when(quoteInfoGroupQuery.where(any(Predicate.class))).thenReturn(quoteInfoGroupQuery);
        when(quoteInfoGroupQuery.findOne(eq(true), any())).thenReturn(qpQuoteInfoGroup);
//
//        when(bqlQueryFactory.select(Expressions.stringPath("qp_quotation_info_group.*"))).thenReturn(mock(BQLQuery.class));
        when(quotationInfoGroupQuery.from(qQpQuotationInfoGroupEntity)).thenReturn(quotationInfoGroupQuery);
        when(quotationInfoGroupQuery.where(any(Predicate.class))).thenReturn(quotationInfoGroupQuery);
        when(quotationInfoGroupQuery.findOne(eq(true), any())).thenReturn(qpQuotationInfoGroup);

        // 执行测试
        Long result = downLoadService.getQuotationInfoId(TEST_QUOTE_INFO_ID);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result).isEqualTo(TEST_QUOTATION_INFO_ID);
    }

    @Test
    @DisplayName("测试getQuotationInfoId方法-找不到报价信息")
    void testGetQuotationInfoId_QuoteInfoNotFound() {
        // 准备测试数据
        QQpQuoteInfo qQpQuoteInfo = QQpQuoteInfo.qp_quote_info;

        // 创建BQLQuery mock对象
        BQLQuery<QpQuoteInfo> quoteInfoQuery = mock(BQLQuery.class);
        // 配置mock行为
        when(bqlQueryFactory.select(any(Expression.class))).thenReturn(quoteInfoQuery);
        when(quoteInfoQuery.from(qQpQuoteInfo)).thenReturn(quoteInfoQuery);
        when(quoteInfoQuery.where(any(Predicate.class))).thenReturn(quoteInfoQuery);
        when(quoteInfoQuery.findOne(eq(true), any())).thenReturn(null);

        // 执行测试
        Long result = downLoadService.getQuotationInfoId(TEST_QUOTE_INFO_ID);

        // 验证结果
        assertThat(result).isNull();
    }

    @Test
    @DisplayName("测试downloadPdfFile方法-正常情况")
    @Disabled
    void testDownloadPdfFile_NormalCase() throws IOException {
        // 准备测试数据
        String htmlData = "<html><body>Test PDF Content</body></html>";
        UploadResultDto uploadResultDto = new UploadResultDto();
        uploadResultDto.setLargeUrl("https://ai.insgeek.com/logo/logo.svg");
        uploadResultDto.setFileName("test.pdf");

        List<UploadResultDto> uploadResultDtos = Arrays.asList(uploadResultDto);
        ResponseVO<List<UploadResultDto>> responseVO = new ResponseVO<>();
        responseVO.setData(uploadResultDtos);

        // 配置mock行为
        DownLoadServiceImpl downLoadServiceSpy = spy(downLoadService);
        doReturn(htmlData).when(downLoadServiceSpy).getDownLoadHtmlData(TEST_QUOTE_INFO_ID, TEST_POLICY_NAME);
        when(htmlHandlerClient.convertHtmlPdfByWk(any(MultipartFile[].class)))
                .thenReturn(responseVO);

        // 执行测试
        MockHttpServletResponse response = new MockHttpServletResponse();
        downLoadServiceSpy.downloadPdfFile(TEST_QUOTE_INFO_ID, TEST_POLICY_NAME, response);

        // 验证结果
        assertThat(response.getContentType()).isEqualTo("application/pdf");
    }

    @Test
    @DisplayName("测试downloadPdfFile方法-HTML数据为空")
    void testDownloadPdfFile_HtmlDataEmpty() {
        // 配置mock行为
        DownLoadServiceImpl downLoadServiceSpy = spy(downLoadService);
        doReturn("").when(downLoadServiceSpy).getDownLoadHtmlData(TEST_QUOTE_INFO_ID, TEST_POLICY_NAME);

        // 执行测试并验证异常
        MockHttpServletResponse response = new MockHttpServletResponse();
        InsgeekException exception = assertThrows(InsgeekException.class, () -> {
            downLoadServiceSpy.downloadPdfFile(TEST_QUOTE_INFO_ID, TEST_POLICY_NAME, response);
        });

        // 验证异常信息
        assertThat(exception.getCode()).isEqualTo(Integer.parseInt(FrontendErrorMsgAndCode.DOWNLOAD_ERR.code()));
        assertThat(exception.getMessage()).isEqualTo(FrontendErrorMsgAndCode.DOWNLOAD_ERR.msg());
    }
}