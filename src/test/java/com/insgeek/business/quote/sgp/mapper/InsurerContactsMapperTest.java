package com.insgeek.business.quote.sgp.mapper;

import cn.hutool.core.collection.CollectionUtil;
import com.insgeek.boot.web.auth.dto.IdentityDto;
import com.insgeek.boot.web.context.IdentityContext;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.entity.QpInsurerContacts;
import org.junit.jupiter.api.*;
import org.mockito.MockedStatic;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@DisplayName("报司联系人Mapper测试")
class InsurerContactsMapperTest {

    private InsurerContactsMapper insurerContactsMapper;
    
    private MockedStatic<IdentityContext> identityContextMockedStatic;

    @BeforeEach
    void setUp() {
        insurerContactsMapper = spy(new InsurerContactsMapper());
        
        // Mock静态方法IdentityContext.getIdentity()
        identityContextMockedStatic = mockStatic(IdentityContext.class);
        IdentityDto identityDto = new IdentityDto();
        identityDto.setUserId(1L);
        identityDto.setTenantId(1L);
        identityContextMockedStatic.when(IdentityContext::getIdentity).thenReturn(identityDto);
    }
    
    @AfterEach
    void tearDown() {
        if (identityContextMockedStatic != null) {
            identityContextMockedStatic.close();
        }
    }

    @Nested
    @DisplayName("查询报司联系人测试")
    class QueryInsurerContactsTest {

        @SuppressWarnings("unchecked")
        @Test
        @DisplayName("应根据当前用户ID查询报司联系人")
        void queryInsurerContacts_shouldQueryByCurrentUserId() {
            // Given
            QpInsurerContacts contacts = new QpInsurerContacts();
            List<QpInsurerContacts> expectedResult = Collections.singletonList(contacts);
            
            // Mock链式调用 this.entity(QpInsurerContacts.class).selectAll(dataCondition, true)
            DataMapper<QpInsurerContacts> dataMapper = mock(DataMapper.class);
            doReturn(dataMapper).when(insurerContactsMapper).entity(QpInsurerContacts.class);
            doReturn(expectedResult).when(dataMapper).selectAll(any(DataCondition.class), eq(true));

            // When
            List<QpInsurerContacts> result = insurerContactsMapper.queryInsurerContacts();

            // Then
            assertThat(result).isSameAs(expectedResult);
            verify(insurerContactsMapper, times(1)).entity(QpInsurerContacts.class);
            verify(dataMapper, times(1)).selectAll(any(DataCondition.class), eq(true));
        }
    }

    @Nested
    @DisplayName("保存报司联系人测试")
    class SaveInsurerContactsTest {

        @SuppressWarnings("unchecked")
        @Test
        @DisplayName("应先删除后插入新的联系人信息")
        void saveInsurerContacts_shouldDeleteThenInsert() {
            // Given
            QpInsurerContacts existingContact = new QpInsurerContacts();
            existingContact.setId(100L);
            List<QpInsurerContacts> existingContacts = Collections.singletonList(existingContact);
            
            QpInsurerContacts newContact = new QpInsurerContacts();
            newContact.setId(0L);
            List<QpInsurerContacts> newContacts = Collections.singletonList(newContact);
            
            // Mock链式调用
            DataMapper<QpInsurerContacts> dataMapper = mock(DataMapper.class);
            doReturn(dataMapper).when(insurerContactsMapper).entity(QpInsurerContacts.class);
            doReturn(existingContacts).when(dataMapper).select(any(DataCondition.class), eq(true));

            // When
            insurerContactsMapper.saveInsurerContacts(newContacts);

            // Then
            // 验证查询操作
            
            // 验证插入操作
            verify(dataMapper, times(1)).insertAll(anyList());
            
            // 验证设置创建人和更新人
            newContacts.forEach(contact -> {
                assertThat(contact.getCreatedBy()).isEqualTo(1L);
                assertThat(contact.getUpdatedBy()).isEqualTo(1L);
            });
        }

        @SuppressWarnings("unchecked")
        @Test
        @DisplayName("当没有现有联系人时应直接插入新联系人")
        void saveInsurerContacts_withNoExistingContacts_shouldOnlyInsert() {
            // Given
            QpInsurerContacts newContact = new QpInsurerContacts();
            newContact.setId(0L);
            List<QpInsurerContacts> newContacts = new ArrayList<>();
            newContacts.add(newContact);
            
            // Mock链式调用 this.entity(QpInsurerContacts.class).select(dataCondition, true)返回空列表
            DataMapper<QpInsurerContacts> dataMapper = mock(DataMapper.class);
            doReturn(dataMapper).when(insurerContactsMapper).entity(QpInsurerContacts.class);
            doReturn(Collections.emptyList()).when(dataMapper).select(any(DataCondition.class), eq(true));

            // When
            insurerContactsMapper.saveInsurerContacts(newContacts);

            
            // 验证插入操作
            verify(dataMapper, times(1)).insertAll(anyList());
            
            // 验证设置创建人和更新人
            newContacts.forEach(contact -> {
                assertThat(contact.getCreatedBy()).isEqualTo(1L);
                assertThat(contact.getUpdatedBy()).isEqualTo(1L);
            });
        }
    }
}