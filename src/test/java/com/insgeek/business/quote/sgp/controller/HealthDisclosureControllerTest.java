package com.insgeek.business.quote.sgp.controller;

import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.sgp.service.HealthDisclosureService;
import com.insgeek.business.standard.dto.vo.req.HealthDisclosureReqDto;
import com.insgeek.protocol.data.client.entity.QpQuoteHealthDisclosure;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("健康信息披露控制器测试")
class HealthDisclosureControllerTest {

    @InjectMocks
    private HealthDisclosureController healthDisclosureController;

    @Mock
    private HealthDisclosureService healthDisclosureService;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(healthDisclosureController, "healthDisclosureService", healthDisclosureService);
    }

    @Nested
    @DisplayName("保存健康信息披露测试")
    class SaveHealthDisclosureTest {

        @Test
        @DisplayName("应调用服务保存方法并返回成功响应")
        void saveHealthDisclosure_shouldCallServiceAndReturnSuccess() {
            // Given
            HealthDisclosureReqDto reqDto = new HealthDisclosureReqDto();

            // When
            ResponseVO result = healthDisclosureController.saveHealthDisclosure(reqDto);

            // Then
            verify(healthDisclosureService, times(1)).saveHealthDisclosure(reqDto);
            assertThat(result).isNotNull();
            // 验证响应是否成功（根据ResponseVO的实现方式可能需要调整）
        }
    }

    @Nested
    @DisplayName("查询健康信息披露测试")
    class QueryHealthDisclosureTest {

        @Test
        @DisplayName("应调用服务查询方法并返回数据")
        void queryHealthDisclosure_shouldCallServiceAndReturnData() {
            // Given
            Long quoteInfoId = 1L;
            QpQuoteHealthDisclosure disclosure = new QpQuoteHealthDisclosure();
            List<QpQuoteHealthDisclosure> disclosures = Collections.singletonList(disclosure);
            
            when(healthDisclosureService.queryHealthDisclosure(quoteInfoId))
                    .thenReturn(disclosures);

            // When
            ResponseVO result = healthDisclosureController.queryHealthDisclosure(quoteInfoId);

            // Then
            verify(healthDisclosureService, times(1)).queryHealthDisclosure(quoteInfoId);
            assertThat(result).isNotNull();
            assertThat(result.getData()).isSameAs(disclosures);
        }
    }
}