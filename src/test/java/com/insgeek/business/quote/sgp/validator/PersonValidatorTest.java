package com.insgeek.business.quote.sgp.validator;

import com.insgeek.boot.web.util.MessageUtil;
import com.insgeek.business.quote.sgp.constant.BizConstants;
import com.insgeek.business.quote.sgp.dto.QpPersonCheckResultDto;
import com.insgeek.business.quote.sgp.enums.MemberTypeEnum;
import org.junit.Before;
import org.junit.Test;
import org.mockito.MockedStatic;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class PersonValidatorTest {

    private PersonValidator personValidator;

    @Before
    public void setUp() {
        personValidator = new PersonValidator();
    }

    @Test
    public void testValidate_MemberLastName_Success() {
        Map<String, Object> personalInfo = new HashMap<>();
        personalInfo.put(BizConstants.PersonsTemplate.MEMBER_LAST_NAME, "张");

        try (MockedStatic<MessageUtil> mockedStatic = mockStatic(MessageUtil.class)) {
            mockedStatic.when(() -> MessageUtil.get(anyString())).thenReturn("error message");

            LinkedHashMap<String, QpPersonCheckResultDto.FieldDetail> result = personValidator.validate(
                    personalInfo, Collections.emptyList(), Collections.emptyList());

            assertTrue(result.get(BizConstants.PersonsTemplate.MEMBER_LAST_NAME).isPass());
            assertEquals("张", result.get(BizConstants.PersonsTemplate.MEMBER_LAST_NAME).getValue());
        }
    }

    @Test
    public void testValidate_MemberGivenName_Success() {
        Map<String, Object> personalInfo = new HashMap<>();
        personalInfo.put(BizConstants.PersonsTemplate.MEMBER_GIVEN_NAME, "三");

        try (MockedStatic<MessageUtil> mockedStatic = mockStatic(MessageUtil.class)) {
            mockedStatic.when(() -> MessageUtil.get(anyString())).thenReturn("error message");

            LinkedHashMap<String, QpPersonCheckResultDto.FieldDetail> result = personValidator.validate(
                    personalInfo, Collections.emptyList(), Collections.emptyList());

            assertTrue(result.get(BizConstants.PersonsTemplate.MEMBER_GIVEN_NAME).isPass());
            assertEquals("三", result.get(BizConstants.PersonsTemplate.MEMBER_GIVEN_NAME).getValue());
        }
    }

    @Test
    public void testValidate_MemberType_Success() {
        Map<String, Object> personalInfo = new HashMap<>();
        personalInfo.put(BizConstants.PersonsTemplate.MEMBER_TYPE, MemberTypeEnum.E.getValue());

        try (MockedStatic<MessageUtil> mockedStatic = mockStatic(MessageUtil.class)) {
            mockedStatic.when(() -> MessageUtil.get(anyString())).thenReturn("error message");

            LinkedHashMap<String, QpPersonCheckResultDto.FieldDetail> result = personValidator.validate(
                    personalInfo, Collections.emptyList(), Collections.emptyList());

            assertEquals(MemberTypeEnum.E.getValue(), result.get(BizConstants.PersonsTemplate.MEMBER_TYPE).getValue());
        }
    }

    @Test
    public void testValidate_DateOfBirth_Success() {
        Map<String, Object> personalInfo = new HashMap<>();
        personalInfo.put(BizConstants.PersonsTemplate.DATE_OF_BIRTH, "01/01/1990");

        try (MockedStatic<MessageUtil> mockedStatic = mockStatic(MessageUtil.class)) {
            mockedStatic.when(() -> MessageUtil.get(anyString())).thenReturn("error message");

            LinkedHashMap<String, QpPersonCheckResultDto.FieldDetail> result = personValidator.validate(
                    personalInfo, Collections.emptyList(), Collections.emptyList());

            assertTrue(result.get(BizConstants.PersonsTemplate.DATE_OF_BIRTH).isPass());
            assertEquals("01/01/1990", result.get(BizConstants.PersonsTemplate.DATE_OF_BIRTH).getValue());
        }
    }

    @Test
    public void testValidate_DateOfBirth_Empty() {
        Map<String, Object> personalInfo = new HashMap<>();
        personalInfo.put(BizConstants.PersonsTemplate.DATE_OF_BIRTH, "");

        try (MockedStatic<MessageUtil> mockedStatic = mockStatic(MessageUtil.class)) {
            mockedStatic.when(() -> MessageUtil.get("b_b_quote_sgp_025")).thenReturn("Date of birth cannot be empty");

            LinkedHashMap<String, QpPersonCheckResultDto.FieldDetail> result = personValidator.validate(
                    personalInfo, Collections.emptyList(), Collections.emptyList());

            assertFalse(result.get(BizConstants.PersonsTemplate.DATE_OF_BIRTH).isPass());
            assertEquals("", result.get(BizConstants.PersonsTemplate.DATE_OF_BIRTH).getValue());
        }
    }

    @Test
    public void testValidate_EmployeeIdNo_RequiredForMemberTypeE() {
        Map<String, Object> personalInfo = new HashMap<>();
        personalInfo.put(BizConstants.PersonsTemplate.MEMBER_TYPE, MemberTypeEnum.E.getValue());
        personalInfo.put(BizConstants.PersonsTemplate.EMPLOYEE_ID_NO, "");

        try (MockedStatic<MessageUtil> mockedStatic = mockStatic(MessageUtil.class)) {
            mockedStatic.when(() -> MessageUtil.get("b_b_quote_sgp_027")).thenReturn("Employee ID cannot be empty");

            LinkedHashMap<String, QpPersonCheckResultDto.FieldDetail> result = personValidator.validate(
                    personalInfo, Collections.emptyList(), Collections.emptyList());

            assertNotNull(result.get(BizConstants.PersonsTemplate.EMPLOYEE_ID_NO).getMsg());
            assertEquals("", result.get(BizConstants.PersonsTemplate.EMPLOYEE_ID_NO).getValue());
        }
    }

    @Test
    public void testValidate_EmployeeIdNo_NotRequiredForMemberTypeD() {
        Map<String, Object> personalInfo = new HashMap<>();
        personalInfo.put(BizConstants.PersonsTemplate.MEMBER_TYPE, "D"); // Dependant
        personalInfo.put(BizConstants.PersonsTemplate.EMPLOYEE_ID_NO, "");

        try (MockedStatic<MessageUtil> mockedStatic = mockStatic(MessageUtil.class)) {
            mockedStatic.when(() -> MessageUtil.get(anyString())).thenReturn("error message");

            LinkedHashMap<String, QpPersonCheckResultDto.FieldDetail> result = personValidator.validate(
                    personalInfo, Collections.emptyList(), Collections.emptyList());

            assertTrue(result.get(BizConstants.PersonsTemplate.EMPLOYEE_ID_NO).isPass());
        }
    }

    @Test
    public void testValidate_EffectiveDate_Success() {
        Map<String, Object> personalInfo = new HashMap<>();
        personalInfo.put(BizConstants.PersonsTemplate.EFFECTIVE_DATE, "01/01/2023");

        try (MockedStatic<MessageUtil> mockedStatic = mockStatic(MessageUtil.class)) {
            mockedStatic.when(() -> MessageUtil.get(anyString())).thenReturn("error message");

            LinkedHashMap<String, QpPersonCheckResultDto.FieldDetail> result = personValidator.validate(
                    personalInfo, Collections.emptyList(), Collections.emptyList());

            assertTrue(result.get(BizConstants.PersonsTemplate.EFFECTIVE_DATE).isPass());
            assertEquals("01/01/2023", result.get(BizConstants.PersonsTemplate.EFFECTIVE_DATE).getValue());
        }
    }

    @Test
    public void testValidate_EmployeeCategory_Empty() {
        Map<String, Object> personalInfo = new HashMap<>();
        personalInfo.put(BizConstants.PersonsTemplate.EMPLOYEE_CATEGORY, "");

        try (MockedStatic<MessageUtil> mockedStatic = mockStatic(MessageUtil.class)) {
            mockedStatic.when(() -> MessageUtil.get("b_b_quote_sgp_033")).thenReturn("Employee category cannot be empty");

            LinkedHashMap<String, QpPersonCheckResultDto.FieldDetail> result = personValidator.validate(
                    personalInfo, Collections.emptyList(), Collections.emptyList());

            assertFalse(result.get(BizConstants.PersonsTemplate.EMPLOYEE_CATEGORY).isPass());
            assertEquals("", result.get(BizConstants.PersonsTemplate.EMPLOYEE_CATEGORY).getValue());
        }
    }

    @Test
    public void testValidate_CoveragePlan_Empty() {
        Map<String, Object> personalInfo = new HashMap<>();
        List<String> coveragePlanHeaders = Collections.singletonList("CORE_PLAN");

        try (MockedStatic<MessageUtil> mockedStatic = mockStatic(MessageUtil.class)) {
            mockedStatic.when(() -> MessageUtil.get("b_b_quote_sgp_036")).thenReturn("Coverage plan cannot be empty: %s");

            LinkedHashMap<String, QpPersonCheckResultDto.FieldDetail> result = personValidator.validate(
                    personalInfo, coveragePlanHeaders, Collections.emptyList());

            assertFalse(result.get("core_plan").isPass());
        }
    }

}