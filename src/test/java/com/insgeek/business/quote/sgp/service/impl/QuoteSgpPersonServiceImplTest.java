package com.insgeek.business.quote.sgp.service.impl;

import com.insgeek.business.quote.common.service.DutyCommonService;
import com.insgeek.business.quote.sgp.dto.QpPersonCheckResultDto;
import com.insgeek.business.quote.sgp.dto.QuoteBenefitDto;
import com.insgeek.business.quote.sgp.dto.QuotePersonOverviewDto;
import com.insgeek.business.quote.sgp.dto.QuoteSavePersonOverviewDto;
import com.insgeek.business.quote.sgp.mapper.QpQuotePersonsMapper;
import com.insgeek.business.quote.sgp.mapper.QpQuotePersonsOverviewMapper;
import com.insgeek.business.quote.sgp.mapper.QuoteConfigSgpMapper;
import com.insgeek.business.quote.sgp.mapper.QuoteSgpMapper;
import com.insgeek.business.quote.sgp.strategy.person.QuotePersonOverviewStrategyFactory;
import com.insgeek.business.quote.sgp.validator.PersonValidator;
import com.insgeek.protocol.data.client.entity.QpQuotePersons;
import com.insgeek.protocol.insurance.client.DutyClient;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class QuoteSgpPersonServiceImplTest {

   @Mock
   private QuoteSgpMapper quoteSgpMapper;

   @Mock
   private QuoteConfigSgpMapper quoteConfigSgpMapper;

   @Mock
   private QpQuotePersonsMapper qpQuotePersonsMapper;

   @Mock
   private QpQuotePersonsOverviewMapper qpQuotePersonsOverviewMapper;

   @Mock
   private DutyClient dutyClient;

   @Mock
   private DutyCommonService dutyCommonService;

   @Mock
   private PersonValidator personValidator;

   @Mock
   private QuotePersonOverviewStrategyFactory quotePersonOverviewStrategyFactory;

   @InjectMocks
   private QuoteSgpPersonServiceImpl quoteSgpPersonService;

   private Workbook testWorkbook;

   private Sheet testSheet;

   private static final Long QUOTE_INFO_ID = 1L;

   @BeforeEach
   void setUp() throws IOException {
       // 创建测试用的Excel工作簿
       testWorkbook = new XSSFWorkbook();
       testSheet = testWorkbook.createSheet("TestSheet");

       // 创建表头行
       Row headerRow = testSheet.createRow(3); // HEADER_ROW = 4，索引为3
       headerRow.createCell(0).setCellValue("Member Last Name");
       headerRow.createCell(1).setCellValue("Member Given Name");
       headerRow.createCell(2).setCellValue("Member Type");
       headerRow.createCell(3).setCellValue("Identity Type");
       headerRow.createCell(4).setCellValue("Identity Number");
       headerRow.createCell(5).setCellValue("Date of Birth\n(DD/MM/YYYY)");
       headerRow.createCell(6).setCellValue("Gender");
       headerRow.createCell(7).setCellValue("Employee Id No");
       headerRow.createCell(8).setCellValue("Nationality");
       headerRow.createCell(9).setCellValue("Country of Residence");
       headerRow.createCell(10).setCellValue("Marital Status");
       headerRow.createCell(11).setCellValue("Occupational Class");
       headerRow.createCell(12).setCellValue("Employment Date\n(DD/MM/YYYY)");
       headerRow.createCell(13).setCellValue("Employee Category");
       headerRow.createCell(14).setCellValue("Effective Date\n(DD/MM/YYYY)");
       headerRow.createCell(15).setCellValue("BENEFIT_1");
       headerRow.createCell(16).setCellValue("BENEFIT_2");

       // 创建数据行
       Row dataRow = testSheet.createRow(6); // DATA_START_ROW = 7，索引为6
       dataRow.createCell(0).setCellValue("张");
       dataRow.createCell(1).setCellValue("三");
       dataRow.createCell(2).setCellValue("Self");
       dataRow.createCell(3).setCellValue("1");
       dataRow.createCell(4).setCellValue("123456789");
       dataRow.createCell(5).setCellValue("01/01/1990");
       dataRow.createCell(6).setCellValue("M");
       dataRow.createCell(7).setCellValue("EMP001");
       dataRow.createCell(8).setCellValue("China");
       dataRow.createCell(9).setCellValue("China");
       dataRow.createCell(10).setCellValue("Married");
       dataRow.createCell(11).setCellValue("Class 1");
       dataRow.createCell(12).setCellValue("01/01/2020");
       dataRow.createCell(13).setCellValue("Category A");
       dataRow.createCell(14).setCellValue("01/01/2023");
       dataRow.createCell(15).setCellValue("10000");
       dataRow.createCell(16).setCellValue("20000");
   }

   @Test
   @DisplayName("根据quoteInfoId获取人员统计信息列表")
   void getQuotePersonOverviewList_normalCase() {
       // 准备测试数据
       List<QuotePersonOverviewDto> mockResult = new ArrayList<>();
       QuotePersonOverviewDto dto1 = new QuotePersonOverviewDto();
       dto1.setTag("tag1");
       dto1.setItemKey("itemKey1");
       dto1.setValue("value1");
       mockResult.add(dto1);

       // 配置mock行为
       when(qpQuotePersonsOverviewMapper.getQuotePersonOverviewList(QUOTE_INFO_ID)).thenReturn(mockResult);

       // 执行测试
       List<QuotePersonOverviewDto> result = quoteSgpPersonService.getQuotePersonOverviewList(QUOTE_INFO_ID);

       // 验证交互
       verify(qpQuotePersonsOverviewMapper, times(1)).getQuotePersonOverviewList(QUOTE_INFO_ID);
   }

   @Test
   @DisplayName("保存人员统计信息")
   void savePersonOverview_normalCase() {
       // 准备测试数据
       QuoteSavePersonOverviewDto saveDto = new QuoteSavePersonOverviewDto();
       saveDto.setQuoteInfoId(QUOTE_INFO_ID);

       List<QuotePersonOverviewDto> overviewList = new ArrayList<>();
       QuotePersonOverviewDto dto1 = new QuotePersonOverviewDto();
       dto1.setTag("tag1");
       dto1.setItemKey("itemKey1");
       dto1.setValue("value1");
       overviewList.add(dto1);

       saveDto.setPersonOverviewList(overviewList);

       // 执行测试
       Long result = quoteSgpPersonService.savePersonOverview(saveDto);

   }

   @Test
   @DisplayName("保存人员统计信息 - 空列表")
   void savePersonOverview_emptyList() {
       // 准备测试数据
       QuoteSavePersonOverviewDto saveDto = new QuoteSavePersonOverviewDto();
       saveDto.setQuoteInfoId(QUOTE_INFO_ID);
       saveDto.setPersonOverviewList(new ArrayList<>());

       // 执行测试
       Long result = quoteSgpPersonService.savePersonOverview(saveDto);

       // 验证交互
       verify(qpQuotePersonsOverviewMapper, never()).deleteByQuoteId(QUOTE_INFO_ID);
       verify(qpQuotePersonsOverviewMapper, never()).insertAll(anyList());
   }

    @Test
    @DisplayName("边界情况：人员列表为空")
    void testGetQuotePersonList_emptyPersonList() {
        // 准备测试数据
        List<QpQuotePersons> personList = Collections.emptyList();
        List<QuoteBenefitDto> benefitList = Collections.singletonList(new QuoteBenefitDto());

        // 配置mock行为
        when(qpQuotePersonsMapper.getQuotePersonList(QUOTE_INFO_ID)).thenReturn(personList);
        when(quoteSgpMapper.getQuoteBenefitList(QUOTE_INFO_ID)).thenReturn(benefitList);

        // 执行测试
        QpPersonCheckResultDto result = quoteSgpPersonService.getQuotePersonList(QUOTE_INFO_ID);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.getPersonalList()).isEmpty();
        assertThat(result.isHasError()).isFalse();

        // 验证交互次数
        verify(qpQuotePersonsMapper, times(1)).getQuotePersonList(QUOTE_INFO_ID);
        verify(quoteSgpMapper, times(1)).getQuoteBenefitList(QUOTE_INFO_ID);
    }

    @Test
    @DisplayName("边界情况：人员列表和险种列表都为空")
    void testGetQuotePersonList_bothEmpty() {
        // 准备测试数据
        List<QpQuotePersons> personList = Collections.emptyList();
        List<QuoteBenefitDto> benefitList = Collections.emptyList();

        // 配置mock行为
        when(qpQuotePersonsMapper.getQuotePersonList(QUOTE_INFO_ID)).thenReturn(personList);
        when(quoteSgpMapper.getQuoteBenefitList(QUOTE_INFO_ID)).thenReturn(benefitList);

        // 执行测试
        QpPersonCheckResultDto result = quoteSgpPersonService.getQuotePersonList(QUOTE_INFO_ID);

        // 验证结果
        assertThat(result).isNotNull();
        assertThat(result.isHasError()).isFalse();

        // 验证交互次数
        verify(qpQuotePersonsMapper, times(1)).getQuotePersonList(QUOTE_INFO_ID);
        verify(quoteSgpMapper, times(1)).getQuoteBenefitList(QUOTE_INFO_ID);
    }

}