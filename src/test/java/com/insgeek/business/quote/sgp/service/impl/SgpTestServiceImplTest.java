package com.insgeek.business.quote.sgp.service.impl;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;

import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;

class SgpTestServiceImplTest {

    private final SgpTestServiceImpl service = new SgpTestServiceImpl();

    @Test
    @DisplayName("验证测试数据生成结构")
    void testBenefitDataStructure() {
        // When
        List<Map<String, Object>> result = service.test();
        // Then
        assertThat(result).isNotNull();
    }

}