package com.insgeek.business.quote.sgp.service.impl;

import com.insgeek.boot.commons.exception.InsgeekException;
import com.insgeek.business.quote.sgp.dto.SingaporeDutyExcelPlanConfigDTO;
import com.insgeek.business.quote.sgp.dto.SingaporeDutyExcelPlanDTO;
import com.insgeek.business.quote.sgp.dto.SingaporeDutyExcelSharedDTO;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontProductRuleItemDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;

@DisplayName("新加坡保险责任Excel服务实现类测试")
class SingaporeDutyExcelServiceImplTest {

    private SingaporeDutyExcelServiceImpl service;

    @BeforeEach
    void setUp() {
        service = new SingaporeDutyExcelServiceImpl();
    }

    @Test
    @DisplayName("测试isEmptyItemValue方法")
    void testIsEmptyItemValue() throws Exception {
        Method method = SingaporeDutyExcelServiceImpl.class.getDeclaredMethod("isEmptyItemValue", Object.class);
        method.setAccessible(true);

        // 测试null值
        boolean nullResult = (boolean) method.invoke(service, (Object) null);
        assertTrue(nullResult, "null值应为空");

        // 测试空字符串
        boolean emptyStringResult = (boolean) method.invoke(service, "");
        assertTrue(emptyStringResult, "空字符串应为空");

        // 测试只包含空格的字符串
        boolean spaceStringResult = (boolean) method.invoke(service, "   ");
        assertTrue(spaceStringResult, "只包含空格的字符串应为空");

        // 测试非空字符串
        boolean nonEmptyStringResult = (boolean) method.invoke(service, "test");
        assertFalse(nonEmptyStringResult, "非空字符串不应为空");

        // 测试空Map
        boolean emptyMapResult = (boolean) method.invoke(service, new HashMap<>());
        assertTrue(emptyMapResult, "空Map应为空");

        // 测试只包含amount为0的Map
        Map<String, Object> zeroAmountMap = new HashMap<>();
        zeroAmountMap.put("amount", "0");
        boolean zeroAmountMapResult = (boolean) method.invoke(service, zeroAmountMap);
        assertTrue(zeroAmountMapResult, "只包含amount为0的Map应为空");

        // 测试包含非零amount的Map
        Map<String, Object> nonZeroAmountMap = new HashMap<>();
        nonZeroAmountMap.put("amount", "100");
        boolean nonZeroAmountMapResult = (boolean) method.invoke(service, nonZeroAmountMap);
        assertFalse(nonZeroAmountMapResult, "包含非零amount的Map不应为空");

        // 测试包含其他键的Map
        Map<String, Object> otherKeyMap = new HashMap<>();
        otherKeyMap.put("other", "value");
        boolean otherKeyMapResult = (boolean) method.invoke(service, otherKeyMap);
        assertFalse(otherKeyMapResult, "包含其他键的Map不应为空");
    }

    @Test
    @DisplayName("测试parseItemValue方法")
    void testParseItemValue() throws Exception {
        Method method = SingaporeDutyExcelServiceImpl.class.getDeclaredMethod("parseItemValue", Object.class);
        method.setAccessible(true);

        // 测试null值
        Integer nullResult = (Integer) method.invoke(service, (Object) null);
        assertNull(nullResult, "null值应返回null");

        // 测试有效数字字符串
        Integer validStringResult = (Integer) method.invoke(service, "123");
        assertEquals(Integer.valueOf(123), validStringResult, "有效数字字符串应正确解析");

        // 测试无效数字字符串
        Integer invalidStringResult = (Integer) method.invoke(service, "abc");
        assertNull(invalidStringResult, "无效数字字符串应返回null");

        // 测试包含amount的有效数字Map
        Map<String, Object> validMap = new HashMap<>();
        validMap.put("amount", "456");
        Integer validMapResult = (Integer) method.invoke(service, validMap);
        assertEquals(Integer.valueOf(456), validMapResult, "包含有效amount的Map应正确解析");

        // 测试包含amount的无效数字Map
        Map<String, Object> invalidMap = new HashMap<>();
        invalidMap.put("amount", "def");
        Integer invalidMapResult = (Integer) method.invoke(service, invalidMap);
        assertNull(invalidMapResult, "包含无效amount的Map应返回null");

        // 测试不包含amount的Map
        Map<String, Object> noAmountMap = new HashMap<>();
        noAmountMap.put("other", "value");
        Integer noAmountMapResult = (Integer) method.invoke(service, noAmountMap);
        assertNull(noAmountMapResult, "不包含amount的Map应返回null");
    }

    @Test
    @DisplayName("测试getStringValue方法")
    void testGetStringValue() throws Exception {
        Method method = SingaporeDutyExcelServiceImpl.class.getDeclaredMethod("getStringValue", Object.class);
        method.setAccessible(true);

        // 测试null值
        String nullResult = (String) method.invoke(service, (Object) null);
        assertEquals("", nullResult, "null值应返回空字符串");

        // 测试字符串值
        String stringResult = (String) method.invoke(service, "test");
        assertEquals("test", stringResult, "字符串值应保持不变");

        // 测试包含amount的Map
        Map<String, Object> amountMap = new HashMap<>();
        amountMap.put("amount", 123);
        String amountMapResult = (String) method.invoke(service, amountMap);
        assertEquals("123", amountMapResult, "包含amount的Map应返回amount值");

        // 测试不包含amount的Map
        Map<String, Object> noAmountMap = new HashMap<>();
        noAmountMap.put("other", "value");
        String noAmountMapResult = (String) method.invoke(service, noAmountMap);
        assertEquals(noAmountMap.toString(), noAmountMapResult, "不包含amount的Map应返回toString结果");

        // 测试其他对象类型
        Object testObject = new Object();
        String objectResult = (String) method.invoke(service, testObject);
        assertEquals(testObject.toString(), objectResult, "其他对象类型应返回toString结果");
    }

    @Test
    @DisplayName("测试isAdditionalInsurance方法")
    void testIsAdditionalInsurance() throws Exception {
        Method method = SingaporeDutyExcelServiceImpl.class.getDeclaredMethod("isAdditionalInsurance", String.class);
        method.setAccessible(true);

        // 测试null值
        boolean nullResult = (boolean) method.invoke(service, (String) null);
        assertFalse(nullResult, "null值不应为附加险");

        // 测试空字符串
        boolean emptyResult = (boolean) method.invoke(service, "");
        assertFalse(emptyResult, "空字符串不应为附加险");

        // 测试附加险类型
        assertTrue((boolean) method.invoke(service, "SP"), "SP应为附加险");
        assertTrue((boolean) method.invoke(service, "GP"), "GP应为附加险");
        assertTrue((boolean) method.invoke(service, "GM"), "GM应为附加险");
        assertTrue((boolean) method.invoke(service, "GMM"), "GMM应为附加险");
        assertTrue((boolean) method.invoke(service, "GD"), "GD应为附加险");
        assertTrue((boolean) method.invoke(service, "GCI"), "GCI应为附加险");

        // 测试非附加险类型
        assertFalse((boolean) method.invoke(service, "GHS"), "GHS不应为附加险");
        assertFalse((boolean) method.invoke(service, "GTL"), "GTL不应为附加险");
        assertFalse((boolean) method.invoke(service, "OTHER"), "OTHER不应为附加险");
    }

    @Test
    @DisplayName("测试hasMasterInsurance方法")
    void testHasMasterInsurance() throws Exception {
        Method method = SingaporeDutyExcelServiceImpl.class.getDeclaredMethod("hasMasterInsurance", List.class, String.class);
        method.setAccessible(true);

        // 准备测试数据
        List<SingaporeDutyExcelPlanConfigDTO> configList = new ArrayList<>();
        SingaporeDutyExcelPlanConfigDTO ghsConfig = new SingaporeDutyExcelPlanConfigDTO();
        ghsConfig.setInsuranceTypeDetail("GHS");
        configList.add(ghsConfig);

        SingaporeDutyExcelPlanConfigDTO gtlConfig = new SingaporeDutyExcelPlanConfigDTO();
        gtlConfig.setInsuranceTypeDetail("GTL");
        configList.add(gtlConfig);

        // 测试null配置列表
        boolean nullListResult = (boolean) method.invoke(service, null, "SP");
        assertFalse(nullListResult, "null配置列表应返回false");

        // 测试空配置列表
        boolean emptyListResult = (boolean) method.invoke(service, new ArrayList<>(), "SP");
        assertFalse(emptyListResult, "空配置列表应返回false");

        // 测试null附加险类型
        boolean nullTypeResult = (boolean) method.invoke(service, configList, null);
        assertFalse(nullTypeResult, "null附加险类型应返回false");

        // 测试空附加险类型
        boolean emptyTypeResult = (boolean) method.invoke(service, configList, "");
        assertFalse(emptyTypeResult, "空附加险类型应返回false");

        // 测试需要GHS主险的附加险
        assertTrue((boolean) method.invoke(service, configList, "SP"), "SP应能找到GHS主险");
        assertTrue((boolean) method.invoke(service, configList, "GP"), "GP应能找到GHS主险");
        assertTrue((boolean) method.invoke(service, configList, "GM"), "GM应能找到GHS主险");
        assertTrue((boolean) method.invoke(service, configList, "GMM"), "GMM应能找到GHS主险");
        assertTrue((boolean) method.invoke(service, configList, "GD"), "GD应能找到GHS主险");

        // 测试需要GTL主险的附加险
        assertTrue((boolean) method.invoke(service, configList, "GCI"), "GCI应能找到GTL主险");

        // 测试无法找到主险的附加险
        assertFalse((boolean) method.invoke(service, configList, "OTHER"), "OTHER应无法找到主险");
    }

    @Test
    @DisplayName("测试getRequiredMasterType方法")
    void testGetRequiredMasterType() throws Exception {
        Method method = SingaporeDutyExcelServiceImpl.class.getDeclaredMethod("getRequiredMasterType", String.class);
        method.setAccessible(true);

        // 测试null值
        String nullResult = (String) method.invoke(service, (String) null);
        assertNull(nullResult, "null值应返回null");

        // 测试空字符串
        String emptyResult = (String) method.invoke(service, "");
        assertNull(emptyResult, "空字符串应返回null");

        // 测试需要GHS主险的附加险类型
        assertEquals("GHS", method.invoke(service, "SP"), "SP应需要GHS主险");
        assertEquals("GHS", method.invoke(service, "GP"), "GP应需要GHS主险");
        assertEquals("GHS", method.invoke(service, "GM"), "GM应需要GHS主险");
        assertEquals("GHS", method.invoke(service, "GMM"), "GMM应需要GHS主险");
        assertEquals("GHS", method.invoke(service, "GD"), "GD应需要GHS主险");

        // 测试需要GTL主险的附加险类型
        assertEquals("GTL", method.invoke(service, "GCI"), "GCI应需要GTL主险");

        // 测试不需要主险的类型
        assertNull(method.invoke(service, "GHS"), "GHS不需要主险");
        assertNull(method.invoke(service, "GTL"), "GTL不需要主险");
        assertNull(method.invoke(service, "OTHER"), "OTHER不需要主险");
    }

    @Test
    @DisplayName("测试validMutilationNameCategory方法 - 无重复")
    void testValidMutilationNameCategoryNoDuplicate() throws Exception {
        Method method = SingaporeDutyExcelServiceImpl.class.getDeclaredMethod("validMutilationNameCategory", List.class);
        method.setAccessible(true);

        // 准备测试数据 - 无重复
        List<SingaporeDutyExcelPlanDTO> planList = new ArrayList<>();
        SingaporeDutyExcelPlanDTO plan1 = new SingaporeDutyExcelPlanDTO();
        plan1.setPlanName("PlanA");
        plan1.setEmployeeCategory("Category1");
        planList.add(plan1);

        SingaporeDutyExcelPlanDTO plan2 = new SingaporeDutyExcelPlanDTO();
        plan2.setPlanName("PlanB");
        plan2.setEmployeeCategory("Category2");
        planList.add(plan2);

        // 应该不抛出异常
        assertDoesNotThrow(() -> method.invoke(service, planList), "无重复计划名称和类别时不应抛出异常");
    }



    @Test
    @DisplayName("测试validMutilationNameCategory方法 - 空列表")
    void testValidMutilationNameCategoryEmptyList() throws Exception {
        Method method = SingaporeDutyExcelServiceImpl.class.getDeclaredMethod("validMutilationNameCategory", List.class);
        method.setAccessible(true);

        // 准备测试数据 - 空列表
        List<SingaporeDutyExcelPlanDTO> planList = new ArrayList<>();

        // 应该不抛出异常
        assertDoesNotThrow(() -> method.invoke(service, planList), "空列表时不应抛出异常");
    }

    @Test
    @DisplayName("测试validMutilationNameCategory方法 - 包含null元素")
    void testValidMutilationNameCategoryWithNullElements() throws Exception {
        Method method = SingaporeDutyExcelServiceImpl.class.getDeclaredMethod("validMutilationNameCategory", List.class);
        method.setAccessible(true);

        // 准备测试数据 - 包含null元素
        List<SingaporeDutyExcelPlanDTO> planList = new ArrayList<>();
        planList.add(null); // 添加null元素
        
        SingaporeDutyExcelPlanDTO plan = new SingaporeDutyExcelPlanDTO();
        plan.setPlanName("PlanA");
        plan.setEmployeeCategory("Category1");
        planList.add(plan);

        // 应该不抛出异常
        assertDoesNotThrow(() -> method.invoke(service, planList), "包含null元素时不应抛出异常");
    }

    @Test
    @DisplayName("测试validateRepeatShareAmountDuty方法 - 无共享责任")
    void testValidateRepeatShareAmountDutyNoShared() throws Exception {
        Method method = SingaporeDutyExcelServiceImpl.class.getDeclaredMethod("validateRepeatShareAmountDuty", List.class);
        method.setAccessible(true);

        // 准备测试数据 - 无共享责任
        List<SingaporeDutyExcelPlanDTO> planList = new ArrayList<>();
        SingaporeDutyExcelPlanDTO plan = new SingaporeDutyExcelPlanDTO();
        plan.setPlanName("PlanA");
        plan.setEmployeeCategory("Category1");
        
        List<SingaporeDutyExcelPlanConfigDTO> configList = new ArrayList<>();
        SingaporeDutyExcelPlanConfigDTO config = new SingaporeDutyExcelPlanConfigDTO();
        config.setInsuranceTypeDetail("GHS");
        configList.add(config);
        plan.setPlanConfigList(configList);
        
        planList.add(plan);

        // 应该不抛出异常
        assertDoesNotThrow(() -> method.invoke(service, planList), "无共享责任时不应抛出异常");
    }










}