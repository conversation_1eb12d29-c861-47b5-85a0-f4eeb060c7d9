package com.insgeek.business.quote.sgp.service.impl;

import com.insgeek.business.quote.sgp.mapper.HealthDisclosureMapper;
import com.insgeek.business.standard.dto.vo.req.HealthDisclosureReqDto;
import com.insgeek.protocol.data.client.entity.QpQuoteHealthDisclosure;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("健康信息披露服务测试")
class HealthDisclosureServiceImplTest {

    @InjectMocks
    private HealthDisclosureServiceImpl healthDisclosureService;

    @Mock
    private HealthDisclosureMapper healthDisclosureMapper;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(healthDisclosureService, "healthDisclosureMapper", healthDisclosureMapper);
    }

    @Nested
    @DisplayName("保存健康信息披露测试")
    class SaveHealthDisclosureTest {

        @Test
        @DisplayName("当报价信息ID不为空时，应先删除后保存")
        void saveHealthDisclosure_withQuoteInfoId_shouldDeleteThenSave() {
            // Given
            Long quoteInfoId = 1L;
            HealthDisclosureReqDto reqDto = new HealthDisclosureReqDto();
            reqDto.setQuoteInfoId(quoteInfoId);

            // When
            healthDisclosureService.saveHealthDisclosure(reqDto);

            // Then
            verify(healthDisclosureMapper, times(1)).deleteHealthDisclosure(quoteInfoId);
            verify(healthDisclosureMapper, times(1)).saveHealthDisclosure(reqDto);
        }

        @Test
        @DisplayName("当报价信息ID为空时，应只保存不删除")
        void saveHealthDisclosure_withoutQuoteInfoId_shouldOnlySave() {
            // Given
            HealthDisclosureReqDto reqDto = new HealthDisclosureReqDto();
            reqDto.setQuoteInfoId(null);

            // When
            healthDisclosureService.saveHealthDisclosure(reqDto);

            // Then
            verify(healthDisclosureMapper, never()).deleteHealthDisclosure(anyLong());
            verify(healthDisclosureMapper, times(1)).saveHealthDisclosure(reqDto);
        }
    }

    @Nested
    @DisplayName("查询健康信息披露测试")
    class QueryHealthDisclosureTest {

        @Test
        @DisplayName("应正确调用mapper查询方法并返回结果")
        void queryHealthDisclosure_shouldCallMapperAndReturnResult() {
            // Given
            Long quoteInfoId = 1L;
            QpQuoteHealthDisclosure disclosure = new QpQuoteHealthDisclosure();
            List<QpQuoteHealthDisclosure> expectedResult = Collections.singletonList(disclosure);
            
            when(healthDisclosureMapper.queryHealthDisclosure(quoteInfoId))
                    .thenReturn(expectedResult);

            // When
            List<QpQuoteHealthDisclosure> result = healthDisclosureService.queryHealthDisclosure(quoteInfoId);

            // Then
            assertThat(result).isSameAs(expectedResult);
            verify(healthDisclosureMapper, times(1)).queryHealthDisclosure(quoteInfoId);
        }
    }
}