package com.insgeek.business.quote.sgp.service.impl;

import com.insgeek.business.quote.sgp.config.CompanyLogConfigs;
import com.insgeek.business.standard.mapper.StandardCompanyMapper;
import com.insgeek.protocol.platform.common.dto.entity.IgCompany;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class CompanyServiceImplTest {

    @Mock
    private StandardCompanyMapper standardCompanyMapper;

    @InjectMocks
    private CompanyServiceImpl companyService;

    @Mock
    private CompanyLogConfigs companyLogConfigs;

    @BeforeEach
    void setUp() {
        companyService.setStandardCompanyMapper(standardCompanyMapper);
    }

    @Test
    void getCompanyList_shouldReturnEmptyList_whenMapperReturnsNull() {
        // Given
        when(standardCompanyMapper.getCompanyList()).thenReturn(null);

        // When
        List<?> result = companyService.getCompanyList();

        // Then
        assertThat(result).isEmpty();
    }

    @Test
    void getCompanyList_shouldReturnConvertedCompanyList_whenMapperReturnsCompanies() {
        // Given
        List<IgCompany> companies = new ArrayList<>();
        IgCompany company1 = new IgCompany();
        company1.setId(1L);
        company1.setCompanyName("Test Company 1");
        company1.setCompanyLogo("logo1.png");
        companies.add(company1);

        IgCompany company2 = new IgCompany();
        company2.setId(2L);
        company2.setCompanyName("Test Company 2");
        company2.setCompanyLogo("logo2.png");
        companies.add(company2);

        when(standardCompanyMapper.getCompanyList()).thenReturn(companies);

        Map<Long, String> companyLogs = new HashMap<>();
        companyLogs.put(0L,"123123.log");
        when(companyLogConfigs.getCompanyLog()).thenReturn(companyLogs);


        // When
        List<?> result = companyService.getCompanyList();

        // Then
        assertThat(result).hasSize(2);
        assertThat(result.get(0)).isNotNull();
        assertThat(result.get(1)).isNotNull();
    }

    @Test
    void getCompanyList_shouldReturnEmptyList_whenMapperReturnsEmptyList() {
        // Given
        when(standardCompanyMapper.getCompanyList()).thenReturn(new ArrayList<>());

        Map<Long, String> companyLogs = new HashMap<>();
        companyLogs.put(0L,"123123.log");
        when(companyLogConfigs.getCompanyLog()).thenReturn(companyLogs);

        // When
        List<?> result = companyService.getCompanyList();

        // Then
        assertThat(result).isEmpty();
    }
}