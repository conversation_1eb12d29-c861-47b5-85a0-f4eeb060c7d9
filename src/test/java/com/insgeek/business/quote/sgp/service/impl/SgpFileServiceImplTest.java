package com.insgeek.business.quote.sgp.service.impl;

import com.insgeek.business.quote.sgp.dto.SavePriceOfferFilesReqDto;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.protocol.data.client.entity.QpQuotationInfo;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.util.*;

import static org.mockito.Mockito.*;
import static org.assertj.core.api.Assertions.*;

@ExtendWith(MockitoExtension.class)
@DisplayName("SGP文件服务实现测试")
public class SgpFileServiceImplTest {
    
    @Mock
    private DataMapper<QpQuotationInfo> quotationInfoDataMapper;

    @InjectMocks
    private SgpFileServiceImpl sgpFileService;

    @Test
    @DisplayName("保存报价文件_正常情况")
    void savePriceOfferFile_success() {
        // Given
        Long quotationInfoId = 1L;
        List<String> ossKeys = Arrays.asList("key1", "key2");
        SavePriceOfferFilesReqDto reqDto = new SavePriceOfferFilesReqDto()
            .setQuotationInfoId(quotationInfoId)
            .setOss_keys(ossKeys);

        QpQuotationInfo quotationInfo = new QpQuotationInfo();
        // 在测试方法内配置必要的stubbing
        when(quotationInfoDataMapper.entity(QpQuotationInfo.class)).thenReturn(quotationInfoDataMapper);
        when(quotationInfoDataMapper.selectOne(quotationInfoId, true)).thenReturn(quotationInfo);
        
        // When
        sgpFileService.savePriceOfferFile(reqDto);
        
        // Then
        verify(quotationInfoDataMapper, times(1)).selectOne(quotationInfoId, true);
        verify(quotationInfoDataMapper, times(1)).updateOne(quotationInfo, true);
        assertThat(quotationInfo.getPriceOfferFiles()).isEqualTo("key1,key2");
    }
    
}