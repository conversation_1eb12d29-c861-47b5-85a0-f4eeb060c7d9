package com.insgeek.business.quote.sgp.service.impl;

import com.insgeek.boot.web.pojo.dto.CurrencyAmount;
import com.insgeek.protocol.insurance.vo.special.SharedSpecialResponseVO;
import com.insgeek.protocol.data.client.entity.QpQuotationConfig;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import java.math.BigDecimal;
import java.util.*;
import static org.assertj.core.api.Assertions.assertThat;

@ExtendWith(MockitoExtension.class)
@DisplayName("Sgp报价服务单元测试")
class QuotationSgpServiceImplTest {

    @Mock
    private QuotationSgpServiceImpl quotationSgpService;

    @Test
    @DisplayName("计算价格总和-正常情况")
    void sumPriceAmountSimple_normalCase() {
        // Given
        List<QpQuotationConfig> configs = new ArrayList<>();
        configs.add(createConfig(BigDecimal.valueOf(100)));
        configs.add(createConfig(BigDecimal.valueOf(200)));

        // When
        CurrencyAmount result = QuotationSgpServiceImpl.sumPriceAmountSimple(configs);

        // Then
        assertThat(result.getAmount()).isEqualTo(BigDecimal.valueOf(300));
        assertThat(result.getCurrency()).isEqualTo("USD");
    }

    @Test
    @DisplayName("计算价格总和-空列表")
    void sumPriceAmountSimple_emptyList() {
        // Given
        List<QpQuotationConfig> configs = new ArrayList<>();
        QpQuotationConfig config = new QpQuotationConfig();
        config.setPrice(new CurrencyAmount(BigDecimal.ZERO, "USD"));
        configs.add(config);

        // When
        CurrencyAmount result = QuotationSgpServiceImpl.sumPriceAmountSimple(configs);

        // Then
        assertThat(result).isEqualTo(new CurrencyAmount(BigDecimal.ZERO, "USD"));
    }

    @Test
    @DisplayName("获取共享保额-空数据")
    void getSharedLimit_nullData() {
        // Given
        SharedSpecialResponseVO responseVO = new SharedSpecialResponseVO();
        responseVO.setIdList(new ArrayList<>());
        Map<String,Object> map = new HashMap<>();
        map.put("amount", "1");
        map.put("quota-1", "1");
        responseVO.setShareData(map);
        responseVO.setShareApiKey("1");
        responseVO.setRelationId("1");
        

        // When
        String result = quotationSgpService.getSharedLimit(responseVO);

        // Then
        assertThat(result).isNull();
    }

    // 辅助方法
    private QpQuotationConfig createConfig(BigDecimal amount) {
        QpQuotationConfig config = new QpQuotationConfig();
        config.setPrice(new CurrencyAmount(amount, "USD"));
        return config;
    }

    @Test
    @DisplayName("比较两个列表是否相等-相同引用")
    void isEqualIgnoreOrder_sameReference_returnsTrue() {
        // Given
        List<Long> list1 = Arrays.asList(1L, 2L, 3L);
        List<Long> list2 = list1;

        // When
        boolean result = QuotationSgpServiceImpl.isEqualIgnoreOrder(list1, list2);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("比较两个列表是否相等-null输入")
    void isEqualIgnoreOrder_nullInput_returnsFalse() {
        // Given
        List<Long> list1 = Arrays.asList(1L, 2L, 3L);
        List<Long> list2 = null;

        // When
        boolean result = QuotationSgpServiceImpl.isEqualIgnoreOrder(list1, list2);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("比较两个列表是否相等-大小不同")
    void isEqualIgnoreOrder_differentSize_returnsFalse() {
        // Given
        List<Long> list1 = Arrays.asList(1L, 2L, 3L);
        List<Long> list2 = Arrays.asList(1L, 2L);

        // When
        boolean result = QuotationSgpServiceImpl.isEqualIgnoreOrder(list1, list2);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("比较两个列表是否相等-元素相同顺序不同")
    void isEqualIgnoreOrder_sameElementsDifferentOrder_returnsTrue() {
        // Given
        List<Long> list1 = Arrays.asList(1L, 2L, 3L);
        List<Long> list2 = Arrays.asList(3L, 1L, 2L);

        // When
        boolean result = QuotationSgpServiceImpl.isEqualIgnoreOrder(list1, list2);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("比较两个列表是否相等-元素不同")
    void isEqualIgnoreOrder_differentElements_returnsFalse() {
        // Given
        List<Long> list1 = Arrays.asList(1L, 2L, 3L);
        List<Long> list2 = Arrays.asList(1L, 2L, 4L);

        // When
        boolean result = QuotationSgpServiceImpl.isEqualIgnoreOrder(list1, list2);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("检查列表是否在列表集合中-存在匹配")
    void isInListIgnoreOrder_exists_returnsTrue() {
        // Given
        List<Long> list1 = Arrays.asList(1L, 2L, 3L);
        List<List<Long>> list2 = Arrays.asList(
                Arrays.asList(4L, 5L),
                Arrays.asList(3L, 1L, 2L)
        );

        // When
        boolean result = QuotationSgpServiceImpl.isInListIgnoreOrder(list1, list2);

        // Then
        assertThat(result).isTrue();
    }

    @Test
    @DisplayName("检查列表是否在列表集合中-不存在匹配")
    void isInListIgnoreOrder_notExists_returnsFalse() {
        // Given
        List<Long> list1 = Arrays.asList(1L, 2L, 3L);
        List<List<Long>> list2 = Arrays.asList(
                Arrays.asList(4L, 5L),
                Arrays.asList(6L, 7L, 8L)
        );

        // When
        boolean result = QuotationSgpServiceImpl.isInListIgnoreOrder(list1, list2);

        // Then
        assertThat(result).isFalse();
    }

    @Test
    @DisplayName("检查列表是否在列表集合中-null集合")
    void isInListIgnoreOrder_nullList_returnsFalse() {
        // Given
        List<Long> list1 = Arrays.asList(1L, 2L, 3L);
        List<List<Long>> list2 = null;

        // When
        boolean result = QuotationSgpServiceImpl.isInListIgnoreOrder(list1, list2);

        // Then
        assertThat(result).isFalse();
    }
}