spring:
  cloud:
    nacos:
      discovery:
        metadata:
          group: discovery-guide-group
          version: 1.0
        server-addr: http://nacos.internal.insgeek
        register-enabled: false
        port: ${server.port}
      username: nacos
      password: nacos
      config:
        server-addr: http://nacos.internal.insgeek
        namespace: pro
        group: insgeek-business-quote
        prefix: ${spring.application.name}
        file-extension: yaml
        extension-configs:
          - data-id: insgeek-ops-common-pro.yaml
            group: insgeek-ops
            refresh: true
          - data-id: insgeek-business-insurance-duty-pro.yaml
            group: insgeek-business-insurance
            refresh: true
          - data-id: insgeek-business-quote-special-pro.yaml
            group: insgeek-business-quote
            refresh: true
          - data-id: ins-common-pro.yaml
            group: insgeek-business-common
            refresh: true
          - data-id: insgeek-ops-secretkey-pro.yaml
            group: insgeek-ops
            refresh: true
        shared-configs:
          - data-id: insgeek-ops-actuator-pro.yaml
            group: insgeek-ops
            refresh: true
        refresh-enabled: true
  profiles:
    active: pro

nacos:
  password: ${spring.cloud.nacos.password}
  server-addr: ${spring.cloud.nacos.discovery.server-addr}
  username: ${spring.cloud.nacos.username}

insgeek:
  rocketmq:
    access-id: ${ops.rockmq[0].access-id}
    access-key: ${ops.rockmq[0].access-key}
    account-endpoint: ${ops.rockmq[0].account-endpoint}
  mq:
    groupConfigs: