<included>

    <!-- 日志的格式-->
    <property name="LOG_PATTERN"
              value="[%.-5p %d{yyyy-MM-dd HH:mm:ss.SSS} %tid %C{1}:%L ${HOSTNAME:-myHost}:%t] [%X{extra}] %m%n"/>
    <!-- 日志的存放目录-->
    <property name="LOG_HOME" value="logs/"/>
    <appender name="business_file" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/business.log</file>
        <append>true</append>
        <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME}/business.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <maxFileSize>1GB</maxFileSize>
            <MaxHistory>30</MaxHistory>
            <totalSizeCap>30GB</totalSizeCap>
            <cleanHistoryOnStart>true</cleanHistoryOnStart>
        </rollingPolicy>
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
            <charset>UTF-8</charset>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
            <level>debug</level>
        </filter>
    </appender>

    <!-- 控制台日志输出-->
    <appender name="${CONSOLE_APPENDER}" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>
                [%d{yyyy-MM-dd HH:mm:ss.SSS,GMT+8}] %-5level [%t] [%c] - %msg%n
            </pattern>
        </layout>
    </appender>

    <springProfile name="uat,pro">
        <logger name="com.insgeek.business" level="info">
            <appender-ref ref="business_file"/>
        </logger>
    </springProfile>

    <springProfile name="fat,dev">
        <logger name="com.insgeek.business" level="debug">
            <appender-ref ref="business_file"/>
            <appender-ref ref="${CONSOLE_APPENDER}"/>
        </logger>
    </springProfile>

</included>
