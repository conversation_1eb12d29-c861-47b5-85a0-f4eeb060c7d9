<!doctype html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
          name="viewport">
    <meta content="ie=edge" http-equiv="X-UA-Compatible">
    <title>[HSBC GIFF] benefits-plus-international_group_insurance_fact_find_form</title>
    <style>
        body {
            width: 1240px;
            font-size: 18px;
            text-align: justify;
            margin: 0 auto;
        }

        * {
            box-sizing: border-box;
        }

        h2 {
            font-size: 18px;
        }

        h2 .fr {
            width: 85%;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
        }

        table.layout-auto {
            table-layout: auto;
        }

        table.border td,
        table.border th {
            border: 1px solid black;
        }

        table.cell-height td,
        table.cell-height th {
            height: 2em;
            box-sizing: border-box;
        }

        table.padding td,
        table.padding th {
            padding: 0.5em 1em;
        }

        table.padding-large td,
        table.padding-large th {
            padding: 1em;
        }

        table.padding-small td,
        table.padding-small th {
            padding: 0.25em 0.5em;
        }

        table.padding-x td,
        table.padding-x th {
            padding: 0 1em;
        }

        table.padding-x-small td,
        table.padding-x-small th {
            padding: 0 0.5em;
        }

        table.padding-y td,
        table.padding-y th {
            padding: 0.5em 0;
        }

        table.padding-y-large td,
        table.padding-y-large th {
            padding: 1em 0;
        }

        table.no-padding td,
        table.no-padding th {
            padding: 0 !important;
        }

        hr {
            margin: 0 0 2px;
            border-bottom: 2px solid black;
        }

        ul {
            list-style: none;
            padding: 0;
        }


        .text-center {
            text-align: center;
        }

        .text-right {
            text-align: right;
        }

        .text-left {
            text-align: left;
        }

        .block {
            display: block;
        }

        .m-auto {
            margin: auto;
        }

        .p-0 {
            padding: 0 !important;
        }

        .py-0 {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
        }

        .px-0 {
            padding-left: 0 !important;
            padding-right: 0 !important;
        }

        .pl-0 {
            padding-left: 0 !important;
        }

        .pl-1 {
            padding-left: 1rem !important;
        }

        .pr-1 {
            padding-right: 1rem !important;
        }

        .pl-2 {
            padding-left: 2rem !important;
        }

        .pr-0 {
            padding-right: 0 !important;
        }

        .pr-2 {
            padding-right: 2rem !important;
        }

        .pl-3 {
            padding-left: 3rem !important;
        }

        .pr-3 {
            padding-right: 3rem !important;
        }

        .m-0 {
            margin: 0 !important;
        }

        .m-1 {
            margin: 1rem;
        }

        .m-2 {
            margin: 2rem;
        }

        .m-4 {
            margin: 4rem;
        }

        .m-8 {
            margin: 8rem;
        }

        .m-6 {
            margin: 6rem;
        }

        .m-8 {
            margin: 8rem;
        }

        .m-10 {
            margin: 10rem;
        }

        .mb-1 {
            margin-bottom: 1rem;
        }

        .mb-2 {
            margin-bottom: 2rem;
        }

        .mb-4 {
            margin-bottom: 4rem;
        }

        .mb-6 {
            margin-bottom: 6rem;
        }

        .mb-8 {
            margin-bottom: 8rem;
        }

        .mb-10 {
            margin-bottom: 10rem;
        }

        .mt-1 {
            margin-top: 1rem;
        }

        .mt-2 {
            margin-top: 2rem;
        }

        .mt-4 {
            margin-top: 4rem;
        }

        .mt-6 {
            margin-top: 6rem;
        }

        .mt-8 {
            margin-top: 8rem;
        }

        .mt-10 {
            margin-top: 10rem;
        }

        .mx-0 {
            margin-left: 0 !important;
            margin-right: 0 !important;
        }

        .mx-1 {
            margin-left: 1rem;
            margin-right: 1rem;
        }

        .mx-2 {
            margin-left: 2rem;
            margin-right: 2rem;
        }

        .mx-4 {
            margin-right: 4rem;
            margin-left: 4rem;
        }

        .my-0 {
            margin-top: 0 !important;
            margin-bottom: 0 !important;
        }

        .my-1 {
            margin-top: 1rem;
            margin-bottom: 1rem;
        }

        .my-2 {
            margin-top: 2rem;
            margin-bottom: 2rem;
        }

        .my-4 {
            margin-top: 4rem;
            margin-bottom: 4rem;
        }

        .center-wrapper {
            display: block;
            text-align: center;
        }

        .center-wrapper > * {
            display: inline-block;
            text-align: left;
        }

        .clear::after {
            content: "";
            display: table;
            clear: both;
        }

        .clear {
            zoom: 1;
        }

        .fl {
            float: left;
        }

        .fr {
            float: right;
        }

        .border {
            border: 1px solid black;
        }

        .border-bold {
            border-width: 2px !important;
        }

        .border-bottom {
            border-bottom: 2px solid black;
        }

        .border-none {
            border: none !important;
        }

        .font-small {
            font-size: smaller;
        }

        .italic {
            font-style: italic;
        }

        .align-top {
            vertical-align: top;
        }

        .align-middle {
            vertical-align: middle;
        }

        .nowrap {
            white-space: nowrap;
        }

        .wrap {
            white-space: normal;
        }

        .dark-bg {
            background-color: #000000;
            color: #fff;
            padding: 10px;
        }

        .font-bold {
            font-weight: bold;
        }

        .caption-bottom {
            caption-side: bottom;
        }

        .inline-block {
            display: inline-block;
        }

        .inline {
            display: inline;
        }

        .relative {
            position: relative;
        }

        .absolute {
            position: absolute;
        }

        .overflow-hidden {
            overflow: hidden;
        }

        .border-box {
            box-sizing: border-box;
        }

        .underline {
            text-decoration: underline;
            text-decoration-thickness: 2px;
        }
    </style>
    <style>
        .page {
            height: 1810px;
            width: 1120px;
            margin: 0 auto;
            box-sizing: border-box;
            padding: 8rem 0 10rem;
            position: relative;
        }

        .logo {
            width: 205px;
            height: 64px;
            background-image: url("data:image/png;base64,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");
            background-repeat: no-repeat;
            background-size: 100% 100%;
        }

        .footer {
            position: absolute;
            bottom: 2rem;
            right: 0;
            white-space: nowrap;
        }

        .header {
            position: absolute;
            left: -1em;
            top: 0;
        }

        .input-bottom-text {
            position: relative;
            margin-bottom: 1em;
        }

        .input-bottom-text .absolute {
            position: absolute;
            top: 102%
        }
    </style>
</head>
<body>
<!--第一页-->
<div class="page">
    <!--    header-->
    <div class="header">
        <div class="logo"></div>
    </div>
    <h1 class="text-center" style="font-size: 1.2em">Group Insurance Fact-finding Form</h1>
    <div class="overflow-hidden">
        <table class="nowrap padding">
            <tr>
                <td>
                    <p class="m-0 font-bold">KINDLY COMPLETE FULLY IN BLOCK LETTER AND INK</p>
                    <p class="m-0">(Tick boxes [ √ ] where appropriate)</p>
                </td>
            </tr>
            <tr>
                <td>
                    <span class="font-bold">PERIOD OF INSURANCE</span>
                    <span>from:</span>
                    <div class="inline-block">
                        <div class="inline-block input-bottom-text text-center">
                            <span class="inline-block border-bottom" style="width: 23em">&emsp;  ${baseInfo.expectStartTime!}</span>
                            <span class="absolute" style="left: 9em;">(dd/mm/yyyy)</span>
                        </div>
                    </div>
                    <span>to</span>
                    <div class="inline-block">
                        <div class="inline-block input-bottom-text text-center">
                            <span class="inline-block border-bottom" style="width: 23em">&emsp; ${baseInfo.expectEndTime!}</span>
                            <span class="absolute" style="left: 9em;">(dd/mm/yyyy)</span>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <span class="font-bold">REQUEST FOR QUOTATION</span>
                    <span>was submitted on</span>
                    <div class="inline-block">
                        <div class="inline-block input-bottom-text text-center">
                            <span class="inline-block border-bottom" style="width: 40em">&emsp;  ${baseInfo.quoteTime!}</span>
                            <span class="absolute" style="left: 17em;">(dd/mm/yyyy)</span>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <span class="font-bold">REQUEST FROM:</span>
                    <div class="inline-block">
                        <div class="inline-block input-bottom-text">
                            <span class="inline-block border-bottom wrap pl-1 text-center" style="width: 55em; padding-right: 6em;">&emsp;</span>
                            <span class="absolute" style="left: 15em;">(Name of Insurance Company)</span>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <td class="font-bold">GENERAL INFORMATION</td>
            </tr>
            <tr>
                <td>
                    <span>Name of Company:</span>
                    <span class="inline-block border-bottom wrap pl-1"
                          style="width: 52em; padding-right: 5em;">&emsp;   ${baseInfo.companyName!}</span>
                </td>
            </tr>
            <tr>
                <td>
                    <span>Nature of Business:</span>
                    <span class="inline-block border-bottom wrap pl-1"
                          style="width: 52em; padding-right: 6em;">&emsp;  ${baseInfo.companyNature!}</span>
                </td>
            </tr>
            <tr>
                <td>
                    <span>Presently insured?</span>
                        <#if baseInfo.presentlyInsured == "1">
                            <strong style="padding-left: 3em;">Yes <input type="checkbox" checked> / No <input type="checkbox"></strong>
                        <#else>
                            <strong style="padding-left: 3em;">No <input type="checkbox">
                        </#if>
                </td>
            </tr>
            <tr>
                <td>
                    <span>If <strong>Yes</strong>, name of current insurer:</span>
                    <span class="inline-block border-bottom wrap pl-1"
                          style="width: 47em; padding-right: 4em;">&emsp;  ${baseInfo.currentEbInsurer!} </span>
                </td>
            </tr>
            <tr>
                <td>
                    <span>Type of Policy:</span>
                    <span class="inline-block border-bottom wrap pl-1"
                          style="width: 54em; padding-right: 4em;">&emsp;</span>
                </td>
            </tr>
            <tr>
                <td>
                    <span>Period of Insurance: </span>
                    <span>From:</span>
                    <div class="inline-block">
                        <div class="inline-block input-bottom-text text-center">
                            <span class="inline-block border-bottom" style="width: 24em">&emsp; ${baseInfo.insuranceStartTime!}</span>
                            <span class="absolute" style="left: 9em;">(dd/mm/yyyy)</span>
                        </div>
                    </div>
                    <span>To</span>
                    <div class="inline-block">
                        <div class="inline-block input-bottom-text text-center">
                            <span class="inline-block border-bottom" style="width: 24em">&emsp;  ${baseInfo.insuranceEndTime!}</span>
                            <span class="absolute" style="left: 9em;">(dd/mm/yyyy)</span>
                        </div>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <span>Total No. of Employees: </span>
                    <span class="inline-block border-bottom" style="width: 14em">&emsp;  ${baseInfo.totalEmployees!}</span>
                    <span>No. of Employees to be insured:</span>
                    <span class="inline-block border-bottom" style="width: 22em">&emsp;  ${baseInfo.insuredEmployees!}</span>
                </td>
            </tr>
            <tr>
                <td class="wrap">
                    <strong>Participation: The insurer will assume that participation of the group insurance programme is on compulsory basis unless otherwise stated. </strong> Please tick [√ ] accordingly to the choice of the insurance product that you like to have a quote from us.
                </td>
            </tr>
        </table>
        <table class="border padding-small mt-1 text-left" style="width: 98%; margin-left: auto; margin-right: 0">
            <colgroup>
                <col style="width: 6em;">
                <col style="width: 2em;">
                <col style="width: 10em;">
                <col>
                <col span="2" style="width: 7em;">
            </colgroup>
            <tr>
                <th rowspan="2">Benefits</th>
                <th colspan="3" rowspan="2">Insurance Coverage</th>
                <th class="text-center" colspan="2">Participation</th>
            </tr>
            <tr>
                <th class="text-center">Compulsory</th>
                <th class="text-center">Voluntary</th>
            </tr>
            <tr>
                <th rowspan="4">Life Insurance</th>
                <th rowspan="3">1</th>
                <td colspan="2">Group Term Life (GTL)</td>
                <#-- 默认两个框都不勾 -->
                <#assign checked1 = false>
                <#assign checked2 = false>
                <#assign hasBenefit = false>
                <#list userPlanDutyList as item>
                    <#if item.benefit == "GTL">
                        <#assign hasBenefit = true>
                        <#if item.participation == "2">
                            <#assign checked2 = true>
                        <#else>
                            <#assign checked1 = true>
                        </#if>
                    </#if>
                </#list>
                <#-- 如果没有该险种，则两个框都不勾 -->
                <#if hasBenefit && !checked2>
                    <#assign checked1 = true>
                </#if>
                <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <tr>
                <td colspan="2">Group Personal Accident (GPA)</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GPA">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <tr>
                <td colspan="2">Group Critical Illness (GCI)</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GCI">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <tr>
                <th>2</th>
                <td colspan="2">Group Disability Income (GDI)</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GDI">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <tr>
                <th rowspan="4">Medical</th>
                <th rowspan="4">3</th>
                <td rowspan="2">Group Hospital & Surgical (GHS)</td>
                <td>Employee only</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GHS">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <tr>
                <td>Dependant (Spouse and/or Children)</td>
                <td class="text-center" ><input type="checkbox"  id="myCheckbox1" name="myCheckbox1"></td>
                <td class="text-center" ><input type="checkbox"  id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <tr>
                <td rowspan="2">Group Major Medical (GMM)</td>
                <td>Employee only</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GMM">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <tr>
                <td>Dependant (Spouse and/or Children)</td>
                    <td class="text-center"><input type="checkbox"  id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center" ><input type="checkbox"  id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <tr>
                <th rowspan="6">Others</th>
                <th rowspan="4">4</th>
                <td rowspan="2">Group Outpatient</td>
                <td>Employee only</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>
                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GP">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <tr>
                <td>Dependant (Spouse and/or Children)</td>
                    <td class="text-center"><input type="checkbox"  id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center" ><input type="checkbox"  id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <tr>
                <td rowspan="2">Dental</td>
                <td>Employee only</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GD">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <tr>
                <td>Dependant (Spouse and/or Children)</td>
                    <td class="text-center"><input type="checkbox"  id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center" ><input type="checkbox"  id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <tr>
                <th rowspan="2">5</th>
                <td rowspan="2">Maternity</td>
                <td>Employee only</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GM">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <tr>
                <td>Dependant (Spouse)</td>
                    <td class="text-center"><input type="checkbox"  id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center" ><input type="checkbox"  id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <caption class="caption-bottom italic text-left">
                Note: Participation is voluntary if employees or dependants are given the choice to opt for the
                cover(s), subject
                to a minimum participation level.
            </caption>
        </table>
    </div>
    <!--    footer-->
    <div class="footer">
        <small>HSBC Life Ver. 1.0</small>
    </div>
</div>

<!--第二页-->
<div class="page">
    <!--    header-->
    <div class="header">
        <div class="logo"></div>
    </div>
    <table class="padding">
        <!--         1-->
        <tr>
            <th class="align-top" style="width: 2em;">1</th>
            <td>
                Are there any members currently in hospital or requires frequent admission (e.g. hospital admission more
                than 2 times per year) to hospital?
                <#assign showFlg = 0>
                <#list healthInfo as info>
                    <#if info.disclosureType=="1">
                        <#assign showFlg = 1>
                    </#if>
                </#list>
                <#if showFlg == 1>
                    <strong style="padding-left: 2em;">Yes <input type="checkbox" checked> / No <input type="checkbox"></strong>
                <#else>
                    <strong style="padding-left: 2em;">Yes <input type="checkbox"> / No <input type="checkbox" checked></strong>
                </#if>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>If <strong>Yes</strong>, kindly provide the following details:</td>
        </tr>
        <tr>
            <td></td>
            <td>
                <table class="border padding-small text-center cell-height">
                    <tr>
                        <th style="width: 4em;">S/N</th>
                        <th style="width: 25%;"># of members / Age</th>
                        <th>Reason of hospitalisation / Nature of illness</th>
                        <th style="width: 8em">Total Sum Insured / Plan</th>
                    </tr>
                    <#assign displayedCount = 0>
                    <#list healthInfo as info>
                        <#if info.disclosureType=="1">
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${displayedCount + 1}</td>
                                    <td>${info.number!""}</td>
                                    <td>${info.content!""}</td>
                                    <td>${(info.totalSumInsured.amount)!""}</td>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="4" class="text-left italic">
                                        More information, please refer to the attachment.
                                    </td>
                                </tr>
                                <#break>
                            </#if>
                        </#if>
                    </#list>
                    <#list 1..(3 - displayedCount) as i>
                        <#if displayedCount!=0 && displayedCount!=3>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                    </#list>
                    <#if displayedCount == 0 >
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#if>
                    <tr>
                        <td class="text-left italic" colspan="4">Note: The insurer will not reimburse the hospital
                            claims for any member in hospital at the time of application.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <!--         2-->
        <tr>
            <th class="align-top" style="width: 2em;">2</th>
            <td>
                Has any member suffered or is suffering from any serious condition such as cancer, organ failure, heart <br>
                disease, stroke, liver disorder, arthritis or any other disorder that causes progressive irreversible functional <br>
                or physical disability?
                    <#assign showFlg = 0>
                    <#list healthInfo as info>
                        <#if info.disclosureType=="2">
                            <#assign showFlg = 1>
                        </#if>
                    </#list>
                    <#if showFlg == 1>
                        <strong style="padding-left: 2em;">Yes <input type="checkbox" checked> / No <input type="checkbox"></strong>
                    <#else>
                        <strong style="padding-left: 2em;">Yes <input type="checkbox"> / No <input type="checkbox" checked></strong>
                    </#if>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>If <strong>Yes</strong>, kindly provide the following details:</td>
        </tr>
        <tr>
            <td></td>
            <td>
                <table class="border padding-small text-center cell-height">
                    <tr>
                        <th style="width: 4em;">S/N</th>
                        <th style="width: 25%;"># of members / Age</th>
                        <th>Reason of hospitalisation / Nature of illness</th>
                        <th style="width: 8em">Total Sum Insured / Plan</th>
                    </tr>
                        <#assign displayedCount = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="2">
                                <#if displayedCount < 3>
                                    <tr>
                                        <td>${displayedCount + 1}</td>
                                        <td>${info.number!""}</td>
                                        <td>${info.content!""}</td>
                                        <td>${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="4" class="text-left italic">
                                            More information, please refer to the attachment.
                                        </td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#if>
                        </#list>
                        <#list 1..(3 - displayedCount) as i>
                            <#if displayedCount!=0 && displayedCount!=3>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#if>
                        </#list>
                        <#if displayedCount == 0 >
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                    <tr>
                        <td class="text-left italic" colspan="4">Note: The insurer will not reimburse the hospital
                            claims for any member in hospital at the time of application.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <!--         3-->
        <tr>
            <th class="align-top" style="width: 2em;">3</th>
            <td>
                Is there any member based outside Singapore?
                    <#assign showFlg = 0>
                    <#list healthInfo as info>
                        <#if info.disclosureType=="3">
                            <#assign showFlg = 1>
                        </#if>
                    </#list>
                    <#if showFlg == 1>
                        <strong style="padding-left: 2em;">Yes <input type="checkbox" checked> / No <input type="checkbox"></strong>
                    <#else>
                        <strong style="padding-left: 2em;">Yes <input type="checkbox"> / No <input type="checkbox" checked></strong>
                    </#if>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>If <strong>Yes</strong>, kindly provide the following details:</td>
        </tr>
        <tr>
            <td></td>
            <td>
                <table class="border padding-small text-center cell-height">
                    <tr>
                        <th style="width: 4em;">S/N</th>
                        <th># of members / Age</th>
                        <th>Country based in</th>
                        <th style="width: 8em">Total Sum Insured / Plan</th>
                    </tr>
                        <#assign displayedCount = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="3">
                                <#if displayedCount < 3>
                                    <tr>
                                        <td>${displayedCount + 1}</td>
                                        <td>${info.number!""}</td>
                                        <td>${info.content!""}</td>
                                        <td>${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="4" class="text-left italic">
                                            More information, please refer to the attachment.
                                        </td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#if>
                        </#list>
                        <#list 1..(3 - displayedCount) as i>
                            <#if displayedCount!=0 && displayedCount!=3>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#if>
                        </#list>
                        <#if displayedCount == 0 >
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                    <tr>
                        <td class="text-left italic" colspan="4">Note: The insurer will not reimburse the hospital
                            claims for any member in hospital at the time of application.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <!--    footer-->
    <div class="footer">
        <small>HSBC Life Ver. 1.0</small>
    </div>
</div>

<!--第三页-->
<div class="page">
    <!--    header-->
    <div class="header">
        <div class="logo"></div>
    </div>
    <table class="padding">
        <!--         4-->
        <tr>
            <th class="align-top" style="width: 2em;">4</th>
            <td>
                Are there any limitations or exclusions imposed on the coverage on any members?
                    <#assign showFlg = 0>
                    <#list healthInfo as info>
                        <#if info.disclosureType=="4">
                            <#assign showFlg = 1>
                        </#if>
                    </#list>
                    <#if showFlg == 1>
                        <strong style="padding-left: 2em;">Yes <input type="checkbox" checked> / No <input type="checkbox"></strong>
                    <#else>
                        <strong style="padding-left: 2em;">Yes <input type="checkbox"> / No <input type="checkbox" checked></strong>
                    </#if>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>If <strong>Yes</strong>, kindly provide the following details:</td>
        </tr>
        <tr>
            <td></td>
            <td>
                <table class="border padding-small text-center cell-height">
                    <tr>
                        <th style="width: 4em;">S/N</th>
                        <th># of members / Age</th>
                        <th>Limitations / Exclusions</th>
                        <th style="width: 8em">Total Sum Insured / Plan</th>
                    </tr>
                        <#assign displayedCount = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="4">
                                <#if displayedCount < 3>
                                    <tr>
                                        <td>${displayedCount + 1}</td>
                                        <td>${info.number!""}</td>
                                        <td>${info.content!""}</td>
                                        <td>${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="4" class="text-left italic">
                                            More information, please refer to the attachment.
                                        </td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#if>
                        </#list>
                        <#list 1..(3 - displayedCount) as i>
                            <#if displayedCount!=0 && displayedCount!=3>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#if>
                        </#list>
                        <#if displayedCount == 0 >
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                    <tr>
                        <td class="text-left italic" colspan="4">Note: The insurer will not reimburse the hospital
                            claims for any member in hospital at the time of application.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <!--         5-->
        <tr>
            <th class="align-top" style="width: 2em;">5</th>
            <td>
                <div>
                    Is there any member engaged in hazardous occupation?
                        <#assign showFlg = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="5">
                                <#assign showFlg = 1>
                            </#if>
                        </#list>
                        <#if showFlg == 1>
                            <strong style="padding-left: 2em;">Yes <input type="checkbox" checked> / No <input type="checkbox"></strong>
                        <#else>
                            <strong style="padding-left: 2em;">Yes <input type="checkbox"> / No <input type="checkbox" checked></strong>
                        </#if>
                </div>
                <div>(Hazardous occupation eg. welder, diver, sandblaster, offshore workers etc.)</div>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>If <strong>Yes</strong>, kindly provide the following details:</td>
        </tr>
        <tr>
            <td></td>
            <td>
                <table class="border padding-small text-center cell-height">
                    <tr>
                        <th style="width: 4em;">S/N</th>
                        <th># of members / Age</th>
                        <th>Nature of work</th>
                        <th style="width: 8em">Total Sum Insured / Plan</th>
                    </tr>
                        <#assign displayedCount = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="5">
                                <#if displayedCount < 3>
                                    <tr>
                                        <td>${displayedCount + 1}</td>
                                        <td>${info.number!""}</td>
                                        <td>${info.content!""}</td>
                                        <td>${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="4" class="text-left italic">
                                            More information, please refer to the attachment.
                                        </td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#if>
                        </#list>
                        <#list 1..(3 - displayedCount) as i>
                            <#if displayedCount!=0 && displayedCount!=3>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#if>
                        </#list>
                        <#if displayedCount == 0 >
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                    <tr>
                        <td class="text-left italic" colspan="4">Note: The insurer will not reimburse the hospital
                            claims for any member in hospital at the time of application.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <!--         6-->
        <tr>
            <th class="align-top" style="width: 2em;">6</th>
            <td>
                <div>
                    To the best of your knowledge, is there any member engaged in hazardous sports?
                        <#assign showFlg = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="6">
                                <#assign showFlg = 1>
                            </#if>
                        </#list>
                        <#if showFlg == 1>
                            <strong style="padding-left: 2em;">Yes <input type="checkbox" checked> / No <input type="checkbox"></strong>
                        <#else>
                            <strong style="padding-left: 2em;">Yes <input type="checkbox"> / No <input type="checkbox" checked></strong>
                        </#if>
                </div>
                <div>
                    (Hazardous sports eg. scuba diving, motor racing, bungee jumping etc.)
                </div>
            </td>
        </tr>
        <tr>
            <td></td>
            <td>If <strong>Yes</strong>, kindly provide the following details:</td>
        </tr>
        <tr>
            <td></td>
            <td>
                <table class="border padding-small text-center cell-height">
                    <tr>
                        <th style="width: 4em;">S/N</th>
                        <th># of members / Age</th>
                        <th>Type of sports</th>
                        <th style="width: 8em">Total Sum Insured / Plan</th>
                    </tr>
                        <#assign displayedCount = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="6">
                                <#if displayedCount < 3>
                                    <tr>
                                        <td>${displayedCount + 1}</td>
                                        <td>${info.number!""}</td>
                                        <td>${info.content!""}</td>
                                        <td>${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="4" class="text-left italic">
                                            More information, please refer to the attachment.
                                        </td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#if>
                        </#list>
                        <#list 1..(3 - displayedCount) as i>
                            <#if displayedCount!=0 && displayedCount!=3>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#if>
                        </#list>
                        <#if displayedCount == 0 >
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                    <tr>
                        <td class="text-left italic" colspan="4">Note: The insurer will not reimburse the hospital
                            claims for any member in hospital at the time of application.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <!--    footer-->
    <div class="footer">
        <small>HSBC Life Ver. 1.0</small>
    </div>
</div>

<!--第四页-->
<div class="page">
    <!--    header-->
    <div class="header">
        <div class="logo"></div>
    </div>
    <table class="padding">
        <colgroup>
            <col span="2" style="width: 2em;">
            <col>
        </colgroup>
        <tr>
            <th class="align-top" style="width: 2em;">1.</th>
            <th class="underline" colspan="2">BENEFIT: GROUP TERM LIFE / GROUP PERSONAL ACCIDENT / GROUP CRITICAL ILLNESS INSURANCE</th>
        </tr>
        <tr>
            <td></td>
            <th colspan="2">Occupational Classifications</th>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                <table class="border padding-small">
                    <tr>
                        <td style="width: 6em;">Class 1</td>
                        <td>Clerical, administrative or other similar non-hazardous occupations</td>
                    </tr>
                    <tr>
                        <td>Class 2</td>
                        <td>Occupations where some degree of risk is involved, e.g. supervision of manual workers,
                            totally
                            administrative job in an industrial environment
                        </td>
                    </tr>
                    <tr>
                        <td>Class 3</td>
                        <td>Occupations involving regular light to medium manual work but no substantial hazard which
                            may increase the risk of sickness or accident
                        </td>
                    </tr>
                    <tr>
                        <td>Class 4</td>
                        <td>High risk occupations involving heavy manual work including hot works</td>
                    </tr>
                </table>
            </td>
        </tr>
        <!--        a)-->
        <tr>
            <td></td>
            <td>a)</td>
            <td>Basis of Coverage</td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                <table class="border border-none padding-small text-center cell-height">
                    <colgroup>
                        <col style="width: 4em">
                        <col style="width: 3em">
                        <col span="2">
                        <col style="width: 8em">
                    </colgroup>
                    <tr>
                        <th class="border-none" colspan="2"></th>
                        <th>Category of Employees/Occupation (refer to the examples)</th>
                        <th>Basis of Coverage – Sum Insured (refer to the examples)</th>
                        <th style="width: 8em"># of Employees</th>
                    </tr>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "gtl" && personOverview.itemKey == "basis_of_coverage">
                            <#assign total = personOverview.valueMaps?size />
                            <#list 0..3 as i>
                                <tr>
                                    <#if i == 0>
                                        <th rowspan="4">GTL</th>
                                    </#if>
                                    <td>
                                        <#if i == 0>(i)
                                        <#elseif i == 1>(ii)
                                        <#elseif i == 2>(iii)
                                        <#elseif i == 3>(iv)
                                        </#if>
                                    </td>
                                    <#if i < total>
                                        <#assign info = personOverview.valueMaps[i] />
                                        <td>${info.category_of_employees_occupation}</td>
                                        <td>${info.no_of_employees}</td>
                                        <td>${info.basis_of_coverage_sum_insured}</td>
                                    <#else>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </#if>
                                </tr>
                            </#list>
                            <#if total gt 4>
                                <tr>
                                    <td colspan="5" class="text-left italic">
                                        More information, please refer to the attachment.
                                    </td>
                                </tr>
                            </#if>
                        </#if>
                    </#list>
                    <tr>
                        <td colspan="5" style="border: none;"></td>
                    </tr>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gpa" && personOverview.itemKey == "basis_of_coverage">
                                <#assign total = personOverview.valueMaps?size />
                                <#list 0..3 as i>
                                    <tr>
                                        <#if i == 0>
                                            <th rowspan="4">GPA</th>
                                        </#if>
                                        <td>
                                            <#if i == 0>(i)
                                            <#elseif i == 1>(ii)
                                            <#elseif i == 2>(iii)
                                            <#elseif i == 3>(iv)
                                            </#if>
                                        </td>
                                        <#if i < total>
                                            <#assign info = personOverview.valueMaps[i] />
                                            <td>${info.category_of_employees_occupation}</td>
                                            <td>${info.no_of_employees}</td>
                                            <td>${info.basis_of_coverage_sum_insured}</td>
                                        <#else>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </#if>
                                    </tr>
                                </#list>
                                <#if total gt 4>
                                    <tr>
                                        <td colspan="5" class="text-left italic">
                                            More information, please refer to the attachment.
                                        </td>
                                    </tr>
                                </#if>
                            </#if>
                        </#list>
                    <tr>
                        <td colspan="5" style="border: none;"></td>
                    </tr>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "gci" && personOverview.itemKey == "basis_of_coverage">
                            <#assign total = personOverview.valueMaps?size />
                            <#list 0..3 as i>
                                <tr>
                                    <#if i == 0>
                                        <th rowspan="4">GCI</th>
                                    </#if>
                                    <td>
                                        <#if i == 0>(i)
                                        <#elseif i == 1>(ii)
                                        <#elseif i == 2>(iii)
                                        <#elseif i == 3>(iv)
                                        </#if>
                                    </td>
                                    <#if i < total>
                                        <#assign info = personOverview.valueMaps[i] />
                                        <td>${info.category_of_employees_occupation}</td>
                                        <td>${info.no_of_employees}</td>
                                        <td>${info.basis_of_coverage_sum_insured}</td>
                                    <#else>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </#if>
                                </tr>
                            </#list>
                            <#if total gt 4>
                                <tr>
                                    <td colspan="5" class="text-left italic">
                                        More information, please refer to the attachment.
                                    </td>
                                </tr>
                            </#if>
                        </#if>
                    </#list>
                </table>
            </td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                <table class="no-padding text-left">
                    <tr>
                        <th class="underline" style="padding-bottom: 0;">Example 1</th>
                        <td style="width: 12.5em;"></td>
                    </tr>
                    <tr>
                        <th>Category of Employees / Occupation</th>
                        <th>Basis of Coverage</th>
                    </tr>
                    <tr>
                        <td>(i) &nbsp; Senior Management (Director, General Manager, Senior Manager)</td>
                        <td>100,000</td>
                    </tr>
                    <tr>
                        <td>(ii) &nbsp; Manager & Executive</td>
                        <td>50,000</td>
                    </tr>
                    <tr>
                        <td>(iii)&nbsp; All Others</td>
                        <td>25,000</td>
                    </tr>
                    <tr>
                        <th class="underline" style="padding-top: 1em !important;">Example 2</th>
                    </tr>
                    <tr>
                        <th>Category of Employees / Occupation</th>
                        <th>Basis of Coverage</th>
                    </tr>
                    <tr>
                        <td>(i) &nbsp; All Employees</td>
                        <td>24X Basic Monthly Salary*</td>
                    </tr>
                    <caption class="italic text-left caption-bottom" style="padding-top: 1em;">
                        * Please provide salary information if the basis of coverage is in terms of basic monthly
                        salary.
                    </caption>
                </table>
            </td>
        </tr>
    </table>
    <!--    footer-->
    <div class="footer">
        <small>HSBC Life Ver. 1.0</small>
    </div>
</div>

<!--第五页-->
<div class="page">
    <!--    header-->
    <div class="header">
        <div class="logo"></div>
    </div>
    <table class="padding text-left">
        <colgroup>
            <col span="2" style="width: 2em;">
            <col>
        </colgroup>
        <!--        b)-->
        <tr>
            <td></td>
            <td>b)</td>
            <td>Please provide Current Non-Medical Limit (if applicable)</td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                <span class="inline-block" style="width: 12em;">Group Term Life: </span>
                <span>S$  </span>
                <span class="inline-block border-bottom" style="width: 14em">&emsp;
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "gtl" && personOverview.itemKey == "current_non_medical_limit">
                            ${personOverview.value!}
                        </#if>
                    </#list>
                </span>
                <span>up to age</span>
                <span class="inline-block border-bottom" style="width: 12em">&emsp;
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "gtl" && personOverview.itemKey == "up_to_age">
                            ${personOverview.value!}
                        </#if>
                    </#list>
                </span>
            </td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                <span class="inline-block" style="width: 12em;">Group Critical Illness: </span>
                <span>S$</span>
                <span class="inline-block border-bottom" style="width: 14em">&emsp;
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "gci" && personOverview.itemKey == "current_non_medical_limit">
                            ${personOverview.value!}
                        </#if>
                    </#list>
                </span>
                <span>up to age</span>
                <span class="inline-block border-bottom" style="width: 12em">&emsp;
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "gci" && personOverview.itemKey == "up_to_age">
                            ${personOverview.value!}
                        </#if>
                    </#list>

                </span>
            </td>
        </tr>
        <!--        c)-->
        <tr>
            <td></td>
            <td>c)</td>
            <td>Group Critical Illness: Basis of Coverage</td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                Is this benefit an advance of or an additional amount to the Term Life?
                <span class="inline-block border-bottom" style="width: 11em;">
                    <#assign personOverviewValue = "">
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "gci" && personOverview.itemKey == "amount_to_the_term_life_option">
                            <#assign personOverviewValue = (personOverview.value)!"" >
                            ${personOverviewValue}
                        </#if>
                    </#list>
                </span>
            </td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                If it is an advance benefit, what percentage on the Term Life sum insured do you want us to
                quote? Please circle as appropriate:
                <#assign personOverviewValue = "">
                <#list personOverviews as personOverview>
                    <#if personOverview.tag == "gci" && personOverview.itemKey == "acceleration_percentage_on_sum_assured">
                        <#assign personOverviewValue = (personOverview.value)!"" >
                        ${personOverviewValue}
                    </#if>
                </#list>
            </td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>Please provide a list of critical illnesses covered (if currently insured).</td>
        </tr>
        <!--        d)-->
        <tr>
            <td></td>
            <td>d)</td>
            <td>Details of Employees</td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                <table class="border border-none text-center">
                    <colgroup>
                        <col span="9" style="width: 11%;">
                    </colgroup>
                    <tr>
                        <th class="border-none"></th>
                        <th colspan="4">GTL</th>
                        <th colspan="4">GCI</th>
                    </tr>
                    <tr>
                        <th rowspan="2">Age Band (Age Next Birthday)</th>
                        <th colspan="2"># of Employees</th>
                        <th colspan="2">Total Sum Insured (S$)</th>
                        <th colspan="2"># of Employees</th>
                        <th colspan="2">Total Sum Insured (S$)</th>
                    </tr>
                    <tr>
                        <th>Male</th>
                        <th>Female</th>
                        <th>Male</th>
                        <th>Female</th>
                        <th>Male</th>
                        <th>Female</th>
                        <th>Male</th>
                        <th>Female</th>
                    </tr>
                        <#assign hasData6= false>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag=="gtl" && personOverview.itemKey=="age_profile_of_employees">
                                <#assign valueMapList = personOverview.value?eval>
                                <#list valueMapList  as info>
                                    <#if info.age_band??>
                                        <#assign hasData6= true>
                                        <tr>
                                            <th>${info.age_band!}</th>
                                            <td>${info.no_of_employees_male!}</td>
                                            <td>${info.no_of_employees_female!}</td>
                                            <td>${info.total_sum_insured_male!}</td>
                                            <td>${info.total_sum_insured_female!}</td>
                                            <#assign gciInfoFound = false>
                                            <#list personOverviews as personOverview>
                                                <#if personOverview.tag=="gci" && personOverview.itemKey=="age_profile_of_employees">
                                                    <#assign gcivalueMapList = personOverview.value?eval>
                                                    <#list gcivalueMapList as info1>
                                                        <#if info1.age_band?? && info1.age_band==info.age_band>
                                                            <#assign gciInfoFound = true>
                                                            <td>${info1.no_of_employees_male!}</td>
                                                            <td>${info1.no_of_employees_female!}</td>
                                                            <td>${info1.total_sum_insured_male!}</td>
                                                            <td>${info1.total_sum_insured_female!}</td>
                                                        </#if>
                                                    </#list>
                                                </#if>
                                            </#list>
                                            <#if !gciInfoFound >
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </#if>
                                        </tr>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>
                        <#if !hasData6>
                            <tr>
                                <th>16-30</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>31-35</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>36-40</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>41-45</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>46-50</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>51-55</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>56-60</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>61-65</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>66-70</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>Total</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </#if>
                </table>
            </td>
        </tr>
    </table>
    <!--    footer-->
    <div class="footer">
        <small>HSBC Life Ver. 1.0</small>
    </div>
</div>

<!--第六页-->
<div class="page">
    <!--    header-->
    <div class="header">
        <div class="logo"></div>
    </div>
    <table class="padding text-left">
        <colgroup>
            <col span="2" style="width: 2em;">
            <col>
        </colgroup>
        <!--        e)-->
        <tr>
            <td></td>
            <td>e)</td>
            <td>Claims Experience for the past 3 years</td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                <table class="border text-center cell-height mb-2">
                    <caption class="font-bold text-left">Paid Claims</caption>
                    <colgroup>
                        <col span="2" style="width: 10em;">
                        <col span="6">
                    </colgroup>
                    <tr>
                        <th rowspan="2">
                            Period of Coverage From / To
                            <span class="border-bottom inline-block" style="width: 6em;"></span>
                            (dd/mm/yyyy)
                        </th>
                        <th rowspan="2"># of Insured as at
                            <span class="border-bottom inline-block" style="width: 6em;"></span>
                            (dd/mm/yyyy)
                        </th>
                        <th colspan="2">GTL</th>
                        <th colspan="2">GPA</th>
                        <th colspan="2">GCI</th>
                    </tr>
                    <tr>
                        <th># of Claims</th>
                        <th>Amount (S$)</th>
                        <th># of Claims</th>
                        <th>Amount (S$)</th>
                        <th># of Claims</th>
                        <th>Amount (S$)</th>
                    </tr>
                        <#if isUploadClaimAttach?? && isUploadClaimAttach>
                            <tr>
                                <td class="text-left italic">Please refer to the attachment</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <#list 1..2 as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>

                        <#assign displayedCount = 0>
                        <#list claimLogs?keys as key>
                            <#if key == "GTL">
                                <#assign gpclaimList = claimLogs[key]>
                                <#list gpclaimList as claim>
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${claim.startTime}</td>
                                            <td>${claim.endTime}</td>
                                            <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                            <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                            <#assign gpclaimList = claimLogs["GPA"]![]>
                                            <#assign spshowflg = false>
                                            <#list gpclaimList as spclaim>
                                                <#if spclaim.startTime == claim.startTime && spclaim.endTime == claim.endTime>
                                                    <#assign spshowflg = true>
                                                    <td>${(spclaim.paidClaimsNum??)?then(spclaim.paidClaimsNum, "")}</td>
                                                    <td>${(spclaim.paidAmount?? && spclaim.paidAmount.amount??)?then(spclaim.paidAmount.amount, "")}</td>
                                                </#if>
                                            </#list>
                                            <#if !spshowflg>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </#if>
                                            <#assign gdclaimList = claimLogs["GCI"]![]>
                                            <#assign gdshowflg = false>
                                            <#list gdclaimList as gdclaim>
                                                <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                    <#assign gdshowflg = true>
                                                    <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                                    <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                                </#if>
                                            </#list>
                                            <#if !gdshowflg>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </#if>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>


                        <#list claimLogs?keys as key>
                            <#if key == "GPA" && displayedCount < 3>
                                <#assign gpclaimList = claimLogs[key]>
                                <#list gpclaimList as claim>
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${claim.startTime}</td>
                                            <td>${claim.endTime}</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                            <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                            <#assign gdclaimList = claimLogs["GCI"]![]>
                                            <#assign gdshowflg = false>
                                            <#list gdclaimList as gdclaim>
                                                <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                    <#assign gdshowflg = true>
                                                    <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                                    <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                                </#if>
                                            </#list>
                                            <#if !gdshowflg>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </#if>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                        </tr>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>
                        <#list claimLogs?keys as key>
                            <#if key == "GCI" && displayedCount < 3>
                                <#assign gpclaimList = claimLogs[key]>
                                <#list gpclaimList as claim>
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${claim.startTime}</td>
                                            <td>${claim.endTime}</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                            <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                        </tr>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>

                        <#if displayedCount < 3 && !isUploadClaimAttach>
                            <#list displayedCount..2 as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>
                    <tr>
                        <td class="italic text-left" colspan="8">
                            Note: The insurer reserves the right to request for more information.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                <table class="border text-center cell-height">
                    <caption class="font-bold text-left">Outstanding Claims</caption>
                    <colgroup>
                        <col span="2" style="width: 10em;">
                        <col span="6">
                    </colgroup>
                    <tr>
                        <th rowspan="2">Period of Coverage From / To
                            <span class="border-bottom inline-block" style="width: 6em;"></span>
                            (dd/mm/yyyy)
                        </th>
                        <th rowspan="2"># of Insured as at
                            <span class="border-bottom inline-block" style="width: 6em;"></span>
                            (dd/mm/yyyy)
                        </th>
                        <th colspan="2">GTL</th>
                        <th colspan="2">GPA</th>
                        <th colspan="2">GCI</th>
                    </tr>
                    <tr>
                        <th># of Claims</th>
                        <th>Amount (S$)</th>
                        <th># of Claims</th>
                        <th>Amount (S$)</th>
                        <th># of Claims</th>
                        <th>Amount (S$)</th>
                    </tr>
                        <#--判断是否上传文件-->
                        <#if isUploadClaimAttach?? && isUploadClaimAttach>
                            <tr>
                                <td  class="text-left italic">Please refer to the attachment</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <#list 1..2 as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>
                        <#assign displayedCount = 0>
                        <#list claimLogs?keys as key>
                            <#if key == "GTL">
                                <#assign gpclaimList = claimLogs[key]>
                                <#list gpclaimList as claim>
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${claim.startTime}</td>
                                            <td>${claim.endTime}</td>
                                            <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                            <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                            <#assign gpclaimList = claimLogs["GPA"]![]>
                                            <#assign spshowflg = false>
                                            <#list gpclaimList as spclaim>
                                                <#if spclaim.startTime == claim.startTime && spclaim.endTime == claim.endTime>
                                                    <#assign spshowflg = true>
                                                    <td>${(spclaim.outstandingClaimsNum??)?then(spclaim.outstandingClaimsNum, "")}</td>
                                                    <td>${(spclaim.outstandingAmount?? && spclaim.outstandingAmount.amount??)?then(spclaim.outstandingAmount.amount, "")}</td>
                                                </#if>
                                            </#list>
                                            <#if !spshowflg>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </#if>
                                            <#assign gdclaimList = claimLogs["GCI"]![]>
                                            <#assign gdshowflg = false>
                                            <#list gdclaimList as gdclaim>
                                                <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                    <#assign gdshowflg = true>
                                                    <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                                    <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                                </#if>
                                            </#list>
                                            <#if !gdshowflg>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </#if>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>
                        <#list claimLogs?keys as key>
                            <#if key == "GPA" && displayedCount < 3 >
                                <#assign gpclaimList = claimLogs[key]>
                                <#list gpclaimList as claim>
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${claim.startTime}</td>
                                            <td>${claim.endTime}</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                            <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                            <#assign gdclaimList = claimLogs["GCI"]![]>
                                            <#assign gdshowflg = false>
                                            <#list gdclaimList as gdclaim>
                                                <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                    <#assign gdshowflg = true>
                                                    <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                                    <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                                </#if>
                                            </#list>
                                            <#if !gdshowflg>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </#if>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>
                        <#list claimLogs?keys as key>
                            <#if key == "GCI" && displayedCount < 3 >
                                <#assign gpclaimList = claimLogs[key]>
                                <#list gpclaimList as claim>
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${claim.startTime}</td>
                                            <td>${claim.endTime}</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                            <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>
                        <#-- 不存在数据时显示空行-->
                        <#if displayedCount < 3 && !isUploadClaimAttach>
                            <#list displayedCount..2 as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>
                    <tr>
                        <td class="italic text-left" colspan="8">
                            Note: The insurer reserves the right to request for more information.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <!--        2. BENEFIT: GROUP DISABILITY INCOME INSURANCE-->
        <tr>
            <th>2.</th>
            <th class="underline" colspan="2">BENEFIT: GROUP DISABILITY INCOME INSURANCE</th>
        </tr>
        <!--        a)-->
        <tr>
            <td></td>
            <td>a)</td>
            <td>If currently insured, please attach a copy of the definition of Disability.</td>
        </tr>
        <!--        b)-->
        <tr>
            <td></td>
            <td>b)</td>
            <td>
                What is the waiting period required? Please circle as appropriate: 3 or 6 months or
                <span class="border-bottom inline-block" style="width: 6em;"></span>
            </td>
        </tr>
        <!--        c)-->
        <tr>
            <td></td>
            <td class="align-top">c)</td>
            <td>
                What is the benefit duration required? <br>
                <span class="border-bottom inline-block" style="width: 26em;"></span>
                (i.e. 2 years, or 5 years, or up to retirement age 60 or 62, or 65)
            </td>
        </tr>
        <!--        d)-->
        <tr>
            <td></td>
            <td class="align-top">d)</td>
            <td>
                What is the escalation benefit required? Please circle as appropriate: 0% or 3% or 5% or
                <span class="inline-block border-bottom" style="width: 18em;"></span>
            </td>
        </tr>
        <!--        e)-->
        <tr>
            <td></td>
            <td class="align-top">e)</td>
            <td>Please provide Current Non-Medical Limit (if applicable): S$
                <span class="inline-block border-bottom" style="width: 6em;"></span>
                up to age
                <span class="inline-block border-bottom" style="width: 6em;"></span>
            </td>
        </tr>
        <!--        f)-->
        <tr>
            <td></td>
            <td>f)</td>
            <td>
                Any requirement for partial disability benefits? &emsp;
                <strong>Yes / No</strong>
            </td>
        </tr>
    </table>
    <!--    footer-->
    <div class="footer">
        <small>HSBC Life Ver. 1.0</small>
    </div>
</div>

<!--第七页-->
<div class="page">
    <!--    header-->
    <div class="header">
        <div class="logo"></div>
    </div>
    <table class="padding text-left">
        <colgroup>
            <col span="2" style="width: 2em;">
            <col>
        </colgroup>
        <!--        g)-->
        <tr>
            <td></td>
            <td>g)</td>
            <td>Basis of Coverage</td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                <table class="border text-center cell-height padding-small">
                    <colgroup>
                        <col style="width: 3em;">
                        <col>
                        <col span="2" style="width: 6em;">
                        <col style="width: 12em;">
                    </colgroup>
                    <tr>
                        <th colspan="2" rowspan="2">Category of Employees / Occupation</th>
                        <th colspan="2">Monthly Salary (S$)</th>
                        <th rowspan="2">Basis of Coverage i.e. % (e.g. 50%) of monthly salary</th>
                    </tr>
                    <tr>
                        <th>Highest*</th>
                        <th>Average*</th>
                    </tr>
                    <tr>
                        <td>(i)</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>(ii)</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>(iii)</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>(iv)</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="italic text-left" colspan="5">
                            * Applicable to the category of employees as stated. Monthly salary will be basic pay +
                            fixed bonus if any. It excludes
                            variable bonus, commissions, etc.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <!--        h)-->
        <tr>
            <td></td>
            <td>h)</td>
            <td>Details of Employees</td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                <table class="border text-center">
                    <colgroup>
                        <col span="5" style="width: 20%;">
                    </colgroup>
                    <tr>
                        <th rowspan="2">Age Band (Age Next Birthday)</th>
                        <th colspan="2"># of Employees</th>
                        <th colspan="2">Sum Insured (S$)</th>
                    </tr>
                    <tr>
                        <th>Male</th>
                        <th>Female</th>
                        <th>Male</th>
                        <th>Female</th>
                    </tr>
                    <tr>
                        <th>16-30</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>31-35</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>36-40</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>41-45</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>46-50</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>

                    </tr>
                    <tr>
                        <th>51-55</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>56-60</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>61-65</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>Total</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </table>
            </td>
        </tr>
        <!--        i)-->
        <tr>
            <td></td>
            <td>i)</td>
            <td>Claims Experience for the past 3 years</td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                <table class="border text-center cell-height padding-small">
                    <colgroup>
                        <col span="4" style="width: 25%;">
                    </colgroup>
                    <tr>
                        <th rowspan="2">
                            Date of Disability <br>
                            <span class="border-bottom inline-block" style="width: 6em;"></span> <br>
                            (dd/mm/yyyy)
                        </th>
                        <th rowspan="2">Cause of Disability / Nature of Illness</th>
                        <th colspan="2">Claims Amount (S$)</th>
                    </tr>
                    <tr>
                        <th>Paid</th>
                        <th>Outstanding</th>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="italic text-left" colspan="4">
                            Note: The Insurer reserves the right to request for more information.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <!--    footer-->
    <div class="footer">
        <small>HSBC Life Ver. 1.0</small>
    </div>
</div>

<!--第八页-->
<div class="page">
    <!--    header-->
    <div class="header">
        <div class="logo"></div>
    </div>
    <table class="padding text-left">
        <colgroup>
            <col span="2" style="width: 2em;">
            <col>
        </colgroup>
        <!--        3. BENEFIT: GROUP HOSPITAL & SURGICAL INSURANCE / MAJOR MEDICAL INSURANCE-->
        <tr>
            <th>3.</th>
            <th class="underline" colspan="2">BENEFIT: GROUP HOSPITAL & SURGICAL INSURANCE / MAJOR MEDICAL INSURANCE
            </th>
        </tr>
        <!--        a)-->
        <tr>
            <td></td>
            <td>a)</td>
            <td>Basis of Coverage</td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                <table class="border text-center cell-height padding-small">
                    <colgroup>
                        <col style="width: 3em;">
                        <col>
                        <col span="3" style="width: 10em;">
                    </colgroup>
                    <tr>
                        <th colspan="2">Category of Employees / Occupation</th>
                        <th>Room & Board Benefit Plan (S$)</th>
                        <th>Currently with TMIS Yes / No</th>
                        <th>Proposal with TMIS Yes / No</th>
                    </tr>
                        <#assign hasData = false>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag=="ghs" && personOverview.itemKey=="basis_of_coverage">
                                <#assign hasData = true>
                                <#assign valueMapList = personOverview.value?eval>
                                <#list valueMapList as info>
                                    <#if info_index < 3>
                                        <tr>
                                            <#if info_index == 0>
                                                <td>(i)</td>
                                            <#elseif info_index == 1>
                                                <td>(ii)</td>
                                            <#elseif info_index == 2>
                                                <td>(iii)</td>
                                            </#if>
                                            <td>${info.category_of_employees_occupation!""}</td>
                                            <td>${info.room_and_board_benefit_plan!""}</td>
                                            <td>${(info.currently_with_tmis?? && info.currently_with_tmis=="1")?then("Yes", "No")}</td>
                                            <td>${(info.proposal_with_tmis?? && info.proposal_with_tmis=="1")?then("Yes", "No")}</td>
<#--                                            <td>${(info.medical_insurance_for_pass_and_work?? && info.medical_insurance_for_pass_and_work=="1")?then("Yes", "No")}</td>-->
                                        </tr>
                                    <#else>
                                        <tr>
                                            <td colspan="5" class="text-left italic"> More information, please refer to the attachment.</td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#list>
                                <#if valueMapList?size < 3>
                                    <#list valueMapList?size..2 as i>
                                        <tr>
                                            <#if i == 0>
                                                <td>(i)</td>
                                            <#elseif i == 1>
                                                <td>(ii)</td>
                                            <#elseif i == 2>
                                                <td>(iii)</td>
                                            </#if>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>
                            </#if>
                        </#list>
                        <#if !hasData>
                            <#list 1..3 as i>
                                <tr>
                                    <#if i == 1>
                                        <td>(i)</td>
                                    <#elseif i == 2>
                                        <td>(ii)</td>
                                    <#elseif i == 3>
                                        <td>(iii)</td>
                                    </#if>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>
                        <#if !hasData>
                            <tr>
                                <td>(i)</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>(ii)</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>(iii)</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                </table>
            </td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <th style="padding-bottom: 0;">Important Note:</th>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <th>
                <table class="no-padding">
                    <tr>
                        <td class="align-top" style="width: 2em;">(1)</td>
                        <td>
                            Dependants can be covered under Group Hospital & Surgical Plan. Their cover should be the
                            same as the employee's cover.
                        </td>
                    </tr>
                </table>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <th>
                <table class="no-padding">
                    <tr>
                        <td class="align-top" style="width: 2em;">(2)</td>
                        <td>
                            Please provide the Deductible /Co-insurance for respective employee category or
                            occupation, if applicable.
                        </td>
                    </tr>
                </table>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                <table class="no-padding text-left">
                    <tr>
                        <th class="underline" style="padding-bottom: 0;">Example 1</th>
                        <td style="width: 12.5em;"></td>
                    </tr>
                    <tr>
                        <th>Category of Employees / Occupation</th>
                        <th>R&B Benefit Plan (S$)</th>
                    </tr>
                    <tr>
                        <td>(i) &nbsp; Senior Management (Director, General Manager, Senior Manager)</td>
                        <td>360</td>
                    </tr>
                    <tr>
                        <td>(ii) &nbsp; Manager & Executive</td>
                        <td>200</td>
                    </tr>
                    <tr>
                        <td>(iii)&nbsp; All Others</td>
                        <td>100</td>
                    </tr>
                    <tr>
                        <th class="underline" style="padding-top: 1em !important;">Example 2</th>
                    </tr>
                    <tr>
                        <th>Category of Employees / Occupation</th>
                        <th>R&B Benefit Plan</th>
                    </tr>
                    <tr>
                        <td>(i) &nbsp; Senior Management (Director, General Manager, Senior Manager)</td>
                        <td>1 Bedded</td>
                    </tr>
                    <tr>
                        <td>(ii) &nbsp; Manager & Executive</td>
                        <td>2 Bedded</td>
                    </tr>
                    <tr>
                        <td>(iii)&nbsp; All Others</td>
                        <td>4 Bedded</td>
                    </tr>
                </table>
            </td>
        </tr>
        <!--        b)-->
        <tr>
            <td></td>
            <td>b)</td>
            <td>Age Profile of Employees</td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                <table class="border text-center">
                    <colgroup>
                        <col style="width: 40%;">
                        <col span="2" style="width: 30%;">
                    </colgroup>
                    <tr>
                        <th rowspan="2">Age Band (Age Next Birthday)</th>
                        <th colspan="2"># of Employees</th>
                    </tr>
                    <tr>
                        <th>Male</th>
                        <th>Female</th>
                    </tr>
                        <#assign  hasData9=false>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag=="ghs" && personOverview.itemKey=="age_profile_of_employees">
                                <#assign valueMapList = personOverview.value?eval>
                                <#list valueMapList as info>
                                    <#if info.age_band??>
                                        <#assign  hasData9=true>
                                        <tr>
                                            <td class="font-bold text-center">${info.age_band!}</td>
                                            <td class="text-center">${info.no_of_employees_male!}</td>
                                            <td class="text-center">${info.no_of_employees_female!}</td>
                                        </tr>
                                    </#if>

                                </#list>
                            </#if>
                        </#list>
                        <#if !hasData9>
                            <tr>
                                <th>16-30</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>31-35</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>36-40</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>41-45</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>46-50</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>51-55</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>56-60</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>61-65</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>66-70</th>
                                <td></td>
                                <td></td>
                            </tr>

                            <tr>
                                <th>Total</th>
                                <td></td>
                                <td></td>
                            </tr>
                        </#if>
                </table>
            </td>
        </tr>
    </table>
    <!--    footer-->
    <div class="footer">
        <small>HSBC Life Ver. 1.0</small>
    </div>
</div>

<!--第九页-->
<div class="page">
    <!--    header-->
    <div class="header">
        <div class="logo"></div>
    </div>
    <table class="padding text-left">
        <colgroup>
            <col span="2" style="width: 2em;">
            <col>
        </colgroup>
        <!--        c)-->
        <tr>
            <td></td>
            <td>c)</td>
            <td>Details of Insured Members</td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <th class="underline">For GHS and GMM:</th>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                <table class="border padding-small border-none text-center mb-2">
                    <tr>
                        <th class="border-none" style="width: 14em;"></th>
                        <th colspan="4"># of Employees (Singaporeans & SPRs*)</th>
                    </tr>
                        <tr>
                            <#assign hasData = false />
                            <#assign noOfEmployeesPlanList = []>
                            <#list personOverviews as personOverview>
                                <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_sgp">
                                    <#if personOverview.valueMaps?has_content>
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                                <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                                <#assign hasData = true />
                                            </#if>
                                        </#list>
                                    </#if>
                                </#if>
                            </#list>
                            <#if hasData>
                                <tr>
                                    <td></td>
                                    <#list noOfEmployeesPlanList as info>
                                        <#if info_index < 4>
                                            <th>${info} </th>
                                        </#if>
                                    </#list>
                                    <#if noOfEmployeesPlanList?size < 4>
                                        <#list 1..(4 - noOfEmployeesPlanList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                            <#else>
                                <tr>
                                    <td>&nbsp;</td>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                    <th></th>
                                </tr>
                            </#if>
                        </tr>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_sgp">
                                <#-- 检查valueMaps是否有数据（避免空表格） -->
                                <#if personOverview.valueMaps?has_content>
                                    <tr>
                                        <td class="text-left font-bold">Employee Only</td>
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_only)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                    <tr>
                                        <td class="font-bold text-left">Employee & Spouse</td>
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_and_spouse)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                    <tr>
                                        <td class="text-left font-bold">Employee & Child(ren)</td>
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_and_children)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                    <tr>
                                        <td class="font-bold text-left">Employee & Family</td>
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_and_family)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                </#if>
                            </#if>
                        </#list>
                        <#if !hasData>
                            <tr>
                                <td class="text-left font-bold" >Employee Only</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="text-left font-bold" >Employee & Spouse</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="text-left font-bold">Employee & Child(ren)</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="text-left font-bold">Employee & Family</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </#if>
                    <tr>
                        <td class="italic text-left" colspan="5">
                            * refers to Singapore Permanent Residents
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                <table class="border padding-small border-none text-center">
                    <tr>
                        <th class="border-none" style="width: 14em;"></th>
                        <th colspan="4"># of Employees (Foreigners* only)</th>
                    </tr>
                        <#assign hasData = false />
                        <#assign noOfEmployeesPlanList = []>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_foreigners">
                                <#if personOverview.valueMaps?has_content>
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                            <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                            <#assign hasData = true />
                                        </#if>
                                    </#list>
                                </#if>
                            </#if>
                        </#list>
                        <#if hasData>
                            <tr>
                                <td></td>
                                <#list noOfEmployeesPlanList as info>
                                    <#if info_index < 4>
                                        <th>${info} </th>
                                    </#if>
                                </#list>
                                <#if noOfEmployeesPlanList?size < 4>
                                    <#list 1..(4 - noOfEmployeesPlanList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                        <#else>
                            <tr>
                                <td>&nbsp;</td>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                        </#if>
                </tr>
                <#list personOverviews as personOverview>
                    <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_foreigners">
                        <#-- 检查valueMaps是否有数据（避免空表格） -->
                        <#assign valueMapList = personOverview.value?eval>
                        <#if personOverview.valueMaps?has_content>
                            <tr>
                                <td class="text-left font-bold">Employee Only</td>

                                <#list valueMapList as info>
                                    <#if info_index < 4>
                                        <td>${(info.employee_only)!""}</td>
                                    </#if>
                                </#list>
                                <#if valueMapList?size < 4>
                                    <#list 1..(4 - valueMapList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                            <tr>
                                <td class="font-bold text-left">Employee & Spouse</td>
                                <#assign valueMapList = personOverview.value?eval>
                                <#list valueMapList as info>
                                    <#if info_index < 4>
                                        <td>${(info.employee_and_spouse)!""}</td>
                                    </#if>
                                </#list>
                                <#if valueMapList?size < 4>
                                    <#list 1..(4 - valueMapList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                            <tr>
                                <td class="text-left font-bold">Employee & Child(ren)</td>
                                <#list valueMapList as info>
                                    <#if info_index < 4>
                                        <td>${(info.employee_and_children)!""}</td>
                                    </#if>
                                </#list>
                                <#if valueMapList?size < 4>
                                    <#list 1..(4 - valueMapList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                            <tr>
                                <td class="font-bold text-left">Employee & Family</td>
                                <#list valueMapList as info>
                                    <#if info_index < 4>
                                        <td>${(info.employee_and_family)!""}</td>
                                    </#if>
                                </#list>
                                <#if valueMapList?size < 4>
                                    <#list 1..(4 - valueMapList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                        </#if>
                    </#if>
                </#list>
                <#if !hasData>
                    <tr>
                        <td class="text-left font-bold" >Employee Only</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="text-left font-bold" >Employee & Spouse</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="text-left font-bold" >Employee & Child(ren)</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="text-left font-bold" >Employee & Family</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </#if>
                    <tr>
                        <td class="italic text-left" colspan="5">
                            * refers to all foreigners holding Employment Pass, S Pass and Work Permit, working in
                            Singapore
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <!--        For GMM (if the basis of coverage differs from GHS)-->
        <tr>
            <td></td>
            <td></td>
            <th class="underline" style="padding-top: 2em;">For GMM (if the basis of coverage differs from GHS):</th>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                <table class="border border-none text-center padding-small mb-2">
                    <tr>
                        <th class="border-none" style="width: 14em;"></th>
                        <th colspan="4"># of Employees (Singaporeans & SPRs*)</th>
                    </tr>
                        <#assign hasData = false />
                        <#assign noOfEmployeesPlanList = []>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_sgp">
                                <#if personOverview.valueMaps?has_content>
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                            <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                            <#assign hasData = true />
                                        </#if>
                                    </#list>
                                </#if>
                            </#if>
                        </#list>
                        <#if hasData>
                            <tr>
                                <td></td>
                                <#list noOfEmployeesPlanList as info>
                                    <#if info_index < 4>
                                        <th>${info} </th>
                                    </#if>
                                </#list>
                                <#if noOfEmployeesPlanList?size < 4>
                                    <#list 1..(4 - noOfEmployeesPlanList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                        <#else>
                            <tr>
                                <td>&nbsp;</td>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                        </#if>
                </tr>
                <#list personOverviews as personOverview>
                    <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_sgp">
                        <#-- 检查valueMaps是否有数据（避免空表格） -->
                        <#if personOverview.valueMaps?has_content>
                            <tr>
                                <td class="text-left font-bold">Employee Only</td>
                                <#assign valueMapList = personOverview.value?eval>
                                <#list valueMapList as info>
                                    <#if info_index < 4>
                                        <td>${(info.employee_only)!""}</td>
                                    </#if>
                                </#list>
                                <#if valueMapList?size < 4>
                                    <#list 1..(4 - valueMapList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                            <tr>
                                <td class="font-bold text-left">Employee & Spouse</td>
                                <#assign valueMapList = personOverview.value?eval>
                                <#list valueMapList as info>
                                    <#if info_index < 4>
                                        <td>${(info.employee_and_spouse)!""}</td>
                                    </#if>
                                </#list>
                                <#if valueMapList?size < 4>
                                    <#list 1..(4 - valueMapList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                            <tr>
                                <td class="text-left font-bold">Employee & Child(ren)</td>
                                <#assign valueMapList = personOverview.value?eval>
                                <#list valueMapList as info>
                                    <#if info_index < 4>
                                        <td>${(info.employee_and_children)!""}</td>
                                    </#if>
                                </#list>
                                <#if valueMapList?size < 4>
                                    <#list 1..(4 - valueMapList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                            <tr>
                                <td class="font-bold text-left">Employee & Family</td>
                                <#assign valueMapList = personOverview.value?eval>
                                <#list valueMapList as info>
                                    <#if info_index < 4>
                                        <td>${(info.employee_and_family)!""}</td>
                                    </#if>
                                </#list>
                                <#if valueMapList?size < 4>
                                    <#list 1..(4 - valueMapList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                        </#if>
                    </#if>
                </#list>
                <#if !hasData>
                    <tr>
                        <td class="text-left font-bold" >Employee Only</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="text-left font-bold" >Employee & Spouse</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="text-left font-bold">Employee & Child(ren)</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="text-left font-bold">Employee & Family</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </#if>
                    <tr>
                        <td class="italic text-left" colspan="5">
                            * refers to Singapore Permanent Residents
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                <table class="border border-none text-center padding-small">
                    <tr>
                        <th class="border-none" style="width: 14em;"></th>
                        <th colspan="4"># of Employees (Foreigners* only)</th>
                    </tr>
                        <#assign hasData = false />
                        <#assign noOfEmployeesPlanList = []>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_foreigners">
                                <#if personOverview.valueMaps?has_content>
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                            <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                            <#assign hasData = true />
                                        </#if>
                                    </#list>
                                </#if>
                            </#if>
                        </#list>
                        <#if hasData>
                            <tr>
                                <td></td>
                                <#list noOfEmployeesPlanList as info>
                                    <#if info_index < 4>
                                        <th>${info} </th>
                                    </#if>
                                </#list>
                                <#if noOfEmployeesPlanList?size < 4>
                                    <#list 1..(4 - noOfEmployeesPlanList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                        <#else>
                            <tr>
                                <td>&nbsp;</td>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                        </#if>
                </tr>
                <#list personOverviews as personOverview>
                    <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_foreigners">
                        <#-- 检查valueMaps是否有数据（避免空表格） -->
                        <#assign valueMapList = personOverview.value?eval>
                        <#if personOverview.valueMaps?has_content>
                            <tr>
                                <td class="text-left font-bold">Employee Only</td>

                                <#list valueMapList as info>
                                    <#if info_index < 4>
                                        <td>${(info.employee_only)!""}</td>
                                    </#if>
                                </#list>
                                <#if valueMapList?size < 4>
                                    <#list 1..(4 - valueMapList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                            <tr>
                                <td class="font-bold text-left">Employee & Spouse</td>
                                <#assign valueMapList = personOverview.value?eval>
                                <#list valueMapList as info>
                                    <#if info_index < 4>
                                        <td>${(info.employee_and_spouse)!""}</td>
                                    </#if>
                                </#list>
                                <#if valueMapList?size < 4>
                                    <#list 1..(4 - valueMapList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                            <tr>
                                <td class="text-left font-bold">Employee & Child(ren)</td>
                                <#list valueMapList as info>
                                    <#if info_index < 4>
                                        <td>${(info.employee_and_children)!""}</td>
                                    </#if>
                                </#list>
                                <#if valueMapList?size < 4>
                                    <#list 1..(4 - valueMapList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                            <tr>
                                <td class="font-bold text-left">Employee & Family</td>
                                <#list valueMapList as info>
                                    <#if info_index < 4>
                                        <td>${(info.employee_and_family)!""}</td>
                                    </#if>
                                </#list>
                                <#if valueMapList?size < 4>
                                    <#list 1..(4 - valueMapList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                        </#if>
                    </#if>
                </#list>
                <#if !hasData>
                    <tr>
                        <td class="text-left font-bold" >Employee Only</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="text-left font-bold" >Employee & Spouse</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="text-left font-bold" >Employee & Child(ren)</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td class="text-left font-bold" >Employee & Family</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </#if>
                    <tr>
                        <td class="italic text-left" colspan="5">
                            * refers to all foreigners holding Employment Pass, S Pass and Work Permit, working in
                            Singapore
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <!--    footer-->
    <div class="footer">
        <small>HSBC Life Ver. 1.0</small>
    </div>
</div>

<!--第十页-->
<div class="page">
    <!--    header-->
    <div class="header">
        <div class="logo"></div>
    </div>
    <table class="padding text-left">
        <colgroup>
            <col span="2" style="width: 2em;">
            <col>
        </colgroup>
        <!--        d)-->
        <tr>
            <td></td>
            <td>d)</td>
            <td>Claims Experience for the past 3 years</td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                <table class="border text-center cell-height padding-small">
                    <colgroup>
                        <col span="2" style="width: 10em;">
                        <col span="4">
                    </colgroup>
                    <tr>
                        <th rowspan="2">Period of Coverage From / To
                            <span class="border-bottom inline-block" style="width: 6em;"></span>
                            (dd/mm/yyyy)
                        </th>
                        <th rowspan="2"># of Insured as at
                            <span class="border-bottom inline-block" style="width: 6em;"></span>
                            (dd/mm/yyyy)
                        </th>
                        <th colspan="2">Paid Claims</th>
                        <th colspan="2">Outstanding Claims</th>
                    </tr>
                    <tr>
                        <th># of Claims</th>
                        <th>Amount (S$)</th>
                        <th># of Claims</th>
                        <th>Amount (S$)</th>
                    </tr>
                        <#if isUploadClaimAttach?? && isUploadClaimAttach>
                            <tr>
                                <td class="text-left">Please refer to the attachment</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <#list 1..2 as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>

                        <#assign displayedCount = 0>
                        <#list claimLogs?keys as key>
                            <#if key == "GHS">
                                <#assign gpclaimList = claimLogs[key]>
                                <#list gpclaimList as claim>
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${claim.startTime}</td>
                                            <td>${claim.endTime}</td>
                                            <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                            <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                            <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                            <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>

                        <#if displayedCount < 3 && !isUploadClaimAttach >
                            <#list displayedCount..2 as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>
                    <tr>
                        <td class="italic text-left" colspan="6">
                            Note: The insurer reserves the right to request for more information.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <!--        e)-->
        <tr>
            <td></td>
            <td>e)</td>
            <td>Kindly attach a copy of the Schedule of Benefits, if the benefits are on insured basis (i.e. currently
                insured).
            </td>
        </tr>
        <tr>
            <td colspan="3" style="padding-top: 2em;"></td>
        </tr>
        <!--        4. BENEFIT: GROUP OUTPATIENT INSURANCE-->
        <tr>
            <th>4.</th>
            <th class="underline" colspan="2">BENEFIT: GROUP OUTPATIENT INSURANCE</th>
        </tr>
        <!--        a)-->
        <tr>
            <td></td>
            <td>a)</td>
            <td>Category of Employees to be insured (please tick as appropriate)</td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                <table class="border padding-small">
                    <colgroup>
                        <col style="width: 3em;">
                        <col>
                        <col span="2" style="width: 7em">
                        <col style="width: 8em;">
                        <col style="width: 7em;">
                    </colgroup>
                    <tr>
                        <th colspan="2">Category of Employees</th>
                        <th class="text-center">Clinical GP</th>
                        <th class="text-center">Specialist</th>
                        <th class="text-center">Diag X-Ray/Lab Tests</th>
                        <th class="text-center">Dental</th>
                    </tr>
                        <#assign groupedItems = {}>
                        <#list userPlanDutyList as item>
                            <#if !groupedItems[item.categoryEmployee]??>
                                <#assign groupedItems = groupedItems + {item.categoryEmployee: []}>
                            </#if>
                            <#assign groupedItems = groupedItems + {item.categoryEmployee: groupedItems[item.categoryEmployee] + [item]}>
                        </#list>

                        <#assign index = 0>
                        <#list groupedItems?keys as category>
                            <#assign itemsInCategory = groupedItems[category]>
                            <#if index < 3>
                                <tr>
                                    <#if index == 0>
                                        <td>(i)</td>
                                    <#elseif index == 1>
                                        <td>(ii)</td>
                                    <#elseif index == 2>
                                        <td>(iii)</td>
                                    <#elseif index == 3>
                                        <td>(iv)</td>
                                    </#if>
                                    <td class="text-left">${category}</td>
                                    <#assign hasGtl = false>
                                    <#assign hasSp = false>
                                    <#assign hasGd = false>
                                    <#list itemsInCategory as item>
                                        <#if item.benefit?? && item.benefit == "GTL">
                                            <#assign hasGtl = true>
                                        </#if>
                                        <#if item.benefit?? && item.benefit == "SP">
                                            <#assign hasSp = true>
                                        </#if>
                                        <#if item.benefit?? && item.benefit == "GD">
                                            <#assign hasGd = true>
                                        </#if>
                                    </#list>
                                    <td class="text-center"><input type="checkbox" <#if hasGtl>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                                    <td class="text-center"><input type="checkbox" <#if hasSp>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                                    <td class="text-center"></td>
                                    <td class="text-center"><input type="checkbox" <#if hasGd>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                                </tr>
                            <#else>
                                <tr>
                                    <td colspan="6" class="text-left italic"> More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                            <#assign index = index + 1>
                        </#list>
                        <#-- 补充空行到3行 -->
                        <#if index < 3>
                            <#list index..2 as i>
                                <tr>
                                    <#if i == 0>
                                        <td>(i)</td>
                                    <#elseif i == 1>
                                        <td>(ii)</td>
                                    <#elseif i == 2>
                                        <td>(iii)</td>
                                    </#if>
                                    <td class="text-left">&nbsp;</td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            </#list>
                        </#if>
                        <tr>
                            <th colspan="2">Dependant (where applicable)</th>
                                <#assign gpDependantsIncluded = "No">
                                <#assign spDependantsIncluded = "No">
                                <#assign gdDependantsIncluded = "No">
                                <#assign gpHeadcount = "">
                                <#assign spHeadcount = "">
                                <#assign gdHeadcount = "">
                                <#list personOverviews as personOverview>
                                    <#if personOverview.itemKey == "dependants_included">
                                        <#if personOverview.tag == "gp">
                                            <#if (personOverview.value!"") == "1">
                                                <#assign gpDependantsIncluded = "Yes">
                                            </#if>
                                        <#elseif personOverview.tag == "sp">
                                            <#if (personOverview.value!"") == "1">
                                                <#assign spDependantsIncluded = "Yes">

                                            </#if>
                                        <#elseif personOverview.tag == "gd">
                                            <#if (personOverview.value!"") == "1">
                                                <#assign gdDependantsIncluded = "Yes">
                                            </#if>
                                        </#if>
                                    </#if>

                                    <#if personOverview.itemKey == "no_of_headcount">
                                        <#if personOverview.tag == "gp">
                                            <#if personOverview.value??>
                                                <#assign gpHeadcount = personOverview.value!>
                                            </#if>
                                        <#elseif personOverview.tag == "sp">
                                            <#if personOverview.value??>
                                                <#assign spHeadcount = personOverview.value!>
                                            </#if>
                                        <#elseif personOverview.tag == "gd">
                                            <#if personOverview.value?? >
                                                <#assign gdHeadcount = personOverview.value!>
                                            </#if>
                                        </#if>
                                    </#if>
                                </#list>


                                <td class="text-center">${gpDependantsIncluded}</td>
                                <td class="text-center">${spDependantsIncluded}</td>
                                <td class="text-center"></td>
                                <td class="text-center">${gdDependantsIncluded}</td>
                        </tr>
                        <tr>
                            <th colspan="2"># of Headcount</th>
                            <td class="text-center">${gpHeadcount}</td>
                            <td class="text-center">${spHeadcount}</td>
                            <td class="text-center"></td>
                            <td class="text-center">${gdHeadcount}</td>
                        </tr>
                </table>
            </td>
        </tr>
        <!--        b)-->
        <tr>
            <td></td>
            <td>b)</td>
            <td>Age Profile of Employees</td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                <table class="border text-center">
                    <colgroup>
                        <col style="width: 40%;">
                        <col span="2" style="width: 30%;">
                    </colgroup>
                    <tr>
                        <th rowspan="2">Age Band (Age Next Birthday)</th>
                        <th colspan="2"># of Employees</th>
                    </tr>
                    <tr>
                        <th>Male</th>
                        <th>Female</th>
                    </tr>
                        <#assign totalMale = 0>
                        <#assign totalFemale = 0>
                        <#assign hasData12 = false>
                        <#list personOverviews as personOverview>
                            <#if personOverview.itemKey=="age_profile_of_employees">
                                <#if personOverview.tag=="group_outpatient_insurance">
                                    <#if personOverview.valueMaps?has_content>
                                        <#assign hasData12 = true>
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <tr>
                                                <th>${info.age_band!}</th>
                                                <td>${(info.no_of_employees_male?has_content && info.no_of_employees_male?number != 0)?then(info.no_of_employees_male, "")}</td>
                                                <td>${(info.no_of_employees_female?has_content && info.no_of_employees_female?number != 0)?then(info.no_of_employees_female, "")}</td>
                                            </tr>
                                        </#list>

                                    </#if>
                                </#if>
                            </#if>
                        </#list>
                        <#if !hasData12>
                            <tr>
                                <th>16-30</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>31-35</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>36-40</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>41-45</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>46-50</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>51-55</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>56-60</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>61-65</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>66-70</th>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>Total</th>
                                <td></td>
                                <td></td>
                            </tr>
                        </#if>
                </table>
            </td>
        </tr>
    </table>
    <!--    footer-->
    <div class="footer">
        <small>HSBC Life Ver. 1.0</small>
    </div>
</div>

<!--第十一页-->
<div class="page">
    <!--    header-->
    <div class="header">
        <div class="logo"></div>
    </div>
    <table class="padding text-left">
        <colgroup>
            <col span="2" style="width: 2em;">
            <col>
        </colgroup>
        <!--        c)-->
        <tr>
            <td></td>
            <td>c)</td>
            <td>Claims Experience for the past 3 years</td>
        </tr>
    </table>
    <table class="border border-none layout-auto text-center cell-height padding-small" style="width: 95%; margin: 0 0 0 auto;">
        <caption class="font-bold text-left underline">Paid Claims</caption>
        <colgroup>
            <col span="2" style="width: 16%;">
            <col span="8" style="width: 8.4%;">
        </colgroup>
        <tr>
            <th class="border-none" colspan="2"></th>
            <th colspan="2">Clinical*</th>
            <th colspan="2">Specialist *</th>
            <th colspan="2">Diagnostic X-Ray / Lab Tests*</th>
            <th colspan="2">Dental*</th>
        </tr>
        <tr>
            <th class="text-left">Period of Coverage From / To
                <span class="border-bottom inline-block" style="width: 6em;"></span> <br>
                (dd/mm/yyyy)
            </th>
            <th class="text-left"># of Insured as at
                <span class="border-bottom inline-block" style="width: 6em;"></span> <br>
                (dd/mm/yyyy)
            </th>
            <th># of Visits</th>
            <th>Amt (S$)</th>
            <th># of Visits</th>
            <th>Amt (S$)</th>
            <th># of Visits</th>
            <th>Amt (S$)</th>
            <th># of Visits</th>
            <th>Amt (S$)</th>
        </tr>
            <#if isUploadClaimAttach?? && isUploadClaimAttach>
                <tr>
                    <td > Please refer to the attachment</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <#list 1..2 as i>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#list>
            </#if>

            <#assign displayedCount = 0>
            <#list claimLogs?keys as key>
                <#if key == "GP">
                    <#assign gpclaimList = claimLogs[key]>
                    <#list gpclaimList as claim>
                        <#if displayedCount < 3>
                            <tr>
                                <td>${claim.startTime}</td>
                                <td>${claim.endTime}</td>
                                <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                <#assign gpclaimList = claimLogs["SP"]![]>
                                <#assign spshowflg = false>
                                <#list gpclaimList as spclaim>
                                    <#if spclaim.startTime == claim.startTime && spclaim.endTime == claim.endTime>
                                        <#assign spshowflg = true>
                                        <td>${(spclaim.paidClaimsNum??)?then(spclaim.paidClaimsNum, "")}</td>
                                        <td>${(spclaim.paidAmount?? && spclaim.paidAmount.amount??)?then(spclaim.paidAmount.amount, "")}</td>
                                    </#if>
                                </#list>
                                <#if !spshowflg>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </#if>
                                <#assign gdclaimList = claimLogs["GD"]![]>
                                <#assign gdshowflg = false>
                                <#list gdclaimList as gdclaim>
                                    <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                        <#assign gdshowflg = true>
                                        <td></td>
                                        <td></td>
                                        <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                        <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                    </#if>
                                </#list>
                                <#if !gdshowflg>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </#if>
                            </tr>
                            <#assign displayedCount = displayedCount + 1>
                        <#else>
                            <tr>
                                <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                            </tr>
                            <#break>
                        </#if>
                    </#list>
                </#if>
            </#list>

            <#list claimLogs?keys as key>
                <#if key == "SP" && displayedCount < 3>
                    <#assign gpclaimList = claimLogs[key]>
                    <#list gpclaimList as claim>
                        <#if displayedCount < 3>
                            <tr>
                                <td>${claim.startTime}</td>
                                <td>${claim.endTime}</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                <#assign gdclaimList = claimLogs["GD"]![]>
                                <#assign gdshowflg = false>
                                <#list gdclaimList as gdclaim>
                                    <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                        <#assign gdshowflg = true>
                                        <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                        <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                    </#if>
                                </#list>
                                <#if !gdshowflg>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </#if>
                            </tr>
                            <#assign displayedCount = displayedCount + 1>
                        <#else>
                            <tr>
                                <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                            </tr>
                            <#break>
                        </#if>
                    </#list>
                </#if>
            </#list>


            <#list claimLogs?keys as key>
                <#if key == "GD" && displayedCount < 3>
                    <#assign gpclaimList = claimLogs[key]>
                    <#list gpclaimList as claim>
                        <#if displayedCount < 3>
                            <tr>
                                <td>${claim.startTime}</td>
                                <td>${claim.endTime}</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                            </tr>
                            <#assign displayedCount = displayedCount + 1>
                        <#else>
                            <tr>
                                <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                            </tr>
                            <#break>
                        </#if>
                    </#list>
                </#if>
            </#list>
            <#if displayedCount < 3 && !isUploadClaimAttach>
                <#list displayedCount..2 as i>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#list>
            </#if>
        <tr>
            <td class="italic text-left" colspan="10">
                * inclusive of visits to non-panel clinics <br>
                Note: The insurer reserves the right to request for more information.
            </td>
        </tr>
    </table>
    <table class="border border-none text-center layout-auto cell-height padding-small" style="width: 95%; margin: 0 0 0 auto;">
        <caption class="font-bold text-left underline" style="padding-top: 2em;">Outstanding Claims
        </caption>
        <colgroup>
            <col span="2" style="width: 16%;">
            <col span="8" style="width: 8.4%;">
        </colgroup>
        <tr>
            <th class="border-none" colspan="2"></th>
            <th colspan="2">Clinical*</th>
            <th colspan="2">Specialist *</th>
            <th colspan="2">Diagnostic X-Ray / Lab Tests*</th>
            <th colspan="2">Dental*</th>
        </tr>
        <tr>
            <th class="text-left">Period of Coverage From / To<span class="border-bottom inline-block"
                                                  style="width: 4em;"></span>(dd/mm/yyyy)
            </th>
            <th class="text-left"># of Insured as at <span class="border-bottom inline-block" style="width: 4em;"></span>(dd/mm/yyyy)
            </th>
            <th># of Visits</th>
            <th>Amt (S$)</th>
            <th># of Visits</th>
            <th>Amt (S$)</th>
            <th># of Visits</th>
            <th>Amt (S$)</th>
            <th># of Visits</th>
            <th>Amt (S$)</th>
        </tr>
            <#if isUploadClaimAttach?? && isUploadClaimAttach>
                <tr>
                    <td >Please refer to the attachment</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <#list 1..2 as i>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#list>
            </#if>

            <#assign displayedCount = 0>
            <#list claimLogs?keys as key>
                <#if key == "GP">
                    <#assign gpclaimList = claimLogs[key]>
                    <#list gpclaimList as claim>
                        <#if displayedCount < 3>
                            <tr>
                                <td>${claim.startTime}</td>
                                <td>${claim.endTime}</td>
                                <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                <#assign gpclaimList = claimLogs["SP"]![]>
                                <#assign spshowflg = false>
                                <#list gpclaimList as spclaim>
                                    <#if spclaim.startTime == claim.startTime && spclaim.endTime == claim.endTime>
                                        <#assign spshowflg = true>
                                        <td>${(spclaim.outstandingClaimsNum??)?then(spclaim.outstandingClaimsNum, "")}</td>
                                        <td>${(spclaim.outstandingAmount?? && spclaim.outstandingAmount.amount??)?then(spclaim.outstandingAmount.amount, "")}</td>
                                    </#if>
                                </#list>
                                <#if !spshowflg>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </#if>
                                <#assign gdclaimList = claimLogs["GD"]![]>
                                <#assign gdshowflg = false>
                                <#list gdclaimList as gdclaim>
                                    <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                        <#assign gdshowflg = true>
                                        <td></td>
                                        <td></td>
                                        <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                        <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                    </#if>
                                </#list>
                                <#if !gdshowflg>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </#if>
                            </tr>
                            <#assign displayedCount = displayedCount + 1>
                        <#else>
                            <tr>
                                <td colspan="10" class="italic text-left">More information, please refer to the attachment.</td>
                            </tr>
                            <#break>
                        </#if>
                    </#list>
                </#if>
            </#list>

            <#list claimLogs?keys as key>
                <#if key == "SP" && displayedCount < 3>
                    <#assign gpclaimList = claimLogs[key]>
                    <#list gpclaimList as claim>
                        <#if displayedCount < 3>
                            <tr>
                                <td>${claim.startTime}</td>
                                <td>${claim.endTime}</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                <#assign gdclaimList = claimLogs["GD"]![]>
                                <#assign gdshowflg = false>
                                <#list gdclaimList as gdclaim>
                                    <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                        <#assign gdshowflg = true>
                                        <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                        <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                    </#if>
                                </#list>
                                <#if !gdshowflg>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </#if>
                            </tr>
                            <#assign displayedCount = displayedCount + 1>
                        <#else>
                            <tr>
                                <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                            </tr>
                            <#break>
                        </#if>
                    </#list>
                </#if>
            </#list>

            <#list claimLogs?keys as key>
                <#if key == "GD" && displayedCount < 3>
                    <#assign gpclaimList = claimLogs[key]>
                    <#list gpclaimList as claim>
                        <#if displayedCount < 3>
                            <tr>
                                <td>${claim.startTime}</td>
                                <td>${claim.endTime}</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                            </tr>
                            <#assign displayedCount = displayedCount + 1>
                        <#else>
                            <tr>
                                <td colspan="10" class="italic text-left">More information, please refer to the attachment.</td>
                            </tr>
                            <#break>
                        </#if>
                    </#list>
                </#if>
            </#list>

            <#if displayedCount < 3 && !isUploadClaimAttach>
                <#list displayedCount..2 as i>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#list>
            </#if>
        <tr>
            <td class="italic text-left" colspan="10">
                * inclusive of visits to non-panel clinics <br>
                Note: The insurer reserves the right to request for more information.
            </td>
        </tr>
    </table>
    <table class="padding text-left">
        <colgroup>
            <col span="2" style="width: 2em;">
            <col>
        </colgroup>
        <!--        d)-->
        <tr>
            <td></td>
            <td class="align-top">d)</td>
            <td>
                <div>
                    Kindly attach a copy of the Schedule of Benefits if the benefits are on insured basis.
                </div>
                <div style="padding: 1em 0;">
                    If currently self-insured, kindly provide the following details:
                </div>
                <div>
                    Please indicate “Unlimited” if there is no cap and “NA” if it is not applicable.
                </div>
            </td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                <table class="padding-small border text-center">
                    <colgroup>
                        <col style="width: 17%">
                        <col span="6">
                    </colgroup>
                    <tr>
                        <th class="text-left" rowspan="2">Benefits</th>
                        <th colspan="2">Maximum Limit per Visit (S$)</th>
                        <th colspan="2">Maximum Limit per Policy Year (S$)</th>
                        <th colspan="2">Co-Payment (S$) / Co-Insurance (%)</th>
                    </tr>
                    <tr>
                        <th>Clinic on Company's panel</th>
                        <th>Non-panel Clinic</th>
                        <th>Clinic on Company's panel</th>
                        <th>Non-panel Clinic</th>
                        <th>Clinic on Company's panel</th>
                        <th>Non-panel Clinic</th>
                    </tr>
                    <tr>
                        <th class="text-left">Clinical GP</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th class="text-left">Specialist</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th class="text-left">Diagnostic X-Ray / Lab Tests</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th class="text-left">Dental</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th class="text-left">Others</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <!--    footer-->
    <div class="footer">
        <small>HSBC Life Ver. 1.0</small>
    </div>
</div>

<!--第十二页-->
<div class="page">
    <!--    header-->
    <div class="header">
        <div class="logo"></div>
    </div>
    <table class="padding text-left">
        <colgroup>
            <col span="2" style="width: 2em;">
            <col>
        </colgroup>
        <!--        5. BENEFIT: MATERNITY INSURANCE-->
        <tr>
            <th>5.</th>
            <th class="underline" colspan="2">BENEFIT: MATERNITY INSURANCE</th>
        </tr>
        <!--        a)-->
        <tr>
            <td></td>
            <td>a)</td>
            <td>Basis of Coverage</td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                <table class="border text-center cell-height padding-small">
                    <colgroup>
                        <col style="width: 3em;">
                        <col>
                        <col style="width: 12em;">
                    </colgroup>
                    <tr>
                        <th colspan="2">Category of Employees (refer to the example)</th>
                        <th># of Headcount</th>
                    </tr>
                        <#assign hasData = false>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gm" && personOverview.itemKey == "basis_of_coverage">
                                <#assign hasData = true>
                                <#assign total = personOverview.valueMaps?size />
                                <#list 0..3 as i>
                                    <tr>
                                        <td>
                                            <#if i == 0>(i)
                                            <#elseif i == 1>(ii)
                                            <#elseif i == 2>(iii)
                                            <#elseif i == 3>(iv)
                                            </#if>
                                        </td>
                                        <#if i < total>
                                            <#assign info = personOverview.valueMaps[i] />
                                            <td>${info.category_of_employees_occupation!}</td>
                                            <td>${info.no_of_headcount!}</td>
                                        <#else>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </#if>
                                    </tr>
                                </#list>
                                <#if total gt 4>
                                    <tr>
                                        <td colspan="3" class="text-left italic">
                                            More information, Please refer to the attachment.
                                        </td>
                                    </tr>
                                </#if>
                            </#if>
                        </#list>
                        <#if !hasData>
                            <#list 1..3 as i>
                                <tr>
                                    <td>
                                        <#if i == 1>(i)
                                        <#elseif i == 2>(ii)
                                        <#elseif i == 3>(iii)
                                        </#if>
                                    </td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>

                </table>
            </td>
        </tr>
        <tr>
            <td></td>
            <td></td>
            <td>
                <table class="no-padding text-left">
                    <tr>
                        <th class="underline" style="padding-bottom: 0;">Example 1</th>
                    </tr>
                    <tr>
                        <th>Category of Employees / Occupation</th>
                    </tr>
                    <tr>
                        <td>(i) &nbsp; Senior Management (Director, General Manager, Senior Manager)</td>
                    </tr>
                    <tr>
                        <td>(ii) &nbsp; Manager & Executive</td>
                    </tr>
                    <tr>
                        <td>(iii)&nbsp; All Others</td>
                    </tr>
                    <tr>
                        <th class="underline" style="padding-top: 1em !important;">Example 2</th>
                    </tr>
                    <tr>
                        <td>(i) &nbsp; All Employees</td>
                    </tr>
                </table>
            </td>
        </tr>
        <!--        b)-->
        <tr>
            <td></td>
            <td>b)</td>
            <td>Claims Experience for past 3 years</td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                <table class="border text-center cell-height padding-small">
                    <colgroup>
                        <col span="2" style="width: 18%">
                        <col span="4">
                    </colgroup>
                    <tr>
                        <th rowspan="2" class="text-left">Period of Coverage From / To<span class="border-bottom inline-block"
                                                                          style="width: 4em;"></span>(dd/mm/yyyy)
                        </th>
                        <th rowspan="2" class="text-left"># of Insured as at <span class="border-bottom inline-block"
                                                                 style="width: 4em;"></span>(dd/mm/yyyy)
                        </th>
                        <th colspan="2">Paid Claims</th>
                        <th colspan="2">Outstanding Claims</th>
                    </tr>
                    <tr>
                        <th># of Claims</th>
                        <th>Amount (S$)</th>
                        <th># of Claims</th>
                        <th>Amount (S$)</th>
                    </tr>
                        <#if isUploadClaimAttach?? && isUploadClaimAttach>
                            <tr>
                                <td class="text-left">Please refer to the attachment</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>

                            </tr>
                            <#list 1..2 as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>
                        <#assign displayedCount = 0>
                        <#list claimLogs?keys as key>
                            <#if key == "GM">
                                <#assign gpclaimList = claimLogs[key]>
                                <#list gpclaimList as claim>
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${claim.startTime}</td>
                                            <td>${claim.endTime}</td>
                                            <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                            <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                            <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                            <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>

                        <#if displayedCount < 3 && !isUploadClaimAttach >
                            <#list displayedCount..2 as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>
                    <tr>
                        <td class="italic text-left" colspan="6">Note: The insurer reserves the right to request for
                            more information.
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td colspan="3"></td>
        </tr>
        <!--        c)-->
        <tr>
            <td></td>
            <td class="align-top">c)</td>
            <td>
                <div>
                    Kindly attach a copy of the Schedule of Benefits if the benefits are on insured basis.
                </div>
                <div style="padding: 1em 0;">
                    If currently self-insured, kindly provide the following details:
                </div>
                <div>
                    Please indicate “Unlimited” if there is no cap and “NA” if it is not applicable.
                </div>
            </td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                <table class="padding border">
                    <tr>
                        <th style="width: 25%;">Benefits</th>
                        <th style="width: 40%;">Maximum Limit per Policy Year (S$)</th>
                        <th>Deductible / Co-insurance (S$)</th>
                    </tr>
                    <tr>
                        <td>Normal Delivery</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>Caesarian Delivery</td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>Others:</td>
                        <td></td>
                        <td></td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <!--    footer-->
    <div class="footer">
        <small>HSBC Life Ver. 1.0</small>
    </div>
</div>

<!--第十三页-->
<div class="page">
    <!--    header-->
    <div class="header">
        <div class="logo"></div>
    </div>
    <table class="padding-small text-left">
        <colgroup>
            <col span="2" style="width: 2em;">
            <col>
        </colgroup>
        <!--        6. NEEDS ANALYSIS & PRODUCT RECOMMENDATION-->
        <tr>
            <th>6.</th>
            <th class="underline" colspan="2">NEEDS ANALYSIS & PRODUCT RECOMMENDATION</th>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">Please tick the appropriate box to indicate the priority of your company's needs:</td>
        </tr>
        <tr>
            <td colspan="3" style="padding-left: 1em;">
                <table class="padding" style="border-collapse: separate; border-spacing: 1em;">
                    <tr>
                        <th class="text-center" style="width: 16em;">Company's Priorities</th>
                        <th class="text-center" style="width: 4em;">Low</th>
                        <th class="text-center" style="width: 4em;">Med</th>
                        <th class="text-center" style="width: 4em;">High</th>
                        <th style="width: 10em">Financial Planner <br> Recommendation</th>
                    </tr>
                    <tr>
                        <td>Cover for Outpatient medical expenses</td>
                        <#assign isChecked = false>
                        <#list userPlanDutyList as item>
                            <#if item.benefit == "GP" && item.participation == "1">
                                <#assign isChecked = true>
                            </#if>
                        </#list>
                        <td class="text-center" ><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                        <td class="text-center" ><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                        <td class="text-center" ><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                        <td  class="border-bottom" >
                            <div class="table-input"></div>
                        </td>

                    </tr>
                    <tr>
                        <td>Cover for Hospital & Surgical expenses</td>
                        <#assign isChecked = false>
                        <#list userPlanDutyList as item>
                            <#if item.benefit == "GHS" && item.participation == "1">
                                <#assign isChecked = true>
                            </#if>
                        </#list>
                        <td class="text-center" ><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                        <td class="text-center" ><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                        <td class="text-center" ><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                        <td  class="border-bottom" >
                            <div class="table-input"></div>
                        </td>
                    </tr>
                    <tr>
                        <td>Cover for Dental expenses</td>
                        <#assign isChecked = false>
                        <#list userPlanDutyList as item>
                            <#if item.benefit == "GD" && item.participation == "1">
                                <#assign isChecked = true>
                            </#if>
                        </#list>
                        <td class="text-center" ><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                        <td class="text-center" ><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                        <td class="text-center" ><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                        <td  class="border-bottom" >
                            <div class="table-input"></div>
                        </td>
                    </tr>
                    <tr>
                        <td>Cover for Major illnesses <br>
                            (e.g. cancer, kidney failure, etc.)
                        </td>
                            <#assign isChecked = false>
                            <#list userPlanDutyList as item>
                                <#if item.benefit == "GMM" && item.participation == "1">
                                    <#assign isChecked = true>
                                </#if>
                            </#list>
                            <td class="text-center" ><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                            <td class="text-center" ><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                            <td class="text-center" ><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                            <td  class="border-bottom" >
                                <div class="table-input"></div>
                            </td>
                    </tr>
                    <tr>
                        <td>Cover for Loss of Income due to <br>
                            sickness or accident
                        </td>
                            <#assign isChecked = false>
                            <#list userPlanDutyList as item>
                                <#if item.benefit == "GDI" && item.participation == "1">
                                    <#assign isChecked = true>
                                </#if>
                            </#list>
                            <td class="text-center" ><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                            <td class="text-center" ><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                            <td class="text-center" ><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                            <td  class="border-bottom" >
                                <div class="table-input"></div>
                            </td>
                    </tr>
                    <tr>
                        <td>Cover for long term medical treatment</td>
                            <#assign isChecked = false>
                            <#list userPlanDutyList as item>
                                <#if item.benefit == "GTL" && item.participation == "1">
                                    <#assign isChecked = true>
                                </#if>
                            </#list>
                            <td class="text-center" ><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                            <td class="text-center" ><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                            <td class="text-center" ><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                            <td  class="border-bottom" >
                                <div class="table-input"></div>
                            </td>
                    </tr>
                    <tr>
                        <td>Others :</td>
                        <td class="border-bottom" colspan="4"></td>
                    </tr>
                </table>
            </td>
        </tr>
        <!--        7-->
        <tr>
            <th>7.</th>
            <th class="underline" colspan="2">DECLARATION</th>
        </tr>
        <tr>
            <th></th>
            <td colspan="2" style="text-align: justify;">I / We hereby declare that, to the best of my / our knowledge and belief, the information given here is true
                and complete, and agree that if a contract of insurance is effected, all information submitted in connection
                with this application shall form the basis of such contract between the Company and the Insurer. We
                further declare, where we have provided any personal data, we have the consent of the data owners to
                disclose such data to HSBC Life (Singapore) Pte. Ltd. or their representatives/agents for the purposes
                of furnishing quote(s) or estimate(s) for Group Insurance Policy.</td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2" style="padding-top: 1em;">
                <div class="border-bottom border-bold" style="width: 20em;">
                    &emsp;
                </div>
                <div>Signature of Authorised Officer</div>
            </td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                <table class="padding-y font-small">
                    <tr>
                        <td style="width: 50%;">
                            <div>Name:</div>
                            <div>NRIC/ Fin No</div>
                            <div>Designation:</div>
                            <div>Date:</div>
                        </td>
                        <td>
                            <div>Company Stamp (if applicable):</div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                I / We declare and acknowledge that I / we have reviewed this Group Insurance Fact-Finding Form with
                the authorised officer of the Company, and that I / we have explained all the requirements of this Fact-Finding form to him / her.
            </td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2" style="padding-top: 1em;">
                <div class="border-bottom border-bold" style="width: 20em;">
                    &emsp;
                </div>
                <div>Signature of Financial Planner</div>
            </td>
        </tr>
        <tr>
            <td></td>
            <td colspan="2">
                <table class="padding-y font-small">
                    <tr>
                        <td style="width: 50%;">
                            <div>Name:</div>
                            <div>NRIC/ Fin No</div>
                            <div>Designation:</div>
                            <div>Date:</div>
                        </td>
                        <td>
                            <div>Company Stamp (if applicable):</div>
                        </td>
                    </tr>
                </table>
            </td>
        </tr>
    </table>
    <!--    footer-->
    <div class="footer">
        <small>HSBC Life Ver. 1.0</small>
    </div>
</div>
</body>
</html>
