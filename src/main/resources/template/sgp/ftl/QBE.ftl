<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>QBE FIFF</title>
        <style>
            body,ol,ul,h1,h2,h3,h4,h5,h6,p,th,td,dl,dd,form,fieldset,legend,input,textarea,select{margin:0;padding:0;}
            .clearfix::after {
            content: "";
            display: table;
            clear: both;
            }
            body{
            background: #ccc;
            }
            .page{
            width: 990px;
            height: 1579px;
            background: #fff;
            margin: 0 auto;
            padding: 50px 80px;
            position: relative;
            }
            .page-footer{
            position: absolute;
            bottom: 20px;
            left: 70px;
            }
            .page-info{
            text-align: right;
            font-size: 14px;
            margin-bottom: 6px;
            width: 940px;
            }
            .header{
            width: 990px;
            height: 167px;
                background-size: 100% 100%;
                background-repeat: no-repeat;
                background-image: url("data:image/png;base64,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");
            }
            .header .title{
            display: inline-block;
            float: left;
            text-align: center;
            margin-top: -30px;
            width: 700px;
            }
            .font12{
            font-size: 12px;
            }
            .font14{
            font-size: 14px;
            }
            .font16{
            font-size: 16px;
            }
            .font18{
            font-size: 18px;
            }
            .form{
            margin-top: 12px;
            }
            .form-item{
            margin-bottom: 16px;
            }
            .sub-item{
            display: inline-block;
            float: left;
            }
            .sub-item-content{
            height: 20px;
            border-bottom: 1px solid #333;
            position: relative;
            }
            .item-describle{
            position: absolute;
            bottom: -16px;
            left: 50%;
            margin-left: -40px;
            }
            .fl{
            display: inline-block;
            float: left;
            }
            .form-item-content{
            padding-left: 10px;
            }
            .mb-8{
            margin-bottom: 12px;
            }
            table{
            width: 100%;
            table-layout: fixed;
            }
            table td{
            padding: 2px 12px;
            }
            .mt-12{
            margin-top: 12px;
            }
            .table-index{
            margin-right: 10px;
            }
            .table-list{
            margin: 50px 0;
            }
            .big-td td{
            padding: 16px;
            }
            .mb-20{
            margin-bottom: 20px;
            }
            .page-five .table-list{
            margin: 8px 0;
            }
            .example-list table td{
            padding: 8px 0;
            }
            .table-input{
            border-bottom: 1px solid #333;
            height: 24px;
            width: 100%;
            }
            .page-fifth .table-list{
            margin: 30px 0;
            }
            .page-fifth td {
            padding: 10px 0;
            }
            .t-center{
            text-align: center;
            }
            .m-tb-20{
            margin: 20px 0;
            }
            .text-center {
                text-align: center;
            }
            .text-left {
                text-align: left;
            }
            .text-right {
                text-align: right;
            }
            .font-bold {
                font-weight: bold;
            }
            .underline {
                text-decoration: underline;
            }
            .underline-bold {
                text-decoration: underline;
                text-decoration-thickness: 2px;
            }
            .nowrap {
                white-space: nowrap;
            }
            .wrap {
                white-space: normal;
            }
            .italic {
                font-style: italic;
            }
            .line-4em {
                display: inline-block;
                border-bottom: 2px solid #333;
                width: 4em;
            }
            .line-6em {
                display: inline-block;
                border-bottom: 2px solid #333;
                width: 6em;
            }
            table.border th,
            table.border td {
                border: 2px solid #333;
            }
            table.padding td,
            table.padding th {
                padding: 4px 8px;
            }
            table.collapse {
                border-collapse: collapse;
            }
            table.common-table {
                border-collapse: collapse;
            }
            table.common-table th,
            table.common-table td {
                padding: 4px 8px;
                border: 2px solid #333;
            }
            .border-none {
                border: none !important;
            }
            .border-bottom-bold {
                border-bottom: 2px solid #333;
            }
            .overflow-hidden {
                overflow: hidden;
            }
            .inline-block {
                display: inline-block;
            }
            .py-0 {
                padding-top: 0 !important;
                padding-bottom: 0 !important;
            }
        </style>
    </head>
    <body>
        <!-- 第一页 -->
        <div class="page">
            <div class="header"></div>
            <h1 class="t-center m-tb-20">GROUP INSURANCE FACT-FINDING FORM</h1>
            <div class="p-content mb-8">
                <strong>KINDLY COMPLETE FULLY IN BLOCK LETTER AND INK </strong>
                (Tick boxes [√] where appropriate)
            </div>
            <div class="form">
                <div class="form-item clearfix">
                    <div class="label fl"><strong>PERIOD OF INSURANCE</strong></div>
                    <div class="form-item-content clearfix fl">
                        <div class="sub-item clearfix">
                            <div class="sub-label fl">from:</div>
                            <div class="sub-item-content fl text-center border-bottom-bold" style="width: 360px;">
                                ${baseInfo.expectStartTime!}
                                <div class="item-describle font12">(dd/mm/yyyy)</div>
                            </div>
                        </div>
                        <div class="sub-item clearfix">
                            <div class="sub-label fl">to</div>
                            <div class="sub-item-content fl text-center border-bottom-bold" style="width: 360px;">
                                ${baseInfo.expectEndTime!}
                                <div class="item-describle font12">(dd/mm/yyyy)</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-item clearfix">
                    <div class="label fl"><strong>REQUEST FOR QUOTATION</strong></div>
                    <div class="form-item-content clearfix fl">
                        <div class="sub-item clearfix">
                            <div class="sub-label fl">was submitted on</div>
                            <div class="sub-item-content fl text-center border-bottom-bold" style="width: 623px;">
                                ${baseInfo.quoteTime!}
                                <div class="item-describle font12">(dd/mm/yyyy)</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-item clearfix">
                    <div class="label fl"><strong>REQUEST FROM:</strong></div>
                    <div class="form-item-content clearfix fl">
                        <div class="sub-item clearfix">
                            <div class="sub-label fl"></div>
                            <div class="sub-item-content fl t-center text-center border-bottom-bold font-bold" style="width: 834px;">
                                QBE INSURANCE (SINGAPORE) PTE LTD
                                <div class="item-describle font12">(Name of Insurance Company)</div>
                            </div>
                        </div>
                    </div>
                </div>
                <h4 class="mb-8">GENERAL INFORMATION</h4>
                <div class="form-item clearfix">
                    <div class="label fl">Name of Company: </div>
                    <div class="form-item-content clearfix fl">
                        <div class="sub-item clearfix">
                            <div class="sub-label fl"></div>
                            <div class="sub-item-content fl text-center border-bottom-bold" style="width: 826px;">  ${baseInfo.companyName!}</div>
                        </div>
                    </div>
                </div>
                <div class="form-item clearfix">
                    <div class="label fl">Nature of Business:</div>
                    <div class="form-item-content clearfix fl">
                        <div class="sub-item clearfix">
                            <div class="sub-label fl"></div>
                            <div class="sub-item-content fl text-center border-bottom-bold" style="width: 824px;">  ${baseInfo.companyNature!} </div>
                        </div>
                    </div>
                </div>
                <div class="mb-8">Presently insured?
                    <strong>
                        <#if baseInfo.presentlyInsured == "1">Yes</#if>
                        <#if baseInfo.presentlyInsured == "0">No</#if>
                    </strong>
                </div>
                <div class="form-item clearfix">
                    <div class="label fl">If <strong>Yes</strong>, name of current insurer:  </div>
                    <div class="form-item-content clearfix fl">
                        <div class="sub-item clearfix">
                            <div class="sub-label fl"></div>
                            <div class="sub-item-content fl text-center border-bottom-bold" style="width: 738px;">
                                ${baseInfo.currentEbInsurer!}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-item clearfix">
                    <div class="label fl">Type of Policy:  </div>
                    <div class="form-item-content clearfix fl">
                        <div class="sub-item clearfix">
                            <div class="sub-label fl"></div>
                            <div class="sub-item-content fl text-center border-bottom-bold" style="width: 859px;">
                                Employee Benefit
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-item clearfix">
                    <div class="label fl">Period of Insurance:</div>
                    <div class="form-item-content clearfix fl">
                        <div class="sub-item clearfix">
                            <div class="sub-label fl">From:</div>
                            <div class="sub-item-content fl text-center border-bottom-bold" style="width: 376px;">
                                ${baseInfo.insuranceStartTime!}
                                <div class="item-describle font12">(dd/mm/yyyy)</div>
                            </div>
                        </div>
                        <div class="sub-item clearfix">
                            <div class="sub-label fl">To</div>
                            <div class="sub-item-content fl text-center border-bottom-bold" style="width: 376px;">
                                ${baseInfo.insuranceEndTime!}
                                <div class="item-describle font12">(dd/mm/yyyy)</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-item clearfix">
                    <div class="label fl"></div>
                    <div class="form-item-content clearfix fl" style="padding-left: 0;">
                        <div class="sub-item clearfix">
                            <div class="sub-label fl">Total No. of Employees:</div>
                            <div class="sub-item-content fl text-center border-bottom-bold" style="width: 276px;">
                                ${baseInfo.totalEmployees!}
                            </div>
                        </div>
                        <div class="sub-item clearfix">
                            <div class="sub-label fl">No. of Employees to be insured:</div>
                            <div class="sub-item-content fl text-center border-bottom-bold" style="width: 276px;">${baseInfo.insuredEmployees!}</div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-content mb-8">
                <strong>Participation: The insurer will assume that participation of the group insurance program is on compulsory basis unless otherwise stated.</strong>
                Please tick [√] accordingly to the choice of the insurance product that you like to have a quote from us.
            </div>
            <table class="common-table">
                <colgroup>
                    <col style="width: 100px;">
                    <col style="width: 40px;" class="text-center">
                    <col style="width: 150px;">
                    <col>
                    <col span="2" style="width: 150px;">
                </colgroup>
                <tr>
                    <th rowspan="2" class="text-left">Benefits</th>
                    <th rowspan="2" colspan="3">Insurance Coverage</th>
                    <th colspan="2">Participation</th>
                </tr>
                <tr>
                    <th>Compulsory</th>
                    <th>Voluntary</th>
                </tr>
                <tr>
                    <td> <strong>Life Insurance</strong></td>
                    <td class="text-center"><strong>1</strong></td>
                    <td colspan="2">Group Personal Accident (GPA) </td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GPA">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center">
                        <input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1">
                    </td>
                    <td class="text-center">
                        <input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2">
                    </td>
                </tr>
                <tr>
                    <td rowspan="4"><strong>Medical</strong></td>
                    <td rowspan="4" class="text-center"><strong>2</strong></td>
                    <td rowspan="2">Group Hospital & Surgical (GHS)</td>
                    <td>Employee only</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GHS">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center">
                        <input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1">
                    </td>
                    <td class="text-center">
                        <input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2">
                    </td>
                </tr>
                <tr>
                    <td>Dependant (Spouse and/or Children)</td>
                    <td class="text-center"><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td rowspan="2">Group Major Medical (GMM)</td>
                    <td>Employee only</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GMM">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center">
                        <input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1">
                    </td>
                    <td class="text-center">
                        <input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2">
                    </td>
                </tr>
                <tr>
                    <td>Dependant (Spouse and/or Children)</td>
                    <td class="text-center"><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td rowspan="6"><strong>Others</strong></td>
                    <td rowspan="4" class="text-center"><strong>3</strong></td>
                    <td rowspan="2">Group Outpatient</td>
                    <td>Employee only</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GP">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center">
                        <input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1">
                    </td>
                    <td class="text-center">
                        <input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2">
                    </td>
                </tr>
                <tr>
                    <td>Dependant (Spouse and/or Children)</td>
                    <td class="text-center"><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td rowspan="2">Dental</td>
                    <td>Employee only</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GD">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center">
                        <input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1">
                    </td>
                    <td class="text-center">
                        <input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2">
                    </td>
                </tr>
                <tr>
                    <td>Dependant (Spouse and/or Children)</td>
                    <td class="text-center"><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td rowspan="2" class="text-center"><strong>4</strong></td>
                    <td rowspan="2">Maternity</td>
                    <td>Employee only</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GM">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center">
                        <input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1">
                    </td>
                    <td class="text-center">
                        <input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2">
                    </td>
                </tr>
                <tr>
                    <td>Dependant (Spouse)</td>
                    <td class="text-center"><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
            </table>
            <p class="mt-12 italic">Note: Participation is voluntary if employees or dependants are given the choice to opt for the cover(s), subject to a minimum participation level.</p>
            <div class="page-footer">
                <div class="page-info">Effective November 2020</div>
            </div>
        </div>
        <!-- 第二页 -->
        <div class="page">
            <div class="header"></div>
            <div class="table-list big-td clearfix">
                <div class="fl table-index font-bold">1</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Are there any members currently in hospital or requires frequent admission (e.g. hospital admission more than 2 times per year) to hospital?
                        <strong>
                            <#assign hasYes=false>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="1">
                                    <#assign hasYes=true>
                                </#if>
                            </#list>
                            <#if hasYes>
                                Yes
                            <#else>
                                No
                            </#if>
                        </strong>
                    </div>
                    <div class="mb-8">If <strong>Yes</strong>, kindly provide the following details:</div>
                    <table class="common-table text-center">
                        <tr>
                            <th style="width: 50px;">S/N</th>
                            <th style="width: 150px;"># of members /Age</th>
                            <th>Reason of hospitalisation / Nature of illness</th>
                            <th style="width: 150px;">Total Sum Insured / Plan</th>
                        </tr>
                        <#-- 遍历数据 -->
                        <#assign count=0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="1">
                                <#if count lt 3>
                                    <tr>
                                        <td>${count + 1}</td>
                                        <td>${info.number!""}</td>
                                        <td class="text-left">${info.content!""}</td>
                                        <td>${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                </#if>
                                <#assign count=count+1>
                            </#if>
                        </#list>

                        <#-- 补空行到3行 -->
                        <#if count lt 3>
                            <#list 1..(3-count) as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>

                        <#-- 超过3条数据，增加提示行 -->
                        <#if count gt 3>
                            <tr>
                                <td colspan="4" class="italic text-left">More information, please refer to the attachment.</td>
                            </tr>
                        </#if>
                        <tr>
                            <td colspan="4" class="italic text-left">
                                Note: The insurer will not reimburse the hospital claims for any member in hospital at the time of application.
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="table-list big-td clearfix">
                <div class="fl table-index font-bold">2</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Has any member suffered or is suffering from any serious condition such as cancer, organ failure, heart disease, stroke, liver disorder, arthritis or any other disorder that causes
                        progressive irreversible functional or physical disability?
                        <strong>
                            <#assign hasYes=false>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="2">
                                    <#assign hasYes=true>
                                </#if>
                            </#list>
                            <#if hasYes>
                                Yes
                            <#else>
                                No
                            </#if>
                        </strong>
                    </div>
                    <div class="mb-8">If <strong>Yes</strong>, kindly provide the following details:</div>
                    <table class="common-table text-center">
                        <tr>
                            <th style="width: 50px;">S/N</th>
                            <th style="width: 150px;"># of members /Age</th>
                            <th>Reason of hospitalisation / Nature of illness</th>
                            <th style="width: 150px;">Total Sum Insured / Plan</th>
                        </tr>
                        <#-- 遍历数据 -->
                        <#assign count=0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="2">
                                <#if count lt 3>
                                    <tr>
                                        <td>${count + 1}</td>
                                        <td>${info.number!""}</td>
                                        <td class="text-left">${info.content!""}</td>
                                        <td>${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                </#if>
                                <#assign count=count+1>
                            </#if>
                        </#list>

                        <#-- 补空行到3行 -->
                        <#if count lt 3>
                            <#list 1..(3-count) as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>

                        <#-- 超过3条数据，增加提示行 -->
                        <#if count gt 3>
                            <tr>
                                <td colspan="4" class="italic text-left">More information, please refer to the attachment.</td>
                            </tr>
                        </#if>
                        <tr>
                            <td colspan="4" class="italic text-left">
                                Note: The insurer will not reimburse the hospital claims for any member in hospital at the time of application.
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="page-footer">
                <div class="page-info">Effective November 2020</div>
            </div>
        </div>
        <!-- 第三页 -->
        <div class="page">
            <div class="header"></div>
            <div class="table-list big-td clearfix">
                <div class="fl table-index font-bold">3</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Is there any member based outside Singapore?
                        <strong>
                            <#assign hasYes=false>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="3">
                                    <#assign hasYes=true>
                                </#if>
                            </#list>
                            <#if hasYes>
                                Yes
                            <#else>
                                No
                            </#if>
                        </strong>
                    </div>
                    <div class="mb-8">If <strong>Yes</strong>, kindly provide the following details:</div>
                    <table class="common-table text-center">
                        <tr>
                            <th style="width: 50px;">S/N</th>
                            <th style="width: 150px;"># of members /Age</th>
                            <th>Country based in</th>
                            <th style="width: 150px;">Total Sum Insured / Plan</th>
                        </tr>
                        <#-- 遍历数据 -->
                        <#assign count=0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="3">
                                <#if count lt 3>
                                    <tr>
                                        <td>${count + 1}</td>
                                        <td>${info.number!""}</td>
                                        <td class="text-left">${info.content!""}</td>
                                        <td>${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                </#if>
                                <#assign count=count+1>
                            </#if>
                        </#list>

                        <#-- 补空行到3行 -->
                        <#if count lt 3>
                            <#list 1..(3-count) as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>

                        <#-- 超过3条数据，增加提示行 -->
                        <#if count gt 3>
                            <tr>
                                <td colspan="4" class="text-left italic">More information, please refer to the attachment.</td>
                            </tr>
                        </#if>
                        <tr>
                            <td colspan="4" class="text-left italic">
                                Note: The insurer will not reimburse the hospital claims for any member in hospital at the time of application.
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="table-list big-td clearfix">
                <div class="fl table-index font-bold">4</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Are there any limitations or exclusions imposed on the coverage on any members?
                        <strong>
                            <#assign hasYes=false>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="4">
                                    <#assign hasYes=true>
                                </#if>
                            </#list>
                            <#if hasYes>
                                Yes
                            <#else>
                                No
                            </#if>
                        </strong>
                    </div>
                    <div class="mb-8">If <strong>Yes</strong>, kindly provide the following details:</div>
                    <table class="common-table text-center">
                        <tr>
                            <th style="width: 50px;">S/N</th>
                            <th style="width: 150px;"># of members /Age</th>
                            <th>Limitations / Exclusions</th>
                            <th style="width: 150px;">Total Sum Insured / Plan</th>
                        </tr>
                        <#-- 遍历数据 -->
                        <#assign count=0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="4">
                                <#if count lt 3>
                                    <tr>
                                        <td>${count + 1}</td>
                                        <td>${info.number!""}</td>
                                        <td class="text-left">${info.content!""}</td>
                                        <td>${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                </#if>
                                <#assign count=count+1>
                            </#if>
                        </#list>

                        <#-- 补空行到3行 -->
                        <#if count lt 3>
                            <#list 1..(3-count) as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>

                        <#-- 超过3条数据，增加提示行 -->
                        <#if count gt 3>
                            <tr>
                                <td colspan="4" class="italic text-left">More information, please refer to the attachment.</td>
                            </tr>
                        </#if>
                        <tr>
                            <td colspan="4" class="italic text-left">
                                Note: The insurer will not reimburse the hospital claims for any member in hospital at the time of application.
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="page-footer">
                <div class="page-info">Effective November 2020</div>
            </div>
        </div>
        <!-- 第四页 -->
        <div class="page">
            <div class="header">
            </div>
            <div class="table-list big-td clearfix">
                <div class="fl table-index font-bold">5</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Is there any member engaged in hazardous occupation?
                        <strong>
                            <#assign hasYes=false>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="5">
                                    <#assign hasYes=true>
                                </#if>
                            </#list>
                            <#if hasYes>
                                Yes
                            <#else>
                                No
                            </#if>
                        </strong>
                        <br> (Hazardous occupation eg. welder, diver, sandblaster, offshore workers etc.)
                    </div>
                    <div class="mb-8">If <strong>Yes</strong>, kindly provide the following details:</div>
                    <table class="common-table text-center">
                        <tr>
                            <th style="width: 50px;">S/N</th>
                            <th style="width: 150px;"># of members /Age</th>
                            <th>Nature of work</th>
                            <th style="width: 150px;">Total Sum Insured / Plan</th>
                        </tr>
                        <#-- 遍历数据 -->
                        <#assign count=0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="5">
                                <#if count lt 3>
                                    <tr>
                                        <td>${count + 1}</td>
                                        <td>${info.number!""}</td>
                                        <td class="text-left">${info.content!""}</td>
                                        <td>${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                </#if>
                                <#assign count=count+1>
                            </#if>
                        </#list>

                        <#-- 补空行到3行 -->
                        <#if count lt 3>
                            <#list 1..(3-count) as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>

                        <#-- 超过3条数据，增加提示行 -->
                        <#if count gt 3>
                            <tr>
                                <td colspan="4" class="italic text-left">More information, please refer to the attachment.</td>
                            </tr>
                        </#if>
                        <tr>
                            <td colspan="4" class="text-left italic">
                                Note: The insurer will not reimburse the hospital claims for any member in hospital at the time of application.
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="table-list big-td clearfix">
                <div class="fl table-index font-bold">6</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">To the best of your knowledge, is there any member engaged in hazardous sports?
                        <strong>
                            <#assign hasYes=false>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="6">
                                    <#assign hasYes=true>
                                </#if>
                            </#list>
                            <#if hasYes>
                                Yes
                            <#else>
                                No
                            </#if>
                        </strong>
                        <br> (Hazardous sports eg. scuba diving, motor racing, bungee jumping etc.)
                    </div>
                    <div class="mb-8">If <strong>Yes</strong>, kindly provide the following details:</div>
                    <table class="common-table text-center">
                        <tr>
                            <th style="width: 50px;">S/N</th>
                            <th style="width: 150px;"># of members /Age</th>
                            <th>Type of sports</th>
                            <th style="width: 150px;">Total Sum Insured / Plan</th>
                        </tr>
                        <#-- 遍历数据 -->
                        <#assign count=0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="6">
                                <#if count lt 3>
                                    <tr>
                                        <td>${count + 1}</td>
                                        <td>${info.number!""}</td>
                                        <td class="text-left">${info.content!""}</td>
                                        <td>${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                </#if>
                                <#assign count=count+1>
                            </#if>
                        </#list>

                        <#-- 补空行到3行 -->
                        <#if count lt 3>
                            <#list 1..(3-count) as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>

                        <#-- 超过3条数据，增加提示行 -->
                        <#if count gt 3>
                            <tr>
                                <td colspan="4" class="text-left italic">More information, please refer to the attachment.</td>
                            </tr>
                        </#if>
                        <tr>
                            <td colspan="4" class="italic text-left">
                                Note: The insurer will not reimburse the hospital claims for any member in hospital at the time of application.
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="page-footer">
                <div class="page-info">Effective November 2020</div>
            </div>
        </div>
        <!-- 第五页 -->
        <div class="page page-five">
            <div class="header">
            </div>
            <div class="table-list clearfix mb-20">
                <div class="fl table-index font-bold">1</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8 underline-bold"><strong>BENEFIT: GROUP PERSONAL ACCIDENT INSURANCE</strong></div>
                    <div class="mb-8"><strong>Occupational Classifications</strong></div>
                    <table border="1" cellspacing="0">
                        <tr>
                            <td style="width: 80px;">Class 1</td>
                            <td>Clerical, administrative or other similar non-hazardous occupations</td>
                        </tr>
                        <tr>
                            <td>Class 2</td>
                            <td>Occupations where some degree of risk is involved, e.g. supervision of manual workers, totally administrative job in an industrial environment</td>
                        </tr>
                        <tr>
                            <td>Class 3</td>
                            <td>Occupations involving regular light to medium manual work but no substantial hazard which may increase the risk of sickness or accident</td>
                        </tr>
                        <tr>
                            <td>Class 4</td>
                            <td>High risk occupations involving heavy manual work including hot works</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">a)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Basis of Coverage</div>
                    <table class="mb-20 common-table text-center">
                        <tr>
                            <th style="width: 100px;" class="border-none"></th>
                            <th style="width: 40px;" class="border-none"></th>
                            <th>Category of <br>Employees/Occupation <br>(refer to the examples)</th>
                            <th>Basis of Coverage - Sum <br>Insured (refer to the <br>examples)</th>
                            <th># of Employees</th>
                        </tr>
                        <#-- 查找数据 -->
                        <#assign gtlData = "">
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gpa" && personOverview.itemKey == "basis_of_coverage">
                                <#assign gtlData = personOverview.valueMaps>
                            </#if>
                        </#list>

                        <#-- 固定四行渲染 -->
                        <#list 0..3 as i>
                            <tr>
                                <#if i == 0>
                                    <td rowspan="4">GPA</td>
                                </#if>
                                <td>
                                    <#if i == 0>(i)
                                    <#elseif i == 1>(ii)
                                    <#elseif i == 2>(iii)
                                    <#elseif i == 3>(iv)
                                    </#if>
                                </td>

                                <#-- 判断当前行是否有数据 -->
                                <#if gtlData?has_content && i < gtlData?size>
                                    <#assign info = gtlData[i]>
                                    <td>${info.category_of_employees_occupation!}</td>
                                    <td>${info.basis_of_coverage_sum_insured!}</td>
                                    <td>${info.no_of_employees!}</td>
                                <#else>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </#if>
                            </tr>
                        </#list>

                        <#-- 如果数据超过四条，显示附件提示 -->
                        <#if gtlData?has_content && 4 < gtlData?size>
                            <tr>
                                <td colspan="5" class="text-left italic">
                                    More information, please refer to the attachment.
                                </td>
                            </tr>
                        </#if>
                    </table>
                </div>
            </div>
            <div class="example-list">
                <h4 class="underline-bold">Example 1</h4>
                <table class="mb-20" cellspacing="0">
                    <tr>
                        <th style="text-align: left;">Category of <br>Employees / <br>Occupation</th>
                        <th style="width: 300px; text-align: left">Basis of Coverage</th>
                    </tr>
                    <tr>
                        <td>(i) Senior Management (Director, General Manager, Senior Manager)</td>
                        <td>100,000</td>
                    </tr>
                    <tr>
                        <td>(ii) Manager & Executive</td>
                        <td>50,000</td>
                    </tr>
                    <tr>
                        <td>(iii) All Others</td>
                        <td>25,000</td>
                    </tr>
                </table>
            </div>
            <div class="example-list">
                <h4 class="underline-bold">Example 2</h4>
                <table class="mb-20" cellspacing="0">
                    <tr>
                        <th style="text-align: left;">Category of Employees / Occupation</th>
                        <th style="width: 300px; text-align: left;">Basis of Coverage</th>
                    </tr>
                    <tr>
                        <td>(i) All Employees</td>
                        <td> 24 X Basic Monthly Salary*</td>
                    </tr>
                </table>
            </div>
            <i>* Please provide salary information if the basis of coverage is in terms of basic monthly salary.</i>
            <div class="page-footer">
                <div class="page-info">Effective November 2020</div>
            </div>
        </div>
        <!-- 第六页 -->
        <div class="page">
            <div class="header">
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">b)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Claims Experience for the past 3 years</div>
                    <h5 class="font18 mb-20 underline-bold">Paid Claims</h5>
                    <table class="mb-20 common-table text-center">
                        <tr>
                            <th rowspan="2" class="text-left">Period of Coverage <br> From / To <br> <span class="line-4em"></span> <br>(dd/mm/yyyy)</th>
                            <th rowspan="2" class="text-left"># of Insured as at <br> <span class="line-4em"></span> <br> (dd/mm/yyyy)</th>
                            <th colspan="2" class="text-center">Paid Claims</th>
                        </tr>
                        <tr>
                            <th class="text-center"># of Claims</th>
                            <th class="text-center">Amount(S$)</th>
                        </tr>
                        <#assign fixedRows = 3>

                        <#if isUploadClaimAttach?? && isUploadClaimAttach>
                            <tr>
                                <td>Please refer to attachment</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <#list 1..(fixedRows - 1) as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        <#else>
                            <#assign displayedCount = 0>
                            <#list claimLogs?keys as key>
                                <#if key == "GPA">
                                    <#assign gpclaimList = claimLogs[key]>
                                    <#list gpclaimList as claim>
                                        <#if displayedCount < 3>
                                            <tr>
                                                <td>${claim.startTime!}</td>
                                                <td>${claim.endTime!}</td>
                                                <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                                <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                            </tr>
                                            <#assign displayedCount = displayedCount + 1>
                                        <#else>
                                            <tr>
                                                <td colspan="4" class="text-left italic">More information, please refer to the attachment.</td>
                                            </tr>
                                            <#break>
                                        </#if>
                                    </#list>
                                </#if>
                            </#list>
                            <#if displayedCount == 0>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#if>
                        </#if>
                        <tr>
                            <td colspan="4" class="text-left">
                                <i>Note: The insurer reserves the right to request for more information.</i>
                            </td>
                        </tr>
                    </table>
                    <h5 class="font18 mb-20 underline-bold">Outstanding Claims</h5>
                    <table class="mb-20 common-table text-center">
                        <tr>
                            <th rowspan="2" class="text-left">Period of Coverage <br> From / To <br> <span class="line-4em"></span> <br>(dd/mm/yyyy)</th>
                            <th rowspan="2" class="text-left"># of Insured as at <br><span class="line-4em"></span><br>(dd/mm/yyyy)</th>
                            <th colspan="2" class="text-center">Outstanding Claims</th>
                        </tr>
                        <tr>
                            <th class="text-center"># of Claims</th>
                            <th class="text-center">Amount(S$)</th>
                        </tr>
                        <#assign fixedRows = 3>

                        <#if isUploadClaimAttach?? && isUploadClaimAttach>
                            <tr>
                                <td>Please refer to attachment</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <#list 1..(fixedRows - 1) as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        <#else>
                            <#assign displayedCount = 0>
                            <#list claimLogs?keys as key>
                                <#if key == "GPA">
                                    <#assign gpclaimList = claimLogs[key]>
                                    <#list gpclaimList as claim>
                                        <#if displayedCount < 3>
                                            <tr>
                                                <td>${claim.startTime!}</td>
                                                <td>${claim.endTime!}</td>
                                                <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                                <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                            </tr>
                                            <#assign displayedCount = displayedCount + 1>
                                        <#else>
                                            <tr>
                                                <td colspan="4" class="italic text-left">More information, please refer to the attachment.</td>
                                            </tr>
                                            <#break>
                                        </#if>
                                    </#list>
                                </#if>
                            </#list>
                            <#if displayedCount == 0>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#if>
                        </#if>
                        <tr>
                            <td colspan="4" class="text-left">
                                <i>Note: The insurer reserves the right to request for more information.</i>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="page-footer">
                <div class="page-info">Effective November 2020</div>
            </div>
        </div>
        <!-- 第七页 -->
        <div class="page">
            <div class="header">
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index"><strong>2</strong></div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8 underline-bold"><strong>BENEFIT: GROUP HOSPITAL & SURGICAL INSURANCE / MAJOR MEDICAL INSURANCE</strong></div>
                </div>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">a)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Basis of Coverage</div>
                </div>
                <table class="mb-20 common-table text-center">
                    <colgroup>
                        <col style="width: 50px;">
                        <col>
                        <col style="width: 200px">
                        <col style="width: 150px" span="2">
                    </colgroup>
                    <tr>
                        <th colspan="2" class="text-left">Category of Employees / Occupation</th>
                        <th>Room & Board Benefit Plan (S$)</th>
                        <th>Currently with TMIS Yes / No</th>
                        <th>Proposal with TMIS Yes / No</th>
                    </tr>
                    <#-- 定义数据列表，避免空指针 -->
                    <#assign filteredList = []>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "ghs" && personOverview.itemKey == "basis_of_coverage">
                            <#assign filteredList = personOverview.valueMaps![]>
                        </#if>
                    </#list>

                    <#-- 固定输出4行，不足补空 -->
                    <#list 0..3 as idx>
                        <tr>
                            <#if idx == 0>
                                <td>(i)</td>
                            <#elseif idx == 1>
                                <td>(ii)</td>
                            <#elseif idx == 2>
                                <td>(iii)</td>
                            <#elseif idx == 3>
                                <td>(iv)</td>
                            </#if>

                            <#if idx lt filteredList?size>
                                <td class="text-left">${filteredList[idx].category_of_employees_occupation!""}</td>
                                <td>${filteredList[idx].room_and_board_benefit_plan!""}</td>
                                <td>
                                    <#if filteredList[idx].currently_with_tmis?string == "1">
                                        Yes
                                    <#elseif filteredList[idx].currently_with_tmis?string == "0">
                                        No
                                    <#else>
                                        &nbsp;
                                    </#if>
                                </td>

                                <td>
                                    <#if filteredList[idx].proposal_with_tmis?string == "1">
                                        Yes
                                    <#elseif filteredList[idx].proposal_with_tmis?string == "0">
                                        No
                                    <#else>
                                        &nbsp;
                                    </#if>
                                </td>
                            <#else>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </#if>
                        </tr>
                    </#list>

                    <#-- 如果有超过4行数据，显示提示 -->
                    <#if filteredList?size gt 4>
                        <tr>
                            <td colspan="5" class="text-left italic" >
                                More information, please refer to the attachment.
                            </td>
                        </tr>
                    </#if>
                </table>
                <div><strong>Important Note:</strong></div>
                <p><strong>(1) Dependants can be covered under Group Hospital & Surgical Plan. Their cover should be the same as the employee's cover.</strong></p>
                <p><strong>(2) Please provide the Deductible /Co-insurance for respective employee category or occupation, if applicable.</strong></p>
            </div>
            <div class="example-list">
                <h4 class="underline-bold">Example 1</h4>
                <table class="mb-20" cellspacing="0">
                    <tr>
                        <th style="text-align: left;">Category of Employees / Occupation</th>
                        <th style="width: 200px; text-align: left"> R&B Benefit Plan (S$)</th>
                    </tr>
                    <tr>
                        <td>(i) Senior Management (Director, General Manager, Senior Manager)</td>
                        <td>360</td>
                    </tr>
                    <tr>
                        <td>(ii) Manager & Executive</td>
                        <td>200</td>
                    </tr>
                    <tr>
                        <td>(iii) All Others</td>
                        <td>100</td>
                    </tr>
                </table>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">b)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Age Profile of Employees – as per namelist attached</div>
                </div>
                <table class="common-table text-center">
                    <tr>
                        <th rowspan="2">Age Band (Age Next Birthday)</th>
                        <th colspan="2"># of Employees</th>
                    </tr>
                    <tr>
                        <th>Male</th>
                        <th>Female</th>
                    </tr>
                    <#-- 固定年龄段列表 -->
                    <#assign ageBands = ["16-30","31-35","36-40","41-45","46-50","51-55","56-60","61-65","66-70","Total"]>

                    <#-- 查找数据 -->
                    <#assign ghsData = "">
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag=="ghs" && personOverview.itemKey=="age_profile_of_employees">
                            <#assign ghsData = personOverview.valueMaps>
                        </#if>
                    </#list>

                    <#-- 渲染每个年龄段 -->
                    <#list ageBands as ageBand>
                        <tr>
                            <th>${ageBand}</th>

                            <#-- 判断当前年龄段是否有数据 -->
                            <#assign found = false>
                            <#if ghsData?has_content>
                                <#list ghsData as info>
                                    <#if info.age_band == ageBand>
                                        <td>${info.no_of_employees_male!}</td>
                                        <td>${info.no_of_employees_female!}</td>
                                        <#assign found = true>
                                    </#if>
                                </#list>
                            </#if>

                            <#-- 如果没找到数据，显示空格 -->
                            <#if !found>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </#if>
                        </tr>
                    </#list>
                </table>
            </div>
            <div class="page-footer">
                <div class="page-info">Effective November 2020</div>
            </div>
        </div>
        <!-- 第八页 -->
        <div class="page">
            <div class="header">
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">c)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Details of Insured Members – as per namelist attached</div>
                </div>
                <h3 class="mb-20 underline-bold">For GHS and GMM:</h3>
                <table class="mb-20 common-table text-center">
                    <tr>
                        <th class="border-none"></th>
                        <th colspan="4"># of Employees (Singaporeans & SPRs*)</th>
                    </tr>
                    <#-- 收集所有 no_of_employees_plan 值 -->
                    <#assign noOfEmployeesPlanList = []>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_sgp">
                            <#if personOverview.valueMaps?has_content>
                                <#list personOverview.valueMaps as info>
                                    <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                        <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                    </#if>
                                </#list>
                            </#if>
                        </#if>
                    </#list>

                    <tr>
                        <th></th>
                        <#-- 固定四列，如果数据不足显示空格 -->
                        <#list 0..3 as i>
                            <th>
                                <#if i < noOfEmployeesPlanList?size>
                                    ${noOfEmployeesPlanList[i]}
                                <#else>
                                    &nbsp;
                                </#if>
                            </th>
                        </#list>
                    </tr>

                    <#-- 固定四行员工类型 -->
                    <#assign employeeTypes = ["Employee only", "Employee and spouse", "Employee and children", "Employee and family"]>

                    <#list employeeTypes as type>
                        <tr>
                            <th class="text-left">${type}</th>
                            <#list 0..3 as i>
                                <td>
                                    <#-- 尝试获取对应数据，如果不存在则显示空格 -->
                                    <#assign value = "">
                                    <#list personOverviews as personOverview>
                                        <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_sgp">
                                            <#if personOverview.valueMaps?has_content && i < personOverview.valueMaps?size>
                                                <#if type == "Employee only">
                                                    <#assign value = (personOverview.valueMaps[i].employee_only)!"" >
                                                <#elseif type == "Employee and spouse">
                                                    <#assign value = (personOverview.valueMaps[i].employee_and_spouse)!"" >
                                                <#elseif type == "Employee and children">
                                                    <#assign value = (personOverview.valueMaps[i].employee_and_children)!"" >
                                                <#elseif type == "Employee and family">
                                                    <#assign value = (personOverview.valueMaps[i].employee_and_family)!"" >
                                                </#if>
                                            </#if>
                                        </#if>
                                    </#list>
                                    ${value?if_exists! "&nbsp;"}
                                </td>
                            </#list>
                        </tr>
                    </#list>

                    <#-- 如果列数超过四，显示附件提示 -->
                    <#if 4 < noOfEmployeesPlanList?size>
                        <tr>
                            <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                        </tr>
                    </#if>
                    <tr>
                        <td colspan="5" class="text-left italic"><i>* refers to Singapore Permanent Residents</i></td>
                    </tr>
                </table>
                <table class="mb-20 common-table text-center">
                    <tr>
                        <th class="border-none"></th>
                        <th colspan="4"># of Employees (Foreigners* only)</th>
                    </tr>
                    <#-- 收集所有 no_of_employees_plan 值 -->
                    <#assign noOfEmployeesPlanList = []>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_foreigners">
                            <#if personOverview.valueMaps?has_content>
                                <#list personOverview.valueMaps as info>
                                    <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                        <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                    </#if>
                                </#list>
                            </#if>
                        </#if>
                    </#list>

                    <tr>
                        <th></th>
                        <#-- 固定四列，如果数据不足显示空格 -->
                        <#list 0..3 as i>
                            <th>
                                <#if i < noOfEmployeesPlanList?size>
                                    ${noOfEmployeesPlanList[i]}
                                <#else>
                                    &nbsp;
                                </#if>
                            </th>
                        </#list>
                    </tr>

                    <#-- 固定四行员工类型 -->
                    <#assign employeeTypes = ["Employee only", "Employee and spouse", "Employee and children", "Employee and family"]>

                    <#list employeeTypes as type>
                        <tr>
                            <th class="text-left">${type}</th>
                            <#list 0..3 as i>
                                <td>
                                    <#-- 尝试获取对应数据，如果不存在则显示空格 -->
                                    <#assign value = "">
                                    <#list personOverviews as personOverview>
                                        <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_foreigners">
                                            <#if personOverview.valueMaps?has_content && i < personOverview.valueMaps?size>
                                                <#if type == "Employee only">
                                                    <#assign value = (personOverview.valueMaps[i].employee_only)!"" >
                                                <#elseif type == "Employee and spouse">
                                                    <#assign value = (personOverview.valueMaps[i].employee_and_spouse)!"" >
                                                <#elseif type == "Employee and children">
                                                    <#assign value = (personOverview.valueMaps[i].employee_and_children)!"" >
                                                <#elseif type == "Employee and family">
                                                    <#assign value = (personOverview.valueMaps[i].employee_and_family)!"" >
                                                </#if>
                                            </#if>
                                        </#if>
                                    </#list>
                                    ${value?if_exists! "&nbsp;"}
                                </td>
                            </#list>
                        </tr>
                    </#list>

                    <#-- 如果列数超过四，显示附件提示 -->
                    <#if 4 < noOfEmployeesPlanList?size>
                        <tr>
                            <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                        </tr>
                    </#if>
                    <tr>
                        <td colspan="5" class="text-left italic"><i>* refers to all foreigners holding Employment Pass, S Pass and Work Permit, working in Singapore</i></td>
                    </tr>
                </table>
                <h3 class="mb-20 underline-bold">For GMM (if the basis of coverage differs from GHS):</h3>
                <table class="mb-20 common-table text-center">
                    <tr>
                        <th class="border-none"></th>
                        <th colspan="4"># of Employees (Singaporeans & SPRs*)</th>
                    </tr>
                    <#assign sameToGhs = false>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "gmm" && personOverview.itemKey == "same_to_the_ghs" && personOverview.value?string == "1">
                            <#assign sameToGhs = true>
                        </#if>
                    </#list>

                    <#-- 收集 gmm 数据 -->
                    <#assign noOfEmployeesPlanList = []>
                    <#if !sameToGhs>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_sgp">
                                <#if personOverview.valueMaps?has_content>
                                    <#list personOverview.valueMaps as info>
                                        <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                            <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                        </#if>
                                    </#list>
                                </#if>
                            </#if>
                        </#list>
                    </#if>

                    <tr>
                        <th></th>
                        <#-- 固定四列，如果没有数据或 same_to_the_ghs 为 1，则显示空格 -->
                        <#list 0..3 as i>
                            <th>
                                <#if !sameToGhs && i < noOfEmployeesPlanList?size>
                                    ${noOfEmployeesPlanList[i]}
                                <#else>
                                    &nbsp;
                                </#if>
                            </th>
                        </#list>
                    </tr>

                    <#-- 固定四行员工类型 -->
                    <#assign employeeTypes = ["Employee only", "Employee and spouse", "Employee and children", "Employee and family"]>

                    <#list employeeTypes as type>
                        <tr>
                            <th  class="text-left">${type}</th>
                            <#list 0..3 as i>
                                <td>
                                    <#if sameToGhs>
                                        &nbsp;
                                    <#else>
                                        <#-- 尝试获取对应数据，如果不存在则显示空格 -->
                                        <#assign value = "">
                                        <#list personOverviews as personOverview>
                                            <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_sgp">
                                                <#if personOverview.valueMaps?has_content && i < personOverview.valueMaps?size>
                                                    <#if type == "Employee only">
                                                        <#assign value = (personOverview.valueMaps[i].employee_only)!"" >
                                                    <#elseif type == "Employee and spouse">
                                                        <#assign value = (personOverview.valueMaps[i].employee_and_spouse)!"" >
                                                    <#elseif type == "Employee and children">
                                                        <#assign value = (personOverview.valueMaps[i].employee_and_children)!"" >
                                                    <#elseif type == "Employee and family">
                                                        <#assign value = (personOverview.valueMaps[i].employee_and_family)!"" >
                                                    </#if>
                                                </#if>
                                            </#if>
                                        </#list>
                                        ${value?if_exists! "&nbsp;"}
                                    </#if>
                                </td>
                            </#list>
                        </tr>
                    </#list>

                    <#-- 如果列数超过四，显示附件提示 -->
                    <#if !sameToGhs && 4 < noOfEmployeesPlanList?size>
                        <tr>
                            <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                        </tr>
                    </#if>
                    <tr>
                        <td colspan="5" class="text-left italic"><i>* refers to Singapore Permanent Residents</i></td>
                    </tr>
                </table>
                <table class="mb-20 common-table text-center">
                    <tr>
                        <th class="border-none"></th>
                        <th colspan="4"># of Employees (Foreigners* only)</th>
                    </tr>
                    <#assign sameToGhs = false>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "gmm" && personOverview.itemKey == "same_to_the_ghs" && personOverview.value?string == "1">
                            <#assign sameToGhs = true>
                        </#if>
                    </#list>

                    <#-- 收集 gmm 数据 -->
                    <#assign noOfEmployeesPlanList = []>
                    <#if !sameToGhs>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_foreigners">
                                <#if personOverview.valueMaps?has_content>
                                    <#list personOverview.valueMaps as info>
                                        <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                            <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                        </#if>
                                    </#list>
                                </#if>
                            </#if>
                        </#list>
                    </#if>

                    <tr>
                        <th></th>
                        <#-- 固定四列，如果没有数据或 same_to_the_ghs 为 1，则显示空格 -->
                        <#list 0..3 as i>
                            <th>
                                <#if !sameToGhs && i < noOfEmployeesPlanList?size>
                                    ${noOfEmployeesPlanList[i]}
                                <#else>
                                    &nbsp;
                                </#if>
                            </th>
                        </#list>
                    </tr>

                    <#-- 固定四行员工类型 -->
                    <#assign employeeTypes = ["Employee only", "Employee and spouse", "Employee and children", "Employee and family"]>

                    <#list employeeTypes as type>
                        <tr>
                            <th class="text-left">${type}</th>
                            <#list 0..3 as i>
                                <td>
                                    <#if sameToGhs>
                                        &nbsp;
                                    <#else>
                                        <#-- 尝试获取对应数据，如果不存在则显示空格 -->
                                        <#assign value = "">
                                        <#list personOverviews as personOverview>
                                            <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_foreigners">
                                                <#if personOverview.valueMaps?has_content && i < personOverview.valueMaps?size>
                                                    <#if type == "Employee only">
                                                        <#assign value = (personOverview.valueMaps[i].employee_only)!"" >
                                                    <#elseif type == "Employee and spouse">
                                                        <#assign value = (personOverview.valueMaps[i].employee_and_spouse)!"" >
                                                    <#elseif type == "Employee and children">
                                                        <#assign value = (personOverview.valueMaps[i].employee_and_children)!"" >
                                                    <#elseif type == "Employee and family">
                                                        <#assign value = (personOverview.valueMaps[i].employee_and_family)!"" >
                                                    </#if>
                                                </#if>
                                            </#if>
                                        </#list>
                                        ${value?if_exists! "&nbsp;"}
                                    </#if>
                                </td>
                            </#list>
                        </tr>
                    </#list>

                    <#-- 如果列数超过四，显示附件提示 -->
                    <#if !sameToGhs && 4 < noOfEmployeesPlanList?size>
                        <tr>
                            <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                        </tr>
                    </#if>
                    <tr>
                        <td colspan="5" class="text-left italic"><i>* refers to all foreigners holding Employment Pass, S Pass and Work Permit, working in Singapore</i></td>
                    </tr>
                </table>
            </div>
            <div class="page-footer">
                <div class="page-info">Effective November 2020</div>
            </div>
        </div>
        <!-- 第九页 -->
        <div class="page">
            <div class="header"></div>
            <div class="table-list clearfix">
                <div class="fl table-index">d)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Claims Experience for the past 3 years</div>
                </div>
                <table class="mb-20 common-table text-center">
                    <tr>
                        <th rowspan="2" class="text-left" style="width: 20%">Period of Coverage <br> From / To <br><span class="line-6em"></span><br> (dd/mm/yyyy)</th>
                        <th rowspan="2" class="text-left" style="width: 20%"># of Insured as at <br><span class="line-6em"></span><br> (dd/mm/yyyy) </th>
                        <th colspan="2">Paid Claims</th>
                        <th colspan="2">Outstanding Claims</th>
                    </tr>
                    <tr>
                        <th># of Claims</th>
                        <th>Amount (S$)</th>
                        <th># of Claims</th>
                        <th>Amount (S$)</th>
                    </tr>
                    <#assign fixedRows = 3>

                    <#if isUploadClaimAttach?? && isUploadClaimAttach>
                        <tr>
                            <td>Please refer to attachment</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <#list 1..(fixedRows - 1) as i>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#list>
                    <#else>
                        <#assign displayedCount = 0>
                        <#list claimLogs?keys as key>
                            <#if key == "GHS">
                                <#assign gpclaimList = claimLogs[key]>
                                <#list gpclaimList as claim>
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${claim.startTime!}</td>
                                            <td>${claim.endTime!}</td>
                                            <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                            <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                            <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                            <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>
                        <#if displayedCount == 0>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                    </#if>

                    <tr>
                        <td colspan="6" class="text-left italic"><i>Note: The insurer reserves the right to request for more information.</i></td>
                    </tr>
                </table>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">e)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Kindly attach a copy of the Schedule of Benefits, if the benefits are on insured basis (i.e. currently
                        insured).</div>
                </div>
            </div>
            <div class="page-footer">
                <div class="page-info">Effective November 2020</div>
            </div>
        </div>
        <!-- 第十页 -->
        <div class="page">
            <div class="header">
            </div>
            <div class="table-list clearfix" style="margin-bottom: 0px;">
                <div class="fl table-index"><strong>3</strong></div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8 underline-bold"><strong>BENEFIT: GROUP OUTPATIENT INSURANCE</strong></div>
                </div>
            </div>
            <div class="table-list clearfix" style="margin: 10px 0;">
                <div class="fl table-index">a)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Category of Employees to be insured (please tick as appropriate)</div>
                </div>
                <table class="mb-20 common-table text-center">
                    <colgroup>
                        <col style="width: 50px">
                        <col style="width: 25%">
                    </colgroup>
                    <tr>
                        <th colspan="2" class="text-left">Category of Employees</th>
                        <th>Clinical GP</th>
                        <th>Specialist</th>
                        <th>Diag X-Ray/Lab Tests</th>
                        <th>Dental</th>
                    </tr>
                    <#-- 去重categoryEmployee -->
                    <#assign uniqueCategories = []>
                    <#list userPlanDutyList as item>
                        <#if !(uniqueCategories?seq_contains(item.categoryEmployee))>
                            <#assign uniqueCategories = uniqueCategories + [item.categoryEmployee]>
                        </#if>
                    </#list>

                    <#-- 序号映射 -->
                    <#assign romanNumerals = ["(i)", "(ii)", "(iii)"]>

                    <#-- 遍历去重后的类别，最多显示三行 -->
                    <#list 0..2 as i>
                        <tr>
                            <#if i < uniqueCategories?size>
                                <#assign category = uniqueCategories[i]>

                                <!-- 第一列序号 -->
                                <td>${romanNumerals[i]}</td>
                                <!-- 第二列员工类别 -->
                                <td class="text-left">${category}</td>

                                <!-- 判断GP列是否勾选 -->
                                <#assign gpChecked = false>
                                <#list userPlanDutyList as item>
                                    <#if item.categoryEmployee == category && item.benefit == "GP">
                                        <#assign gpChecked = true>
                                    </#if>
                                </#list>
                                <td><input type="checkbox" <#if gpChecked>checked</#if>></td>

                                <!-- 判断SP列是否勾选 -->
                                <#assign spChecked = false>
                                <#list userPlanDutyList as item>
                                    <#if item.categoryEmployee == category && item.benefit == "SP">
                                        <#assign spChecked = true>
                                    </#if>
                                </#list>
                                <td><input type="checkbox" <#if spChecked>checked</#if>></td>

                                <!-- Diag X-Ray/Lab Tests 空列 -->
                                <td><input type="checkbox"></td>

                                <!-- 判断GD列是否勾选 -->
                                <#assign gdChecked = false>
                                <#list userPlanDutyList as item>
                                    <#if item.categoryEmployee == category && item.benefit == "GD">
                                        <#assign gdChecked = true>
                                    </#if>
                                </#list>
                                <td><input type="checkbox" <#if gdChecked>checked</#if>></td>
                            <#else>
                                <!-- 空行补齐 -->
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </#if>
                        </tr>
                    </#list>

                    <!-- 提示行 -->
                    <#if 3 < uniqueCategories?size>
                        <tr>
                            <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                        </tr>
                    </#if>

                    <!-- Dependant行 -->
                    <tr>
                        <th colspan="2" class="text-left">Dependant (where applicable)</th>
                        <#assign gpDependantsIncluded = "No">
                        <#assign spDependantsIncluded = "No">
                        <#assign gdDependantsIncluded = "No">
                        <#assign gpHeadcount = "">
                        <#assign spHeadcount = "">
                        <#assign gdHeadcount = "">
                        <#list personOverviews as personOverview>
                            <#if personOverview.itemKey == "dependants_included">
                                <#if personOverview.tag == "gp">
                                    <#if (personOverview.value!"") == "1">
                                        <#assign gpDependantsIncluded = "Yes">
                                    </#if>
                                <#elseif personOverview.tag == "sp">
                                    <#if (personOverview.value!"") == "1">
                                        <#assign spDependantsIncluded = "Yes">
                                    </#if>
                                <#elseif personOverview.tag == "gd">
                                    <#if (personOverview.value!"") == "1">
                                        <#assign gdDependantsIncluded = "Yes">
                                    </#if>
                                </#if>
                            </#if>

                            <#if personOverview.itemKey == "no_of_headcount">
                                <#if personOverview.tag == "gp">
                                    <#if personOverview.value??>
                                        <#assign gpHeadcount = personOverview.value!>
                                    </#if>
                                <#elseif personOverview.tag == "sp">
                                    <#if personOverview.value??>
                                        <#assign spHeadcount = personOverview.value!>
                                    </#if>
                                <#elseif personOverview.tag == "gd">
                                    <#if personOverview.value?? >
                                        <#assign gdHeadcount = personOverview.value!>
                                    </#if>
                                </#if>
                            </#if>
                        </#list>

                        <td>${gpDependantsIncluded}</td>
                        <td>${spDependantsIncluded}</td>
                        <td></td>
                        <td>${gdDependantsIncluded}</td>
                    </tr>
                    <tr>
                        <th class="text-left" colspan="2"># of Headcount</th>
                        <td>${gpHeadcount}</td>
                        <td>${spHeadcount}</td>
                        <td></td>
                        <td>${gdHeadcount}</td>
                    </tr>
                </table>
            </div>
            <div class="table-list clearfix" style="margin: 10px 0;">
                <div class="fl table-index">b)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Age Profile of Employees</div>
                </div>
                <table class="common-table text-center">
                    <tr>
                        <th rowspan="2" style="width: 150px;">Age Band (Age Next Birthday)</th>
                        <th colspan="2"># of Employees</th>
                    </tr>
                    <tr>
                        <th>Male</th>
                        <th>Female</th>
                    </tr>
                    <#-- 固定年龄段列表 -->
                    <#assign ageBands = ["16-30","31-35","36-40","41-45","46-50","51-55","56-60","61-65","66-70","Total"]>

                    <#-- 查找数据 -->
                    <#assign data = "">
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag=="group_outpatient_insurance" && personOverview.itemKey=="age_profile_of_employees">
                            <#assign data = personOverview.valueMaps>
                        </#if>
                    </#list>

                    <#-- 渲染每个年龄段 -->
                    <#list ageBands as ageBand>
                        <tr>
                            <th>${ageBand}</th>

                            <#-- 判断当前年龄段是否有数据 -->
                            <#assign found = false>
                            <#if data?has_content>
                                <#list data as info>
                                    <#if info.age_band == ageBand>
                                        <td>${info.no_of_employees_male!}</td>
                                        <td>${info.no_of_employees_female!}</td>
                                        <#assign found = true>
                                    </#if>
                                </#list>
                            </#if>

                            <#-- 如果没找到数据，显示空格 -->
                            <#if !found>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </#if>
                        </tr>
                    </#list>
                </table>
            </div>
            <div class="table-list clearfix" style="margin: 10px 0;">
                <div class="fl table-index">c)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Claims Experience for the past 3 years</div>
                </div>
                <h4 class="mb-20 underline-bold">Paid Claims</h4>
                <table class="mb-20 common-table text-center">
                    <tr>
                        <th class="border-none" style="width: 15%"></th>
                        <th class="border-none" style="width: 15%"></th>
                        <th colspan="2">Clinical*</th>
                        <th colspan="2"> Specialist * </th>
                        <th colspan="2">Diagnostic X-Ray / Lab Tests*</th>
                        <th colspan="2">Dental*</th>
                    </tr>
                    <tr>
                        <th class="text-left">Period of Coverage From / To <span class="line-6em"></span> <br> (dd/mm/yyyy)</th>
                        <th class="text-left"># of Insured as at <br><span class="line-6em"></span><br> (dd/mm/yyyy)</th>
                        <th># of Visits</th>
                        <th>Amt (S$)</th>
                        <th># of Visits</th>
                        <th>Amt (S$)</th>
                        <th># of Visits</th>
                        <th>Amt (S$)</th>
                        <th># of Visits</th>
                        <th>Amt (S$)</th>
                    </tr>
                    <#if isUploadClaimAttach?? && isUploadClaimAttach>
                        <tr>
                            <td>Please refer to attachment</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <#list 1..2 as i>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#list>
                    </#if>

                    <#assign displayedCount = 0>
                    <#list claimLogs?keys as key>
                        <#if key == "GP">
                            <#assign gpclaimList = claimLogs[key]>
                            <#list gpclaimList as claim>
                                <#if displayedCount < 3>
                                    <tr>
                                        <td>${claim.startTime}</td>
                                        <td>${claim.endTime}</td>
                                        <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                        <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                        <#assign spclaimList = claimLogs["SP"]![]>
                                        <#assign spshowflg = false>
                                        <#list spclaimList as spclaim>
                                            <#if spclaim.startTime == claim.startTime>
                                                <#assign spshowflg = true>
                                                <td>${(spclaim.paidClaimsNum??)?then(spclaim.paidClaimsNum, "")}</td>
                                                <td>${(spclaim.paidAmount?? && spclaim.paidAmount.amount??)?then(spclaim.paidAmount.amount, "")}</td>
                                            </#if>
                                        </#list>
                                        <#if !spshowflg>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </#if>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <#assign gdclaimList = claimLogs["GD"]![]>
                                        <#assign gdshowflg = false>
                                        <#list gdclaimList as gdclaim>
                                            <#if gdclaim.startTime == claim.startTime>
                                                <#assign gdshowflg = true>
                                                <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                                <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                            </#if>
                                        </#list>
                                        <#if !gdshowflg>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </#if>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#list>
                        </#if>
                    </#list>

                    <#list claimLogs?keys as key>
                        <#if key == "SP" && displayedCount < 3>
                            <#assign spclaimList = claimLogs[key]>
                            <#list spclaimList as claim>
                                <#if displayedCount < 3>
                                    <tr>
                                        <td>${claim.startTime}</td>
                                        <td>${claim.endTime}</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                        <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <#assign gdclaimList = claimLogs["GD"]![]>
                                        <#assign gdshowflg = false>
                                        <#list gdclaimList as gdclaim>
                                            <#if gdclaim.startTime == claim.startTime>
                                                <#assign gdshowflg = true>
                                                <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                                <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                            </#if>
                                        </#list>
                                        <#if !gdshowflg>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </#if>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#list>
                        </#if>
                    </#list>


                    <#list claimLogs?keys as key>
                        <#if key == "GD" && displayedCount < 3>
                            <#assign gdclaimList = claimLogs[key]>
                            <#list gdclaimList as claim>
                                <#if displayedCount < 3>
                                    <tr>
                                        <td>${claim.startTime}</td>
                                        <td>${claim.endTime}</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                        <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#list>
                        </#if>
                    </#list>
                    <#if displayedCount < 3 && !isUploadClaimAttach>
                        <#list displayedCount..2 as i>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#list>
                    </#if>
                    <tr>
                        <td colspan="10" class="text-left"><i>* inclusive of visits to non-panel clinics <br> Note: The insurer reserves the right to request for more information.</i></td>
                    </tr>
                </table>
            </div>
            <div class="page-footer">
                <div class="page-info">Effective November 2020</div>
            </div>
        </div>
        <!-- 第十一页 -->
        <div class="page">
            <div class="header"></div>
            <div class="table-list clearfix">
                <h4 class="mb-20 underline-bold">Outstanding Claims</h4>
                <table class="mb-20 common-table text-center">
                    <tr>
                        <th class="border-none" style="width: 15%"></th>
                        <th class="border-none" style="width: 15%"></th>
                        <th colspan="2">Clinical*</th>
                        <th colspan="2"> Specialist * </th>
                        <th colspan="2">Diagnostic X-Ray / Lab Tests*</th>
                        <th colspan="2">Dental*</th>
                    </tr>
                    <tr>
                        <th class="text-left">Period of Coverage From / To <span class="line-6em"></span><br> (dd/mm/yyyy)</th>
                        <th class="text-left"># of Insured as at <br><span class="line-6em"></span><br> (dd/mm/yyyy)</th>
                        <th># of Visits</th>
                        <th>Amt (S$)</th>
                        <th># of Visits</th>
                        <th>Amt (S$)</th>
                        <th># of Visits</th>
                        <th>Amt (S$)</th>
                        <th># of Visits</th>
                        <th>Amt (S$)</th>
                    </tr>
                    <#if isUploadClaimAttach?? && isUploadClaimAttach>
                        <tr>
                            <td>Please refer to attachment</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <#list 1..2 as i>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#list>
                    </#if>

                    <#assign displayedCount = 0>
                    <#list claimLogs?keys as key>
                        <#if key == "GP">
                            <#assign gpclaimList = claimLogs[key]>
                            <#list gpclaimList as claim>
                                <#if displayedCount < 3>
                                    <tr>
                                        <td>${claim.startTime}</td>
                                        <td>${claim.endTime}</td>
                                        <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                        <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                        <#assign spclaimList = claimLogs["SP"]![]>
                                        <#assign spshowflg = false>
                                        <#list spclaimList as spclaim>
                                            <#if spclaim.startTime == claim.startTime>
                                                <#assign spshowflg = true>
                                                <td>${(spclaim.outstandingClaimsNum??)?then(spclaim.outstandingClaimsNum, "")}</td>
                                                <td>${(spclaim.outstandingAmount?? && spclaim.outstandingAmount.amount??)?then(spclaim.outstandingAmount.amount, "")}</td>
                                            </#if>
                                        </#list>
                                        <#if !spshowflg>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </#if>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <#assign gdclaimList = claimLogs["GD"]![]>
                                        <#assign gdshowflg = false>
                                        <#list gdclaimList as gdclaim>
                                            <#if gdclaim.startTime == claim.startTime>
                                                <#assign gdshowflg = true>
                                                <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                                <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                            </#if>
                                        </#list>
                                        <#if !gdshowflg>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </#if>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#list>
                        </#if>
                    </#list>

                    <#list claimLogs?keys as key>
                        <#if key == "SP" && displayedCount < 3>
                            <#assign spclaimList = claimLogs[key]>
                            <#list spclaimList as claim>
                                <#if displayedCount < 3>
                                    <tr>
                                        <td>${claim.startTime}</td>
                                        <td>${claim.endTime}</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                        <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <#assign gdclaimList = claimLogs["GD"]![]>
                                        <#assign gdshowflg = false>
                                        <#list gdclaimList as gdclaim>
                                            <#if gdclaim.startTime == claim.startTime>
                                                <#assign gdshowflg = true>
                                                <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                                <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                            </#if>
                                        </#list>
                                        <#if !gdshowflg>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </#if>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#list>
                        </#if>
                    </#list>

                    <#list claimLogs?keys as key>
                        <#if key == "GD" && displayedCount < 3>
                            <#assign gdclaimList = claimLogs[key]>
                            <#list gdclaimList as claim>
                                <#if displayedCount < 3>
                                    <tr>
                                        <td>${claim.startTime}</td>
                                        <td>${claim.endTime}</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                        <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#list>
                        </#if>
                    </#list>

                    <#if displayedCount < 3 && !isUploadClaimAttach>
                        <#list displayedCount..2 as i>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#list>
                    </#if>
                    <tr>
                        <td colspan="10" class="text-left italic"><i>* inclusive of visits to non-panel clinics <br> Note: The insurer reserves the right to request for more information.</i></td>
                    </tr>
                </table>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">d)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8"> Kindly attach a copy of the Schedule of Benefits if the benefits are on insured basis.</div>
                    <div class="mb-8"> If currently self-insured, kindly provide the following details:</div>
                    <div class="mb-8"> Please indicate “Unlimited” if there is no cap and “NA” if it is not applicable.</div>
                </div>
                <table class="mb-20 common-table text-center">
                    <tr>
                        <th rowspan="2" class="text-left" style="width: 16%">Benefits</th>
                        <th colspan="2">Maximum Limit per Visit (S$)</th>
                        <th colspan="2">Maximum Limit per Policy Year (S$)</th>
                        <th colspan="2">Co-Payment (S$) / Co-Insurance (%)</th>
                    </tr>
                    <tr>
                        <th>Clinic on Company's panel</th>
                        <th>Non-panel Clinic</th>
                        <th>Clinic on Company's panel</th>
                        <th>Non-panel Clinic</th>
                        <th>Clinic on Company's panel</th>
                        <th>Non-panel Clinic</th>
                    </tr>
                    <tr>
                        <th class="text-left">Clinical GP</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th class="text-left">Specialist</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th class="text-left">Diagnostic X-Ray / <br> Lab Tests</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th class="text-left">Dental</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th class="text-left">Others</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </table>
            </div>
            <div class="page-footer">
                <div class="page-info">Effective November 2020</div>
            </div>
        </div>
        <!-- 第十二页 -->
        <div class="page">
            <div class="header">
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index"><strong>4</strong></div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8 underline-bold"><strong>BENEFIT: MATERNITY INSURANCE</strong></div>
                </div>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">a)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Basis of Coverage</div>
                </div>
                <table class="mb-20 common-table text-center">
                    <colgroup>
                        <col style="width: 80px;">
                        <col>
                        <col style="width: 300px">
                    </colgroup>
                    <tr>
                        <th colspan="2" class="text-left">Category of Employees (refer to the example)</th>
                        <th> # of Headcount</th>
                    </tr>
                    <#-- 定义数据列表，避免空指针 -->
                    <#assign filteredList = []>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "gm" && personOverview.itemKey == "basis_of_coverage">
                            <#assign filteredList = personOverview.valueMaps![]>
                        </#if>
                    </#list>

                    <#-- 固定输出3行，不足补空 -->
                    <#list 0..2 as idx>
                        <tr>
                            <#if idx == 0>
                                <td>(i)</td>
                            <#elseif idx == 1>
                                <td>(ii)</td>
                            <#elseif idx == 2>
                                <td>(iii)</td>
                            </#if>

                            <#if idx lt filteredList?size>
                                <td>${filteredList[idx].category_of_employees_occupation!""}</td>
                                <td>${filteredList[idx].no_of_headcount!""}</td>
                            <#else>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </#if>
                        </tr>
                    </#list>

                    <#-- 如果有超过3行数据，显示提示 -->
                    <#if filteredList?size gt 3>
                        <tr>
                            <td colspan="3" class="text-left italic">
                                More information, please refer to the attachment.
                            </td>
                        </tr>
                    </#if>
                </table>
            </div>
            <div class="example-list">
                <h4 class="underline-bold">Example 1</h4>
                <table class="mb-20" cellspacing="0">
                    <tr>
                        <th style="text-align: left;">Category of Employees/Occupation</th>
                    </tr>
                    <tr>
                        <td>(i) Senior Management (Director, General Manager, Senior Manager)</td>
                    </tr>
                    <tr>
                        <td>(ii) Manager & Executive</td>
                    </tr>
                    <tr>
                        <td>(iii) All Others</td>
                    </tr>
                </table>
            </div>
            <div class="example-list">
                <h4 class="underline-bold">Example 2</h4>
                <table class="mb-20" cellspacing="0">
                    <tr>
                        <td>(i) All Employees</td>
                    </tr>
                </table>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">b)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Claims Experience for the past 3 years</div>
                </div>
                <table class="common-table text-center">
                    <tr>
                        <th rowspan="2" style="width: 15%">Period of Coverage From / To <span class="line-6em"></span> <br> (dd/mm/yyyy)</th>
                        <th rowspan="2" style="width: 15%"># of Insured as at <br><span class="line-6em"></span><br> (dd/mm/yyyy)</th>
                        <th colspan="2">Paid Claims</th>
                        <th colspan="2">Outstanding Claims</th>
                    </tr>
                    <tr>
                        <th># of Claims</th>
                        <th>Amount (S$)</th>
                        <th># of Claims</th>
                        <th>Amount (S$)</th>
                    </tr>
                    <#assign fixedRows = 3>

                    <#if isUploadClaimAttach?? && isUploadClaimAttach>
                        <tr>
                            <td>Please refer to attachment</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <#list 1..(fixedRows - 1) as i>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#list>
                    <#else>
                        <#assign displayedCount = 0>
                        <#list claimLogs?keys as key>
                            <#if key == "GM">
                                <#assign gpclaimList = claimLogs[key]>
                                <#list gpclaimList as claim>
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${claim.startTime!}</td>
                                            <td>${claim.endTime!}</td>
                                            <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                            <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                            <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                            <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>
                        <#if displayedCount == 0>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                    </#if>
                    <tr>
                        <td colspan="6" class="text-left italic"><i>Note: The insurer reserves the right to request for more information.</i></td>
                    </tr>
                </table>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">c)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Kindly attach a copy of the Schedule of Benefits if the benefits are on insured basis.</div>
                    <div class="mb-8">If currently self-insured, kindly provide the following details:</div>
                    <div class="mb-8">Please indicate “Unlimited” if there is no cap and “NA” if it is not applicable.</div>
                </div>
                <table class="mb-20 common-table text-center">
                    <tr>
                        <th class="text-left">Benefits</th>
                        <th colspan="2">Maximum Limit per Policy Year (S$)</th>
                        <th colspan="2">Deductible / Co-insurance (S$)</th>
                    </tr>
                    <tr>
                        <th class="text-left">Normal Delivery</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th class="text-left">Caesarian Delivery</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th class="text-left">Others:</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </table>
            </div>
            <div class="page-footer">
                <div class="page-info">Effective November 2020</div>
            </div>
        </div>
        <!-- 第十三页 -->
        <div class="page page-fifth">
            <div class="header"></div>
            <div class="table-list clearfix">
                <div class="fl table-index"><strong>5</strong></div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8 underline-bold"><strong>NEEDS ANALYSIS & PRODUCT RECOMMENDATION </strong></div>
                </div>
            </div>
            <p class="mb-8">Please tick the appropriate box to indicate the priority of your company's needs:</p>
            <table cellspacing="0" class="text-center">
                <tr>
                    <th style="width: 400px;" class="underline-bold text-left">Company's Priorities</th>
                    <th style="width: 60px;">Low</th>
                    <th style="width: 60px;">Med</th>
                    <th style="width: 60px;">High</th>
                    <th class="underline-bold">Advisor's Recommendation</th>
                </tr>
                <tr>
                    <td class="text-left">Cover for Outpatient medical <br>expenses</td>
                    <#assign isChecked = false>
                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GP" && item.participation == "1">
                            <#assign isChecked = true>
                        </#if>
                    </#list>
                    <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                    <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                    <td>
                        <div class="table-input border-bottom-bold"></div>
                    </td>
                </tr>
                <tr>
                    <td class="text-left">Cover for Hospital & Surgical <br>expenses</td>
                    <#assign isChecked = false>
                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GHS" && item.participation == "1">
                            <#assign isChecked = true>
                        </#if>
                    </#list>
                    <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                    <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                    <td>
                        <div class="table-input border-bottom-bold"></div>
                    </td>
                </tr>
                <tr>
                    <td class="text-left">Cover for Dental expenses</td>
                    <#assign isChecked = false>
                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GD" && item.participation == "1">
                            <#assign isChecked = true>
                        </#if>
                    </#list>
                    <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                    <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                    <td>
                        <div class="table-input border-bottom-bold"></div>
                    </td>
                </tr>
                <tr>
                    <td class="text-left">Cover for Major illnesses <br>(e.g. cancer, kidney failure, etc.)</td>
                    <#assign isChecked = false>
                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GCI" && item.participation == "1">
                            <#assign isChecked = true>
                        </#if>
                    </#list>
                    <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                    <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                    <td>
                        <div class="table-input border-bottom-bold"></div>
                    </td>
                </tr>
                <tr>
                    <td class="text-left">Cover for Loss of Income due to <br>sickness or accident</td>
                    <#assign isChecked = false>
                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GDI" && item.participation == "1">
                            <#assign isChecked = true>
                        </#if>
                    </#list>
                    <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                    <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                    <td>
                        <div class="table-input border-bottom-bold"></div>
                    </td>
                </tr>
                <tr>
                    <td class="text-left">Cover for long term medical treatment</td>
                    <#assign isChecked = false>
                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GTL" && item.participation == "1">
                            <#assign isChecked = true>
                        </#if>
                    </#list>
                    <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                    <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                    <td>
                        <div class="table-input border-bottom-bold"></div>
                    </td>
                </tr>
                <tr>
                    <td class="text-left">Others:</td>
                    <td colspan="4"><div class="table-input border-bottom-bold"></div></td>
                </tr>
            </table>
            <div class="page-footer">
                <div class="page-info">Effective November 2020</div>
            </div>
        </div>
        <!-- 第十四页 -->
        <div class="page" style="height: 1560px;">
            <div class="header"></div>
            <div class="table-list clearfix">
                <div class="fl table-index"><strong>6</strong></div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8 underline-bold"><strong>DECLARATION</strong></div>
                </div>
            </div>
            <div class="mb-20">I / We hereby declare that, to the best of my / our knowledge and belief, the information given here
                are true and complete, and agree that if a contract of insurance is effected, all information submitted
                in connection with this application shall form the basis of such contract between the Company and the
                Insurer. </div>
            <div class="mb-20">I / We acknowledge, declare and agree that collective consent have been obtained from each of the
                employees/Group Member and/or their dependents allowing QBE to collect, use, process and
                disclose the personal data in accordance with the PDPA and the Company's 'Privacy Policy' which we
                have read, understood and agreed to the same. (Refer to the Privacy Policy at www.qbe.com.sg)</div>
            <div class="clearfix">
                <div class="fl" style="width: 480px;">
                    <div class="input-item mb-20">
                        <div class="table-input" style="width: 80%;"></div>
                        <div>Signature of Authorised Officer</div>
                    </div>
                    <div class="input-item">Name:</div>
                    <div class="input-item">NRIC/ Fin No.</div>
                    <div class="input-item">Designation:</div>
                    <div class="input-item">Date:</div>
                </div>
                <div class="fl" style="width: 480px;padding-top: 120px;">
                    <div class="input-item">Company Stamp (if applicable):</div>
                </div>
            </div>
            <div class="mb-20" style="margin-top: 60px;">I / We declare and acknowledge that I / we have reviewed this Group Insurance Fact-Finding Form with
                the authorised officer of the Company, and that I / we have explained all the requirements of this Fact-Finding form to him / her.</div>
            <div class="clearfix">
                <div class="fl" style="width: 480px;">
                    <div class="input-item mb-20">
                        <div class="table-input" style="width: 80%;"></div>
                        <div>Signature of Insurance Representative</div>
                    </div>
                    <div class="input-item">Name:</div>
                    <div class="input-item">NRIC/ Fin No.</div>
                    <div class="input-item">Designation:</div>
                    <div class="input-item">Date:</div>
                </div>
                <div class="fl" style="width: 480px;padding-top: 120px;">
                    <div class="input-item">Company Stamp (if applicable):</div>
                </div>
            </div>
            <div class="page-footer">
                <div class="page-info">Effective November 2020</div>
            </div>
        </div>
    </body>
</html>