<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[LIBERTY GIFF] GIFF Form</title>
    <style>
        body,ol,ul,h1,h2,h3,h4,h5,h6,p,th,td,dl,dd,form,fieldset,legend,input,textarea,select{margin:0;padding:0;}
		.clearfix::after {
			content: "";
			display: table;
			clear: both;
		}
        body{
			background: #ccc;
		}
        .page{
            width: 970px;
            height: 1396px;
            background: #fff;
            margin: 0 auto 20px;
            padding: 30px;
            position: relative;
        }
        .page-header{
            width: 100%;
            height: 30px;
            background: rgba(255, 208, 0, 1);
        }
        .page-content{
            padding: 30px;
        }
        .page-footer{
            position: absolute;
            bottom: 0px;
            left: 60px;
            font-size: 12px;
            width: 860px;
        }
        .main-color{
            color: rgba(0, 150, 169, 1);
            font-weight: 600;
        }
        .header img{
            width: 100%;
		}
        .font12{
            font-size: 12px;
        }
        .font14{
            font-size: 14px;
        }
        .font16{
            font-size: 16px;
        }
        .font18{
            font-size: 18px;
        }
        .t-center{
            text-align: center;
        }
        .t-bg{
            color: #fff;
            padding: 6px 10px;
            background-color: #333;
            text-align: center;
        }
        .mb-8{
            margin-bottom: 8px;
        }
        .mb-20{
            margin-bottom: 20px;
        }
        table{
            width: 100%;
            border-color: rgba(120, 225, 225, 1);
        }
        table th{
            padding: 12px 12px;
        }
        table td{
            padding: 8px 12px;
        }
        .table-text{
            height: 24px;
        }
        .ml-30{
            margin-left: 30px;
        }
        .mb-36{
            margin-bottom: 36px;
        }
        .table-input{
            width: 300px;
            display: inline-block;
            border-bottom: 1px solid #333;
        }
        .table-input{
            border-bottom: 1px solid #333;
            height: 24px;
            width: 100%;
        }
        .fl{
            display: inline-block;
            float: left;
        }
        .fr{
            display: inline-block;
            float: right;
        }
        .u-list{
            padding-left: 50px;
            list-style: lower-alpha;
        }

    </style>
</head>
<body>
    <!-- 第一页 -->
    <div class="page">
        <div class="page-header"></div>
        <div class="page-content">
            <div class="clearfix">
                <div class="fl">
                    <img src="data:image/png;base64,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" alt=""/>
                </div>
                <div class="fr">
                    <p class="font12"><strong>Liberty Insurance Pte Ltd </strong></p>
                    <p class="font12">One Raffles Quay #25-01 North Tower</p>
                    <p class="font12">Singapore 048583</p>
                    <p class="font12">Tel: 1800-LIBERTY (542 3789)</p>
                    <p class="font12">Reg. No. 199002791D ç GST Reg. No. M2-0093571-3 </p>
                    <p class="font12">www.libertyinsurance.com.sg</p>
                </div>
            </div>
            <h1 class="mb-36">Fact-Find for Group – Accident & Health</h1>
            <p class="mb-20">Please complete all sections to facilitate the processing of your application.</p>
            <p class="mb-8">Statement pursuant to Section 23(5) of the Insurance Act 1966 or any subsequent amendments thereof.
                You are to disclose in the proposal form fully and faithfully all facts which you know or ought to know,
                otherwise the Policy issued hereunder may be void. </p>
            <table border="1" cellspacing="0" class="mb-20">
                <tr>
                    <td colspan="3">
                        <div class="fl">Name of Producer & Producer Code: </div>
                        <div class="table-input fl" style="width: 600px;"></div>
                    </td>
                </tr>
                <tr>
                    <td colspan="2">
                        Request from (Name of Insurer):
                        <div class="table-input fl">Liberty Insurance Pte Ltd</div>
                    </td>
                    <td>
                        Request for Quotation submitted on:
                        <div class="table-input fl">
                            ${baseInfo.quoteTime!}
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        Period of Insurance:
                        <div class="clearfix">
                            <div class="fl">From</div>
                            <div class="table-input fl" style="width: 300px;">
                                ${baseInfo.expectStartTime!}
                            </div>
                            <div class="fl">To</div>
                            <div class="table-input fl" style="width: 300px;">
                                ${baseInfo.expectEndTime!}
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
            <h3 class="mb-8">Particulars of Proposer</h3>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <td colspan="2">
                        Name of Proposer:
                        <div class="table-input fl">
                            ${baseInfo.companyName!}
                        </div>
                    </td>
                    <td>
                        Business Registration No.:
                        <div class="table-input fl">
                            UEN
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        GST registered Company1?
                        <div>
                            <label for="myCheckbox1"><input class="ml-30" type="checkbox" checked id="myCheckbox1" name="myCheckbox1"> Yes</label>
                            <label for="myCheckbox2"><input class="ml-30" type="checkbox" id="myCheckbox2" name="myCheckbox2"> No</label>
                        </div>
                    </td>
                    <td>
                        Nature of Business:
                        <div class="table-input fl">
                            ${baseInfo.companyNature!}
                        </div>
                    </td>
                    <td>
                        Type of Policy:
                        <div class="table-input fl">
                            Employee Benefit
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        Total No. of Employees:
                        <div class="table-input fl">
                            ${baseInfo.totalEmployees!}
                        </div>
                    </td>
                    <td>
                        No. of Employees to be insured:
                        <div class="table-input fl">
                            ${baseInfo.insuredEmployees!}
                        </div>
                    </td>
                    <td>
                        Presently Insured:
                        <div>
                            <label for="myCheckbox1"><input class="ml-30" type="checkbox" <#if baseInfo.presentlyInsured== "1">checked</#if> id="myCheckbox1" name="myCheckbox1"> Yes</label>
                            <label for="myCheckbox2"><input class="ml-30" type="checkbox" <#if baseInfo.presentlyInsured== "0">checked</#if> id="myCheckbox2" name="myCheckbox2"> No</label>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td colspan="3">
                        <div class="clearfix">
                            <div class="fl" style="width: 300px;">
                                If yes, name of current insurer:
                                <div class="table-input fl">
                                    ${baseInfo.currentEbInsurer!}
                                </div>
                            </div>
                            <div class="fl" style="width: 530px;margin-left: 30px;">
                                Period of Insurance:
                                <div class="clearfix">
                                    <div class="fl">From</div>
                                    <div class="table-input fl" style="width: 240px;">
                                        ${baseInfo.insuranceStartTime!}
                                    </div>
                                    <div class="fl">To</div>
                                    <div class="table-input fl" style="width: 230px;">
                                        ${baseInfo.insuranceEndTime!}
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                    </td>
                </tr>
            </table>
            <p class="mb-36">1 If yes, please complete the GST Declaration Form</p>
            <h3 class="mb-8">Participation</h3>
            <p class="mb-8">The insurer will assume that participation of the group insurance program is on compulsory basis unless
                otherwise stated. Please tick [Ö] accordingly to the choice of the insurance product that you like to have a quote
                from us.</p>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <th rowspan="2">Benefits</th>
                    <th rowspan="2" colspan="2">Insurance Coverage</th>
                    <th colspan="2">Participation</th>
                </tr>
                <tr>
                    <th >Compulsory</th>
                    <th >Voluntary</th>
                </tr>
                <tr>
                    <td>1. Accident Insurance </td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GPA">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>
                    <td colspan="2">
                        <label for="myCheckbox1">
                            <input class="ml-30" type="checkbox" <#if hasBenefit>checked</#if> id="myCheckbox1" name="myCheckbox1">
                                Group Personal Accident (GPA)
                        </label>
                    </td>
                    <td><input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td rowspan="2">2. Medical</td>
                    <td rowspan="2">Group Hospital & Surgical (GHS)</td>
                    <td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GHS">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>
                        <label for="myCheckbox1">
                            <input class="ml-30" type="checkbox" <#if hasBenefit>checked</#if> id="myCheckbox1" name="myCheckbox1">
                                Employee only
                        </label>
                    </td>
                    <td><input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td>
                        <label for="myCheckbox1"><input class="ml-30" type="checkbox" id="myCheckbox1" name="myCheckbox1"> Dependant (Spouse and/or Children)</label>
                    </td>
                    <td><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                    <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
            </table>
            <div class="page-footer clearfix" style="border-top: 1px solid rgba(98, 37, 35, 1);padding: 20px;">
                <div class="fl">
                    <p>Page 1 of 12</p>
                    <p>AUG 2023</p>
                </div>
                <div class="fr">
                    <img src="data:image/png;base64,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" alt=""/>
                </div>
            </div>
        </div>
    </div>
    <!-- 第二页 -->
    <div class="page">
        <div class="page-header"></div>
        <div class="page-content">
            <h1 class="mb-8">Fact-Find for Group – Accident & Health</h1>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <td colspan="5">
                        <div class="fl">Name of Proposer:</div>
                        <div class="table-input fl" style="width: 500px;">
                            ${baseInfo.companyName!}
                        </div>
                    </td>
                </tr>
                <tr>
                    <th rowspan="2">Benefits</th>
                    <th rowspan="2" colspan="2">Insurance Coverage</th>
                    <th colspan="2">Participation</th>
                </tr>
                <tr>
                    <th >Compulsory</th>
                    <th >Voluntary</th>
                </tr>
                <tr>
                    <td rowspan="2">2. Medical</td>
                    <td rowspan="2">Group Major Medical (GMM)</td>
                    <td>
                        <#-- 默认两个框都不勾 -->
                        <#assign checked1 = false>
                        <#assign checked2 = false>
                        <#assign hasBenefit = false>

                        <#list userPlanDutyList as item>
                            <#if item.benefit == "GHS">
                                <#assign hasBenefit = true>
                                <#if item.participation == "2">
                                    <#assign checked2 = true>
                                </#if>
                            </#if>
                        </#list>

                        <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                        <#if hasBenefit && !checked2>
                            <#assign checked1 = true>
                        </#if>
                        <label for="myCheckbox1">
                            <input class="ml-30" type="checkbox" <#if hasBenefit>checked</#if> id="myCheckbox1" name="myCheckbox1">
                                Employee only
                        </label>
                    </td>
                    <td><input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td>
                        <label for="myCheckbox1">
                            <input class="ml-30" type="checkbox" id="myCheckbox1" name="myCheckbox1">
                                Dependant (Spouse and/or Children)
                        </label>
                    </td>
                    <td><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                    <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td rowspan="2">3. Others</td>
                    <td rowspan="2">Group Outpatient Insurance</td>
                    <td>
                        <#-- 默认两个框都不勾 -->
                        <#assign checked1 = false>
                        <#assign checked2 = false>
                        <#assign hasBenefit = false>

                        <#list userPlanDutyList as item>
                            <#if item.benefit == "GP">
                                <#assign hasBenefit = true>
                                <#if item.participation == "2">
                                    <#assign checked2 = true>
                                </#if>
                            </#if>
                        </#list>

                        <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                        <#if hasBenefit && !checked2>
                            <#assign checked1 = true>
                        </#if>
                        <label for="myCheckbox1">
                            <input class="ml-30" type="checkbox" <#if hasBenefit>checked</#if> id="myCheckbox1" name="myCheckbox1">
                                Employee only
                        </label>
                    </td>
                    <td><input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td>
                        <label for="myCheckbox1"><input class="ml-30" type="checkbox" id="myCheckbox1" name="myCheckbox1">
                            Dependant (Spouse and/or Children)
                        </label>
                    </td>
                    <td><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                    <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td rowspan="2">4. Others</td>
                    <td rowspan="2">Maternity</td>
                    <td>
                        <#-- 默认两个框都不勾 -->
                        <#assign checked1 = false>
                        <#assign checked2 = false>
                        <#assign hasBenefit = false>

                        <#list userPlanDutyList as item>
                            <#if item.benefit == "GP">
                                <#assign hasBenefit = true>
                                <#if item.participation == "2">
                                    <#assign checked2 = true>
                                </#if>
                            </#if>
                        </#list>

                        <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                        <#if hasBenefit && !checked2>
                            <#assign checked1 = true>
                        </#if>
                        <label for="myCheckbox1"><input class="ml-30" type="checkbox" <#if hasBenefit>checked</#if> id="myCheckbox1" name="myCheckbox1">
                            Employee only</label>
                    </td>
                    <td><input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td>
                        <label for="myCheckbox1"><input class="ml-30" type="checkbox" id="myCheckbox1" name="myCheckbox1"> Dependant (Spouse and/or Children)</label>
                    </td>
                    <td><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                    <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
            </table>
            <p class="mb-8">
                Note: <br>
                Participation is voluntary if employees or dependents are given the choice to opt for the cover(s), subject to a
                minimum participation level.
            </p>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <td colspan="3">
                        <div class="fl">
                            1. Are there any members currently in hospital or requires frequent admission (e.g.,<br>
                            hospital admission more than 2 times per year) to hospital?<br>
                            If yes, please provide details.
                        </div>
                        <#assign hasYes=false>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="1">
                                <#assign hasYes=true>
                            </#if>
                        </#list>
                        <div class="fr">
                            <label for="myCheckbox1"><input class="ml-30" type="checkbox" <#if hasYes>checked</#if> id="myCheckbox1" name="myCheckbox1"> Yes</label>
                            <label for="myCheckbox2"><input class="ml-30" type="checkbox" <#if !hasYes>checked</#if> id="myCheckbox2" name="myCheckbox2"> No</label>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>No. of Members/Age</td>
                    <td>Reason for Hospitalisation/Nature of illness</td>
                    <td>Total Sum Insured/Plan</td>
                </tr>
                <#-- 遍历数据 -->
                <#assign count=0>
                <#list healthInfo as info>
                    <#if info.disclosureType=="1">
                        <#if count lt 3>
                            <tr>
                                <td>${info.number!""}</td>
                                <td>${info.content!""}</td>
                                <td>${(info.totalSumInsured.amount)!""}</td>
                            </tr>
                        </#if>
                        <#assign count=count+1>
                    </#if>
                </#list>

                <#-- 补空行到3行 -->
                <#if count lt 3>
                    <#list 1..(3-count) as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>

                <#-- 超过3条数据，增加提示行 -->
                <#if count gt 3>
                    <tr>
                        <td colspan="3">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
                <tr>
                    <td colspan="3">
                        <div class="fl">
                            2. Has any member suffered from any serious condition such as cancer, organ failure,<br>
                            heart disease, stroke, liver disorder, arthritis or any other disorder that cause progressive<br>
                            irreversible functional or physical disability?<br>
                            If yes, please provide details
                        </div>
                        <div class="fr">
                            <#assign hasYes=false>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="2">
                                    <#assign hasYes=true>
                                </#if>
                            </#list>
                            <div class="fr">
                                <label for="myCheckbox1"><input class="ml-30" type="checkbox" <#if hasYes>checked</#if> id="myCheckbox1" name="myCheckbox1"> Yes</label>
                                <label for="myCheckbox2"><input class="ml-30" type="checkbox" <#if !hasYes>checked</#if> id="myCheckbox2" name="myCheckbox2"> No</label>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>No. of Members/Age</td>
                    <td>Reason for Hospitalisation/Nature of illness</td>
                    <td>Total Sum Insured/Plan</td>
                </tr>
                <#-- 遍历数据 -->
                <#assign count=0>
                <#list healthInfo as info>
                    <#if info.disclosureType=="2">
                        <#if count lt 3>
                            <tr>
                                <td>${info.number!""}</td>
                                <td>${info.content!""}</td>
                                <td>${(info.totalSumInsured.amount)!""}</td>
                            </tr>
                        </#if>
                        <#assign count=count+1>
                    </#if>
                </#list>

                <#-- 补空行到3行 -->
                <#if count lt 3>
                    <#list 1..(3-count) as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>

                <#-- 超过3条数据，增加提示行 -->
                <#if count gt 3>
                    <tr>
                        <td colspan="3">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
                <tr>
                    <td colspan="3">
                        <div class="fl">
                            3. Is there any member based outside Singapore? <br> If yes, please provide details.
                        </div>
                        <div class="fr">
                            <#assign hasYes=false>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="3">
                                    <#assign hasYes=true>
                                </#if>
                            </#list>
                            <div class="fr">
                                <label for="myCheckbox1"><input class="ml-30" type="checkbox" <#if hasYes>checked</#if> id="myCheckbox1" name="myCheckbox1"> Yes</label>
                                <label for="myCheckbox2"><input class="ml-30" type="checkbox" <#if !hasYes>checked</#if> id="myCheckbox2" name="myCheckbox2"> No</label>
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
            <div class="page-footer clearfix" style="border-top: 1px solid rgba(98, 37, 35, 1);padding: 20px;">
                <div class="fl">
                    <p>Page 2 of 12</p>
                    <p>AUG 2023</p>
                </div>
                <div class="fr">
                    <img src="data:image/png;base64,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" alt=""/>
                </div>
            </div>
        </div>
    </div>
    <!-- 第三页 -->
    <div class="page">
        <div class="page-header"></div>
        <div class="page-content">
            <h1 class="mb-36">Fact-Find for Group – Accident & Health</h1>
            <table border="1" cellspacing="0" class="mb-20">
                <tr>
                    <td colspan="4">
                        <div class="fl">Name of Proposer:</div>
                        <div class="table-input fl" style="width: 500px;">
                            ${baseInfo.companyName!}
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>No. of Members/Age</td>
                    <td>Country Based In</td>
                    <td>Total Sum Insured/Plan</td>
                </tr>
                <#-- 遍历数据 -->
                <#assign count=0>
                <#list healthInfo as info>
                    <#if info.disclosureType=="3">
                        <#if count lt 3>
                            <tr>
                                <td>${info.number!""}</td>
                                <td>${info.content!""}</td>
                                <td>${(info.totalSumInsured.amount)!""}</td>
                            </tr>
                        </#if>
                        <#assign count=count+1>
                    </#if>
                </#list>

                <#-- 补空行到3行 -->
                <#if count lt 3>
                    <#list 1..(3-count) as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>

                <#-- 超过3条数据，增加提示行 -->
                <#if count gt 3>
                    <tr>
                        <td colspan="3">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
                <tr>
                    <td colspan="3">
                        <div class="fl">
                            4. Are there any limitations or exclusions imposed on the coverage on any members? <br>
                            If yes, please provide details.
                        </div>
                        <div class="fr">
                            <#assign hasYes=false>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="4">
                                    <#assign hasYes=true>
                                </#if>
                            </#list>
                            <div class="fr">
                                <label for="myCheckbox1"><input class="ml-30" type="checkbox" <#if hasYes>checked</#if> id="myCheckbox1" name="myCheckbox1"> Yes</label>
                                <label for="myCheckbox2"><input class="ml-30" type="checkbox" <#if !hasYes>checked</#if> id="myCheckbox2" name="myCheckbox2"> No</label>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>No. of Members/Age</td>
                    <td>Limitations/Exclusions</td>
                    <td>Total Sum Insured/Plan</td>
                </tr>
                <#-- 遍历数据 -->
                <#assign count=0>
                <#list healthInfo as info>
                    <#if info.disclosureType=="4">
                        <#if count lt 3>
                            <tr>
                                <td>${info.number!""}</td>
                                <td>${info.content!""}</td>
                                <td>${(info.totalSumInsured.amount)!""}</td>
                            </tr>
                        </#if>
                        <#assign count=count+1>
                    </#if>
                </#list>

                <#-- 补空行到3行 -->
                <#if count lt 3>
                    <#list 1..(3-count) as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>

                <#-- 超过3条数据，增加提示行 -->
                <#if count gt 3>
                    <tr>
                        <td colspan="3">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
                <tr>
                    <td colspan="3">
                        <div class="fl">
                            5. Is there any member engaged in hazardous occupation? (e.g., welder, diver, sandblaster, <br>
                            offshore workers etc) <br>
                            If yes, please provide details
                        </div>
                        <div class="fr">
                            <#assign hasYes=false>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="5">
                                    <#assign hasYes=true>
                                </#if>
                            </#list>
                            <div class="fr">
                                <label for="myCheckbox1"><input class="ml-30" type="checkbox" <#if hasYes>checked</#if> id="myCheckbox1" name="myCheckbox1"> Yes</label>
                                <label for="myCheckbox2"><input class="ml-30" type="checkbox" <#if !hasYes>checked</#if> id="myCheckbox2" name="myCheckbox2"> No</label>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>No. of Members/Age</td>
                    <td>Nature of Work</td>
                    <td>Total Sum Insured/Plan</td>
                </tr>
                <#-- 遍历数据 -->
                <#assign count=0>
                <#list healthInfo as info>
                    <#if info.disclosureType=="5">
                        <#if count lt 3>
                            <tr>
                                <td>${info.number!""}</td>
                                <td>${info.content!""}</td>
                                <td>${(info.totalSumInsured.amount)!""}</td>
                            </tr>
                        </#if>
                        <#assign count=count+1>
                    </#if>
                </#list>

                <#-- 补空行到3行 -->
                <#if count lt 3>
                    <#list 1..(3-count) as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>

                <#-- 超过3条数据，增加提示行 -->
                <#if count gt 3>
                    <tr>
                        <td colspan="3">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
                <tr>
                    <td colspan="3">
                        <div class="fl">
                            6. To the best of your knowledge, is there any member engaged in hazardous sports? (e.g., <br>
                            scuba diving, motor racing, bungee jumping etc) <br>
                            If yes, please provide details.
                        </div>
                        <div class="fr">
                            <#assign hasYes=false>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="6">
                                    <#assign hasYes=true>
                                </#if>
                            </#list>
                            <div class="fr">
                                <label for="myCheckbox1"><input class="ml-30" type="checkbox" <#if hasYes>checked</#if> id="myCheckbox1" name="myCheckbox1"> Yes</label>
                                <label for="myCheckbox2"><input class="ml-30" type="checkbox" <#if !hasYes>checked</#if> id="myCheckbox2" name="myCheckbox2"> No</label>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>No. of Members/Age</td>
                    <td>Type of Sports</td>
                    <td>Total Sum Insured/Plan</td>
                </tr>
                <#-- 遍历数据 -->
                <#assign count=0>
                <#list healthInfo as info>
                    <#if info.disclosureType=="6">
                        <#if count lt 3>
                            <tr>
                                <td>${info.number!""}</td>
                                <td>${info.content!""}</td>
                                <td>${(info.totalSumInsured.amount)!""}</td>
                            </tr>
                        </#if>
                        <#assign count=count+1>
                    </#if>
                </#list>

                <#-- 补空行到3行 -->
                <#if count lt 3>
                    <#list 1..(3-count) as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>

                <#-- 超过3条数据，增加提示行 -->
                <#if count gt 3>
                    <tr>
                        <td colspan="3">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
            </table>
            <p class="mb-36">
                Notes (Applicable from questions 1-6): <br>
                The insurer will not reimburse the hospital claims for any member in hospital at the time of application.
            </p>
            <div class="mb-8">
                1. Benefit: Group Personal Accident Insurance <br>
                For your information: Occupational Classifications
            </div>
            <table border="1" cellspacing="0" class="mb-20">
                <tr>
                    <td style="width: 80px;">Class 1</td>
                    <td>Clerical, administrative or other similar non-hazardous occupations</td>
                </tr>
                <tr>
                    <td>Class 2</td>
                    <td>Occupations where some degree of risk is involved, e.g., supervision of manual workers,
                        totally administrative job in an industrial environment</td>
                </tr>
            </table>
            <div class="page-footer clearfix" style="border-top: 1px solid rgba(98, 37, 35, 1);padding: 20px;">
                <div class="fl">
                    <p>Page 3 of 12</p>
                    <p>AUG 2023</p>
                </div>
                <div class="fr">
                    <img src="data:image/png;base64,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" alt=""/>
                </div>
            </div>
        </div>
    </div>
    <!-- 第四页 -->
    <div class="page">
        <div class="page-header"></div>
        <div class="page-content">
            <h1 class="mb-36">Fact-Find for Group – Accident & Health</h1>
            <table border="1" cellspacing="0" class="mb-20">
                <tr>
                    <td colspan="2">
                        <div class="fl">Name of Proposer:</div>
                        <div class="table-input fl" style="width: 500px;">
                            ${baseInfo.companyName!}
                        </div>
                    </td>
                </tr>
                <tr>
                    <td style="width: 80px;">Class 3</td>
                    <td>Occupations involving regular light to medium manual work but no substantial hazard which
                        may increase the risk of sickness or accident</td>
                </tr>
                <tr>
                    <td>Class 4</td>
                    <td>High risk occupations involving heavy manual work including hot works</td>
                </tr>
            </table>
            <h4 class="mb-8">a)Basic Coverage</h4>
            <table border="1" cellspacing="0" class="mb-20">
                <tr>
                    <th></th>
                    <th>Category of Employees/Occupation </th>
                    <th>Basis of Coverage – Sum Insured</th>
                    <th>No. of Employees</th>
                </tr>
                <#-- 查找数据 -->
                <#assign gpaData = "">
                <#list personOverviews as personOverview>
                    <#if personOverview.tag == "gpa" && personOverview.itemKey == "basis_of_coverage">
                        <#assign gpaData = personOverview.valueMaps>
                    </#if>
                </#list>

                <#-- 固定四行渲染 -->
                <#list 0..3 as i>
                    <tr>
                        <td>
                            <#if i == 0>GPA (i)
                            <#elseif i == 1>GPA ii)
                            <#elseif i == 2>GPA iii)
                            <#elseif i == 3>GPA iv)
                            </#if>
                        </td>

                        <#-- 判断当前行是否有数据 -->
                        <#if gpaData?has_content && i < gpaData?size>
                            <#assign info = gpaData[i]>
                            <td class="text-left">${info.category_of_employees_occupation!}</td>
                            <td>${info.basis_of_coverage_sum_insured!}</td>
                            <td>${info.no_of_employees!}</td>
                        <#else>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </#if>
                    </tr>
                </#list>

                <#-- 如果数据超过四条，显示附件提示 -->
                <#if gpaData?has_content && 4 < gpaData?size>
                    <tr>
                        <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
                <tr>
                    <td colspan="4">
                        <div class="clearfix mb-20">
                            <div>Example 1:</div>
                            <div class="fl">
                                <div>Category of Employees/Occupation</div>
                                <div style="padding-left: 10px;">
                                    <p>i. Senior Management (Director, General Manager, Senior Manager)</p>
                                    <p>ii. Managers & Executive</p>
                                    <p>iii. All Others</p>
                                </div>
                            </div>
                            <div class="fr">
                                <div>Basic Coverage</div>
                                <div style="padding-left: 10px;">
                                    <p>S$100,000</p>
                                    <p>S$50,000</p>
                                    <p>S$50,000</p>
                                </div>
                            </div>
                        </div>
                        <div class="clearfix">
                            <div>Example 2:</div>
                            <div class="fl">
                                <div>Category of Employees/Occupation</div>
                                <div style="padding-left: 10px;">
                                    <p>i. All Employees</p>
                                </div>
                            </div>
                            <div class="fr">
                                <div>Basic Coverage</div>
                                <div style="padding-left: 10px;">
                                    <p>
                                        24x Basic <br>
                                        Monthly Salary2
                                    </p>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
            <p class="mb-36">2 Please provide salary information if the basis of coverage is in terms of basic monthly salary</p>
            <h4 class="mb-8">b) Details of Employees</h4>
            <table border="1" cellspacing="0" class="mb-20">
                <tr>
                    <th></th>
                    <th colspan="4">GPA</th>
                </tr>
                <tr>
                    <th rowspan="2">Age Band (Age Next Birthday)</th>
                    <th colspan="2">No. of Employees</th>
                    <th colspan="2">Total Sum Insured (S$)</th>
                </tr>
                <tr>
                    <th>Female</th>
                    <th>Male</th>
                    <th>Female</th>
                    <th>male</th>
                </tr>
                <#-- 固定年龄段列表 -->
                <#assign ageBands = ["16-30","31-35","36-40","41-45","46-50","51-55","56-60"]>

                <#list ageBands as ageBand>
                    <tr>
                        <td>${ageBand}</td>

                        <#-- 查找数据 -->
                        <#assign gpaFound = false>
                        <#list personOverviews?filter(p -> p.tag=="gpa" && p.itemKey=="age_profile_of_employees") as gpaOverview>
                            <#list gpaOverview.valueMaps as info>
                                <#if info.age_band == ageBand>
                                    <#assign gpaFound = true>
                                    <td>${info.no_of_employees_male!""}</td>
                                    <td>${info.no_of_employees_female!""}</td>
                                    <td>${info.total_sum_insured_male!""}</td>
                                    <td>${info.total_sum_insured_female!""}</td>
                                </#if>
                            </#list>
                        </#list>
                        <#if !gpaFound>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </#if>
                    </tr>
                </#list>
            </table>
            <div class="page-footer clearfix" style="border-top: 1px solid rgba(98, 37, 35, 1);padding: 20px;">
                <div class="fl">
                    <p>Page 4 of 12</p>
                    <p>AUG 2023</p>
                </div>
                <div class="fr">
                    <img src="data:image/png;base64,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" alt=""/>
                </div>
            </div>
        </div>
    </div>
    <!-- 第五页 -->
    <div class="page five">
        <div class="page-header"></div>
        <div class="page-content">
            <h1 class="mb-8">Fact-Find for Group – Accident & Health</h1>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <td colspan="5">
                        <div class="fl">Name of Proposer:</div>
                        <div class="table-input fl" style="width: 500px;">
                            ${baseInfo.companyName!}
                        </div>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <th colspan="4">GPA</th>
                </tr>
                <tr>
                    <th rowspan="2">Age Band (Age Next Birthday)</th>
                    <th colspan="2">No. of Employees</th>
                    <th colspan="2">Total Sum Insured (S$)</th>
                </tr>
                <tr>
                    <th>Female</th>
                    <th>Male</th>
                    <th>Female</th>
                    <th>male</th>
                </tr>
                <#-- 固定年龄段列表 -->
                <#assign ageBands = ["61-65","66-70","Total"]>

                <#list ageBands as ageBand>
                    <tr>
                        <td>${ageBand}</td>

                        <#-- 查找数据 -->
                        <#assign gpaFound = false>
                        <#list personOverviews?filter(p -> p.tag=="gpa" && p.itemKey=="age_profile_of_employees") as gpaOverview>
                            <#list gpaOverview.valueMaps as info>
                                <#if info.age_band == ageBand>
                                    <#assign gpaFound = true>
                                    <td>${info.no_of_employees_male!""}</td>
                                    <td>${info.no_of_employees_female!""}</td>
                                    <td>${info.total_sum_insured_male!""}</td>
                                    <td>${info.total_sum_insured_female!""}</td>
                                </#if>
                            </#list>
                        </#list>
                        <#if !gpaFound>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </#if>
                    </tr>
                </#list>
            </table>
            <h4 class="mb-8">c) Claims Experience for the past 3 years</h4>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <th colspan="3"></th>
                    <th colspan="4">GPA</th>
                </tr>
                <tr>
                    <th colspan="2">Period of Coverage</th>
                    <th rowspan="2">No. of Insured as at</th>
                    <th colspan="2">Paid Claims</th>
                    <th colspan="2">Outstanding Claims</th>
                </tr>
                <tr>
                    <th>From</th>
                    <th>To</th>
                    <th>No. of Claims</th>
                    <th>Amount (S$)</th>
                    <th>No. of Claims</th>
                    <th>Amount (S$)</th>
                </tr>
                <#assign fixedRows = 3>

                <#if isUploadClaimAttach?? && isUploadClaimAttach>
                    <tr>
                        <td>Please refer to the attachment</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <#list 1..(fixedRows - 1) as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                <#else>
                    <#assign displayedCount = 0>
                    <#list claimLogs?keys as key>
                        <#if key == "GPA">
                            <#assign gpclaimList = claimLogs[key]>
                            <#list gpclaimList as claim>
                                <#if displayedCount < 3>
                                    <tr>
                                        <td>${claim.startTime!}</td>
                                        <td>${claim.endTime!}</td>
                                        <td>${claim.insuredNum!}</td>
                                        <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                        <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                        <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                        <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="7" class="text-left italic">More information, please refer to the attachment.</td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#list>
                        </#if>
                    </#list>
                    <#if displayedCount == 0>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#if>
                </#if>
            </table>
            <h3 class="mb-8">2. Benefit: Group Hospital & Surgical Insurance/Major Medical Insurance</h3>
            <h4 class="mb-8">a) Basis of Coverage</h4>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <th>Category of Employee/Occupation</th>
                    <th>Room & Board (R&B) Benefit Plan (S$)</th>
                    <th style="width: 180px;">Currently with TMIS</th>
                    <th style="width: 180px;">Proposal with TMIS</th>
                </tr>
                <#-- 定义数据列表，避免空指针 -->
                <#assign filteredList = []>
                <#list personOverviews as personOverview>
                    <#if personOverview.tag == "ghs" && personOverview.itemKey == "basis_of_coverage">
                        <#assign filteredList = personOverview.valueMaps![]>
                    </#if>
                </#list>

                <#-- 固定输出4行，不足补空 -->
                <#list 0..3 as idx>
                    <tr>
                        <#if idx lt filteredList?size>
                            <td class="text-left">
                                <#if idx == 0>
                                    i.
                                <#elseif idx == 1>
                                    ii.
                                <#elseif idx == 2>
                                    iii.
                                <#elseif idx == 3>
                                    iv.
                                </#if>
                                ${filteredList[idx].category_of_employees_occupation!""}</td>
                            <td>${filteredList[idx].room_and_board_benefit_plan!""}</td>
                            <td>
                                <label for="myCheckbox1_${idx}">
                                    <input class="ml-30" type="checkbox" id="myCheckbox1_${idx}" name="myCheckbox1_${idx}"
                                    <#if filteredList[idx].currently_with_tmis?string == "1">checked</#if>> Yes
                                </label>
                                <label for="myCheckbox2_${idx}">
                                    <input class="ml-30" type="checkbox" id="myCheckbox2_${idx}" name="myCheckbox2_${idx}"
                                    <#if filteredList[idx].currently_with_tmis?string == "0">checked</#if>> No
                                </label>
                            </td>
                            <td>
                                <label for="proposalCheckbox1_${idx}">
                                    <input class="ml-30" type="checkbox" id="proposalCheckbox1_${idx}" name="proposal_with_tmis_${idx}"
                                    <#if filteredList[idx].proposal_with_tmis?string == "1">checked</#if>> Yes
                                </label>
                                <label for="proposalCheckbox2_${idx}">
                                    <input class="ml-30" type="checkbox" id="proposalCheckbox2_${idx}" name="proposal_with_tmis_${idx}"
                                    <#if filteredList[idx].proposal_with_tmis?string == "0">checked</#if>> No
                                </label>
                            </td>
                        <#else>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </#if>
                    </tr>
                </#list>

                <#-- 如果有超过4行数据，显示提示 -->
                <#if filteredList?size gt 4>
                    <tr>
                        <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
            </table>
            <p>Note:</p>
            <ul class="mb-8" style="padding-left: 40px;">
                <li>
                    Dependents can be covered under Group Hospital & Surgical Plan. Their cover should be the same as the
                    employer’s cover
                </li>
                <li>
                    Please provide the Deductible/Co-insurance for respective employee category or occupation if application
                </li>
            </ul>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <td>
                        <div class="clearfix mb-20">
                            <div>Example 1:</div>
                            <div class="fl">
                                <div>Category of Employees/Occupation</div>
                                <div style="padding-left: 10px;">
                                    <p>i. Senior Management (Director, General Manager, Senior Manager)</p>
                                    <p>ii. Managers & Executive</p>
                                    <p>iii. All Others</p>
                                </div>
                            </div>
                            <div class="fr">
                                <div>R&B Benefit Plan</div>
                                <div style="padding-left: 10px;">
                                    <p>S$360</p>
                                    <p>S$200</p>
                                    <p>S$100</p>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            </table>
            <div class="page-footer clearfix" style="border-top: 1px solid rgba(98, 37, 35, 1);padding: 20px;">
                <div class="fl">
                    <p>Page 5 of 12</p>
                    <p>AUG 2023</p>
                </div>
                <div class="fr">
                    <img src="data:image/png;base64,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" alt=""/>
                </div>
            </div>
        </div>
    </div>
    <!-- 第六页 -->
    <div class="page">
        <div class="page-header"></div>
        <div class="page-content">
            <h1 class="mb-36">Fact-Find for Group – Accident & Health</h1>
            <table border="1" cellspacing="0" class="mb-20">
                <tr>
                    <td>
                        <div class="fl">Name of Proposer:</div>
                        <div class="table-input fl" style="width: 500px;">
                            ${baseInfo.companyName!}
                        </div>
                    </td>
                </tr>
            </table>
            <h4 class="mb-8">b) Age Profile of Employees</h4>
            <table border="1" cellspacing="0" class="mb-36">
                <tr>
                    <th rowspan="2">Age Band (Age Next Birthday)</th>
                    <th colspan="2">No. of Employees</th>
                </tr>
                <tr>
                    <th>Female</th>
                    <th>Male</th>
                </tr>
                <#-- 固定年龄段列表 -->
                <#assign ageBands = ["16-30","31-35","36-40","41-45","46-50","51-55","56-60","61-65","66-70","Total"]>

                <#-- 查找数据 -->
                <#assign ghsData = "">
                <#list personOverviews as personOverview>
                    <#if personOverview.tag=="ghs" && personOverview.itemKey=="age_profile_of_employees">
                        <#assign ghsData = personOverview.valueMaps>
                    </#if>
                </#list>

                <#-- 渲染每个年龄段 -->
                <#list ageBands as ageBand>
                    <tr>
                        <td>${ageBand}</td>

                        <#-- 判断当前年龄段是否有数据 -->
                        <#assign found = false>
                        <#if ghsData?has_content>
                            <#list ghsData as info>
                                <#if info.age_band == ageBand>
                                    <td>${info.no_of_employees_male!}</td>
                                    <td>${info.no_of_employees_female!}</td>
                                    <#assign found = true>
                                </#if>
                            </#list>
                        </#if>

                        <#-- 如果没找到数据，显示空格 -->
                        <#if !found>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </#if>
                    </tr>
                </#list>
            </table>
            <h4 class="mb-8">c) Details of Insured Members</h4>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <th rowspan="2">For GHS & GMM</th>
                    <th colspan="4">No. of Employees (Singaporeans, SPRs³) </th>
                </tr>
                <#-- 收集所有 no_of_employees_plan 值 -->
                <#assign noOfEmployeesPlanList = []>
                <#list personOverviews as personOverview>
                    <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_sgp">
                        <#if personOverview.valueMaps?has_content>
                            <#list personOverview.valueMaps as info>
                                <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                    <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                </#if>
                            </#list>
                        </#if>
                    </#if>
                </#list>

                <tr>
                    <#-- 固定四列，如果数据不足显示空格 -->
                    <#list 0..3 as i>
                        <th>
                            <#if i < noOfEmployeesPlanList?size>
                                ${noOfEmployeesPlanList[i]}
                            <#else>
                                &nbsp;
                            </#if>
                        </th>
                    </#list>
                </tr>

                <#-- 固定四行员工类型 -->
                <#assign employeeTypes = ["Employee only", "Employee and spouse", "Employee and children", "Employee and family"]>

                <#list employeeTypes as type>
                    <tr>
                        <td>${type}</td>
                        <#list 0..3 as i>
                            <td>
                                <#-- 尝试获取对应数据，如果不存在则显示空格 -->
                                <#assign value = "">
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_sgp">
                                        <#if personOverview.valueMaps?has_content && i < personOverview.valueMaps?size>
                                            <#if type == "Employee only">
                                                <#assign value = (personOverview.valueMaps[i].employee_only)!"" >
                                            <#elseif type == "Employee and spouse">
                                                <#assign value = (personOverview.valueMaps[i].employee_and_spouse)!"" >
                                            <#elseif type == "Employee and children">
                                                <#assign value = (personOverview.valueMaps[i].employee_and_children)!"" >
                                            <#elseif type == "Employee and family">
                                                <#assign value = (personOverview.valueMaps[i].employee_and_family)!"" >
                                            </#if>
                                        </#if>
                                    </#if>
                                </#list>
                                ${value?if_exists! "&nbsp;"}
                            </td>
                        </#list>
                    </tr>
                </#list>

                <#-- 如果列数超过四，显示附件提示 -->
                <#if 4 < noOfEmployeesPlanList?size>
                    <tr>
                        <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
                <tr>
                    <th rowspan="2">For GHS & GMM</th>
                    <th colspan="4">No. of Employees (Foreigners⁴ only)</th>
                </tr>
                <#-- 收集所有 no_of_employees_plan 值 -->
                <#assign noOfEmployeesPlanList = []>
                <#list personOverviews as personOverview>
                    <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_foreigners">
                        <#if personOverview.valueMaps?has_content>
                            <#list personOverview.valueMaps as info>
                                <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                    <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                </#if>
                            </#list>
                        </#if>
                    </#if>
                </#list>

                <tr>
                    <#-- 固定四列，如果数据不足显示空格 -->
                    <#list 0..3 as i>
                        <th>
                            <#if i < noOfEmployeesPlanList?size>
                                ${noOfEmployeesPlanList[i]}
                            <#else>
                                &nbsp;
                            </#if>
                        </th>
                    </#list>
                </tr>

                <#-- 固定四行员工类型 -->
                <#assign employeeTypes = ["Employee only", "Employee and spouse"]>

                <#list employeeTypes as type>
                    <tr>
                        <td>${type}</td>
                        <#list 0..3 as i>
                            <td>
                                <#-- 尝试获取对应数据，如果不存在则显示空格 -->
                                <#assign value = "">
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_foreigners">
                                        <#if personOverview.valueMaps?has_content && i < personOverview.valueMaps?size>
                                            <#if type == "Employee only">
                                                <#assign value = (personOverview.valueMaps[i].employee_only)!"" >
                                            <#elseif type == "Employee and spouse">
                                                <#assign value = (personOverview.valueMaps[i].employee_and_spouse)!"" >
                                            </#if>
                                        </#if>
                                    </#if>
                                </#list>
                                ${value?if_exists! "&nbsp;"}
                            </td>
                        </#list>
                    </tr>
                </#list>
            </table>
            <div class="page-footer clearfix" style="border-top: 1px solid rgba(98, 37, 35, 1);padding: 20px;">
                <div class="fl">
                    <p>Page 6 of 12</p>
                    <p>AUG 2023</p>
                </div>
                <div class="fr">
                    <img src="data:image/png;base64,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" alt=""/>
                </div>
            </div>
        </div>
    </div>
    <!-- 第七页 -->
   <div class="page">
        <div class="page-header"></div>
        <div class="page-content">
            <h1 class="mb-36">Fact-Find for Group – Accident & Health</h1>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <td colspan="5">
                        <div class="fl">Name of Proposer:</div>
                        <div class="table-input fl" style="width: 500px;">
                            ${baseInfo.companyName!}
                        </div>
                    </td>
                </tr>
                <tr>
                    <th rowspan="2">For GHS & GMM</th>
                    <th colspan="4">No. of Employees (Foreigners4 only) </th>
                </tr>
                <#-- 收集所有 no_of_employees_plan 值 -->
                <#assign noOfEmployeesPlanList = []>
                <#list personOverviews as personOverview>
                    <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_foreigners">
                        <#if personOverview.valueMaps?has_content>
                            <#list personOverview.valueMaps as info>
                                <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                    <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                </#if>
                            </#list>
                        </#if>
                    </#if>
                </#list>

                <tr>
                    <#-- 固定四列，如果数据不足显示空格 -->
                    <#list 0..3 as i>
                        <td>
                            <#if i < noOfEmployeesPlanList?size>
                                ${noOfEmployeesPlanList[i]}
                            <#else>
                                &nbsp;
                            </#if>
                        </td>
                    </#list>
                </tr>

                <#-- 固定四行员工类型 -->
                <#assign employeeTypes = ["Employee and children", "Employee and family"]>

                <#list employeeTypes as type>
                    <tr>
                        <td>${type}</td>
                        <#list 0..3 as i>
                            <td>
                                <#-- 尝试获取对应数据，如果不存在则显示空格 -->
                                <#assign value = "">
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_foreigners">
                                        <#if personOverview.valueMaps?has_content && i < personOverview.valueMaps?size>
                                            <#if type == "Employee and children">
                                                <#assign value = (personOverview.valueMaps[i].employee_and_children)!"" >
                                            <#elseif type == "Employee and family">
                                                <#assign value = (personOverview.valueMaps[i].employee_and_family)!"" >
                                            </#if>
                                        </#if>
                                    </#if>
                                </#list>
                                ${value?if_exists! "&nbsp;"}
                            </td>
                        </#list>
                    </tr>
                </#list>

                <#-- 如果列数超过四，显示附件提示 -->
                <#if 4 < noOfEmployeesPlanList?size>
                    <tr>
                        <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
                <tr>
                    <th rowspan="2">For GMM(if the basis of coverage differs from GHS)</th>
                    <th colspan="4">No. of Employees (Singaporeans, SPRs³) </th>
                </tr>
                <#assign sameToGhs = false>
                <#list personOverviews as personOverview>
                    <#if personOverview.tag == "gmm" && personOverview.itemKey == "same_to_the_ghs" && personOverview.value?string == "1">
                        <#assign sameToGhs = true>
                    </#if>
                </#list>

                <#-- 收集 gmm 数据 -->
                <#assign noOfEmployeesPlanList = []>
                <#if !sameToGhs>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_sgp">
                            <#if personOverview.valueMaps?has_content>
                                <#list personOverview.valueMaps as info>
                                    <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                        <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                    </#if>
                                </#list>
                            </#if>
                        </#if>
                    </#list>
                </#if>

                <tr>
                    <#-- 固定四列，如果没有数据或 same_to_the_ghs 为 1，则显示空格 -->
                    <#list 0..3 as i>
                        <td>
                            <#if !sameToGhs && i < noOfEmployeesPlanList?size>
                                ${noOfEmployeesPlanList[i]}
                            <#else>
                                &nbsp;
                            </#if>
                        </td>
                    </#list>
                </tr>

                <#-- 固定四行员工类型 -->
                <#assign employeeTypes = ["Employee only", "Employee and spouse", "Employee and children", "Employee and family"]>

                <#list employeeTypes as type>
                    <tr>
                        <td>${type}</td>
                        <#list 0..3 as i>
                            <td>
                                <#if sameToGhs>
                                    &nbsp;
                                <#else>
                                    <#-- 尝试获取对应数据，如果不存在则显示空格 -->
                                    <#assign value = "">
                                    <#list personOverviews as personOverview>
                                        <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_sgp">
                                            <#if personOverview.valueMaps?has_content && i < personOverview.valueMaps?size>
                                                <#if type == "Employee only">
                                                    <#assign value = (personOverview.valueMaps[i].employee_only)!"" >
                                                <#elseif type == "Employee and spouse">
                                                    <#assign value = (personOverview.valueMaps[i].employee_and_spouse)!"" >
                                                <#elseif type == "Employee and children">
                                                    <#assign value = (personOverview.valueMaps[i].employee_and_children)!"" >
                                                <#elseif type == "Employee and family">
                                                    <#assign value = (personOverview.valueMaps[i].employee_and_family)!"" >
                                                </#if>
                                            </#if>
                                        </#if>
                                    </#list>
                                    ${value?if_exists! "&nbsp;"}
                                </#if>
                            </td>
                        </#list>
                    </tr>
                </#list>

                <#-- 如果列数超过四，显示附件提示 -->
                <#if !sameToGhs && 4 < noOfEmployeesPlanList?size>
                    <tr>
                        <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
                <tr>
                    <th rowspan="2">For GMM(if the basis of coverage differs from GHS)</th>
                    <th colspan="4">No. of Employees (Foreigners⁴ only) </th>
                </tr>
                <#assign sameToGhs = false>
                <#list personOverviews as personOverview>
                    <#if personOverview.tag == "gmm" && personOverview.itemKey == "same_to_the_ghs" && personOverview.value?string == "1">
                        <#assign sameToGhs = true>
                    </#if>
                </#list>

                <#-- 收集 gmm 数据 -->
                <#assign noOfEmployeesPlanList = []>
                <#if !sameToGhs>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_foreigners">
                            <#if personOverview.valueMaps?has_content>
                                <#list personOverview.valueMaps as info>
                                    <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                        <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                    </#if>
                                </#list>
                            </#if>
                        </#if>
                    </#list>
                </#if>

                <tr>
                    <#-- 固定四列，如果没有数据或 same_to_the_ghs 为 1，则显示空格 -->
                    <#list 0..3 as i>
                        <td>
                            <#if !sameToGhs && i < noOfEmployeesPlanList?size>
                                ${noOfEmployeesPlanList[i]}
                            <#else>
                                &nbsp;
                            </#if>
                        </td>
                    </#list>
                </tr>

                <#-- 固定四行员工类型 -->
                <#assign employeeTypes = ["Employee only", "Employee and spouse", "Employee and children", "Employee and family"]>

                <#list employeeTypes as type>
                    <tr>
                        <td>${type}</td>
                        <#list 0..3 as i>
                            <td>
                                <#if sameToGhs>
                                    &nbsp;
                                <#else>
                                    <#-- 尝试获取对应数据，如果不存在则显示空格 -->
                                    <#assign value = "">
                                    <#list personOverviews as personOverview>
                                        <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_foreigners">
                                            <#if personOverview.valueMaps?has_content && i < personOverview.valueMaps?size>
                                                <#if type == "Employee only">
                                                    <#assign value = (personOverview.valueMaps[i].employee_only)!"" >
                                                <#elseif type == "Employee and spouse">
                                                    <#assign value = (personOverview.valueMaps[i].employee_and_spouse)!"" >
                                                <#elseif type == "Employee and children">
                                                    <#assign value = (personOverview.valueMaps[i].employee_and_children)!"" >
                                                <#elseif type == "Employee and family">
                                                    <#assign value = (personOverview.valueMaps[i].employee_and_family)!"" >
                                                </#if>
                                            </#if>
                                        </#if>
                                    </#list>
                                    ${value?if_exists! "&nbsp;"}
                                </#if>
                            </td>
                        </#list>
                    </tr>
                </#list>

                <#-- 如果列数超过四，显示附件提示 -->
                <#if !sameToGhs && 4 < noOfEmployeesPlanList?size>
                    <tr>
                        <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
            </table>
            <p>3 Refers to Singapore Permanent Residents</p>
            <p class="mb-8">4 Refers to all foreigners holding Employment Pass, S Pass and Work Permit, working in Singapore</p>
            <h4 class="mb-8">d) Claims Experience for the past 3 years</h4>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <th colspan="2">Period of Coverage</th>
                    <th rowspan="2">No. of Insured as at</th>
                    <th colspan="2">Paid Claims</th>
                    <th colspan="2">Outstanding Claims</th>
                </tr>
                <tr>
                    <th>From</th>
                    <th>To</th>
                    <th>No. of Claims</th>
                    <th>Amount (S$)</th>
                    <th>No. of Claims</th>
                    <th>Amount (S$)</th>
                </tr>
                <#assign fixedRows = 3>

                <#if isUploadClaimAttach?? && isUploadClaimAttach>
                    <tr>
                        <td>Please refer to attachment</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <#list 1..(fixedRows - 1) as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                <#else>
                    <#assign displayedCount = 0>
                    <#list claimLogs?keys as key>
                        <#if key == "GHS">
                            <#assign gpclaimList = claimLogs[key]>
                            <#list gpclaimList as claim>
                                <#if displayedCount < 3>
                                    <tr>
                                        <td>${claim.startTime!}</td>
                                        <td>${claim.endTime!}</td>
                                        <td>${claim.insuredNum!}</td>
                                        <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                        <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                        <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                        <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="7" class="text-left italic">More information, please refer to the attachment.</td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#list>
                        </#if>
                    </#list>
                    <#if displayedCount == 0>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#if>
                </#if>
            </table>
            <p class="mb-8">Note: The insurer reserves the rights to request for more information.</p>
            <h4 class="mb-8">e) Please attached a copy of Schedule of Benefits, if the benefits are on insured basis (i.e., currently insured)</h4>
            <div class="page-footer clearfix" style="border-top: 1px solid rgba(98, 37, 35, 1);padding: 20px;">
                <div class="fl">
                    <p>Page 7 of 12</p>
                    <p>AUG 2023</p>
                </div>
                <div class="fr">
                    <img src="data:image/png;base64,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" alt=""/>
                </div>
            </div>
        </div>
    </div>
    <!-- 第八页 -->
    <div class="page">
        <div class="page-header"></div>
        <div class="page-content">
            <h1 class="mb-36">Fact-Find for Group – Accident & Health</h1>
            <table border="1" cellspacing="0" class="mb-20">
                <tr>
                    <td>
                        <div class="fl">Name of Proposer:</div>
                        <div class="table-input fl" style="width: 500px;">
                            ${baseInfo.companyName!}
                        </div>
                    </td>
                </tr>
            </table>
            <h3 class="mb-8">3. Benefit: Group Outpatient Insurance</h3>
            <h4 class="mb-8">a) Category of employment to be insured</h4>
            <table border="1" cellspacing="0" class="mb-36">
                <tr>
                    <th>Category of Employees </th>
                    <th>Clinical GP</th>
                    <th>Specialist</th>
                    <th>Diag X-Ray/Lab Tests</th>
                </tr>
                <#-- 去重categoryEmployee -->
                <#assign uniqueCategories = []>
                <#list userPlanDutyList as item>
                    <#if !(uniqueCategories?seq_contains(item.categoryEmployee))>
                        <#assign uniqueCategories = uniqueCategories + [item.categoryEmployee]>
                    </#if>
                </#list>

                <#-- 序号映射 -->
                <#assign romanNumerals = ["i.", "ii.", "iii.", "iv."]>

                <#-- 遍历去重后的类别，最多显示四行 -->
                <#list 0..3 as i>
                    <tr>
                        <#if i < uniqueCategories?size>
                            <#assign category = uniqueCategories[i]>

                            <!-- 员工类别 -->
                            <td class="text-left">${romanNumerals[i]} ${category}</td>

                            <!-- 判断GP列是否勾选 -->
                            <#assign gpChecked = false>
                            <#list userPlanDutyList as item>
                                <#if item.categoryEmployee == category && item.benefit == "GP">
                                    <#assign gpChecked = true>
                                </#if>
                            </#list>
                            <td><input type="checkbox" <#if gpChecked>checked</#if>></td>

                            <!-- 判断SP列是否勾选 -->
                            <#assign spChecked = false>
                            <#list userPlanDutyList as item>
                                <#if item.categoryEmployee == category && item.benefit == "SP">
                                    <#assign spChecked = true>
                                </#if>
                            </#list>
                            <td><input type="checkbox" <#if spChecked>checked</#if>></td>

                            <!-- Diag X-Ray/Lab Tests 空列 -->
                            <td><input type="checkbox"></td>
                        <#else>
                            <!-- 空行补齐 -->
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </#if>
                    </tr>
                </#list>

                <!-- 提示行 -->
                <#if 4 < uniqueCategories?size>
                    <tr>
                        <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                    </tr>
                </#if>

                <!-- Dependant行 -->
                <tr>
                    <td>Dependant (where applicable)</td>
                    <#assign gpDependantsIncluded = "No">
                    <#assign spDependantsIncluded = "No">
                    <#assign gdDependantsIncluded = "No">
                    <#assign gpHeadcount = "">
                    <#assign spHeadcount = "">
                    <#assign gdHeadcount = "">
                    <#list personOverviews as personOverview>
                        <#if personOverview.itemKey == "dependants_included">
                            <#if personOverview.tag == "gp">
                                <#if (personOverview.value!"") == "1">
                                    <#assign gpDependantsIncluded = "Yes">
                                </#if>
                            <#elseif personOverview.tag == "sp">
                                <#if (personOverview.value!"") == "1">
                                    <#assign spDependantsIncluded = "Yes">
                                </#if>
                            <#elseif personOverview.tag == "gd">
                                <#if (personOverview.value!"") == "1">
                                    <#assign gdDependantsIncluded = "Yes">
                                </#if>
                            </#if>
                        </#if>

                        <#if personOverview.itemKey == "no_of_headcount">
                            <#if personOverview.tag == "gp">
                                <#if personOverview.value??>
                                    <#assign gpHeadcount = personOverview.value!>
                                </#if>
                            <#elseif personOverview.tag == "sp">
                                <#if personOverview.value??>
                                    <#assign spHeadcount = personOverview.value!>
                                </#if>
                            <#elseif personOverview.tag == "gd">
                                <#if personOverview.value?? >
                                    <#assign gdHeadcount = personOverview.value!>
                                </#if>
                            </#if>
                        </#if>
                    </#list>

                    <td>${gpDependantsIncluded}</td>
                    <td>${spDependantsIncluded}</td>
                    <td>${gdDependantsIncluded}</td>
                </tr>
                <tr>
                    <td>No. of Headcounts</td>
                    <td>${gpHeadcount}</td>
                    <td>${spHeadcount}</td>
                    <td>${gdHeadcount}</td>
                </tr>
            </table>
            <h4 class="mb-8">b) Age profile of employees</h4>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <th rowspan="2">Age Band (Age Next Birthday)</th>
                    <th colspan="2">No. of Employees</th>
                </tr>
                <tr>
                    <th>Female</th>
                    <th>Male</th>
                </tr>
                <#-- 固定年龄段列表 -->
                <#assign ageBands = ["16-30","31-35","36-40","41-45","46-50","51-55","56-60","61-65","66-70","Total"]>

                <#-- 查找数据 -->
                <#assign data = "">
                <#list personOverviews as personOverview>
                    <#if personOverview.tag=="group_outpatient_insurance" && personOverview.itemKey=="age_profile_of_employees">
                        <#assign data = personOverview.valueMaps>
                    </#if>
                </#list>

                <#-- 渲染每个年龄段 -->
                <#list ageBands as ageBand>
                    <tr>
                        <td>${ageBand}</td>

                        <#-- 判断当前年龄段是否有数据 -->
                        <#assign found = false>
                        <#if data?has_content>
                            <#list data as info>
                                <#if info.age_band == ageBand>
                                    <td>${info.no_of_employees_male!}</td>
                                    <td>${info.no_of_employees_female!}</td>
                                    <#assign found = true>
                                </#if>
                            </#list>
                        </#if>

                        <#-- 如果没找到数据，显示空格 -->
                        <#if !found>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </#if>
                    </tr>
                </#list>
            </table>
            <div class="page-footer clearfix" style="border-top: 1px solid rgba(98, 37, 35, 1);padding: 20px;">
                <div class="fl">
                    <p>Page 8 of 12</p>
                    <p>AUG 2023</p>
                </div>
                <div class="fr">
                    <img src="data:image/png;base64,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" alt=""/>
                </div>
            </div>
        </div>
    </div>
    <!-- 第九页 -->
    <div class="page">
        <div class="page-header"></div>
        <div class="page-content">
            <h1 class="mb-36">Fact-Find for Group – Accident & Health</h1>
            <table border="1" cellspacing="0" class="mb-20">
                <tr>
                    <td>
                        <div class="fl">Name of Proposer:</div>
                        <div class="table-input fl" style="width: 500px;">
                            ${baseInfo.companyName!}
                        </div>
                    </td>
                </tr>
            </table>
            <h4 class="mb-8">c)Claims experience for the past 3 years - Paid claims</h4>
            <table border="1" cellspacing="0" class="mb-36">
                <tr>
                    <th colspan="2">Period of Coverage</th>
                    <th rowspan="2">No. of Insured as at</th>
                    <th colspan="2">Clinical GP</th>
                    <th colspan="2">Specialist</th>
                    <th colspan="2">Diag X-Ray/Lab Tests</th>
                </tr>
                <tr>
                    <th>From</th>
                    <th>To</th>
                    <th>No. of Claims</th>
                    <th>Amount(S$)</th>
                    <th>No. of Claims</th>
                    <th>Amount(S$)</th>
                    <th>No. of Claims</th>
                    <th>Amount(S$)</th>
                </tr>
                <#if isUploadClaimAttach?? && isUploadClaimAttach>
                    <tr>
                        <td>Please refer to attachment</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <#list 1..2 as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>

                <#assign displayedCount = 0>
                <#list claimLogs?keys as key>
                    <#if key == "GP">
                        <#assign gpclaimList = claimLogs[key]>
                        <#list gpclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime}</td>
                                    <td>${claim.endTime}</td>
                                    <td>&nbsp;</td>
                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                    <#assign spclaimList = claimLogs["SP"]![]>
                                    <#assign spshowflg = false>
                                    <#list spclaimList as spclaim>
                                        <#if spclaim.startTime == claim.startTime>
                                            <#assign spshowflg = true>
                                            <td>${(spclaim.paidClaimsNum??)?then(spclaim.paidClaimsNum, "")}</td>
                                            <td>${(spclaim.paidAmount?? && spclaim.paidAmount.amount??)?then(spclaim.paidAmount.amount, "")}</td>
                                        </#if>
                                    </#list>
                                    <#if !spshowflg>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </#if>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="9" class="text-left italic">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>

                <#list claimLogs?keys as key>
                    <#if key == "SP" && displayedCount < 3>
                        <#assign spclaimList = claimLogs[key]>
                        <#list spclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime}</td>
                                    <td>${claim.endTime}</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="9" class="text-left italic">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>

                <#if displayedCount < 3 && !isUploadClaimAttach>
                    <#list displayedCount..2 as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>
            </table>
            <h4 class="mb-8">d) Claims experience for the past 3 years - Outstanding claims</h4>
            <table border="1" cellspacing="0" class="mb-36">
                <tr>
                    <th colspan="2">Period of Coverage</th>
                    <th rowspan="2">No. of Insured as at</th>
                    <th colspan="2">Clinical GP</th>
                    <th colspan="2">Specialist</th>
                    <th colspan="2">Diag X-Ray/Lab Tests</th>
                </tr>
                <tr>
                    <th>From</th>
                    <th>To</th>
                    <th>No. of Claims</th>
                    <th>Amount(S$)</th>
                    <th>No. of Claims</th>
                    <th>Amount(S$)</th>
                    <th>No. of Claims</th>
                    <th>Amount(S$)</th>
                </tr>
                <#if isUploadClaimAttach?? && isUploadClaimAttach>
                    <tr>
                        <td>Please refer to attachment</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <#list 1..2 as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>

                <#assign displayedCount = 0>
                <#list claimLogs?keys as key>
                    <#if key == "GP">
                        <#assign gpclaimList = claimLogs[key]>
                        <#list gpclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime}</td>
                                    <td>${claim.endTime}</td>
                                    <td>&nbsp;</td>
                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                    <#assign spclaimList = claimLogs["SP"]![]>
                                    <#assign spshowflg = false>
                                    <#list spclaimList as spclaim>
                                        <#if spclaim.startTime == claim.startTime>
                                            <#assign spshowflg = true>
                                            <td>${(spclaim.outstandingClaimsNum??)?then(spclaim.outstandingClaimsNum, "")}</td>
                                            <td>${(spclaim.outstandingAmount?? && spclaim.outstandingAmount.amount??)?then(spclaim.outstandingAmount.amount, "")}</td>
                                        </#if>
                                    </#list>
                                    <#if !spshowflg>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </#if>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="9" class="italic text-left">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>

                <#list claimLogs?keys as key>
                    <#if key == "SP" && displayedCount < 3>
                        <#assign spclaimList = claimLogs[key]>
                        <#list spclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime}</td>
                                    <td>${claim.endTime}</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="9" class="text-left italic">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>

                <#list claimLogs?keys as key>
                    <#if key == "GD" && displayedCount < 3>
                        <#assign gdclaimList = claimLogs[key]>
                        <#list gdclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime}</td>
                                    <td>${claim.endTime}</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="9" class="text-left italic">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>

                <#if displayedCount < 3 && !isUploadClaimAttach>
                    <#list displayedCount..2 as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>
            </table>
            <h4 class="mb-8">e) Please attach a copy of the Schedule of Benefits if the benefits are on insured basis. If currently selfinsured, kindly provide the following details:
                (Please indicate “Unlimited” if there is no cap and “N.A” if it is not applicable)</h4>
            <table border="1" cellspacing="0" class="mb-36">
                <tr>
                    <th rowspan="2">Benefits</th>
                    <th colspan="2">Maximum Limit per Visit (S$)</th>
                    <th colspan="2">Maximum Limit per Policy Year (S$)</th>
                    <th colspan="4">Co-payment (S$)/Co-insurance (%)</th>
                </tr>
                <tr>
                    <th>Panel clinic</th>
                    <th>Non-panel clinic</th>
                    <th>Panel clinic</th>
                    <th>Non-panel clinic</th>
                    <th>Panel clinic</th>
                    <th>Non-panel clinic</th>
                    <th>Panel clinic</th>
                    <th>Non-panel clinic</th>
                </tr>
                <tr>
                    <td>Clinical GP</td>
                    <td>S$</td>
                    <td>S$</td>
                    <td>S$</td>
                    <td>S$</td>
                    <td>S$</td>
                    <td>S$</td>
                    <td>0%</td>
                    <td>0%</td>
                </tr>
                <tr>
                    <td>Specialist</td>
                    <td>S$</td>
                    <td>S$</td>
                    <td>S$</td>
                    <td>S$</td>
                    <td>S$</td>
                    <td>S$</td>
                    <td>0%</td>
                    <td>0%</td>
                </tr>
                <tr>
                    <td>Diag XRay/Lab Test</td>
                    <td>S$</td>
                    <td>S$</td>
                    <td>S$</td>
                    <td>S$</td>
                    <td>S$</td>
                    <td>S$</td>
                    <td>0%</td>
                    <td>0%</td>
                </tr>
            </table>
            <div class="page-footer clearfix" style="border-top: 1px solid rgba(98, 37, 35, 1);padding: 20px;">
                <div class="fl">
                    <p>Page 9 of 12</p>
                    <p>AUG 2023</p>
                </div>
                <div class="fr">
                    <img src="data:image/png;base64,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" alt=""/>
                </div>
            </div>
        </div>
    </div>
    <!-- 第十页 -->
    <div class="page">
        <div class="page-header"></div>
        <div class="page-content">
            <h1 class="mb-36">Fact-Find for Group – Accident & Health</h1>
            <table border="1" cellspacing="0" class="mb-20">
                <tr>
                    <td>
                        <div class="fl">Name of Proposer:</div>
                        <div class="table-input fl" style="width: 500px;">
                            ${baseInfo.companyName!}
                        </div>
                    </td>
                </tr>
            </table>
            <h3 class="mb-8">4. Benefit: Maternity Insurance</h3>
            <h4 class="mb-8">a) Basis of Coverage</h4>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <th>Category of Employees</th>
                    <th>No. of Headcount</th>
                </tr>
                <#-- 定义数据列表，避免空指针 -->
                <#assign filteredList = []>
                <#list personOverviews as personOverview>
                    <#if personOverview.tag == "gm" && personOverview.itemKey == "basis_of_coverage">
                        <#assign filteredList = personOverview.valueMaps![]>
                    </#if>
                </#list>

                <#-- 固定输出3行，不足补空 -->
                <#list 0..2 as idx>
                    <tr>
                        <#if idx lt filteredList?size>
                            <td>${filteredList[idx].category_of_employees_occupation!""}</td>
                            <td>${filteredList[idx].no_of_headcount!""}</td>
                        <#else>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </#if>
                    </tr>
                </#list>

                <#-- 如果有超过3行数据，显示提示 -->
                <#if filteredList?size gt 3>
                    <tr>
                        <td colspan="2" class="text-left italic">
                            More information, please refer to the attachment.
                        </td>
                    </tr>
                </#if>
            </table>
            <div>Example 1:</div>
            <table>
                <tr>
                    <th style="text-align: left;">Category of Employees/Occupation</th>
                    <th></th>
                </tr>
                <tr>
                    <td>i. Senior Management (Director, General Manager, Senior Manager)</td>
                    <td></td>
                </tr>
                <tr>
                    <td>ii. Managers & Executive</td>
                    <td></td>
                </tr>
                <tr>
                    <td>iii. All Others</td>
                    <td></td>
                </tr>
            </table>
            <div>Example 2:</div>
            <table>
                <tr>
                    <td>i. Senior Management (Director, General Manager, Senior Manager)</td>
                </tr>
            </table>
            <h4 class="mb-8">b) Claims Experience for the past 3 years</h4>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <th colspan="2">Period of Coverage</th>
                    <th rowspan="2">No. of Insured as at</th>
                    <th colspan="2">Paid Claims</th>
                    <th colspan="2">Outstanding Claims</th>
                </tr>
                <tr>
                    <th>From</th>
                    <th>To</th>
                    <th>No. of Claims</th>
                    <th>Amount (S$)</th>
                    <th>No. of Claims</th>
                    <th>Amount (S$)</th>
                </tr>
                <#assign fixedRows = 3>

                <#if isUploadClaimAttach?? && isUploadClaimAttach>
                    <tr>
                        <td>Please refer to attachment</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <#list 1..(fixedRows - 1) as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                <#else>
                    <#assign displayedCount = 0>
                    <#list claimLogs?keys as key>
                        <#if key == "GM">
                            <#assign gpclaimList = claimLogs[key]>
                            <#list gpclaimList as claim>
                                <#if displayedCount < 3>
                                    <tr>
                                        <td>${claim.startTime!}</td>
                                        <td>${claim.endTime!}</td>
                                        <td>${claim.insuredNum!}</td>
                                        <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                        <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                        <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                        <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="7" class="text-left italic">More information, please refer to the attachment.</td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#list>
                        </#if>
                    </#list>
                    <#if displayedCount == 0>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#if>
                </#if>
            </table>
            <p class="mb-20">Note: The insurer reserves the rights to request for more information</p>
            <h4 class="mb-8">c) Please attached a copy of Schedule of Benefits, if the benefits are on insured basis (i.e., currently insured).
                If currently self-insured, kindly provide the following details (Please indicate unlimited if there is no cap and
                N.A. if it is not applicable).</h4>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <th>Benefits</th>
                    <th>Maximum Limit per Policy Year (S$)</th>
                    <th>Deductible (S$)/Co-Insurance (%)</th>
                </tr>
                <tr>
                    <td>Normal Delivery</td>
                    <td>S$</td>
                    <td></td>
                </tr>
                <tr>
                    <td>Caesarian Delivery</td>
                    <td>S$</td>
                    <td></td>
                </tr>
                <tr>
                    <td>Others:</td>
                    <td>S$</td>
                    <td></td>
                </tr>
            </table>
            <div class="page-footer clearfix" style="border-top: 1px solid rgba(98, 37, 35, 1);padding: 20px;">
                <div class="fl">
                    <p>Page 10 of 12</p>
                    <p>AUG 2023</p>
                </div>
                <div class="fr">
                    <img src="data:image/png;base64,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" alt=""/>
                </div>
            </div>
        </div>
    </div>
    <!-- 第十一页 -->
    <div class="page">
        <div class="page-header"></div>
        <div class="page-content">
            <h1 class="mb-20">Fact-Find for Group – Accident & Health</h1>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <td>
                        <div class="fl">Name of Proposer:</div>
                        <div class="table-input fl" style="width: 500px;">
                            ${baseInfo.companyName!}
                        </div>
                    </td>
                </tr>
            </table>
            <p>Needs Analysis & Product Recommendation</p>
            <p class="mb-8">Please select the priority of your company’s needs:</p>
            <table border="1" cellspacing="0" class="mb-8">
                <tr>
                    <th>Company’s Priorities</th>
                    <th style="width: 300px;">Level of Priority</th>
                    <th>Advisor’s Recommendations</th>
                </tr>
                <tr>
                    <td>Cover for Outpatient Medical expenses</td>
                    <#assign isChecked = false>
                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GP" && item.participation == "1">
                            <#assign isChecked = true>
                        </#if>
                    </#list>
                    <td>
                        <label for="myCheckbox1"><input class="ml-30" type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"> Low</label>
                        <label for="myCheckbox2"><input class="ml-30" type="checkbox" id="myCheckbox2" name="myCheckbox2"> Medium</label>
                        <label for="myCheckbox2"><input class="ml-30" type="checkbox" <#if isChecked>checked</#if> id="myCheckbox2" name="myCheckbox2"> High</label>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>Cover for Hospital & Surgical expenses</td>
                    <#assign isChecked = false>
                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GHS" && item.participation == "1">
                            <#assign isChecked = true>
                        </#if>
                    </#list>
                    <td>
                        <label for="myCheckbox1"><input class="ml-30" type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"> Low</label>
                        <label for="myCheckbox2"><input class="ml-30" type="checkbox" id="myCheckbox2" name="myCheckbox2"> Medium</label>
                        <label for="myCheckbox2"><input class="ml-30" type="checkbox" <#if isChecked>checked</#if> id="myCheckbox2" name="myCheckbox2"> High</label>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>Cover for Dental expenses</td>
                    <#assign isChecked = false>
                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GD" && item.participation == "1">
                            <#assign isChecked = true>
                        </#if>
                    </#list>
                    <td>
                        <label for="myCheckbox1"><input class="ml-30" type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"> Low</label>
                        <label for="myCheckbox2"><input class="ml-30" type="checkbox" id="myCheckbox2" name="myCheckbox2"> Medium</label>
                        <label for="myCheckbox2"><input class="ml-30" type="checkbox" <#if isChecked>checked</#if> id="myCheckbox2" name="myCheckbox2"> High</label>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>Cover for major illnesses(e.g., Cancer, Kidney failure etc)</td>
                    <#assign isChecked = false>
                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GCI" && item.participation == "1">
                            <#assign isChecked = true>
                        </#if>
                    </#list>
                    <td>
                        <label for="myCheckbox1"><input class="ml-30" type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"> Low</label>
                        <label for="myCheckbox2"><input class="ml-30" type="checkbox" id="myCheckbox2" name="myCheckbox2"> Medium</label>
                        <label for="myCheckbox2"><input class="ml-30" type="checkbox" <#if isChecked>checked</#if> id="myCheckbox2" name="myCheckbox2"> High</label>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>Cover for Loss of Income due to sickness or accident</td>
                    <#assign isChecked = false>
                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GDI" && item.participation == "1">
                            <#assign isChecked = true>
                        </#if>
                    </#list>
                    <td>
                        <label for="myCheckbox1"><input class="ml-30" type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"> Low</label>
                        <label for="myCheckbox2"><input class="ml-30" type="checkbox" id="myCheckbox2" name="myCheckbox2"> Medium</label>
                        <label for="myCheckbox2"><input class="ml-30" type="checkbox" <#if isChecked>checked</#if> id="myCheckbox2" name="myCheckbox2"> High</label>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>Cover for long-term medical treatment</td>
                    <#assign isChecked = false>
                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GTL" && item.participation == "1">
                            <#assign isChecked = true>
                        </#if>
                    </#list>
                    <td>
                        <label for="myCheckbox1"><input class="ml-30" type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"> Low</label>
                        <label for="myCheckbox2"><input class="ml-30" type="checkbox" id="myCheckbox2" name="myCheckbox2"> Medium</label>
                        <label for="myCheckbox2"><input class="ml-30" type="checkbox" <#if isChecked>checked</#if> id="myCheckbox2" name="myCheckbox2"> High</label>
                    </td>
                    <td></td>
                </tr>
                <tr>
                    <td>Others:</td>
                    <td>
                        <label for="myCheckbox1"><input class="ml-30" type="checkbox" id="myCheckbox1" name="myCheckbox1"> Low</label>
                        <label for="myCheckbox2"><input class="ml-30" type="checkbox" id="myCheckbox2" name="myCheckbox2"> Medium</label>
                        <label for="myCheckbox2"><input class="ml-30" type="checkbox" id="myCheckbox2" name="myCheckbox2"> High</label>
                    </td>
                    <td></td>
                </tr>
            </table>
            <h3>DECLARATION</h3>
            <p>I, the Proposer, declare and warrant that:</p>
            <ul class="u-list mb-8">
                <li>All information provided by me/us in connection with this application are true, accurate and complete</li>
                <li>I agree that this application and declaration shall be the basis of the contract between Liberty and myself</li>
                <li>
                    I agree to accept the Company’s policy subject to the terms, exclusions, and conditions to be
                    expressed therein, endorsed thereon or attached thereto
                </li>
                <li>
                    If I do not fully and faithfully give the facts as I know them or ought to know them, I may receive
                    nothing from the policy
                </li>
                <li>
                    I agree to the policy terms, exclusions, and conditions as expressed in the brochure, proposal form,
                    policy wordings and endorsements
                </li>
                <li>
                    I/We have read & agreed entirely to all terms in Liberty’s Data Protection Policy, available on request &
                    also at <a href="#">www.libertyinsurance.com.sg/data-protection-policy</a>, both now & in advance as it may be
                    amended from time to time
                </li>
            </ul>
            <h3 style="color: rgba(192, 0, 0, 1);">IMPORTANT NOTICE TO SUBMITTER</h3>
            <p style="color: rgba(192, 0, 0, 1);">If you, the submitter of this form, are submitting this form for another person who is the actual Proposer; and in
                consideration for Liberty processing this application upon your request:</p>
            <ul class="u-list mb-20" style="color: rgba(192, 0, 0, 1);">
                <li>You agree that you have been validly & legally authorised by the Proposer to do so; and</li>
                <li>
                    You warrant that you have shown this entire completed document to the intended Proposer and had
                    obtained his/her agreement to everything; and
                </li>
                <li>
                    You, in your personal capacity, agree to indemnify and keep Liberty Insurance Pte Ltd indemnified
                    against all proceedings, costs, expenses, claims, liabilities, losses or damages if any part of this Notice
                    turns out to be false, howsoever whatsoever, on a strict liability basis, that is, even if your state of mind
                    was unintentional, intentional, negligent, inadvertent, accidental, unknowing, et
                </li>
            </ul>
            <div class="clearfix">
                <div class="fl" style="width: 300px;margin-left: 40px;">
                    <div class="table-input"></div>
                    Date
                </div>
                <div class="fr" style="width: 300px;margin-right: 40px;">
                    <div class="table-input"></div>
                    Signatory of Authorised Officer & Company Stamp
                    <div class="table-input" style="margin-top: 30px;"></div>
                    Name
                </div>
            </div>
            <div class="page-footer clearfix" style="border-top: 1px solid rgba(98, 37, 35, 1);padding: 20px;">
                <div class="fl">
                    <p>Page 11 of 12</p>
                    <p>AUG 2023</p>
                </div>
                <div class="fr">
                    <img src="data:image/png;base64,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" alt=""/>
                </div>
            </div>
        </div>
    </div>
    <!-- 第十二页 -->
    <div class="page">
        <div class="page-header"></div>
        <div class="page-content">
            <h1 class="mb-36">Fact-Find for Group – Accident & Health</h1>
            <table border="1" cellspacing="0" class="mb-36">
                <tr>
                    <td>
                        <div class="fl">Name of Proposer:</div>
                        <div class="table-input fl" style="width: 500px;">
                            ${baseInfo.companyName!}
                        </div>
                    </td>
                </tr>
            </table>
            <div class="clearfix mb-36">
                <div class="fl" style="width: 300px;margin-left: 40px;"></div>
                <div class="fr" style="width: 300px;margin-right: 40px;">
                    <div class="table-input"></div>
                    NRIC/FIN No.
                    <div class="table-input" style="margin-top: 30px;"></div>
                    Designation
                </div>
            </div>
            <h3 class="mb-8">DECLARATION – INSURANCE REPRESENTATIVE</h3>
            <p class="mb-36">I/We hereby declare that I/we have reviewed this Fact-Find Group form with the authorised officer of the
                Company, and that I/we have explained all the requirements of this Fact-Find to him/her.</p>
            <div class="clearfix">
                <div class="fl" style="width: 300px;margin-left: 40px;">
                    <div class="table-input"></div>
                    Date
                </div>
                <div class="fr" style="width: 300px;margin-right: 40px;">
                    <div class="table-input"></div>
                    Signatory of Authorised Officer & Company Stamp
                    <div class="table-input" style="margin-top: 30px;"></div>
                    Name
                    <div class="table-input" style="margin-top: 30px;"></div>
                    NRIC/FIN No.
                    <div class="table-input" style="margin-top: 30px;"></div>
                    Designation
                </div>
            </div>
            <div class="page-footer clearfix" style="border-top: 1px solid rgba(98, 37, 35, 1);padding: 20px;">
                <div class="fl">
                    <p>Page 12 of 12</p>
                    <p>AUG 2023</p>
                </div>
                <div class="fr">
                    <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAnCAIAAADyyp3vAAAACXBIWXMAAAsTAAALEwEAmpwYAAAF70lEQVRYheWYWVCTVxTHz5eEQEIQZDMQECEIhKAIYa0QFhHXwYJWpFILKq04QLG11arTkdalrda1peOC0oKKKAWxKhBFHVEgqK0SMEBYQwKEsCQkJGTtQ+xMx9EEBPWh/6dzZ+6c3z33O/eecz9Eq9XCEwTeulBvH/mOwRiDM24zrfuHsDbTFRwuPjZSwBNgnezlFmaKSYIR/d8443sv9bS1gcFeEomM3dje1VAkVrmvCru3Ob5jkmB9EUtGsdWN3sy/tyHI85VxOAn7vz1Dcb4xSaoBMM5YYQQ9BefL+bz+gCCv61ereOz8pJi2iADh5MH6kmt/jqsticrn9fvSPFj1nMpbdQFUQeRUUPWBhUMm9T0rsvalSSSjjPIajVrzZ9nRykduU0LVB37YaIagjJYuTO/mCoh21ldL7y1flCHTzub14d8s2MpCce/uYwAYHBJjMGgfH/fubkFTI9timuqFmZcZdkoVeqLgVyaXP1WUuKxbY5myafOq7OOFPx76zAiLQQ8eMcW9eIKZbDcnu1F/L9GEwPqSKzW+o5cvuHGtqpvbpxhTpnwaq1CgAIDDxR/IJSds86UnzacnzVeaRJ0q9mjqMJ0QWN9xmmU/ag5lUqnbucJ9rHqO9zy32ibvzw+gL5bb/XfFK2Y6Mdh+kRvNYhf07UtvwuPUe06St37cQcDru90MXJmxkVymQIPBoMfGlAiCZHyxKfWT/Q4OtsYmxq0crm7OTCeip6dz/ZOW4lt2LZ2m7h4kPFZBwDfr92ygSFiYKUsulVPIcQPCYQBYuCgIALq7BckbY3xpHs83Zpa9B8VZZ8sR6uKVmWFzmfrdGgbTPEWZCVUudnyhcBgAcHiTmPfDACD7WGFuflZ4pB8AnM0prX7wFADc3J2Krhy8VMCQylAAoNHAnTrrITH2pZ4NV6eEJfwQn+E/GsS6YcaWhNKSu3x+/9HDF7RazZm83e1tPGcXEtnVgdvV197Gqyivaal3aeXZdIiiHEyvh/u//KYbVz0eFGEEgkGd7UGZZWFBAICckyV+/lTRsKSzvUcmlTNrWHO9XVNT9hKJVmvWp9FX5VkZP9qxgf0qn4YjBgBGtU3vwIDORhAkNW21TCa/XHgzJHQes5Y13dKMw+GmZ64Ri6VxH0R1tvND6D4Hv0nLzapCXt1TjSvixnazmup6rVarG/rQ3Bsb2smujo8fs6Oig/B4EyzWKPt4oYODbUtz101GbXzcdrVyEINW6/E5rog5XabDwyN8Xj/JwRYAfGmUirJtADAwIKKH+XpSyQQCbuXqBadPFDNrWHK5AkDB4kzT73NcEXf24ADgSskdAJBKZVvSD0YvDnanOPXw++VyRWSUf0CQF4lkG5+wyIfmMdd7NgAMirD/btDrgpUqtEqFAoATvxSpVOqU5O9279l0Nm93fsFeiWS08iZTrX6+pbcr62h+lMBgL91QrjCabMQ6CYXDe7NOJ22IIRKtRkakF89XEInWSqVKoVCpVGoA+DBxSfHlyrUfLUVjUACg1ehr1w2DjTBqDEajs/N+v7YwOhAA0lN/iF4c9PWu9TmnSrZvPYbBoAHA3JxAj6Ddr3qybHkogAaLnXRyORFlrd2mAGBlaY4gCPtZh6urI9WLTPUiH/4p34VMGhoSl117UFNdX8dsOJdb7EiUvec9NgVZTXaU6sDGxlgAYNVzAoLnAIBSqYqI8D//24WcX8+q1erIgP4d64ThfoP669IEwD4UUUW1LQCIRiQA0NLctS55eWEBoyj/5EzLRidb3ILAgfWxXCOMvhBfkIGGXqdWLp6eHAIAFuaEBk5RYvxOkXgknHIdZ6Ju7jTdldJqPV0+fqRO48pqsuPonNliACCY4W8xmNUPatNiCrp6cQol6shXDa9BncBx2pnSDAD2JJutWw5l73haft/W2kKRmdj2GsiJgUN9B5eG9j58+CyO/herZZpIitYt5Y2DAeDwl+wV4bzIwP7SuzN+3v4MNbkX7riS678qvUOcYSULnDOxZnYKwFOl/9+viHcG/gfboXYPCPuG/wAAAABJRU5ErkJggg==" alt=""/>
                </div>
            </div>
        </div>
    </div>
</body>
</html>