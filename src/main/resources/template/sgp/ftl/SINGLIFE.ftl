<!doctype html>
<html lang="en" xmlns="http://www.w3.org/1999/html">
    <head>
        <meta charset="UTF-8">
        <meta content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0"
        name="viewport">
        <meta content="ie=edge" http-equiv="X-UA-Compatible">
        <title>[SINGLIFE GIFF] GIFF Form (For Customised Plan Quotation) - (07 Mar 2022)</title>
        <style>
            body {
            width: 1240px;
            font-size: 18px;
            /*text-align: justify;*/
            margin: 0 auto;
            }

            * {
            box-sizing: border-box;
            }

            h1 {
            font-size: 1.2em;
            }

            h2 {
            font-size: 1.1em;
            }

            h2 .fr {
            width: 85%;
            }

            table {
            width: 100%;
            border-collapse: collapse;
            table-layout: fixed;
            }

            table.layout-auto {
            table-layout: auto;
            }

            table.border td,
            table.border th {
            border: 1px solid black;
            }

            table.cell-height td {
            height: 6em;
            box-sizing: border-box;
            }

            table.cell-height-small td {
            height: 2em;
            }

            .h-2em {
            height: 2em !important;
            }

            table.padding td,
            table.padding th {
            padding: 0.5em 1em;
            }

            table.padding-large td,
            table.padding-large th {
            padding: 1em;
            }

            table.padding-small td,
            table.padding-small th {
            padding: 0.25em 0.5em;
            }

            table.padding-x td,
            table.padding-x th {
            padding: 0 1em;
            }

            table.padding-x-small td,
            table.padding-x-small th {
            padding: 0 0.5em;
            }

            table.padding-y td,
            table.padding-y th {
            padding: 0.5em 0;
            }

            table.padding-y-large td,
            table.padding-y-large th {
            padding: 1em 0;
            }

            table.no-padding td,
            table.no-padding th {
            padding: 0 !important;
            }

            hr {
            margin: 0 0 2px;
            border-bottom: 2px solid black;
            }


            .text-center {
            text-align: center !important;
            }

            .text-right {
            text-align: right !important;
            }

            .text-left {
            text-align: left !important;
            }

            .block {
            display: block;
            }

            .m-auto {
            margin: auto;
            }

            .p-0 {
            padding: 0 !important;
            }

            .py-0 {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            }

            .px-0 {
            padding-left: 0 !important;
            padding-right: 0 !important;
            }

            .pl-0 {
            padding-left: 0 !important;
            }

            .pl-1 {
            padding-left: 1rem !important;
            }

            .pr-1 {
            padding-right: 1rem !important;
            }

            .pl-2 {
            padding-left: 2rem !important;
            }

            .pr-0 {
            padding-right: 0 !important;
            }

            .pr-2 {
            padding-right: 2rem !important;
            }

            .pl-3 {
            padding-left: 3rem !important;
            }

            .pr-3 {
            padding-right: 3rem !important;
            }

            .m-0 {
            margin: 0 !important;
            }

            .m-1 {
            margin: 1rem;
            }

            .m-2 {
            margin: 2rem;
            }

            .m-4 {
            margin: 4rem;
            }

            .m-8 {
            margin: 8rem;
            }

            .m-6 {
            margin: 6rem;
            }

            .m-8 {
            margin: 8rem;
            }

            .m-10 {
            margin: 10rem;
            }

            .mb-1 {
            margin-bottom: 1rem;
            }

            .mb-2 {
            margin-bottom: 2rem;
            }

            .mb-4 {
            margin-bottom: 4rem;
            }

            .mb-6 {
            margin-bottom: 6rem;
            }

            .mb-8 {
            margin-bottom: 8rem;
            }

            .mb-10 {
            margin-bottom: 10rem;
            }

            .mt-1 {
            margin-top: 1rem;
            }

            .mt-2 {
            margin-top: 2rem;
            }

            .mt-4 {
            margin-top: 4rem;
            }

            .mt-6 {
            margin-top: 6rem;
            }

            .mt-8 {
            margin-top: 8rem;
            }

            .mt-10 {
            margin-top: 10rem;
            }

            .mx-0 {
            margin-left: 0 !important;
            margin-right: 0 !important;
            }

            .mx-1 {
            margin-left: 1rem;
            margin-right: 1rem;
            }

            .mx-2 {
            margin-left: 2rem;
            margin-right: 2rem;
            }

            .mx-4 {
            margin-right: 4rem;
            margin-left: 4rem;
            }

            .my-0 {
            margin-top: 0 !important;
            margin-bottom: 0 !important;
            }

            .my-1 {
            margin-top: 1rem;
            margin-bottom: 1rem;
            }

            .my-2 {
            margin-top: 2rem;
            margin-bottom: 2rem;
            }

            .my-4 {
            margin-top: 4rem;
            margin-bottom: 4rem;
            }

            .center-wrapper {
            display: block;
            text-align: center;
            }

            .center-wrapper > * {
            display: inline-block;
            text-align: left;
            }

            .clear::after {
            content: "";
            display: table;
            clear: both;
            }

            .clear {
            zoom: 1;
            }

            .fl {
            float: left;
            }

            .fr {
            float: right;
            }

            .border {
            border: 1px solid black;
            }

            .border-bold {
            border-width: 2px !important;
            }

            .border-bottom {
            border-bottom: 1px solid black;
            }

            .border-none {
            border: none !important;
            }

            .font-small {
            font-size: smaller;
            }

            .italic {
            font-style: italic;
            }

            .align-top {
            vertical-align: top;
            }

            .align-middle {
            vertical-align: middle;
            }

            .nowrap {
            white-space: nowrap;
            }

            .wrap {
            white-space: normal;
            }

            .dark-bg {
            background-color: #000000;
            color: #fff;
            padding: 10px;
            }

            .font-bold {
            font-weight: bold;
            }

            .caption-bottom {
            caption-side: bottom;
            }

            .inline-block {
            display: inline-block;
            }

            .inline {
            display: inline;
            }

            .relative {
            position: relative;
            }

            .absolute {
            position: absolute;
            }

            .overflow-hidden {
            overflow: hidden;
            }

            .border-box {
            box-sizing: border-box;
            }

            .underline {
            text-decoration: underline;
            text-decoration-thickness: 2px;
            }
        </style>
        <style>
            .page {
            height: 1810px;
            width: 1120px;
            margin: 0 auto;
            box-sizing: border-box;
            padding: 0 0 2rem;
            position: relative;
            }


            .footer {
            position: absolute;
            bottom: 2rem;
            right: 0;
            white-space: nowrap;
            text-align: center;
            font-size: 14px;
            width: 100%;
            }

            .footer .public {
            font-size: 16px;
            font-weight: bold;

            }

            .footer .public .fl {
            color: rgb(34, 138, 0);
            }

            .footer .date {
            position: absolute;
            right: 0;
            bottom: 8em;
            right: -2em;
            background: url("data:image/png;base64,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");
            width: 14px;
            height: 161px;
            }

            .input-bottom-text {
            position: relative;
            margin-bottom: 1em;
            }

            .input-bottom-text .absolute {
            position: absolute;
            top: 102%
            }

            .common-table {
            text-align: left;
            }

            .common-table th,
            .common-table td {
            padding: 0.5em 1em;
            }

            .common-table > tbody > tr > th:first-child {
            vertical-align: top;
            padding-left: 0;
            width: 1em;
            white-space: nowrap;
            }
        </style>
    </head>
    <body>
        <!--第一页-->
        <div class="page">
            <img alt="sing life with aviva"
            src="data:image/png;base64,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"
            style="display: block; margin-right: 0; margin-left: auto;">
            <div class="text-center">
                <h1 class="m-0">SINGLIFE GROUP INSURANCE FACT-FIND FORM</h1>
                <h2 class="m-0">#01-01, 4 Shenton Way, SGX Centre 2, Singapore 068807</h2>
                <h2 class="m-0">Product underwritten by Singapore Life Ltd.</h2>
            </div>
            <p>
                <strong>Singapore Life Ltd.</strong>
                is a registered insurer under the Insurance Act (Cap 142) and an exempt financial adviser under the Financial
                Advisers' Act (Cap110). As a registered insurer, Singapore Life Ltd. provides and sells insurance products such
                as life policies and
                accident and health policies.
            </p>
            <p><strong>Singapore Life Ltd. Group Business Sales Representative's Declaration </strong> <br>Your Singapore Life
                Ltd. Group Business Sales Representative is authorized to provide financial advisory service in relation to and
                sell:</p>
            <table class="nowrap padding-y">
                <tr>
                    <td class="text-center" style="width: 10em;">
                        <input type="checkbox">
                    </td>
                    <td>Group Life Insurance products provided by Singapore Life Ltd</td>
                </tr>
                <tr>
                    <td class="text-center">
                        <input type="checkbox">
                    </td>
                    <td>Group Health & Accident Insurance products provided by Singapore Life Ltd</td>
                </tr>
            </table>
            <p>
                <strong>Personal Data</strong><br>
                In this document, we may collect, use, disclose and/or process certain personal information or data about your
                employees. Such personal
                data will be collected, used, disclosed and/or processed by Singapore Life Ltd(or Singapore Life Ltd group of
                companies) for the
                purpose(s) of performing financial needs analysis and planning, including providing financial advice and product
                recommendations. We
                may also use the personal information to perform reviews of your financial plans from time to time.
            </p>
            <p>
                You may view the full content of the Personal Data Protection Notice at <span class="underline">www.singlife.com/pdpa</span>
                and the Personal Data Protection
                Compliance Undertakings (By Corporate Prospect/Policyholder) at <span class="underline">www.singlife.com/business/pdpa</span>.
            </p>
            <p class="mb-4">
                Singapore Life Ltd.'s Data Protection Notice and Personal Data Protection Compliance Undertakings (By Corporate
                Prospect/Policyholder) may be updated from time to time without notice. Please do visit our website regularly to
                ensure that you are
                well informed of the updates.
            </p>
            <div style="border: 2px dashed black; width: 95%"></div>
            <div class="overflow-hidden" style="width: 95%;">
                <table class="nowrap padding-y">
                    <tr>
                        <td>
                            <p class="m-0 font-bold">KINDLY COMPLETE FULLY IN BLOCK LETTERS AND INK</p>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span class="font-bold">PERIOD OF INSURANCE</span>
                            <span>from:</span>
                            <div class="inline-block">
                                <div class="inline-block input-bottom-text text-center">
                                    <span class="inline-block border-bottom" style="width: 22em">&emsp; ${baseInfo.expectStartTime!}  </span>
                                    <span class="absolute" style="left: 6em;">   (dd/mm/yyyy)</span>
                                </div>
                            </div>
                            <span>to</span>
                            <div class="inline-block">
                                <div class="inline-block input-bottom-text text-center">
                                    <span class="inline-block border-bottom" style="width: 22em">&emsp;  ${baseInfo.expectEndTime!}  </span>
                                    <span class="absolute" style="left: 6em;">(dd/mm/yyyy)</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span class="font-bold">REQUEST FOR QUOTATION</span>
                            <span>was submitted on</span>
                            <div class="inline-block">
                                <div class="inline-block input-bottom-text text-center">
                                    <span class="inline-block border-bottom" style="width: 40em">&emsp;  ${baseInfo.quoteTime!}   </span>
                                    <span class="absolute" style="left: 14em;">(dd/mm/yyyy)</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span class="font-bold">REQUEST FROM:</span>
                            <div class="inline-block">
                                <div class="inline-block input-bottom-text">
                                    <span class="inline-block border-bottom wrap pl-1"
                                    style="width: 52em; padding-right: 12em;">&emsp; </span>
                                    <span class="absolute" style="left: 15em;">(Name of Insurance Company)</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td class="font-bold">GENERAL INFORMATION</td>
                    </tr>
                    <tr>
                        <td>
                            <span>Name of Business:</span>
                            <span class="inline-block border-bottom wrap pl-1"
                            style="width: 100%; padding-right: 12em;">&emsp;  ${baseInfo.companyName!}  </span>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span>Nature of Business:</span>
                            <span class="inline-block border-bottom wrap pl-1"
                            style="width: 100%; padding-right: 12em;">&emsp; ${baseInfo.companyNature!}</span>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span>Presently insured?</span>
                            <#if baseInfo.presentlyInsured == "1">
                                    <strong style="padding-left: 3em;">Yes <input type="checkbox" checked> / No <input type="checkbox"></strong>
                            <#else>
                                    <strong style="padding-left: 3em;">No <input type="checkbox">
                            </#if>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span>If <strong>Yes</strong>, name of current insurer:</span>
                            <span class="inline-block border-bottom wrap pl-1"
                            style="width: 100%; padding-right: 8em;">&emsp;  ${baseInfo.currentEbInsurer!}  </span>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span>Type of Policy:</span>
                            <span class="inline-block border-bottom wrap pl-1"
                            style="width: 100%; padding-right: 13em;">&emsp; Employee Benefit </span>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span>Period of Insurance: </span>
                            <span>From:</span>
                            <div class="inline-block">
                                <div class="inline-block input-bottom-text text-center">
                                    <span class="inline-block border-bottom" style="width: 20em">&emsp; ${baseInfo.insuranceStartTime!}</span>
                                    <span class="absolute" style="left: 5em;">(dd/mm/yyyy)</span>
                                </div>
                            </div>
                            <span style="padding: 0 1em;">To</span>
                            <div class="inline-block">
                                <div class="inline-block input-bottom-text text-center">
                                    <span class="inline-block border-bottom" style="width: 26em">&emsp;   ${baseInfo.insuranceEndTime!} </span>
                                    <span class="absolute" style="left: 5em;">(dd/mm/yyyy)</span>
                                </div>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span>Total No. of Employees: </span>
                            <span class="inline-block border-bottom" style="width: 14em">&emsp;  ${baseInfo.totalEmployees!} </span>
                            <span style="padding-left: 4em;">No. of Employees to be insured:</span>
                            <span class="inline-block border-bottom" style="width: 18em">&emsp;  ${baseInfo.insuredEmployees!}</span>
                        </td>
                    </tr>
                </table>

            </div>
            <!--    footer-->
            <div class="footer">
                <div style="margin: 0 auto;">
                    <div><strong>Singapore Life Ltd 4</strong> Shenton Way #01-01 SGX Centre 2 Singapore 068807 singlife.com
                    </div>
                    <div>Company Reg.No. 196900499K GST Reg.No. MR-8500166-8 | Navigator Investment Services Ltd Company Reg.No.
                        200103470W GST Reg.No. 20-0103470-W
                    </div>
                </div>
                <div class="clear public">
                    <span class="fl">Public</span>
                    <strong class="fr">Page 1 of 15</strong>
                </div>
                <!--        接口不支持文字竖排，只能用图片替换-->
                <div class="date"></div>
            </div>
        </div>

        <!--第二页-->
        <div class="page">
            <p>
                <strong>Participation: </strong>Singapore Life Ltd <strong> will assume that participation of the group
                insurance program is on compulsory
                basis unless otherwise stated.</strong> Please tick [√ ] accordingly to the choice of the insurance product that
                you like to have a
                quote from us.
            </p>
            <table class="border padding mt-1 text-left" style="width: 95%;">
                <colgroup>
                    <col style="width: 6em;">
                    <col style="width: 2.5em;">
                    <col style="width: 11em;">
                    <col>
                    <col span="2" style="width: 8em;">
                </colgroup>
                <tr>
                    <th rowspan="2">Benefits</th>
                    <th class="text-center" colspan="3" rowspan="2">Insurance Coverage</th>
                    <th class="text-center" colspan="2">Participation</th>
                </tr>
                <tr>
                    <th class="text-center">Compulsory</th>
                    <th class="text-center">Voluntary</th>
                </tr>
                <tr>
                    <th rowspan="4">Life Insurance</th>
                    <th rowspan="3">1</th>
                    <td colspan="2">Group Term Life (GTL)</td>
                        <#-- 默认两个框都不勾 -->
                        <#assign checked1 = false>
                        <#assign checked2 = false>
                        <#assign hasBenefit = false>
                        <#list userPlanDutyList as item>
                            <#if item.benefit == "GTL">
                                <#assign hasBenefit = true>
                                <#if item.participation == "2">
                                    <#assign checked2 = true>
                                <#else>
                                    <#assign checked1 = true>
                                </#if>
                            </#if>
                        </#list>
                        <#-- 如果没有该险种，则两个框都不勾 -->
                        <#if hasBenefit && !checked2>
                            <#assign checked1 = true>
                        </#if>
                        <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                        <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td colspan="2">Group Personal Accident (GPA)</td>
                        <#-- 默认两个框都不勾 -->
                        <#assign checked1 = false>
                        <#assign checked2 = false>
                        <#assign hasBenefit = false>

                        <#list userPlanDutyList as item>
                            <#if item.benefit == "GPA">
                                <#assign hasBenefit = true>
                                <#if item.participation == "2">
                                    <#assign checked2 = true>
                                <#else>
                                    <#assign checked1 = true>
                                </#if>
                            </#if>
                        </#list>

                        <#-- 如果没有该险种，则两个框都不勾 -->
                        <#if hasBenefit && !checked2>
                            <#assign checked1 = true>
                        </#if>

                        <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                        <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td colspan="2">Group Critical Illness (GCI)</td>
                        <#-- 默认两个框都不勾 -->
                        <#assign checked1 = false>
                        <#assign checked2 = false>
                        <#assign hasBenefit = false>

                        <#list userPlanDutyList as item>
                            <#if item.benefit == "GCI">
                                <#assign hasBenefit = true>
                                <#if item.participation == "2">
                                    <#assign checked2 = true>
                                <#else>
                                    <#assign checked1 = true>
                                </#if>
                            </#if>
                        </#list>

                        <#-- 如果没有该险种，则两个框都不勾 -->
                        <#if hasBenefit && !checked2>
                            <#assign checked1 = true>
                        </#if>

                        <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                        <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <th>2</th>
                    <td colspan="2">Group Disability Income (GDI)</td>
                        <#-- 默认两个框都不勾 -->
                        <#assign checked1 = false>
                        <#assign checked2 = false>
                        <#assign hasBenefit = false>

                        <#list userPlanDutyList as item>
                            <#if item.benefit == "GDI">
                                <#assign hasBenefit = true>
                                <#if item.participation == "2">
                                    <#assign checked2 = true>
                                <#else>
                                    <#assign checked1 = true>
                                </#if>
                            </#if>
                        </#list>

                        <#-- 如果没有该险种，则两个框都不勾 -->
                        <#if hasBenefit && !checked2>
                            <#assign checked1 = true>
                        </#if>

                        <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                        <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <th rowspan="4">Medical</th>
                    <th rowspan="4">3</th>
                    <td rowspan="2">Group Hospital & Surgical (GHS)</td>
                    <td>Employee only</td>
                        <#-- 默认两个框都不勾 -->
                        <#assign checked1 = false>
                        <#assign checked2 = false>
                        <#assign hasBenefit = false>

                        <#list userPlanDutyList as item>
                            <#if item.benefit == "GHS">
                                <#assign hasBenefit = true>
                                <#if item.participation == "2">
                                    <#assign checked2 = true>
                                <#else>
                                    <#assign checked1 = true>
                                </#if>
                            </#if>
                        </#list>

                        <#-- 如果没有该险种，则两个框都不勾 -->
                        <#if hasBenefit && !checked2>
                            <#assign checked1 = true>
                        </#if>

                        <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                        <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td>Dependant (Spouse and/or Children)</td>
                    <td class="text-center" ><input type="checkbox"  id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center" ><input type="checkbox"  id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td rowspan="2">Group Major Medical (GMM)</td>
                    <td>Employee only</td>
                        <#-- 默认两个框都不勾 -->
                        <#assign checked1 = false>
                        <#assign checked2 = false>
                        <#assign hasBenefit = false>

                        <#list userPlanDutyList as item>
                            <#if item.benefit == "GMM">
                                <#assign hasBenefit = true>
                                <#if item.participation == "2">
                                    <#assign checked2 = true>
                                <#else>
                                    <#assign checked1 = true>
                                </#if>
                            </#if>
                        </#list>

                        <#-- 如果没有该险种，则两个框都不勾 -->
                        <#if hasBenefit && !checked2>
                            <#assign checked1 = true>
                        </#if>

                        <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                        <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td>Dependant (Spouse and/or Children)</td>
                    <td class="text-center"><input type="checkbox"  id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center" ><input type="checkbox"  id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <th rowspan="6">Others</th>
                    <th rowspan="4">4</th>
                    <td rowspan="2">Group Outpatient</td>
                    <td>Employee only</td>
                        <#-- 默认两个框都不勾 -->
                        <#assign checked1 = false>
                        <#assign checked2 = false>
                        <#assign hasBenefit = false>
                        <#list userPlanDutyList as item>
                            <#if item.benefit == "GP">
                                <#assign hasBenefit = true>
                                <#if item.participation == "2">
                                    <#assign checked2 = true>
                                <#else>
                                    <#assign checked1 = true>
                                </#if>
                            </#if>
                        </#list>

                        <#-- 如果没有该险种，则两个框都不勾 -->
                        <#if hasBenefit && !checked2>
                            <#assign checked1 = true>
                        </#if>

                        <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                        <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                <tr>
                    <td>Dependant (Spouse and/or Children)</td>
                    <td class="text-center"><input type="checkbox"  id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center" ><input type="checkbox"  id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td rowspan="2">Dental</td>
                    <td>Employee only</td>
                        <#-- 默认两个框都不勾 -->
                        <#assign checked1 = false>
                        <#assign checked2 = false>
                        <#assign hasBenefit = false>

                        <#list userPlanDutyList as item>
                            <#if item.benefit == "GD">
                                <#assign hasBenefit = true>
                                <#if item.participation == "2">
                                    <#assign checked2 = true>
                                <#else>
                                    <#assign checked1 = true>
                                </#if>
                            </#if>
                        </#list>

                        <#-- 如果没有该险种，则两个框都不勾 -->
                        <#if hasBenefit && !checked2>
                            <#assign checked1 = true>
                        </#if>

                        <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                        <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td>Dependant (Spouse and/or Children)</td>
                    <td class="text-center"><input type="checkbox"  id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center" ><input type="checkbox"  id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <th rowspan="2">5</th>
                    <td rowspan="2">Maternity</td>
                    <td>Employee only</td>
                        <#-- 默认两个框都不勾 -->
                        <#assign checked1 = false>
                        <#assign checked2 = false>
                        <#assign hasBenefit = false>

                        <#list userPlanDutyList as item>
                            <#if item.benefit == "GM">
                                <#assign hasBenefit = true>
                                <#if item.participation == "2">
                                    <#assign checked2 = true>
                                <#else>
                                    <#assign checked1 = true>
                                </#if>
                            </#if>
                        </#list>

                        <#-- 如果没有该险种，则两个框都不勾 -->
                        <#if hasBenefit && !checked2>
                            <#assign checked1 = true>
                        </#if>

                        <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                        <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td>Dependant (Spouse)</td>
                    <td class="text-center"><input type="checkbox"  id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center" ><input type="checkbox"  id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <caption class="caption-bottom italic text-left">
                    Note: Participation is voluntary if employees or dependants are given the choice to opt for the
                    cover(s), subject
                    to a minimum participation level.
                </caption>
            </table>
            <table class="common-table mt-1">
                <!--        1.-->
                <tr>
                    <th>1.</th>
                    <td>
                        Are there any members currently in hospital or requires frequent admission (e.g. hospital admission more
                        <br>
                        than 2 times per year) to hospital?
                        <#assign showFlg = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="1">
                                <#assign showFlg = 1>
                            </#if>
                        </#list>
                        <#if showFlg == 1>
                            <strong style="padding-left: 2em;">Yes <input type="checkbox" checked> / No <input type="checkbox"></strong>
                        <#else>
                            <strong style="padding-left: 2em;">Yes <input type="checkbox"> / No <input type="checkbox" checked></strong>
                        </#if>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>If <strong>Yes</strong>, kindly provide the following details:</td>
                </tr>
                <tr>
                    <td></td>
                    <td>
                        <table class="border padding-small text-center cell-height-small">
                            <tr>
                                <th style="width: 4em;">S/N</th>
                                <th style="width: 10em;"># of members / Age</th>
                                <th>Reason of hospitalisation / Nature of illness</th>
                                <th style="width: 8em">Total Sum Insured / Plan</th>
                            </tr>
                            <#assign displayedCount = 0>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="1">
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${displayedCount + 1}</td>
                                            <td>${info.number!""}</td>
                                            <td>${info.content!""}</td>
                                            <td>${(info.totalSumInsured.amount)!""}</td>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="4" class="text-left italic">
                                                More information, please refer to the attachment.
                                            </td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#if>
                            </#list>
                            <#list 1..(3 - displayedCount) as i>
                                <#if displayedCount!=0 && displayedCount!=3>
                                    <tr>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                    </tr>
                                </#if>
                            </#list>
                            <#if displayedCount == 0 >
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            </#if>
                            <tr>
                                <td class="text-left italic h-2em" colspan="4">
                                    Note: The insurer will not reimburse the hospital claims for any member in hospital at the
                                    time of
                                    application.
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <!--    footer-->
            <div class="footer">
                <div style="margin: 0 auto;">
                    <div><strong>Singapore Life Ltd 4</strong> Shenton Way #01-01 SGX Centre 2 Singapore 068807 singlife.com
                    </div>
                    <div>Company Reg.No. 196900499K GST Reg.No. MR-8500166-8 | Navigator Investment Services Ltd Company Reg.No.
                        200103470W GST Reg.No. 20-0103470-W
                    </div>
                </div>
                <div class="clear public">
                    <span class="fl">Public</span>
                    <strong class="fr">Page 2 of 15</strong>
                </div>
                <!--        接口不支持文字竖排，只能用图片替换-->
                <div class="date"></div>
            </div>
        </div>

        <!--第三页-->
        <div class="page">
            <table class="common-table">
                <!--         2-->
                <tr>
                    <th>2.</th>
                    <td>
                        Has any member suffered or is suffering from any serious condition such as cancer, organ failure, heart
                        <br>
                        disease, stroke, liver disorder, arthritis or any other disorder that causes progressive irreversible
                        functional or
                        <br>
                        physical disability?
                        <#assign showFlg = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="2">
                                <#assign showFlg = 1>
                            </#if>
                        </#list>
                        <#if showFlg == 1>
                            <strong style="padding-left: 2em;">Yes <input type="checkbox" checked> / No <input type="checkbox"></strong>
                        <#else>
                            <strong style="padding-left: 2em;">Yes <input type="checkbox"> / No <input type="checkbox" checked></strong>
                        </#if></td>
                </tr>
                <tr>
                    <th></th>
                    <td>If <strong>Yes</strong>, kindly provide the following details:</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border padding-small text-center cell-height-small">
                            <tr>
                                <th style="width: 4em;">S/N</th>
                                <th style="width: 10em;"># of members / Age</th>
                                <th>Reason of hospitalisation / Nature of illness</th>
                                <th style="width: 8em">Total Sum Insured / Plan</th>
                            </tr>
                            <#assign displayedCount = 0>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="2">
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${displayedCount + 1}</td>
                                            <td>${info.number!""}</td>
                                            <td>${info.content!""}</td>
                                            <td>${(info.totalSumInsured.amount)!""}</td>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="4" class="text-left italic">
                                                More information, please refer to the attachment.
                                            </td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#if>
                            </#list>
                            <#list 1..(3 - displayedCount) as i>
                                <#if displayedCount!=0 && displayedCount!=3>
                                    <tr>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                    </tr>
                                </#if>
                            </#list>
                            <#if displayedCount == 0 >
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            </#if>
                            <tr>
                                <td class="text-left italic h-2em" colspan="4">Note: The insurer will not reimburse the hospital
                                    claims for any member in hospital at the time of application.
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <!--         3-->
                <tr>
                    <th></th>
                    <td style="height: 2em;"></td>
                </tr>
                <tr>
                    <th>3.</th>
                    <td>
                        Is there any member based outside Singapore?
                        <#assign showFlg = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="3">
                                <#assign showFlg = 1>
                            </#if>
                        </#list>
                        <#if showFlg == 1>
                            <strong style="padding-left: 2em;">Yes <input type="checkbox" checked> / No <input type="checkbox"></strong>
                        <#else>
                            <strong style="padding-left: 2em;">Yes <input type="checkbox"> / No <input type="checkbox" checked></strong>
                        </#if> </td>
                </tr>
                <tr>
                    <th></th>
                    <td>If <strong>Yes</strong>, kindly provide the following details:</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border padding-small text-center cell-height-small">
                            <tr>
                                <th style="width: 4em;">S/N</th>
                                <th style="width: 10em;"># of members / Age</th>
                                <th>Country based in</th>
                                <th style="width: 8em">Total Sum Insured / Plan</th>
                            </tr>
                            <#assign displayedCount = 0>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="3">
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${displayedCount + 1}</td>
                                            <td>${info.number!""}</td>
                                            <td>${info.content!""}</td>
                                            <td>${(info.totalSumInsured.amount)!""}</td>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="4" class="text-left italic">
                                                More information, please refer to the attachment.
                                            </td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#if>
                            </#list>
                            <#list 1..(3 - displayedCount) as i>
                                <#if displayedCount!=0 && displayedCount!=3>
                                    <tr>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                    </tr>
                                </#if>
                            </#list>
                            <#if displayedCount == 0 >
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            </#if>
                            <tr>
                                <td class="text-left italic h-2em" colspan="4">Note: The insurer will not reimburse the hospital
                                    claims for any member in hospital at the time of application.
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <!--    footer-->
            <div class="footer">
                <div style="margin: 0 auto;">
                    <div><strong>Singapore Life Ltd 4</strong> Shenton Way #01-01 SGX Centre 2 Singapore 068807 singlife.com
                    </div>
                    <div>Company Reg.No. 196900499K GST Reg.No. MR-8500166-8 | Navigator Investment Services Ltd Company Reg.No.
                        200103470W GST Reg.No. 20-0103470-W
                    </div>
                </div>
                <div class="clear public">
                    <span class="fl">Public</span>
                    <strong class="fr">Page 3 of 15</strong>
                </div>
                <!--        接口不支持文字竖排，只能用图片替换-->
                <div class="date"></div>
            </div>
        </div>

        <!--第四页-->
        <div class="page">
            <table class="common-table">
                <!--         4-->
                <tr>
                    <th>4.</th>
                    <td>
                        Are there any limitations or exclusions imposed on the coverage on any members?
                        <#assign showFlg = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="4">
                                <#assign showFlg = 1>
                            </#if>
                        </#list>
                        <#if showFlg == 1>
                            <strong style="padding-left: 2em;">Yes <input type="checkbox" checked> / No <input type="checkbox"></strong>
                        <#else>
                            <strong style="padding-left: 2em;">Yes <input type="checkbox"> / No <input type="checkbox" checked></strong>
                        </#if> </td>
                </tr>
                <tr>
                    <th></th>
                    <td>If <strong>Yes</strong>, kindly provide the following details:</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border padding-small text-center cell-height-small">
                            <tr>
                                <th style="width: 4em;">S/N</th>
                                <th style="width: 10em;"># of members / Age</th>
                                <th>Limitations / Exclusions</th>
                                <th style="width: 8em">Total Sum Insured / Plan</th>
                            </tr>
                            <#assign displayedCount = 0>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="4">
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${displayedCount + 1}</td>
                                            <td>${info.number!""}</td>
                                            <td>${info.content!""}</td>
                                            <td>${(info.totalSumInsured.amount)!""}</td>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="4" class="text-left italic">
                                                More information, please refer to the attachment.
                                            </td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#if>
                            </#list>
                            <#list 1..(3 - displayedCount) as i>
                                <#if displayedCount!=0 && displayedCount!=3>
                                    <tr>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                    </tr>
                                </#if>
                            </#list>
                            <#if displayedCount == 0 >
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            </#if>
                            <tr>
                                <td class="text-left italic h-2em" colspan="4">Note: The insurer will not reimburse the hospital
                                    claims for any member in hospital at the time of application.
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td style="height: 2em;"></td>
                </tr>
                <!--         5-->
                <tr>
                    <th>5.</th>
                    <td>
                        <div>
                            Is there any member engaged in hazardous occupation?
                            <#assign showFlg = 0>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="5">
                                    <#assign showFlg = 1>
                                </#if>
                            </#list>
                            <#if showFlg == 1>
                                <strong style="padding-left: 2em;">Yes <input type="checkbox" checked> / No <input type="checkbox"></strong>
                            <#else>
                                <strong style="padding-left: 2em;">Yes <input type="checkbox"> / No <input type="checkbox" checked></strong>
                            </#if></div>
                        <div>(Hazardous occupation eg. welder, diver, sandblaster, offshore workers etc.)</div>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>If <strong>Yes</strong>, kindly provide the following details:</td>
                </tr>
                <tr>
                    <td></td>
                    <td>
                        <table class="border padding-small text-center cell-height-small">
                            <tr>
                                <th style="width: 4em;">S/N</th>
                                <th style="width: 10em;"># of members / Age</th>
                                <th>Nature of work</th>
                                <th style="width: 8em">Total Sum Insured / Plan</th>
                            </tr>
                            <#assign displayedCount = 0>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="5">
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${displayedCount + 1}</td>
                                            <td>${info.number!""}</td>
                                            <td>${info.content!""}</td>
                                            <td>${(info.totalSumInsured.amount)!""}</td>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="4" class="text-left italic">
                                                More information, please refer to the attachment.
                                            </td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#if>
                            </#list>
                            <#list 1..(3 - displayedCount) as i>
                                <#if displayedCount!=0 && displayedCount!=3>
                                    <tr>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                    </tr>
                                </#if>
                            </#list>
                            <#if displayedCount == 0 >
                                <tr>
                                    <td>&nbsp; </td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>&nbsp; </td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td>&nbsp; </td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            </#if>
                            <tr>
                                <td class="text-left italic h-2em" colspan="4">Note: The insurer will not reimburse the hospital
                                    claims for any member in hospital at the time of application.
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>

            </table>
            <!--    footer-->
            <div class="footer">
                <div style="margin: 0 auto;">
                    <div><strong>Singapore Life Ltd 4</strong> Shenton Way #01-01 SGX Centre 2 Singapore 068807 singlife.com
                    </div>
                    <div>Company Reg.No. 196900499K GST Reg.No. MR-8500166-8 | Navigator Investment Services Ltd Company Reg.No.
                        200103470W GST Reg.No. 20-0103470-W
                    </div>
                </div>
                <div class="clear public">
                    <span class="fl">Public</span>
                    <strong class="fr">Page 4 of 15</strong>
                </div>
                <!--        接口不支持文字竖排，只能用图片替换-->
                <div class="date"></div>
            </div>
        </div>

        <!--第五页-->
        <div class="page">
            <table class="common-table">
                <!--         6-->
                <tr>
                    <th>6.</th>
                    <td>
                        <div>
                            To the best of your knowledge, is there any member engaged in hazardous sports?
                            <#assign showFlg = 0>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="6">
                                    <#assign showFlg = 1>
                                </#if>
                            </#list>
                            <#if showFlg == 1>
                                <strong style="padding-left: 2em;">Yes <input type="checkbox" checked> / No <input type="checkbox"></strong>
                            <#else>
                                <strong style="padding-left: 2em;">Yes <input type="checkbox"> / No <input type="checkbox" checked></strong>
                            </#if> </div>
                        <div>
                            (Hazardous sports eg. scuba diving, motor racing, bungee jumping etc.)
                        </div>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>If <strong>Yes</strong>, kindly provide the following details:</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border padding-small text-center cell-height-small">
                            <tr>
                                <th style="width: 4em;">S/N</th>
                                <th style="width: 10em;"># of members / Age</th>
                                <th>Type of sports</th>
                                <th style="width: 8em">Total Sum Insured / Plan</th>
                            </tr>
                            <#assign displayedCount = 0>
                            <#list healthInfo as info>
                                <#if info.disclosureType=="6">
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${displayedCount + 1}</td>
                                            <td>${info.number!""}</td>
                                            <td>${info.content!""}</td>
                                            <td>${(info.totalSumInsured.amount)!""}</td>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="4" class="text-left italic">
                                                More information, please refer to the attachment.
                                            </td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#if>
                            </#list>
                            <#list 1..(3 - displayedCount) as i>
                                <#if displayedCount!=0 && displayedCount!=3>
                                    <tr>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                        <td>&nbsp; </td>
                                    </tr>
                                </#if>
                            </#list>
                            <#if displayedCount == 0 >
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                                <tr>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                    <td></td>
                                </tr>
                            </#if>
                            <tr>
                                <td class="text-left italic h-2em" colspan="4">Note: The insurer will not reimburse the hospital
                                    claims for any member in hospital at the time of application.
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>

                <!--         7-->
                <tr>
                    <th>7.</th>
                    <th class="underline">
                        BENEFIT: GROUP TERM LIFE / GROUP PERSONAL ACCIDENT / GROUP CRITICAL ILLNESS INSURANCE
                    </th>
                </tr>
                <tr>
                    <th></th>
                    <th>Occupational Classifications</th>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border padding">
                            <tr>
                                <td style="width: 6em;">Class 1</td>
                                <td>Clerical, administrative or other similar non-hazardous occupations</td>
                            </tr>
                            <tr>
                                <td>Class 2</td>
                                <td>Occupations where some degree of risk is involved, e.g. supervision of manual workers,
                                    totally
                                    administrative job in an industrial environment
                                </td>
                            </tr>
                            <tr>
                                <td>Class 3</td>
                                <td>Occupations involving regular light to medium manual work but no substantial hazard which
                                    may increase the risk of sickness or accident
                                </td>
                            </tr>
                            <tr>
                                <td>Class 4</td>
                                <td>High risk occupations involving heavy manual work including hot works</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <!--        a)-->
                <tr>
                    <th>a)</th>
                    <td>Basis of Coverage</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border border-none padding-small text-center">
                            <colgroup>
                                <col style="width: 4em">
                                <col style="width: 3em">
                                <col span="2">
                                <col style="width: 12em">
                            </colgroup>
                            <tr>
                                <th class="border-none" colspan="2"></th>
                                <th>Category of Employees/Occupation (refer to the examples)</th>
                                <th>Basis of Coverage – Sum Insured (refer to the examples)</th>
                                <th style="width: 8em"># of Employees</th>
                            </tr>
                                <#assign hasData = false >
                                <#assign rowCount = 0 >
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag == "gtl" && personOverview.itemKey == "basis_of_coverage">
                                        <#assign total = personOverview.valueMaps?size />
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if info_index < 4 && info ??>
                                                <#assign hasData = true >
                                                <#assign rowCount = rowCount + 1 >
                                                <tr>
                                                    <#if info_index == 0>
                                                        <th rowspan="4">GTL</th>
                                                    </#if>
                                                    <td>
                                                        <#if info_index == 0>(i)
                                                        <#elseif info_index == 1>(ii)
                                                        <#elseif info_index == 2>(iii)
                                                        <#elseif info_index == 3>(iv)
                                                        </#if>
                                                    </td>
                                                    <td>${info.category_of_employees_occupation!}</td>
                                                    <td>${info.basis_of_coverage_sum_insured!}</td>
                                                    <td>${info.no_of_employees!}</td>
                                                </tr>
                                            </#if>
                                            <#if info_index == 3>
                                                <tr>
                                                    <td colspan="5" style="text-align:left; font-style:italic;">More information, Please refer to the attachment.</td>
                                                </tr>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>
                                <#if hasData && rowCount < 4>
                                    <#list rowCount..3 as i>
                                        <tr>
                                            <#if i == 0>
                                                <th rowspan="4">GTL</th>
                                            </#if>
                                            <td>
                                                <#if i == 0>(i)
                                                <#elseif i == 1>(ii)
                                                <#elseif i == 2>(iii)
                                                <#elseif i == 3>(iv)
                                                </#if>
                                            </td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>
                                <#if !hasData>
                                    <#list 1..4 as i>
                                        <tr>
                                            <#if i == 1>
                                                <th rowspan="4">GTL</th>
                                            </#if>
                                            <td>
                                                <#if i == 1>(i)
                                                <#elseif i == 2>(ii)
                                                <#elseif i == 3>(iii)
                                                <#elseif i == 4>(iv)
                                                </#if>
                                            </td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>
                            <tr>
                                <th colspan="5" style="border: none; height: 2em;"></th>
                            </tr>
                                <#assign hasData = false >
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag == "gpa" && personOverview.itemKey == "basis_of_coverage">
                                        <#assign total = personOverview.valueMaps?size />
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if info_index < 4 && info ??>
                                                <#assign hasData = true >
                                                <tr>
                                                    <#if info_index == 0>
                                                        <th rowspan="4">GPA</th>
                                                    </#if>
                                                    <td>
                                                        <#if info_index == 0>(i)
                                                        <#elseif info_index == 1>(ii)
                                                        <#elseif info_index == 2>(iii)
                                                        <#elseif info_index == 3>(iv)
                                                        </#if>
                                                    </td>
                                                    <td>${info.category_of_employees_occupation!}</td>
                                                    <td>${info.basis_of_coverage_sum_insured!}</td>
                                                    <td>${info.no_of_employees!}</td>
                                                </tr>
                                            </#if>
                                            <#if info_index == 3>
                                                <tr>
                                                    <td colspan="5" style="text-align:left; font-style:italic;">More information, Please refer to the attachment.</td>
                                                </tr>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>
                                <#if !hasData>
                                    <tr>
                                        <th rowspan="4">GPA</th>
                                        <td>
                                            (i)
                                        </td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td>(ii)</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td>(iii)</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td>(iv)</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </tr>
                                </#if>
                        </table>
                    </td>
                </tr>

            </table>
            <!--    footer-->
            <div class="footer">
                <div style="margin: 0 auto;">
                    <div><strong>Singapore Life Ltd 4</strong> Shenton Way #01-01 SGX Centre 2 Singapore 068807 singlife.com
                    </div>
                    <div>Company Reg.No. 196900499K GST Reg.No. MR-8500166-8 | Navigator Investment Services Ltd Company Reg.No.
                        200103470W GST Reg.No. 20-0103470-W
                    </div>
                </div>
                <div class="clear public">
                    <span class="fl">Public</span>
                    <strong class="fr">Page 5 of 15</strong>
                </div>
                <!--        接口不支持文字竖排，只能用图片替换-->
                <div class="date"></div>
            </div>
        </div>

        <!--第六页-->
        <div class="page">
            <table class="common-table">
                <tr>
                    <th></th>
                    <td>
                        <table class="border border-none padding-small text-center">
                            <colgroup>
                                <col style="width: 4em">
                                <col style="width: 3em">
                                <col span="2">
                                <col style="width: 12em">
                            </colgroup>

                                <#assign hasData = false >
                                <#assign rowCount = 0 >
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag == "gci" && personOverview.itemKey == "basis_of_coverage">
                                        <#assign total = personOverview.valueMaps?size />
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if info_index < 4 && info ??>
                                                <#assign hasData = true >
                                                <#assign rowCount = rowCount + 1 >
                                                <tr>
                                                    <#if info_index == 0>
                                                        <th rowspan="4">GCI</th>
                                                    </#if>
                                                    <td>
                                                        <#if info_index == 0>(i)
                                                        <#elseif info_index == 1>(ii)
                                                        <#elseif info_index == 2>(iii)
                                                        <#elseif info_index == 3>(iv)
                                                        </#if>
                                                    </td>
                                                    <td>${info.category_of_employees_occupation!}</td>
                                                    <td>${info.basis_of_coverage_sum_insured!}</td>
                                                    <td>${info.no_of_employees!}</td>
                                                </tr>
                                            </#if>
                                            <#if info_index == 3>
                                                <tr>
                                                    <td colspan="5" style="text-align:left; font-style:italic;">More information, Please refer to the attachment.</td>
                                                </tr>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>
                                <#if hasData && rowCount < 4>
                                    <#list rowCount..3 as i>
                                        <tr>
                                            <#if i == 0>
                                                <th rowspan="4">GCI</th>
                                            </#if>
                                            <td>
                                                <#if i == 0>(i)
                                                <#elseif i == 1>(ii)
                                                <#elseif i == 2>(iii)
                                                <#elseif i == 3>(iv)
                                                </#if>
                                            </td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>
                                <#if !hasData>
                                    <#list 1..4 as i>
                                        <tr>
                                            <#if i == 1>
                                                <th rowspan="4">GCI</th>
                                            </#if>
                                            <td>
                                                <#if i == 1>(i)
                                                <#elseif i == 2>(ii)
                                                <#elseif i == 3>(iii)
                                                <#elseif i == 4>(iv)
                                                </#if>
                                            </td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>
                        </table>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="no-padding text-left">
                            <tr>
                                <th class="underline" style="padding-bottom: 0;">Example 1</th>
                                <td style="width: 12.5em;"></td>
                            </tr>
                            <tr>
                                <th>Category of Employees / Occupation</th>
                                <th>Basis of Coverage</th>
                            </tr>
                            <tr>
                                <td>(i) &nbsp; Senior Management (Director, General Manager, Senior Manager)</td>
                                <td>100,000</td>
                            </tr>
                            <tr>
                                <td>(ii) &nbsp; Manager & Executive</td>
                                <td>50,000</td>
                            </tr>
                            <tr>
                                <td>(iii)&nbsp; All Others</td>
                                <td>25,000</td>
                            </tr>
                            <tr>
                                <th class="underline" style="padding-top: 1em !important;">Example 2</th>
                            </tr>
                            <tr>
                                <th>Category of Employees / Occupation</th>
                                <th>Basis of Coverage</th>
                            </tr>
                            <tr>
                                <td>(i) &nbsp; All Employees</td>
                                <td>24X Basic Monthly Salary*</td>
                            </tr>
                            <caption class="italic text-left caption-bottom" style="padding-top: 1em;">
                                * Please provide salary information if the basis of coverage is in terms of basic monthly
                                salary.
                            </caption>
                        </table>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td style="height: 2em;"></td>
                </tr>
                <!--        b)-->
                <tr>
                    <th>b)</th>
                    <td>Please provide Current Non-Medical Limit (if applicable)</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <span class="inline-block" style="width: 12em;">Group Term Life: </span>
                        <span>S$</span>
                        <span class="inline-block border-bottom" style="width: 14em">&emsp;
                            <#list personOverviews as personOverview>
                                <#if personOverview.tag == "gtl" && personOverview.itemKey == "current_non_medical_limit">
                                    ${personOverview.value!}
                                </#if>
                            </#list>
                        </span>
                        <span>up to age</span>
                        <span class="inline-block border-bottom" style="width: 12em">&emsp;
                            <#list personOverviews as personOverview>
                                <#if personOverview.tag == "gtl" && personOverview.itemKey == "up_to_age">
                                    ${personOverview.value!}
                                </#if>
                            </#list>
                        </span>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <span class="inline-block" style="width: 12em;">Group Critical Illness: </span>
                        <span>S$</span>
                        <span class="inline-block border-bottom" style="width: 14em">&emsp;
                            <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gci" && personOverview.itemKey == "current_non_medical_limit">
                                ${personOverview.value!}
                            </#if>
                        </#list></span>
                        <span>up to age</span>
                        <span class="inline-block border-bottom" style="width: 12em">&emsp;
                            <#list personOverviews as personOverview>
                                <#if personOverview.tag == "gci" && personOverview.itemKey == "up_to_age">
                                    ${personOverview.value!}
                                </#if>
                            </#list>
                        </span>
                    </td>
                </tr>

                <tr>
                    <th></th>
                    <td style="height: 2em;"></td>
                </tr>
                <!--        c)-->
                <tr>
                    <th>c)</th>
                    <td>Group Critical Illness: Basis of Coverage</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        Is this benefit an advance of or an additional amount to the Term Life?
                        <span class="inline-block border-bottom" style="width: 11em;">

                            <#assign personOverviewValue = "">
                            <#list personOverviews as personOverview>
                                <#if personOverview.tag == "gci" && personOverview.itemKey == "amount_to_the_term_life_option">
                                    <#assign personOverviewValue = (personOverview.value)!"" >
                                     ${personOverviewValue}
                                </#if>
                            </#list>
                        </span>

                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        If it is an advance benefit, what percentage on the Term Life sum insured you want us to quote? Please indicate as appropriate:
                        <#assign personOverviewValue = "">
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gci" && personOverview.itemKey == "acceleration_percentage_on_sum_assured">
                                <#assign personOverviewValue = (personOverview.value)!"" >
                                 ${personOverviewValue}
                            </#if>
                        </#list>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>Please provide a list of critical illnesses covered (if currently insured).</td>
                </tr>
            </table>
            <!--    footer-->
            <div class="footer">
                <div style="margin: 0 auto;">
                    <div><strong>Singapore Life Ltd 4</strong> Shenton Way #01-01 SGX Centre 2 Singapore 068807 singlife.com
                    </div>
                    <div>Company Reg.No. 196900499K GST Reg.No. MR-8500166-8 | Navigator Investment Services Ltd Company Reg.No.
                        200103470W GST Reg.No. 20-0103470-W
                    </div>
                </div>
                <div class="clear public">
                    <span class="fl">Public</span>
                    <strong class="fr">Page 6 of 15</strong>
                </div>
                <!--        接口不支持文字竖排，只能用图片替换-->
                <div class="date"></div>
            </div>
        </div>

        <!--第七页-->
        <div class="page">
            <table class="common-table">
                <!--        d)-->
                <tr>
                    <th>d)</th>
                    <td>Details of Employees</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border border-none text-center">
                            <colgroup>
                                <col span="9" style="width: 11%;">
                            </colgroup>
                            <tr>
                                <th class="border-none"></th>
                                <th colspan="4">GTL</th>
                                <th colspan="4">GCI</th>
                            </tr>
                            <tr>
                                <th rowspan="2">Age Band (Age Next Birthday)</th>
                                <th colspan="2"># of Employees</th>
                                <th colspan="2">Total Sum Insured (S$)</th>
                                <th colspan="2"># of Employees</th>
                                <th colspan="2">Total Sum Insured (S$)</th>
                            </tr>
                            <tr>
                                <th>Male</th>
                                <th>Female</th>
                                <th>Male</th>
                                <th>Female</th>
                                <th>Male</th>
                                <th>Female</th>
                                <th>Male</th>
                                <th>Female</th>
                            </tr>
                                <#assign hasData6= false>
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag=="gtl" && personOverview.itemKey=="age_profile_of_employees">
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList  as info>
                                            <#if info.age_band??>
                                                <#assign hasData6= true>
                                                <tr>
                                                    <th>${info.age_band!}</th>
                                                    <td>${info.no_of_employees_male!}</td>
                                                    <td>${info.no_of_employees_female!}</td>
                                                    <td>${info.total_sum_insured_male!}</td>
                                                    <td>${info.total_sum_insured_female!}</td>
                                                    <#assign gciInfoFound = false>
                                                    <#list personOverviews as personOverview>
                                                        <#if personOverview.tag=="gci" && personOverview.itemKey=="age_profile_of_employees">
                                                            <#assign gcivalueMapList = personOverview.value?eval>
                                                            <#list gcivalueMapList as info1>
                                                                <#if info1.age_band?? && info1.age_band==info.age_band>
                                                                    <#assign gciInfoFound = true>
                                                                    <td>${info1.no_of_employees_male!}</td>
                                                                    <td>${info1.no_of_employees_female!}</td>
                                                                    <td>${info1.total_sum_insured_male!}</td>
                                                                    <td>${info1.total_sum_insured_female!}</td>
                                                                </#if>
                                                            </#list>
                                                        </#if>
                                                    </#list>
                                                    <#if !gciInfoFound >
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                    </#if>
                                                </tr>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>
                                <#if !hasData6>
                                    <tr>
                                        <th>16-30</th>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>31-35</th>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>36-40</th>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>41-45</th>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>46-50</th>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>51-55</th>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>56-60</th>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>61-65</th>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>66-70</th>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>Total</th>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                </#if>
                        </table>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td style="height: 2em;"></td>
                </tr>
                <!--        e)-->
                <tr>
                    <th>e)</th>
                    <td>Claims Experience for the past 3 years</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border text-center cell-height-small mb-2">
                            <caption class="font-bold text-left underline">Paid Claims</caption>
                            <colgroup>
                                <col span="2" style="width: 10em;">
                                <col span="6">
                            </colgroup>
                            <tr>
                                <th rowspan="2">
                                    Period of Coverage From / To
                                    <span class="border-bottom inline-block" style="width: 6em;"></span>
                                    (dd/mm/yyyy)
                                </th>
                                <th rowspan="2"># of Insured as at
                                    <span class="border-bottom inline-block" style="width: 6em;"></span>
                                    (dd/mm/yyyy)
                                </th>
                                <th colspan="2">GTL</th>
                                <th colspan="2">GPA</th>
                                <th colspan="2">GCI</th>
                            </tr>
                            <tr>
                                <th># of Claims</th>
                                <th>Amount (S$)</th>
                                <th># of Claims</th>
                                <th>Amount (S$)</th>
                                <th># of Claims</th>
                                <th>Amount (S$)</th>
                            </tr>
                                <#if isUploadClaimAttach?? && isUploadClaimAttach>
                                    <tr>
                                        <td class="text-left italic">Please refer to the attachment</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </tr>
                                    <#list 1..2 as i>
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>

                                <#assign displayedCount = 0>
                                <#list claimLogs?keys as key>
                                    <#if key == "GTL">
                                        <#assign gpclaimList = claimLogs[key]>
                                        <#list gpclaimList as claim>
                                            <#if displayedCount < 3>
                                                <tr>
                                                    <td>${claim.startTime}</td>
                                                    <td>${claim.endTime}</td>
                                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                                    <#assign gpclaimList = claimLogs["GPA"]![]>
                                                    <#assign spshowflg = false>
                                                    <#list gpclaimList as spclaim>
                                                        <#if spclaim.startTime == claim.startTime && spclaim.endTime == claim.endTime>
                                                            <#assign spshowflg = true>
                                                            <td>${(spclaim.paidClaimsNum??)?then(spclaim.paidClaimsNum, "")}</td>
                                                            <td>${(spclaim.paidAmount?? && spclaim.paidAmount.amount??)?then(spclaim.paidAmount.amount, "")}</td>
                                                        </#if>
                                                    </#list>
                                                    <#if !spshowflg>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                    </#if>
                                                    <#assign gdclaimList = claimLogs["GCI"]![]>
                                                    <#assign gdshowflg = false>
                                                    <#list gdclaimList as gdclaim>
                                                        <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                            <#assign gdshowflg = true>
                                                            <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                                            <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                                        </#if>
                                                    </#list>
                                                    <#if !gdshowflg>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                    </#if>
                                                </tr>
                                                <#assign displayedCount = displayedCount + 1>
                                            <#else>
                                                <tr>
                                                    <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                                </tr>
                                                <#break>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>


                                <#list claimLogs?keys as key>
                                    <#if key == "GPA" && displayedCount < 3>
                                        <#assign gpclaimList = claimLogs[key]>
                                        <#list gpclaimList as claim>
                                            <#if displayedCount < 3>
                                                <tr>
                                                    <td>${claim.startTime}</td>
                                                    <td>${claim.endTime}</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                                    <#assign gdclaimList = claimLogs["GCI"]![]>
                                                    <#assign gdshowflg = false>
                                                    <#list gdclaimList as gdclaim>
                                                        <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                            <#assign gdshowflg = true>
                                                            <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                                            <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                                        </#if>
                                                    </#list>
                                                    <#if !gdshowflg>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                    </#if>
                                                </tr>
                                                <#assign displayedCount = displayedCount + 1>
                                            <#else>
                                                <tr>
                                                    <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                                </tr>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>
                                <#list claimLogs?keys as key>
                                    <#if key == "GCI" && displayedCount < 3>
                                        <#assign gpclaimList = claimLogs[key]>
                                        <#list gpclaimList as claim>
                                            <#if displayedCount < 3>
                                                <tr>
                                                    <td>${claim.startTime}</td>
                                                    <td>${claim.endTime}</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                                </tr>
                                                <#assign displayedCount = displayedCount + 1>
                                            <#else>
                                                <tr>
                                                    <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                                </tr>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>

                                <#if displayedCount < 3 && !isUploadClaimAttach>
                                    <#list displayedCount..2 as i>
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>
                            <tr>
                                <td class="italic text-left" colspan="8">
                                    Note: The insurer reserves the right to request for more information.
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <!--    footer-->
            <div class="footer">
                <div style="margin: 0 auto;">
                    <div><strong>Singapore Life Ltd 4</strong> Shenton Way #01-01 SGX Centre 2 Singapore 068807 singlife.com
                    </div>
                    <div>Company Reg.No. 196900499K GST Reg.No. MR-8500166-8 | Navigator Investment Services Ltd Company Reg.No.
                        200103470W GST Reg.No. 20-0103470-W
                    </div>
                </div>
                <div class="clear public">
                    <span class="fl">Public</span>
                    <strong class="fr">Page 7 of 15</strong>
                </div>
                <!--        接口不支持文字竖排，只能用图片替换-->
                <div class="date"></div>
            </div>
        </div>

        <!--第八页-->
        <div class="page">
            <table class="common-table">
                <tr>
                    <th></th>
                    <td>
                        <table class="border text-center cell-height-small">
                            <caption class="font-bold text-left underline">Outstanding Claims</caption>
                            <colgroup>
                                <col span="2" style="width: 10em;">
                                <col span="6">
                            </colgroup>
                            <tr>
                                <th rowspan="2">Period of Coverage From / To
                                    <span class="border-bottom inline-block" style="width: 6em;"></span>
                                    (dd/mm/yyyy)
                                </th>
                                <th rowspan="2"># of Insured as at
                                    <span class="border-bottom inline-block" style="width: 6em;"></span>
                                    (dd/mm/yyyy)
                                </th>
                                <th colspan="2">GTL</th>
                                <th colspan="2">GPA</th>
                                <th colspan="2">GCI</th>
                            </tr>
                            <tr>
                                <th># of Claims</th>
                                <th>Amount (S$)</th>
                                <th># of Claims</th>
                                <th>Amount (S$)</th>
                                <th># of Claims</th>
                                <th>Amount (S$)</th>
                            </tr>
                                <#--判断是否上传文件-->
                                <#if isUploadClaimAttach?? && isUploadClaimAttach>
                                    <tr>
                                        <td  class="text-left italic">Please refer to the attachment</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                    </tr>
                                    <#list 1..2 as i>
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>
                                <#assign displayedCount = 0>
                                <#list claimLogs?keys as key>
                                    <#if key == "GTL">
                                        <#assign gpclaimList = claimLogs[key]>
                                        <#list gpclaimList as claim>
                                            <#if displayedCount < 3>
                                                <tr>
                                                    <td>${claim.startTime}</td>
                                                    <td>${claim.endTime}</td>
                                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                                    <#assign gpclaimList = claimLogs["GPA"]![]>
                                                    <#assign spshowflg = false>
                                                    <#list gpclaimList as spclaim>
                                                        <#if spclaim.startTime == claim.startTime && spclaim.endTime == claim.endTime>
                                                            <#assign spshowflg = true>
                                                            <td>${(spclaim.outstandingClaimsNum??)?then(spclaim.outstandingClaimsNum, "")}</td>
                                                            <td>${(spclaim.outstandingAmount?? && spclaim.outstandingAmount.amount??)?then(spclaim.outstandingAmount.amount, "")}</td>
                                                        </#if>
                                                    </#list>
                                                    <#if !spshowflg>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                    </#if>
                                                    <#assign gdclaimList = claimLogs["GCI"]![]>
                                                    <#assign gdshowflg = false>
                                                    <#list gdclaimList as gdclaim>
                                                        <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                            <#assign gdshowflg = true>
                                                            <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                                            <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                                        </#if>
                                                    </#list>
                                                    <#if !gdshowflg>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                    </#if>
                                                </tr>
                                                <#assign displayedCount = displayedCount + 1>
                                            <#else>
                                                <tr>
                                                    <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                                </tr>
                                                <#break>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>
                                <#list claimLogs?keys as key>
                                    <#if key == "GPA" && displayedCount < 3 >
                                        <#assign gpclaimList = claimLogs[key]>
                                        <#list gpclaimList as claim>
                                            <#if displayedCount < 3>
                                                <tr>
                                                    <td>${claim.startTime}</td>
                                                    <td>${claim.endTime}</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                                    <#assign gdclaimList = claimLogs["GCI"]![]>
                                                    <#assign gdshowflg = false>
                                                    <#list gdclaimList as gdclaim>
                                                        <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                            <#assign gdshowflg = true>
                                                            <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                                            <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                                        </#if>
                                                    </#list>
                                                    <#if !gdshowflg>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                    </#if>
                                                </tr>
                                                <#assign displayedCount = displayedCount + 1>
                                            <#else>
                                                <tr>
                                                    <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                                </tr>
                                                <#break>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>
                                <#list claimLogs?keys as key>
                                    <#if key == "GCI" && displayedCount < 3 >
                                        <#assign gpclaimList = claimLogs[key]>
                                        <#list gpclaimList as claim>
                                            <#if displayedCount < 3>
                                                <tr>
                                                    <td>${claim.startTime}</td>
                                                    <td>${claim.endTime}</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                                </tr>
                                                <#assign displayedCount = displayedCount + 1>
                                            <#else>
                                                <tr>
                                                    <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                                </tr>
                                                <#break>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>
                                <#-- 不存在数据时显示空行-->
                                <#if displayedCount < 3 && !isUploadClaimAttach>
                                    <#list displayedCount..2 as i>
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>
                            <tr>
                                <td class="italic text-left h-2em" colspan="8">
                                    Note: The insurer reserves the right to request for more information.
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <!--        8. BENEFIT: GROUP DISABILITY INCOME INSURANCE-->
                <tr>
                    <th>8.</th>
                    <th class="underline">BENEFIT: GROUP DISABILITY INCOME INSURANCE</th>
                </tr>
                <!--        a)-->
                <tr>
                    <th>a)</th>
                    <td style="padding-left: 2em;">If currently insured, please attach a copy of the definition of Disability.
                    </td>
                </tr>
                <!--        b)-->
                <tr>
                    <th>b)</th>
                    <td style="padding-left: 2em;">
                        What is the waiting period required? Please indicate as appropriate: 3 or 6 months or
                        <span class="border-bottom inline-block" style="width: 10em;">&nbsp;</span>
                    </td>
                </tr>
                <!--        c)-->
                <tr>
                    <th>c)</th>
                    <td style="padding-left: 2em;">
                        What is the benefit duration required?
                        <span class="border-bottom inline-block" style="width: 28em;"></span> <br>
                        (i.e. 2 years, or 5 years, or up to retirement age 60 or 62, or 65)
                    </td>
                </tr>
                <!--        d)-->
                <tr>
                    <th>d)</th>
                    <td style="padding-left: 2em;">
                        What is the escalation benefit required? Please indicate as appropriate: 0% or 3% or 5% or
                        <span class="inline-block border-bottom" style="width: 8em;"></span>
                    </td>
                </tr>
                <!--        e)-->
                <tr>
                    <th>e)</th>
                    <td style="padding-left: 2em;">Please provide Current Non-Medical Limit (if applicable): S$
                        <span class="inline-block border-bottom" style="width: 7em;"></span>
                        up to age
                        <span class="inline-block border-bottom" style="width: 8em;"></span>
                    </td>
                </tr>
                <!--        f)-->
                <tr>
                    <th>f)</th>
                    <td style="padding-left: 2em;">
                        Any requirement for partial disability benefits? &emsp;
                        <strong>Yes <input type="checkbox"> / No <input type="checkbox"></strong>
                    </td>
                </tr>
                <!--        g)-->
                <tr>
                    <th>g)</th>
                    <td style="padding-left: 2em;">Basis of Coverage</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border text-center padding-small">
                            <colgroup>
                                <col style="width: 3em;">
                                <col>
                                <col span="2" style="width: 6em;">
                                <col style="width: 14em;">
                            </colgroup>
                            <tr>
                                <th colspan="2" rowspan="2">Category of Employees / Occupation</th>
                                <th colspan="2">Monthly Salary (S$)</th>
                                <th rowspan="2">Basis of Coverage i.e. % (e.g. 50%) of monthly salary</th>
                            </tr>
                            <tr>
                                <th>Highest*</th>
                                <th>Average*</th>
                            </tr>
                            <tr>
                                <td>(i)</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>(ii)</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>(iii)</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td>(iv)</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="italic text-left" colspan="5">
                                    * Applicable to the category of employees as stated. Monthly salary will be basic pay +
                                    fixed bonus if any. It excludes
                                    variable bonus, commissions, etc.
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <!--    footer-->
            <div class="footer">
                <div style="margin: 0 auto;">
                    <div><strong>Singapore Life Ltd 4</strong> Shenton Way #01-01 SGX Centre 2 Singapore 068807 singlife.com
                    </div>
                    <div>Company Reg.No. 196900499K GST Reg.No. MR-8500166-8 | Navigator Investment Services Ltd Company Reg.No.
                        200103470W GST Reg.No. 20-0103470-W
                    </div>
                </div>
                <div class="clear public">
                    <span class="fl">Public</span>
                    <strong class="fr">Page 8 of 15</strong>
                </div>
                <!--        接口不支持文字竖排，只能用图片替换-->
                <div class="date"></div>
            </div>
        </div>

        <!--第九页-->
        <div class="page">
            <table class="common-table">
                <!--        h)-->
                <tr>
                    <th>h)</th>
                    <td style="padding-left: 2em;">Details of Employees</td>
                </tr>
                <tr>
                    <th colspan="2">
                        <table class="border text-center">
                            <colgroup>
                                <col span="5" style="width: 20%;">
                            </colgroup>
                            <tr>
                                <th rowspan="2">Age Band (Age <br> Next Birthday)</th>
                                <th colspan="2"># of Employees</th>
                                <th colspan="2">Sum Insured (S$)</th>
                            </tr>
                            <tr>
                                <th>Male</th>
                                <th>Female</th>
                                <th>Male</th>
                                <th>Female</th>
                            </tr>
                                <#assign  hasData9=false>
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag=="ghs" && personOverview.itemKey=="age_profile_of_employees">
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if info.age_band??>
                                                <#assign  hasData9=true>
                                                <#if info.age_band!="66-70" >
                                                    <tr>
                                                        <td class="font-bold text-center">${info.age_band!}</td>
                                                        <td class="font-bold text-center">${info.no_of_employees_male!}</td>
                                                        <td class="text-center">${info.no_of_employees_female!}</td>
                                                        <td class="text-center">${info.total_sum_insured_male!}</td>
                                                        <td class="text-center" >${info.total_sum_insured_female!}</td>
                                                    </tr>
                                                </#if>

                                            </#if>

                                        </#list>
                                    </#if>
                                </#list>
                                <#if !hasData9>
                                    <tr>
                                        <th>16-30</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>31-35</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>36-40</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>41-45</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>46-50</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>51-55</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>56-60</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>61-65</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>66-70</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>Total</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                </#if>
                        </table>
                    </th>
                </tr>
                <tr>
                    <th></th>
                    <td style="height: 2em;"></td>
                </tr>
                <!--        i)-->
                <tr>
                    <th>i)</th>
                    <td style="padding-left: 2em;">Claims Experience for the past 3 years</td>
                </tr>
                <tr>
                    <td colspan="2">
                        <table class="border text-center cell-height-small padding">
                            <colgroup>
                                <col span="4" style="width: 25%;">
                            </colgroup>
                            <tr>
                                <th rowspan="2">
                                    Date of Disability <br>
                                    <span class="border-bottom inline-block" style="width: 6em;"></span> <br>
                                    (dd/mm/yyyy)
                                </th>
                                <th rowspan="2">Cause of Disability / Nature of Illness</th>
                                <th colspan="2">Claims Amount (S$)</th>
                            </tr>
                            <tr>
                                <th>Paid</th>
                                <th>Outstanding</th>
                            </tr>
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="italic text-left h-2em" colspan="4">
                                    Note: The Insurer reserves the right to request for more information.
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <!--    footer-->
            <div class="footer">
                <div style="margin: 0 auto;">
                    <div><strong>Singapore Life Ltd 4</strong> Shenton Way #01-01 SGX Centre 2 Singapore 068807 singlife.com
                    </div>
                    <div>Company Reg.No. 196900499K GST Reg.No. MR-8500166-8 | Navigator Investment Services Ltd Company Reg.No.
                        200103470W GST Reg.No. 20-0103470-W
                    </div>
                </div>
                <div class="clear public">
                    <span class="fl">Public</span>
                    <strong class="fr">Page 9 of 15</strong>
                </div>
                <!--        接口不支持文字竖排，只能用图片替换-->
                <div class="date"></div>
            </div>
        </div>

        <!--第十页-->
        <div class="page">
            <table class="common-table">
                <!--        9. BENEFIT: GROUP HOSPITAL & SURGICAL INSURANCE / MAJOR MEDICAL INSURANCE-->
                <tr>
                    <th>9.</th>
                    <th class="underline">BENEFIT: GROUP HOSPITAL & SURGICAL INSURANCE / MAJOR MEDICAL INSURANCE
                    </th>
                </tr>
                <tr>
                    <td colspan="2">Please check on the relevant boxes:</td>
                </tr>
                <tr>
                    <td colspan="2">
                        <table class="padding">
                            <tr>
                                <td style="width: 2em;">i)</td>
                                <td>I have provided a copy of the benefits schedule with the class of ward to the insurers.</td>
                                <td style="width: 6em;"><input checked type="checkbox"></td>
                            </tr>
                            <tr>
                                <td>ii)</td>
                                <td>I am currently not covered under any corporate insurance and I will indicate the required class of ward in table below:</td>
                                <td><input type="checkbox"></td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <!--        a)-->
                <tr>
                    <th>a)</th>
                    <td>Basis of Coverage</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border text-center padding-small">
                            <colgroup>
                                <col style="width: 3em;">
                                <col>
                                <col span="4" style="width: 10em;">
                            </colgroup>
                            <tr>
                                <th colspan="2">Category of Employees / Occupation</th>
                                <th>Room & Board Benefit Plan (S$)</th>
                                <th>Class Ward</th>
                                <th>Currently with TMIS Yes / No</th>
                                <th>Proposal with TMIS Yes / No</th>
                            </tr>
                            <#assign hasData = false>
                            <#list personOverviews as personOverview>
                                <#if personOverview.tag=="ghs" && personOverview.itemKey=="basis_of_coverage">
                                    <#assign hasData = true>
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <#if info_index < 3>
                                            <tr>
                                                <#if info_index == 0>
                                                    <td>(i)</td>
                                                <#elseif info_index == 1>
                                                    <td>(ii)</td>
                                                <#elseif info_index == 2>
                                                    <td>(iii)</td>
                                                </#if>
                                                <td>${info.category_of_employees_occupation!""}</td>
                                                <td>${info.room_and_board_benefit_plan!""}</td>
                                                <td></td>
                                                <td>${(info.currently_with_tmis?? && info.currently_with_tmis=="1")?then("Yes", "No")}</td>
                                                <td>${(info.proposal_with_tmis?? && info.proposal_with_tmis=="1")?then("Yes", "No")}</td>
<#--                                                <td>${(info.medical_insurance_for_pass_and_work?? && info.medical_insurance_for_pass_and_work=="1")?then("Yes", "No")}</td>-->
                                            </tr>
                                        <#else>
                                            <tr>
                                                <td colspan="6" class="text-left italic"> More information, please refer to the attachment.</td>
                                            </tr>
                                            <#break>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 3>
                                        <#list valueMapList?size..2 as i>
                                            <tr>
                                                <#if i == 0>
                                                    <td>(i)</td>
                                                <#elseif i == 1>
                                                    <td>(ii)</td>
                                                <#elseif i == 2>
                                                    <td>(iii)</td>
                                                </#if>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </tr>
                                        </#list>
                                    </#if>
                                </#if>
                            </#list>
                            <#if !hasData>
                                <#list 1..3 as i>
                                    <tr>
                                        <#if i == 1>
                                            <td>(i)</td>
                                        <#elseif i == 2>
                                            <td>(ii)</td>
                                        <#elseif i == 3>
                                            <td>(iii)</td>
                                        </#if>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </tr>
                                </#list>
                            </#if>
                            <#if !hasData>
                                <tr>
                                    <td>(i)</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>(ii)</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>(iii)</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#if>
                        </table>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <th style="padding-bottom: 0;">Important Note:</th>
                </tr>
                <tr>
                    <th></th>
                    <th>
                    <table class="no-padding">
                        <tr>
                            <td class="align-top" style="width: 2em;">(1)</td>
                            <td>
                                Dependants can be covered under Group Hospital & Surgical Plan. Their cover should be the
                                same as the employee's cover.
                            </td>
                        </tr>
                    </table>
                </tr>
                <tr>
                    <th></th>
                    <th>
                    <table class="no-padding">
                        <tr>
                            <td class="align-top" style="width: 2em;">(2)</td>
                            <td>
                                Please provide the Deductible /Co-insurance for respective employee category or
                                occupation, if applicable.
                            </td>
                        </tr>
                    </table>
                </tr>
                <tr>
                    <td></td>
                    <td>
                        <table class="no-padding text-left">
                            <tr>
                                <th class="underline" style="padding-bottom: 0;">Example 1</th>
                                <td style="width: 12.5em;"></td>
                            </tr>
                            <tr>
                                <th>Category of Employees / Occupation</th>
                                <th>R&B Benefit Plan (S$)</th>
                            </tr>
                            <tr>
                                <td>(i) &nbsp; Senior Management (Director, General Manager, Senior Manager)</td>
                                <td>360</td>
                            </tr>
                            <tr>
                                <td>(ii) &nbsp; Manager & Executive</td>
                                <td>200</td>
                            </tr>
                            <tr>
                                <td>(iii)&nbsp; All Others</td>
                                <td>100</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <!--        b)-->
                <tr>
                    <th>b)</th>
                    <td>Age Profile of Employees</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border text-center padding-small">
                            <colgroup>
                                <col style="width: 40%;">
                                <col span="2" style="width: 30%;">
                            </colgroup>
                            <tr>
                                <th rowspan="2">Age Band (Age Next Birthday)</th>
                                <th colspan="2"># of Employees</th>
                            </tr>
                            <tr>
                                <th>Male</th>
                                <th>Female</th>
                            </tr>
                                <#assign  hasData9=false>
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag=="ghs" && personOverview.itemKey=="age_profile_of_employees">
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if info.age_band??>
                                                <#assign  hasData9=true>
                                                <tr>
                                                    <td class="font-bold text-center">${info.age_band!}</td>
                                                    <td class="text-center">${info.no_of_employees_male!}</td>
                                                    <td class="text-center">${info.no_of_employees_female!}</td>
                                                </tr>
                                            </#if>

                                        </#list>
                                    </#if>
                                </#list>
                                <#if !hasData9>
                                    <tr>
                                        <th>16-30</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>31-35</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>36-40</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>41-45</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>46-50</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>51-55</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>56-60</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>61-65</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>66-70</th>
                                        <td></td>
                                        <td></td>
                                    </tr>

                                    <tr>
                                        <th>Total</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                </#if>
                        </table>
                    </td>
                </tr>
            </table>
            <!--    footer-->
            <div class="footer">
                <div style="margin: 0 auto;">
                    <div><strong>Singapore Life Ltd 4</strong> Shenton Way #01-01 SGX Centre 2 Singapore 068807 singlife.com
                    </div>
                    <div>Company Reg.No. 196900499K GST Reg.No. MR-8500166-8 | Navigator Investment Services Ltd Company Reg.No.
                        200103470W GST Reg.No. 20-0103470-W
                    </div>
                </div>
                <div class="clear public">
                    <span class="fl">Public</span>
                    <strong class="fr">Page 10 of 15</strong>
                </div>
                <!--        接口不支持文字竖排，只能用图片替换-->
                <div class="date"></div>
            </div>
        </div>

        <!--第十一页-->
        <div class="page">
            <table class="common-table">
                <!--        c)-->
                <tr>
                    <th>c)</th>
                    <td>Details of Insured Members</td>
                </tr>
                <tr>
                    <th></th>
                    <th class="underline">For GHS and GMM:</th>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border padding-small border-none text-center mb-2">
                            <tr>
                                <th class="border-none" style="width: 14em;"></th>
                                <th colspan="4"># of Employees (Singaporeans & SPRs*)</th>
                            </tr>
                            <tr>
                                <#assign hasData = false />
                                <#assign noOfEmployeesPlanList = []>
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_sgp">
                                        <#if personOverview.valueMaps?has_content>
                                            <#assign valueMapList = personOverview.value?eval>
                                            <#list valueMapList as info>
                                                <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                                    <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                                    <#assign hasData = true />
                                                </#if>
                                            </#list>
                                        </#if>
                                    </#if>
                                </#list>
                                <#if hasData>
                                    <tr>
                                        <td></td>
                                        <#list noOfEmployeesPlanList as info>
                                            <#if info_index < 4>
                                                <th>${info} </th>
                                            </#if>
                                        </#list>
                                        <#if noOfEmployeesPlanList?size < 4>
                                            <#list 1..(4 - noOfEmployeesPlanList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                <#else>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                    </tr>
                                </#if>
                            </tr>
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_sgp">
                                        <#-- 检查valueMaps是否有数据（避免空表格） -->
                                        <#if personOverview.valueMaps?has_content>
                                            <tr>
                                                <td class="text-left font-bold">Employee Only</td>
                                                <#assign valueMapList = personOverview.value?eval>
                                                <#list valueMapList as info>
                                                    <#if info_index < 4>
                                                        <td>${(info.employee_only)!""}</td>
                                                    </#if>
                                                </#list>
                                                <#if valueMapList?size < 4>
                                                    <#list 1..(4 - valueMapList?size) as i>
                                                        <td></td>
                                                    </#list>
                                                </#if>
                                            </tr>
                                            <tr>
                                                <td class="font-bold text-left">Employee & Spouse</td>
                                                <#assign valueMapList = personOverview.value?eval>
                                                <#list valueMapList as info>
                                                    <#if info_index < 4>
                                                        <td>${(info.employee_and_spouse)!""}</td>
                                                    </#if>
                                                </#list>
                                                <#if valueMapList?size < 4>
                                                    <#list 1..(4 - valueMapList?size) as i>
                                                        <td></td>
                                                    </#list>
                                                </#if>
                                            </tr>
                                            <tr>
                                                <td class="text-left font-bold">Employee & Child(ren)</td>
                                                <#assign valueMapList = personOverview.value?eval>
                                                <#list valueMapList as info>
                                                    <#if info_index < 4>
                                                        <td>${(info.employee_and_children)!""}</td>
                                                    </#if>
                                                </#list>
                                                <#if valueMapList?size < 4>
                                                    <#list 1..(4 - valueMapList?size) as i>
                                                        <td></td>
                                                    </#list>
                                                </#if>
                                            </tr>
                                            <tr>
                                                <td class="font-bold text-left">Employee & Family</td>
                                                <#assign valueMapList = personOverview.value?eval>
                                                <#list valueMapList as info>
                                                    <#if info_index < 4>
                                                        <td>${(info.employee_and_family)!""}</td>
                                                    </#if>
                                                </#list>
                                                <#if valueMapList?size < 4>
                                                    <#list 1..(4 - valueMapList?size) as i>
                                                        <td></td>
                                                    </#list>
                                                </#if>
                                            </tr>
                                        </#if>
                                    </#if>
                                </#list>
                                <#if !hasData>
                                    <tr>
                                        <td class="text-left font-bold" >Employee Only</td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td class="text-left font-bold" >Employee & Spouse</td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td class="text-left font-bold">Employee & Child(ren)</td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <td class="text-left font-bold">Employee & Family</td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                </#if>
                            <tr>
                                <td class="italic text-left" colspan="5">
                                    * refers to Singapore Permanent Residents
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border padding-small border-none text-center">
                            <tr>
                                <th class="border-none" style="width: 14em;"></th>
                                <th colspan="4"># of Employees (Foreigners* only)</th>
                            </tr>
                                <#assign hasData = false />
                                <#assign noOfEmployeesPlanList = []>
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_foreigners">
                                        <#if personOverview.valueMaps?has_content>
                                            <#assign valueMapList = personOverview.value?eval>
                                            <#list valueMapList as info>
                                                <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                                    <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                                    <#assign hasData = true />
                                                </#if>
                                            </#list>
                                        </#if>
                                    </#if>
                                </#list>
                                <#if hasData>
                                    <tr>
                                        <td></td>
                                        <#list noOfEmployeesPlanList as info>
                                            <#if info_index < 4>
                                                <th>${info} </th>
                                            </#if>
                                        </#list>
                                        <#if noOfEmployeesPlanList?size < 4>
                                            <#list 1..(4 - noOfEmployeesPlanList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                <#else>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                    </tr>
                                </#if>
                        </tr>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_foreigners">
                                <#-- 检查valueMaps是否有数据（避免空表格） -->
                                <#assign valueMapList = personOverview.value?eval>
                                <#if personOverview.valueMaps?has_content>
                                    <tr>
                                        <td class="text-left font-bold">Employee Only</td>

                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_only)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                    <tr>
                                        <td class="font-bold text-left">Employee & Spouse</td>
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_and_spouse)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                    <tr>
                                        <td class="text-left font-bold">Employee & Child(ren)</td>
                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_and_children)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                    <tr>
                                        <td class="font-bold text-left">Employee & Family</td>
                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_and_family)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                </#if>
                            </#if>
                        </#list>
                        <#if !hasData>
                            <tr>
                                <td class="text-left font-bold" >Employee Only</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="text-left font-bold" >Employee & Spouse</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="text-left font-bold" >Employee & Child(ren)</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="text-left font-bold" >Employee & Family</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </#if>
                            <tr>
                                <td class="italic text-left" colspan="5">
                                    * refers to all foreigners holding Employment Pass, S Pass and Work Permit, working in
                                    Singapore
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <!--        For GMM (if the basis of coverage differs from GHS)-->
                <tr>
                    <th></th>
                    <th class="underline" style="padding-top: 2em;">For GMM (if the basis of coverage differs from GHS):</th>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border border-none text-center padding-small mb-2">
                            <tr>
                                <th class="border-none" style="width: 14em;"></th>
                                <th colspan="4"># of Employees (Singaporeans & SPRs*)</th>
                            </tr>
                                <#assign hasData = false />
                                <#assign noOfEmployeesPlanList = []>
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_sgp">
                                        <#if personOverview.valueMaps?has_content>
                                            <#assign valueMapList = personOverview.value?eval>
                                            <#list valueMapList as info>
                                                <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                                    <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                                    <#assign hasData = true />
                                                </#if>
                                            </#list>
                                        </#if>
                                    </#if>
                                </#list>
                                <#if hasData>
                                    <tr>
                                        <td></td>
                                        <#list noOfEmployeesPlanList as info>
                                            <#if info_index < 4>
                                                <th>${info} </th>
                                            </#if>
                                        </#list>
                                        <#if noOfEmployeesPlanList?size < 4>
                                            <#list 1..(4 - noOfEmployeesPlanList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                <#else>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                    </tr>
                                </#if>
                        </tr>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_sgp">
                                <#-- 检查valueMaps是否有数据（避免空表格） -->
                                <#if personOverview.valueMaps?has_content>
                                    <tr>
                                        <td class="text-left font-bold">Employee Only</td>
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_only)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                    <tr>
                                        <td class="font-bold text-left">Employee & Spouse</td>
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_and_spouse)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                    <tr>
                                        <td class="text-left font-bold">Employee & Child(ren)</td>
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_and_children)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                    <tr>
                                        <td class="font-bold text-left">Employee & Family</td>
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_and_family)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                </#if>
                            </#if>
                        </#list>
                        <#if !hasData>
                            <tr>
                                <td class="text-left font-bold" >Employee Only</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="text-left font-bold" >Employee & Spouse</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="text-left font-bold">Employee & Child(ren)</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="text-left font-bold">Employee & Family</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </#if>
                            <tr>
                                <td class="italic text-left" colspan="5">
                                    * refers to Singapore Permanent Residents
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border border-none text-center padding-small">
                            <tr>
                                <th class="border-none" style="width: 14em;"></th>
                                <th colspan="4"># of Employees (Foreigners* only)</th>
                            </tr>
                                <#assign hasData = false />
                                <#assign noOfEmployeesPlanList = []>
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_foreigners">
                                        <#if personOverview.valueMaps?has_content>
                                            <#assign valueMapList = personOverview.value?eval>
                                            <#list valueMapList as info>
                                                <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                                    <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                                    <#assign hasData = true />
                                                </#if>
                                            </#list>
                                        </#if>
                                    </#if>
                                </#list>
                                <#if hasData>
                                    <tr>
                                        <td></td>
                                        <#list noOfEmployeesPlanList as info>
                                            <#if info_index < 4>
                                                <th>${info} </th>
                                            </#if>
                                        </#list>
                                        <#if noOfEmployeesPlanList?size < 4>
                                            <#list 1..(4 - noOfEmployeesPlanList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                <#else>
                                    <tr>
                                        <td>&nbsp;</td>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                        <th></th>
                                    </tr>
                                </#if>
                        </tr>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_foreigners">
                                <#-- 检查valueMaps是否有数据（避免空表格） -->
                                <#assign valueMapList = personOverview.value?eval>
                                <#if personOverview.valueMaps?has_content>
                                    <tr>
                                        <td class="text-left font-bold">Employee Only</td>

                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_only)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                    <tr>
                                        <td class="font-bold text-left">Employee & Spouse</td>
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_and_spouse)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                    <tr>
                                        <td class="text-left font-bold">Employee & Child(ren)</td>
                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_and_children)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                    <tr>
                                        <td class="font-bold text-left">Employee & Family</td>
                                        <#list valueMapList as info>
                                            <#if info_index < 4>
                                                <td>${(info.employee_and_family)!""}</td>
                                            </#if>
                                        </#list>
                                        <#if valueMapList?size < 4>
                                            <#list 1..(4 - valueMapList?size) as i>
                                                <td></td>
                                            </#list>
                                        </#if>
                                    </tr>
                                </#if>
                            </#if>
                        </#list>
                        <#if !hasData>
                            <tr>
                                <td class="text-left font-bold" >Employee Only</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="text-left font-bold" >Employee & Spouse</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="text-left font-bold" >Employee & Child(ren)</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="text-left font-bold" >Employee & Family</td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </#if>
                            <tr>
                                <td class="italic text-left" colspan="5">
                                    * refers to all foreigners holding Employment Pass, S Pass and Work Permit, working in
                                    Singapore
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <!--    footer-->
            <div class="footer">
                <div style="margin: 0 auto;">
                    <div><strong>Singapore Life Ltd 4</strong> Shenton Way #01-01 SGX Centre 2 Singapore 068807 singlife.com
                    </div>
                    <div>Company Reg.No. 196900499K GST Reg.No. MR-8500166-8 | Navigator Investment Services Ltd Company Reg.No.
                        200103470W GST Reg.No. 20-0103470-W
                    </div>
                </div>
                <div class="clear public">
                    <span class="fl">Public</span>
                    <strong class="fr">Page 11 of 15</strong>
                </div>
                <!--        接口不支持文字竖排，只能用图片替换-->
                <div class="date"></div>
            </div>
        </div>

        <!--第十二页-->
        <div class="page">
            <table class="common-table">
                <!--        d)-->
                <tr>
                    <th>d)</th>
                    <td>Claims Experience for the past 3 years</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border text-center cell-height-small padding-small">
                            <colgroup>
                                <col span="2" style="width: 10em;">
                                <col span="4">
                            </colgroup>
                            <tr>
                                <th rowspan="2">Period of Coverage From / To
                                    <span class="border-bottom inline-block" style="width: 6em;"></span>
                                    (dd/mm/yyyy)
                                </th>
                                <th rowspan="2"># of Insured as at
                                    <span class="border-bottom inline-block" style="width: 6em;"></span>
                                    (dd/mm/yyyy)
                                </th>
                                <th colspan="2">Paid Claims</th>
                                <th colspan="2">Outstanding Claims</th>
                            </tr>
                            <tr>
                                <th># of Claims</th>
                                <th>Amount (S$)</th>
                                <th># of Claims</th>
                                <th>Amount (S$)</th>
                            </tr>
                                <#if isUploadClaimAttach?? && isUploadClaimAttach>
                                    <tr>
                                        <td class="text-left">Please refer to the attachment</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                    </tr>
                                    <#list 1..2 as i>
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>

                                <#assign displayedCount = 0>
                                <#list claimLogs?keys as key>
                                    <#if key == "GHS">
                                        <#assign gpclaimList = claimLogs[key]>
                                        <#list gpclaimList as claim>
                                            <#if displayedCount < 3>
                                                <tr>
                                                    <td>${claim.startTime}</td>
                                                    <td>${claim.endTime}</td>
                                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                                </tr>
                                                <#assign displayedCount = displayedCount + 1>
                                            <#else>
                                                <tr>
                                                    <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                                                </tr>
                                                <#break>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>

                                <#if displayedCount < 3 && !isUploadClaimAttach >
                                    <#list displayedCount..2 as i>
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>
                            <tr>
                                <td class="italic text-left h-2em" colspan="6">
                                    Note: The insurer reserves the right to request for more information.
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <!--        e)-->
                <tr>
                    <th>e)</th>
                    <td>Kindly attach a copy of the Schedule of Benefits, if the benefits are on insured basis (i.e. currently
                        insured).
                    </td>
                </tr>
                <tr>
                    <td colspan="2" style="padding-top: 2em;"></td>
                </tr>
                <!--        10. BENEFIT: GROUP OUTPATIENT INSURANCE-->
                <tr>
                    <th>10.</th>
                    <th class="underline">BENEFIT: GROUP OUTPATIENT INSURANCE</th>
                </tr>
                <!--        a)-->
                <tr>
                    <th>a)</th>
                    <td>Category of Employees to be insured (please tick as appropriate)</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border padding-small">
                            <colgroup>
                                <col style="width: 3em;">
                                <col>
                                <col span="2" style="width: 7em">
                                <col style="width: 8em;">
                                <col style="width: 7em;">
                            </colgroup>
                            <tr>
                                <th colspan="2">Category of Employees</th>
                                <th class="text-center">Clinical GP</th>
                                <th class="text-center">Specialist</th>
                                <th class="text-center">Diag X-Ray/Lab Tests</th>
                                <th class="text-center">Dental</th>
                            </tr>
                                <#assign groupedItems = {}>
                                <#list userPlanDutyList as item>
                                    <#if !groupedItems[item.categoryEmployee]??>
                                        <#assign groupedItems = groupedItems + {item.categoryEmployee: []}>
                                    </#if>
                                    <#assign groupedItems = groupedItems + {item.categoryEmployee: groupedItems[item.categoryEmployee] + [item]}>
                                </#list>

                                <#assign index = 0>
                                <#list groupedItems?keys as category>
                                    <#assign itemsInCategory = groupedItems[category]>
                                    <#if index < 3>
                                        <tr>
                                            <#if index == 0>
                                                <td>(i)</td>
                                            <#elseif index == 1>
                                                <td>(ii)</td>
                                            <#elseif index == 2>
                                                <td>(iii)</td>
                                            <#elseif index == 3>
                                                <td>(iv)</td>
                                            </#if>
                                            <td class="text-left">${category}</td>
                                            <#assign hasGtl = false>
                                            <#assign hasSp = false>
                                            <#assign hasGd = false>
                                            <#list itemsInCategory as item>
                                                <#if item.benefit?? && item.benefit == "GTL">
                                                    <#assign hasGtl = true>
                                                </#if>
                                                <#if item.benefit?? && item.benefit == "SP">
                                                    <#assign hasSp = true>
                                                </#if>
                                                <#if item.benefit?? && item.benefit == "GD">
                                                    <#assign hasGd = true>
                                                </#if>
                                            </#list>
                                            <td><input type="checkbox" <#if hasGtl>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                                            <td><input type="checkbox" <#if hasSp>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                                            <td></td>
                                            <td><input type="checkbox" <#if hasGd>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                                        </tr>
                                    <#else>
                                        <tr>
                                            <td colspan="6" class="text-left italic"> More information, please refer to the attachment.</td>
                                        </tr>
                                        <#break>
                                    </#if>
                                    <#assign index = index + 1>
                                </#list>
                                <#-- 补充空行到3行 -->
                                <#if index < 3>
                                    <#list index..2 as i>
                                        <tr>
                                            <#if i == 0>
                                                <td>(i)</td>
                                            <#elseif i == 1>
                                                <td>(ii)</td>
                                            <#elseif i == 2>
                                                <td>(iii)</td>
                                            </#if>
                                            <td class="text-left">&nbsp;</td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                    </#list>
                                </#if>
                            <tr>
                                <th colspan="2">Dependant (where applicable)</th>
                                    <#assign gpDependantsIncluded = "No">
                                    <#assign spDependantsIncluded = "No">
                                    <#assign gdDependantsIncluded = "No">
                                    <#assign gpHeadcount = "">
                                    <#assign spHeadcount = "">
                                    <#assign gdHeadcount = "">
                                    <#list personOverviews as personOverview>
                                        <#if personOverview.itemKey == "dependants_included">
                                            <#if personOverview.tag == "gp">
                                                <#if (personOverview.value!"") == "1">
                                                    <#assign gpDependantsIncluded = "Yes">
                                                </#if>
                                            <#elseif personOverview.tag == "sp">
                                                <#if (personOverview.value!"") == "1">
                                                    <#assign spDependantsIncluded = "Yes">

                                                </#if>
                                            <#elseif personOverview.tag == "gd">
                                                <#if (personOverview.value!"") == "1">
                                                    <#assign gdDependantsIncluded = "Yes">
                                                </#if>
                                            </#if>
                                        </#if>

                                        <#if personOverview.itemKey == "no_of_headcount">
                                            <#if personOverview.tag == "gp">
                                                <#if personOverview.value??>
                                                    <#assign gpHeadcount = personOverview.value!>
                                                </#if>
                                            <#elseif personOverview.tag == "sp">
                                                <#if personOverview.value??>
                                                    <#assign spHeadcount = personOverview.value!>
                                                </#if>
                                            <#elseif personOverview.tag == "gd">
                                                <#if personOverview.value?? >
                                                    <#assign gdHeadcount = personOverview.value!>
                                                </#if>
                                            </#if>
                                        </#if>
                                    </#list>


                                    <td>${gpDependantsIncluded}</td>
                                    <td>${spDependantsIncluded}</td>
                                    <td></td>
                                    <td>${gdDependantsIncluded}</td>
                            </tr>
                            <tr>
                                <th colspan="2"># of Headcount</th>
                                <td>${gpHeadcount}</td>
                                <td>${spHeadcount}</td>
                                <td></td>
                                <td>${gdHeadcount}</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <!--        b)-->
                <tr>
                    <th>b)</th>
                    <td>Age Profile of Employees</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border text-center padding-small">
                            <colgroup>
                                <col style="width: 40%;">
                                <col span="2" style="width: 30%;">
                            </colgroup>
                            <tr>
                                <th rowspan="2">Age Band (Age Next Birthday)</th>
                                <th colspan="2"># of Employees</th>
                            </tr>
                            <tr>
                                <th>Male</th>
                                <th>Female</th>
                            </tr>
                                <#assign totalMale = 0>
                                <#assign totalFemale = 0>
                                <#assign hasData12 = false>
                                <#list personOverviews as personOverview>
                                    <#if personOverview.itemKey=="age_profile_of_employees">
                                        <#if personOverview.tag=="group_outpatient_insurance">
                                            <#if personOverview.valueMaps?has_content>
                                                <#assign hasData12 = true>
                                                <#assign valueMapList = personOverview.value?eval>
                                                <#list valueMapList as info>
                                                    <tr>
                                                        <th>${info.age_band!}</th>
                                                        <td>${(info.no_of_employees_male?has_content && info.no_of_employees_male?number != 0)?then(info.no_of_employees_male, "")}</td>
                                                        <td>${(info.no_of_employees_female?has_content && info.no_of_employees_female?number != 0)?then(info.no_of_employees_female, "")}</td>
                                                    </tr>
                                                </#list>

                                            </#if>
                                        </#if>
                                    </#if>
                                </#list>
                                <#if !hasData12>
                                    <tr>
                                        <th>16-30</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>31-35</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>36-40</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>41-45</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>46-50</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>51-55</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>56-60</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>61-65</th>
                                    <td></td>
                                    <td></td>
                                    </tr>
                                    <tr>
                                        <th>66-70</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    <tr>
                                        <th>Total</th>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                </#if>
                        </table>
                    </td>
                </tr>
            </table>
            <!--    footer-->
            <div class="footer">
                <div style="margin: 0 auto;">
                    <div><strong>Singapore Life Ltd 4</strong> Shenton Way #01-01 SGX Centre 2 Singapore 068807 singlife.com
                    </div>
                    <div>Company Reg.No. 196900499K GST Reg.No. MR-8500166-8 | Navigator Investment Services Ltd Company Reg.No.
                        200103470W GST Reg.No. 20-0103470-W
                    </div>
                </div>
                <div class="clear public">
                    <span class="fl">Public</span>
                    <strong class="fr">Page 12 of 15</strong>
                </div>
                <!--        接口不支持文字竖排，只能用图片替换-->
                <div class="date"></div>
            </div>
        </div>

        <!--第十三页-->
        <div class="page">
            <table class="common-table">
                <!--        c)-->
                <tr>
                    <th>c)</th>
                    <td>Claims Experience for the past 3 years</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border border-none layout-auto cell-height-small text-center padding-small">
                            <caption class="font-bold text-left underline">Paid Claims</caption>
                            <colgroup>
                                <col span="2" style="width: 16%;">
                                <col span="8" style="width: 8.4%;">
                            </colgroup>
                            <tr>
                                <th class="border-none" colspan="2"></th>
                                <th colspan="2">Clinical*</th>
                                <th colspan="2">Specialist *</th>
                                <th colspan="2">Diagnostic X-Ray / Lab Tests*</th>
                                <th colspan="2">Dental*</th>
                            </tr>
                            <tr>
                                <th>Period of Coverage From / To
                                    <span class="border-bottom inline-block" style="width: 6em;"></span> <br>
                                    (dd/mm/yyyy)
                                </th>
                                <th># of Insured as at
                                    <span class="border-bottom inline-block" style="width: 6em;"></span> <br>
                                    (dd/mm/yyyy)
                                </th>
                                <th># of Visits</th>
                                <th>Amt (S$)</th>
                                <th># of Visits</th>
                                <th>Amt (S$)</th>
                                <th># of Visits</th>
                                <th>Amt (S$)</th>
                                <th># of Visits</th>
                                <th>Amt (S$)</th>
                            </tr>
                                <#if isUploadClaimAttach?? && isUploadClaimAttach>
                                    <tr>
                                        <td > Please refer to the attachment</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                    </tr>
                                    <#list 1..2 as i>
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>

                                <#assign displayedCount = 0>
                                <#list claimLogs?keys as key>
                                    <#if key == "GP">
                                        <#assign gpclaimList = claimLogs[key]>
                                        <#list gpclaimList as claim>
                                            <#if displayedCount < 3>
                                                <tr>
                                                    <td>${claim.startTime}</td>
                                                    <td>${claim.endTime}</td>
                                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                                    <#assign gpclaimList = claimLogs["SP"]![]>
                                                    <#assign spshowflg = false>
                                                    <#list gpclaimList as spclaim>
                                                        <#if spclaim.startTime == claim.startTime && spclaim.endTime == claim.endTime>
                                                            <#assign spshowflg = true>
                                                            <td>${(spclaim.paidClaimsNum??)?then(spclaim.paidClaimsNum, "")}</td>
                                                            <td>${(spclaim.paidAmount?? && spclaim.paidAmount.amount??)?then(spclaim.paidAmount.amount, "")}</td>
                                                        </#if>
                                                    </#list>
                                                    <#if !spshowflg>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                    </#if>
                                                    <#assign gdclaimList = claimLogs["GD"]![]>
                                                    <#assign gdshowflg = false>
                                                    <#list gdclaimList as gdclaim>
                                                        <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                            <#assign gdshowflg = true>
                                                            <td></td>
                                                            <td></td>
                                                            <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                                            <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                                        </#if>
                                                    </#list>
                                                    <#if !gdshowflg>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                    </#if>
                                                </tr>
                                                <#assign displayedCount = displayedCount + 1>
                                            <#else>
                                                <tr>
                                                    <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                                </tr>
                                                <#break>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>

                                <#list claimLogs?keys as key>
                                    <#if key == "SP" && displayedCount < 3>
                                        <#assign gpclaimList = claimLogs[key]>
                                        <#list gpclaimList as claim>
                                            <#if displayedCount < 3>
                                                <tr>
                                                    <td>${claim.startTime}</td>
                                                    <td>${claim.endTime}</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <#assign gdclaimList = claimLogs["GD"]![]>
                                                    <#assign gdshowflg = false>
                                                    <#list gdclaimList as gdclaim>
                                                        <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                            <#assign gdshowflg = true>
                                                            <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                                            <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                                        </#if>
                                                    </#list>
                                                    <#if !gdshowflg>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                    </#if>
                                                </tr>
                                                <#assign displayedCount = displayedCount + 1>
                                            <#else>
                                                <tr>
                                                    <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                                </tr>
                                                <#break>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>


                                <#list claimLogs?keys as key>
                                    <#if key == "GD" && displayedCount < 3>
                                        <#assign gpclaimList = claimLogs[key]>
                                        <#list gpclaimList as claim>
                                            <#if displayedCount < 3>
                                                <tr>
                                                    <td>${claim.startTime}</td>
                                                    <td>${claim.endTime}</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                                </tr>
                                                <#assign displayedCount = displayedCount + 1>
                                            <#else>
                                                <tr>
                                                    <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                                </tr>
                                                <#break>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>
                                <#if displayedCount < 3 && !isUploadClaimAttach>
                                    <#list displayedCount..2 as i>
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>
                            <tr>
                                <td class="italic text-left" colspan="10">
                                    * inclusive of visits to non-panel clinics <br>
                                    Note: The insurer reserves the right to request for more information.
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border border-none text-center layout-auto cell-height-small padding-small">
                            <caption class="font-bold text-left underline" style="padding-top: 2em;">Outstanding Claims
                            </caption>
                            <colgroup>
                                <col span="2" style="width: 16%;">
                                <col span="8" style="width: 8.4%;">
                            </colgroup>
                            <tr>
                                <th class="border-none" colspan="2"></th>
                                <th colspan="2">Clinical*</th>
                                <th colspan="2">Specialist *</th>
                                <th colspan="2">Diagnostic X-Ray / Lab Tests*</th>
                                <th colspan="2">Dental*</th>
                            </tr>
                            <tr>
                                <th>Period of Coverage From / To<span class="border-bottom inline-block"
                                style="width: 4em;"></span>(dd/mm/yyyy)
                                </th>
                                <th># of Insured as at <span class="border-bottom inline-block" style="width: 4em;"></span>(dd/mm/yyyy)
                                </th>
                                <th># of Visits</th>
                                <th>Amt (S$)</th>
                                <th># of Visits</th>
                                <th>Amt (S$)</th>
                                <th># of Visits</th>
                                <th>Amt (S$)</th>
                                <th># of Visits</th>
                                <th>Amt (S$)</th>
                            </tr>
                                <#if isUploadClaimAttach?? && isUploadClaimAttach>
                                    <tr>
                                        <td >Please refer to the attachment</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                    </tr>
                                    <#list 1..2 as i>
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>

                                <#assign displayedCount = 0>
                                <#list claimLogs?keys as key>
                                    <#if key == "GP">
                                        <#assign gpclaimList = claimLogs[key]>
                                        <#list gpclaimList as claim>
                                            <#if displayedCount < 3>
                                                <tr>
                                                    <td>${claim.startTime}</td>
                                                    <td>${claim.endTime}</td>
                                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                                    <#assign gpclaimList = claimLogs["SP"]![]>
                                                    <#assign spshowflg = false>
                                                    <#list gpclaimList as spclaim>
                                                        <#if spclaim.startTime == claim.startTime && spclaim.endTime == claim.endTime>
                                                            <#assign spshowflg = true>
                                                            <td>${(spclaim.outstandingClaimsNum??)?then(spclaim.outstandingClaimsNum, "")}</td>
                                                            <td>${(spclaim.outstandingAmount?? && spclaim.outstandingAmount.amount??)?then(spclaim.outstandingAmount.amount, "")}</td>
                                                        </#if>
                                                    </#list>
                                                    <#if !spshowflg>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                    </#if>
                                                    <#assign gdclaimList = claimLogs["GD"]![]>
                                                    <#assign gdshowflg = false>
                                                    <#list gdclaimList as gdclaim>
                                                        <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                            <#assign gdshowflg = true>
                                                            <td></td>
                                                            <td></td>
                                                            <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                                            <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                                        </#if>
                                                    </#list>
                                                    <#if !gdshowflg>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                    </#if>
                                                </tr>
                                                <#assign displayedCount = displayedCount + 1>
                                            <#else>
                                                <tr>
                                                    <td colspan="10" class="italic text-left">More information, please refer to the attachment.</td>
                                                </tr>
                                                <#break>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>

                                <#list claimLogs?keys as key>
                                    <#if key == "SP" && displayedCount < 3>
                                        <#assign gpclaimList = claimLogs[key]>
                                        <#list gpclaimList as claim>
                                            <#if displayedCount < 3>
                                                <tr>
                                                    <td>${claim.startTime}</td>
                                                    <td>${claim.endTime}</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                                    <td>&nbsp; 123131</td>
                                                    <td>&nbsp;</td>
                                                    <#assign gdclaimList = claimLogs["GD"]![]>
                                                    <#assign gdshowflg = false>
                                                    <#list gdclaimList as gdclaim>
                                                        <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                            <#assign gdshowflg = true>
                                                            <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                                            <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                                        </#if>
                                                    </#list>
                                                    <#if !gdshowflg>
                                                        <td>&nbsp;</td>
                                                        <td>&nbsp;</td>
                                                    </#if>
                                                </tr>
                                                <#assign displayedCount = displayedCount + 1>
                                            <#else>
                                                <tr>
                                                    <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                                </tr>
                                                <#break>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>

                                <#list claimLogs?keys as key>
                                    <#if key == "GD" && displayedCount < 3>
                                        <#assign gpclaimList = claimLogs[key]>
                                        <#list gpclaimList as claim>
                                            <#if displayedCount < 3>
                                                <tr>
                                                    <td>${claim.startTime}</td>
                                                    <td>${claim.endTime}</td>
                                                    <td>&nbsp; </td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                                </tr>
                                                <#assign displayedCount = displayedCount + 1>
                                            <#else>
                                                <tr>
                                                    <td colspan="10" class="italic text-left">More information, please refer to the attachment.</td>
                                                </tr>
                                                <#break>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>

                                <#if displayedCount < 3 && !isUploadClaimAttach>
                                    <#list displayedCount..2 as i>
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>
                            <tr>
                                <td class="italic text-left" colspan="10">
                                    * inclusive of visits to non-panel clinics <br>
                                    Note: The insurer reserves the right to request for more information.
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td style="height: 2em;"></td>
                </tr>
                <!--        d)-->
                <tr>
                    <th>d)</th>
                    <td>
                        <div>
                            Kindly attach a copy of the Schedule of Benefits if the benefits are on insured basis.
                        </div>
                        <div style="padding: 1em 0;">
                            If currently self-insured, kindly provide the following details:
                        </div>
                        <div>
                            Please indicate “Unlimited” if there is no cap and “NA” if it is not applicable.
                        </div>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="padding-small border text-center">
                            <colgroup>
                                <col style="width: 17%">
                                <col span="6">
                            </colgroup>
                            <tr>
                                <th class="text-left" rowspan="2">Benefits</th>
                                <th colspan="2">Maximum Limit per Visit (S$)</th>
                                <th colspan="2">Maximum Limit per Policy Year (S$)</th>
                                <th colspan="2">Co-Payment (S$) / Co-Insurance (%)</th>
                            </tr>
                            <tr>
                                <th>Clinic on Company's panel</th>
                                <th>Non-panel Clinic</th>
                                <th>Clinic on Company's panel</th>
                                <th>Non-panel Clinic</th>
                                <th>Clinic on Company's panel</th>
                                <th>Non-panel Clinic</th>
                            </tr>
                            <tr>
                                <th class="text-left">Clinical GP</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th class="text-left">Specialist</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th class="text-left">Diagnostic X-Ray / Lab Tests</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th class="text-left">Dental</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th class="text-left">Others</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <!--    footer-->
            <div class="footer">
                <div style="margin: 0 auto;">
                    <div><strong>Singapore Life Ltd 4</strong> Shenton Way #01-01 SGX Centre 2 Singapore 068807 singlife.com
                    </div>
                    <div>Company Reg.No. 196900499K GST Reg.No. MR-8500166-8 | Navigator Investment Services Ltd Company Reg.No.
                        200103470W GST Reg.No. 20-0103470-W
                    </div>
                </div>
                <div class="clear public">
                    <span class="fl">Public</span>
                    <strong class="fr">Page 13 of 15</strong>
                </div>
                <!--        接口不支持文字竖排，只能用图片替换-->
                <div class="date"></div>
            </div>
        </div>

        <!--第十四页-->
        <div class="page">
            <table class="common-table">
                <!--        11. BENEFIT: MATERNITY INSURANCE-->
                <tr>
                    <th>11.</th>
                    <th class="underline">BENEFIT: MATERNITY INSURANCE</th>
                </tr>
                <!--        a)-->
                <tr>
                    <th>a)</th>
                    <td>Basis of Coverage</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border text-center padding-small">
                            <colgroup>
                                <col style="width: 3em;">
                                <col>
                                <col style="width: 12em;">
                            </colgroup>
                            <tr>
                                <th colspan="2">Category of Employees (refer to the example)</th>
                                <th># of Headcount</th>
                            </tr>
                            <#assign hasData = false>
                            <#list personOverviews as personOverview>
                                <#if personOverview.tag == "gm" && personOverview.itemKey == "basis_of_coverage">
                                    <#assign hasData = true>
                                    <#assign total = personOverview.valueMaps?size />
                                    <#list 0..3 as i>
                                        <tr>
                                            <td>
                                                <#if i == 0>(i)
                                                <#elseif i == 1>(ii)
                                                <#elseif i == 2>(iii)
                                                <#elseif i == 3>(iv)
                                                </#if>
                                            </td>
                                            <#if i < total>
                                                <#assign info = personOverview.valueMaps[i] />
                                                <td>${info.category_of_employees_occupation!}</td>
                                                <td>${info.no_of_headcount!}</td>
                                            <#else>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </#if>
                                        </tr>
                                    </#list>
                                    <#if total gt 4>
                                        <tr>
                                            <td colspan="3" style="text-align:center; font-style:italic;">
                                                More information, Please refer to the attachment.
                                            </td>
                                        </tr>
                                    </#if>
                                </#if>
                            </#list>
                            <#if !hasData>
                                <#list 1..3 as i>
                                    <tr>
                                        <td>
                                            <#if i == 1>(i)
                                            <#elseif i == 2>(ii)
                                            <#elseif i == 3>(iii)
                                            </#if>
                                        </td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </tr>
                                </#list>
                            </#if>
                        </table>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="no-padding text-left">
                            <tr>
                                <th class="underline" style="padding-bottom: 0;">Example 1</th>
                            </tr>
                            <tr>
                                <th>Category of Employees / Occupation</th>
                            </tr>
                            <tr>
                                <td>(i) &nbsp; Senior Management (Director, General Manager, Senior Manager)</td>
                            </tr>
                            <tr>
                                <td>(ii) &nbsp; Manager & Executive</td>
                            </tr>
                            <tr>
                                <td>(iii)&nbsp; All Others</td>
                            </tr>
                            <tr>
                                <th class="underline" style="padding-top: 1em !important;">Example 2</th>
                            </tr>
                            <tr>
                                <td>(i) &nbsp; All Employees</td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td style="height: 2em;"></td>
                </tr>
                <!--        b)-->
                <tr>
                    <th>b)</th>
                    <td>Claims Experience for past 3 years</td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="border text-center cell-height-small padding-small">
                            <colgroup>
                                <col span="2" style="width: 18%">
                                <col span="4">
                            </colgroup>
                            <tr>
                                <th rowspan="2">Period of Coverage From / To<span class="border-bottom inline-block"
                                style="width: 4em;"></span>(dd/mm/yyyy)
                                </th>
                                <th rowspan="2"># of Insured as at <span class="border-bottom inline-block"
                                style="width: 4em;"></span>(dd/mm/yyyy)
                                </th>
                                <th colspan="2">Paid Claims</th>
                                <th colspan="2">Outstanding Claims</th>
                            </tr>
                            <tr>
                                <th># of Claims</th>
                                <th>Amount (S$)</th>
                                <th># of Claims</th>
                                <th>Amount (S$)</th>
                            </tr>
                                <#if isUploadClaimAttach?? && isUploadClaimAttach>
                                    <tr>
                                        <td class="text-left">Please refer to the attachment</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>

                                    </tr>
                                    <#list 1..2 as i>
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>

                                <#assign displayedCount = 0>
                                <#list claimLogs?keys as key>
                                    <#if key == "GM">
                                        <#assign gpclaimList = claimLogs[key]>
                                        <#list gpclaimList as claim>
                                            <#if displayedCount < 3>
                                                <tr>
                                                    <td>${claim.startTime}</td>
                                                    <td>${claim.endTime}</td>
                                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                                </tr>
                                                <#assign displayedCount = displayedCount + 1>
                                            <#else>
                                                <tr>
                                                    <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                                                </tr>
                                                <#break>
                                            </#if>
                                        </#list>
                                    </#if>
                                </#list>

                                <#if displayedCount < 3 && !isUploadClaimAttach >
                                    <#list displayedCount..2 as i>
                                        <tr>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                        </tr>
                                    </#list>
                                </#if>
                            <tr>
                                <td class="italic text-left" colspan="6">Note: The insurer reserves the right to request for
                                    more information.
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td colspan="2" style="height: 2em;"></td>
                </tr>
                <!--        c)-->
                <tr>
                    <th>c)</th>
                    <td>
                        <div>
                            Kindly attach a copy of the Schedule of Benefits if the benefits are on insured basis.
                        </div>
                        <div style="padding: 1em 0;">
                            If currently self-insured, kindly provide the following details:
                        </div>
                        <div>
                            Please indicate “Unlimited” if there is no cap and “NA” if it is not applicable.
                        </div>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="padding border">
                            <tr>
                                <th style="width: 25%;">Benefits</th>
                                <th colspan="2" style="width: 40%;">Maximum Limit per Policy Year (S$)</th>
                                <th colspan="2">Deductible / Co-insurance (S$)</th>
                            </tr>
                            <tr>
                                <th>Normal Delivery</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>Caesarian Delivery</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>Others:</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </table>
                    </td>
                </tr>
            </table>
            <!--    footer-->
            <div class="footer">
                <div style="margin: 0 auto;">
                    <div><strong>Singapore Life Ltd 4</strong> Shenton Way #01-01 SGX Centre 2 Singapore 068807 singlife.com
                    </div>
                    <div>Company Reg.No. 196900499K GST Reg.No. MR-8500166-8 | Navigator Investment Services Ltd Company Reg.No.
                        200103470W GST Reg.No. 20-0103470-W
                    </div>
                </div>
                <div class="clear public">
                    <span class="fl">Public</span>
                    <strong class="fr">Page 14 of 15</strong>
                </div>
                <!--        接口不支持文字竖排，只能用图片替换-->
                <div class="date"></div>
            </div>
        </div>

        <!--第十五页-->
        <div class="page">
            <table class="common-table">
                <!--        12. NEEDS ANALYSIS & PRODUCT RECOMMENDATION-->
                <tr>
                    <th>12.</th>
                    <th class="underline">NEEDS ANALYSIS & PRODUCT RECOMMENDATION</th>
                </tr>
                <tr>
                    <th></th>
                    <td>Please tick the appropriate box to indicate the priority of your company's needs:</td>
                </tr>
                <tr>
                    <td colspan="2" style="padding-left: 1em;">
                        <table class="padding">
                            <tr>
                                <th class="underline" style="width: 16em;">Company's Priorities</th>
                                <th class="text-center" style="width: 4em;">Low</th>
                                <th class="text-center" style="width: 4em;">Med</th>
                                <th class="text-center" style="width: 4em;">High</th>
                                <th class="underline" style="width: 10em">Advisor's Recommendation</th>
                            </tr>
                            <tr>
                                <td>Cover for Outpatient medical expenses</td>
                                <#assign isChecked = false>
                                <#list userPlanDutyList as item>
                                    <#if item.benefit == "GP" && item.participation == "1">
                                        <#assign isChecked = true>
                                    </#if>
                                </#list>
                                <td class="text-center"><input <#if !isChecked>checked</#if> type="checkbox"></td>
                                <td class="text-center"><input type="checkbox"></td>
                                <td class="text-center"><input <#if isChecked>checked</#if>  type="checkbox"></td>
                                <td class="border-bottom"></td>
                            </tr>
                            <tr>
                                <td>Cover for Hospital & Surgical expenses</td>
                                <#assign isChecked = false>
                                <#list userPlanDutyList as item>
                                    <#if item.benefit == "GHS" && item.participation == "1">
                                        <#assign isChecked = true>
                                    </#if>
                                </#list>
                                <td class="text-center"><input <#if !isChecked>checked</#if> type="checkbox"></td>
                                <td class="text-center"><input type="checkbox"></td>
                                <td class="text-center"><input <#if isChecked>checked</#if>  type="checkbox"></td>
                                <td class="border-bottom"></td>
                            </tr>
                            <tr>
                                <td>Cover for Dental expenses</td>
                                <#assign isChecked = false>
                                <#list userPlanDutyList as item>
                                    <#if item.benefit == "GD" && item.participation == "1">
                                        <#assign isChecked = true>
                                    </#if>
                                </#list>
                                <td class="text-center"><input <#if !isChecked>checked</#if> type="checkbox"></td>
                                <td class="text-center"><input type="checkbox"></td>
                                <td class="text-center"><input <#if isChecked>checked</#if>  type="checkbox"></td>
                                <td class="border-bottom"></td>
                            </tr>
                            <tr>
                                <td>Cover for Major illnesses <br>
                                    (e.g. cancer, kidney failure, etc.)
                                </td>
                                <#assign isChecked = false>
                                <#list userPlanDutyList as item>
                                    <#if item.benefit == "GCI" && item.participation == "1">
                                        <#assign isChecked = true>
                                    </#if>
                                </#list>
                                <td class="text-center"><input <#if !isChecked>checked</#if> type="checkbox"></td>
                                <td class="text-center"><input type="checkbox"></td>
                                <td class="text-center"><input <#if isChecked>checked</#if>  type="checkbox"></td>
                                <td class="border-bottom"></td>
                            </tr>
                            <tr>
                                <td>Cover for Loss of Income due to <br>
                                    sickness or accident
                                </td>
                                <#assign isChecked = false>
                                <#list userPlanDutyList as item>
                                    <#if item.benefit == "GDI" && item.participation == "1">
                                        <#assign isChecked = true>
                                    </#if>
                                </#list>
                                <td class="text-center"><input <#if !isChecked>checked</#if> type="checkbox"></td>
                                <td class="text-center"><input type="checkbox"></td>
                                <td class="text-center"><input <#if isChecked>checked</#if>  type="checkbox"></td>
                                <td class="border-bottom"></td>
                            </tr>
                            <tr>
                                <td>Cover for long term medical treatment</td>
                                <#assign isChecked = false>
                                <#list userPlanDutyList as item>
                                    <#if item.benefit == "GTL" && item.participation == "1">
                                        <#assign isChecked = true>
                                    </#if>
                                </#list>
                                <td class="text-center"><input <#if !isChecked>checked</#if> type="checkbox"></td>
                                <td class="text-center"><input type="checkbox"></td>
                                <td class="text-center"><input <#if isChecked>checked</#if>  type="checkbox"></td>
                                <td class="border-bottom"></td>
                            </tr>
                            <tr>
                                <td colspan="5" class="nowrap overflow-hidden">
                                    Others :
                                    <div class="border-bottom inline-block wrap" style="width: 88%; margin-left: 4em;">&emsp;</div>
                                </td>
                            </tr>
                        </table>
                    </td>
                </tr>
                <!--        13-->
                <tr>
                    <th>13.</th>
                    <th class="underline">DECLARATION</th>
                </tr>
                <tr>
                    <th></th>
                    <td style="text-align: justify;">
                        <ul style="padding-left: 4em;" class="">
                            <li>I / We hereby declare that, to the best of my / our knowledge and belief, the information given here are true
                                and complete, and any misstatement or non-disclosure of material facts may affect the validity of the policy</li>
                            <li style="list-style: none; padding: 1em 0;">I / We agree that if a contract of insurance is effected, all information submitted in connection with this
                                application shall form the basis of such contract between the Company and Singapore Life Ltd</li>
                            <li>
                                I / We declare that I/we have read, understood and have obtained the appropriate consent from the
                                insured members whose personal data are being disclosed for the purposes stated in Singapore Life Ltd.'s
                                Personal Data Protection Compliance Undertakings (For Corporate Prospect/Policyholder).
                            </li>
                        </ul>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td style="padding-top: 1em;">
                        <div class="border-bottom border-bold" style="width: 20em;">
                            &emsp;
                        </div>
                        <div>Signature of Authorised Officer</div>
                    </td>
                </tr>
                <tr>
                    <th></th>
                    <td>
                        <table class="padding-y">
                            <tr>
                                <td style="width: 40%;" class="overflow-hidden nowrap">
                                    Name:
                                    <span class="border-bottom border-bold inline-block wrap" style="width: 24em;">
                                        &emsp;
                                    </span>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="overflow-hidden nowrap">
                                    NRIC/ Fin No
                                    <span class="border-bottom border-bold inline-block wrap" style="width: 20em;">
                                        &emsp;
                                    </span>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="overflow-hidden nowrap">
                                    Designation:
                                    <span class="border-bottom border-bold inline-block wrap" style="width: 20em;">
                                        &emsp;
                                    </span>
                                </td>
                                <td>
                                    <div style="padding-left: 4em;">Company Stamp (if applicable):</div>
                                </td>
                            </tr>
                            <tr>
                                <td class="overflow-hidden nowrap">
                                    Date:
                                    <span class="border-bottom border-bold inline-block wrap" style="width: 24em;">
                                        &emsp;
                                    </span>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td colspan="2">
                                    I/We acknowledge that I / we have reviewed this Group Business Insurance Fact-Find Form with the authorised
                                    officer of the Company, and that I / we have explained all the requirements of this Fact-Find form to him / her.
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2" style="padding-top: 1em;">
                                    <div class="border-bottom border-bold" style="width: 20em;">
                                        &emsp;
                                    </div>
                                    <div>Signature of Authorised Representative</div>
                                </td>
                            </tr>
                            <tr>
                                <td class="overflow-hidden nowrap">
                                    Name:
                                    <span class="border-bottom border-bold inline-block wrap" style="width: 24em;">
                                        &emsp;
                                    </span>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="overflow-hidden nowrap">
                                    NRIC/ Fin No
                                    <span class="border-bottom border-bold inline-block wrap" style="width: 20em;">
                                        &emsp;
                                    </span>
                                </td>
                                <td></td>
                            </tr>
                            <tr>
                                <td class="overflow-hidden nowrap">
                                    Designation:
                                    <span class="border-bottom border-bold inline-block wrap" style="width: 20em;">
                                        &emsp;
                                    </span>
                                </td>
                                <td>
                                    <div style="padding-left: 4em;">Company Stamp (if applicable):</div>
                                </td>
                            </tr>
                            <tr>
                                <td class="overflow-hidden nowrap">
                                    Date:
                                    <span class="border-bottom border-bold inline-block wrap" style="width: 24em;">
                                        &emsp;
                                    </span>
                                </td>
                                <td></td>
                            </tr>
                        </table>
                    </td>
                </tr>


            </table>
            <!--    footer-->
            <div class="footer">
                <div style="margin: 0 auto;">
                    <div><strong>Singapore Life Ltd 4</strong> Shenton Way #01-01 SGX Centre 2 Singapore 068807 singlife.com
                    </div>
                    <div>Company Reg.No. 196900499K GST Reg.No. MR-8500166-8 | Navigator Investment Services Ltd Company Reg.No.
                        200103470W GST Reg.No. 20-0103470-W
                    </div>
                </div>
                <div class="clear public">
                    <span class="fl">Public</span>
                    <strong class="fr">Page 15 of 15</strong>
                </div>
                <!--        接口不支持文字竖排，只能用图片替换-->
                <div class="date"></div>
            </div>
        </div>
    </body>
</html>
