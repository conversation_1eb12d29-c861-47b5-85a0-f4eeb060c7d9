<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[INCOME GIFF] Group Insurance Fact Finding Form (Jul 2024)</title>
    <style>
        body,ol,ul,h1,h2,h3,h4,h5,h6,p,th,td,dl,dd,form,fieldset,legend,input,textarea,select{margin:0;padding:0;}
		.clearfix::after {
			content: "";
			display: table;
			clear: both;
		}
        body{
			background: #ccc;
		}
        .page{
            width: 990px;
            height: 1579px;
            background: #fff;
            margin: 0 auto;
            padding: 50px 80px;
            position: relative;
        }
        .page-footer{
            position: absolute;
            bottom: 20px;
            left: 70px;
        }
        .page-info{
            text-align: right;
            font-size: 12px;
            margin-bottom: 6px;
            width: 980px;
        }
        .header img{
			margin-right: 30px;
			display: inline-block;
		}
        .font12{
            font-size: 12px;
        }
        .font14{
            font-size: 14px;
        }
        .font16{
            font-size: 16px;
        }
        .font18{
            font-size: 18px;
        }
        .t-center{
            text-align: center;
        }
        .t-bg{
            color: #fff;
            padding: 6px 10px;
            background-color: rgba(243, 112, 50, 1);
        }
        .mb-8{
            margin-bottom: 8px;
        }
        .mb-20{
            margin-bottom: 20px;
        }
        table{
            width: 100%;
        }
        table th{
            padding: 10px 12px;
        }
        table td{
            padding: 12px 12px;
            height: 20px;
        }
        .table-text{
            height: 24px;
        }
        .ml-30{
            margin-left: 30px;
        }
        .mb-36{
            margin-bottom: 36px;
        }
        .table-input{
            width: 300px;
            display: inline-block;
            border-bottom: 1px solid #333;
        }
        label{
            margin-left: 10px;
        }
        input{
            margin-right: 10px;
        }
        .page-eight td{
            padding: 4px 10px;
        }
        .table-input{
            border-bottom: 1px solid #333;
            height: 24px;
            width: 100%;
        }
        .fl{
            display: inline-block;
            float: left;
        }
        .text-center {
            text-align: center;
        }
        .text-left {
            text-align: left;
        }
        .text-right {
            text-align: right;
        }
        .font-bold {
            font-weight: bold;
        }
        .underline {
            text-decoration: underline;
        }
        .underline-bold {
            text-decoration: underline;
            text-decoration-thickness: 2px;
        }
        .nowrap {
            white-space: nowrap;
        }
        .wrap {
            white-space: normal;
        }
        .italic {
            font-style: italic;
        }
        .line-4em {
            display: inline-block;
            border-bottom: 2px solid #333;
            width: 4em;
        }
        .line-6em {
            display: inline-block;
            border-bottom: 2px solid #333;
            width: 6em;
        }
        table.border th,
        table.border td {
            border: 2px solid #333;
        }
        table.padding td,
        table.padding th {
            padding: 4px 8px;
        }
        table.collapse {
            border-collapse: collapse;
        }
        table.common-table {
            border-collapse: collapse;
        }
        table.common-table th,
        table.common-table td {
            padding: 4px 8px;
            border: 2px solid #333;
        }
        .border-none {
            border: none !important;
        }
        .border-bottom-bold {
            border-bottom: 2px solid #333;
        }
        .overflow-hidden {
            overflow: hidden;
        }
        .inline-block {
            display: inline-block;
        }
        .py-0 {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
        }
        .q-title-table {
            width: 100%;
            margin-bottom: 20px;
        }
        .q-title-table td {
            padding: 0 !important;
        }
        .q-title-table td:first-child {
            vertical-align: top;
            width: 34px;
            white-space: nowrap;
        }
        .q-title-table td:last-child {
            width: 200px;
            vertical-align: top;
        }
    </style>
</head>
<body>
    <!-- 第一页 -->
    <div class="page">
        <div class="header clearfix">
			<img src="data:image/png;base64,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********************************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" alt=""/>
		</div>
        <div class="t-center t-bg mb-20 font18">
            Group Insurance Fact Finding Form
        </div>
        <div class="font16 t-center mb-8">
            <p><strong>Statement under section 23(5) of Insurance Act 1966 (or any future amendments to it)</strong></p>
            <p>You must reveal all facts you know, or ought to know, which may affect the insurance cover you are applying for.</p>
            <p>Otherwise, the insurance policy may not be valid.</p>
            <p>Please email the completed form to Group Business – Employee <NAME_EMAIL></p>
        </div>
        <div class="t-center t-bg font14">
            Company information
        </div>
        <table border="1" cellspacing="0" class="mb-8">
            <tr>
                <td colspan="2">
                    <div>Name of company</div>
                    <div class="table-text">${baseInfo.companyName!}</div>
                </td>
                <td>
                    <div>Nature of business</div>
                    <div class="table-text">${baseInfo.companyNature!}</div>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <div>Contact person</div>
                    <div class="table-text">${baseInfo.contactPerson!}</div>
                </td>
                <td>
                    <div>Designation</div>
                    <div class="table-text">${baseInfo.designation!}</div>
                </td>
            </tr>
            <tr>
                <td>
                    <div>Contact number</div>
                    <div class="table-text">${baseInfo.contactNumber!}</div>
                </td>
                <td>
                    <div>Fax number</div>
                    <div class="table-text"></div>
                </td>
                <td>
                    <div>Email</div>
                    <div class="table-text">${baseInfo.email!}</div>
                </td>
            </tr>
        </table>
        <div class="t-center t-bg font14">
            General information
        </div>
        <table border="1" cellspacing="0">
            <tr>
                <td colspan="3">
                    <div>
                        Presently insured?
                        <label for="myCheckbox1" class="label-item">
                            <input type="checkbox" id="myCheckbox1" name="myCheckbox1"
                            <#if baseInfo.presentlyInsured== "1">checked</#if>>
                            Yes
                        </label>

                        <label for="myCheckbox2" class="label-item">
                            <input type="checkbox" id="myCheckbox2" name="myCheckbox2"
                            <#if baseInfo.presentlyInsured == "0">checked</#if>>
                            No
                        </label>
                    </div>
                    <div>
                        If “Yes”, name of current insurer
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; ${baseInfo.currentEbInsurer!}
                    </div>
                </td>
            </tr>
            <tr>
                <td colspan="2">
                    <div>Type of policy</div>
                    <div class="table-text">Employee Benefit</div>
                </td>
                <td>
                    <div>Current period of insurance (dd/mm/yyyy)</div>
                    <div class="table-text">${baseInfo.expectStartTime!} To ${baseInfo.expectEndTime!}</div>
                </td>
            </tr>
            <tr>
                <td>
                    <div>Proposed period of insurance (dd/mm/yyyy)</div>
                    <div class="table-text">${baseInfo.insuranceStartTime!} To ${baseInfo.insuranceEndTime!}</div>
                </td>
                <td>
                    <div>Total number of employees</div>
                    <div class="table-text">${baseInfo.totalEmployees!}</div>
                </td>
                <td>
                    <div>Number of employees to be insured</div>
                    <div class="table-text">${baseInfo.insuredEmployees!}</div>
                </td>
            </tr>
        </table>
        <p class="mb-20">
            Participation: The insurer will assume that participation of the group insurance program is on compulsory basis unless otherwise stated.
        </p>
        <p class="mb-8">Please tick [√] accordingly to the choice of the insurance product that you like to have a quote from us.</p>
        <table border="1" cellspacing="0">
            <tr class="t-bg">
                <th style="border-color: #fff;border-bottom: 1px solid #333;" rowspan="2">Benefits</th>
                <th style="border-color: #fff;border-bottom: 1px solid #333;" rowspan="2" colspan="2">Insurance Coverage</th>
                <th style="border-color: #fff;" colspan="2">Participation</th>
            </tr>
            <tr class="t-bg">
                <th style="border-color: #fff;border-bottom: 1px solid #333;">Compulsory</th>
                <th style="border-color: #fff;border-bottom: 1px solid #333;">Voluntary</th>
            </tr>
            <tr>
                <td rowspan="3"><strong>Life Insurance</strong></td>
                <td colspan="2">Group Term Life (GTL)</td>
                <#-- 默认两个框都不勾 -->
                <#assign checked1 = false>
                <#assign checked2 = false>
                <#assign hasBenefit = false>

                <#list userPlanDutyList as item>
                    <#if item.benefit == "GTL">
                        <#assign hasBenefit = true>
                        <#if item.participation == "2">
                            <#assign checked2 = true>
                        </#if>
                    </#if>
                </#list>

                <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                <#if hasBenefit && !checked2>
                    <#assign checked1 = true>
                </#if>

                <td>
                    <input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1">
                </td>
                <td>
                    <input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2">
                </td>
            </tr>
            <tr>
                <td colspan="2">Group Critical Illness (GCI)</td>
                <#-- 默认两个框都不勾 -->
                <#assign checked1 = false>
                <#assign checked2 = false>
                <#assign hasBenefit = false>

                <#list userPlanDutyList as item>
                    <#if item.benefit == "GCI">
                        <#assign hasBenefit = true>
                        <#if item.participation == "2">
                            <#assign checked2 = true>
                        </#if>
                    </#if>
                </#list>

                <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                <#if hasBenefit && !checked2>
                    <#assign checked1 = true>
                </#if>

                <td>
                    <input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1">
                </td>
                <td>
                    <input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2">
                </td>
            </tr>
            <tr>
                <td colspan="2">Group Personal Accident (GPA)</td>
                <#-- 默认两个框都不勾 -->
                <#assign checked1 = false>
                <#assign checked2 = false>
                <#assign hasBenefit = false>

                <#list userPlanDutyList as item>
                    <#if item.benefit == "GPA">
                        <#assign hasBenefit = true>
                        <#if item.participation == "2">
                            <#assign checked2 = true>
                        </#if>
                    </#if>
                </#list>

                <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                <#if hasBenefit && !checked2>
                    <#assign checked1 = true>
                </#if>

                <td>
                    <input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1">
                </td>
                <td>
                    <input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2">
                </td>
            </tr>
            <tr>
                <td rowspan="4"><strong>Medical</strong></td>
                <td rowspan="2">Group Hospital & Surgical (GHS)</td>
                <td>Employee only</td>
                <#-- 默认两个框都不勾 -->
                <#assign checked1 = false>
                <#assign checked2 = false>
                <#assign hasBenefit = false>

                <#list userPlanDutyList as item>
                    <#if item.benefit == "GHS">
                        <#assign hasBenefit = true>
                        <#if item.participation == "2">
                            <#assign checked2 = true>
                        </#if>
                    </#if>
                </#list>

                <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                <#if hasBenefit && !checked2>
                    <#assign checked1 = true>
                </#if>

                <td>
                    <input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1">
                </td>
                <td>
                    <input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2">
                </td>
            </tr>
            <tr>
                <td>Dependant (Spouse and/or Children)</td>
                <td><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <tr>
                <td rowspan="2">Group Major Medical (GMM)</td>
                <td>Employee only</td>
                <#-- 默认两个框都不勾 -->
                <#assign checked1 = false>
                <#assign checked2 = false>
                <#assign hasBenefit = false>

                <#list userPlanDutyList as item>
                    <#if item.benefit == "GMM">
                        <#assign hasBenefit = true>
                        <#if item.participation == "2">
                            <#assign checked2 = true>
                        </#if>
                    </#if>
                </#list>

                <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                <#if hasBenefit && !checked2>
                    <#assign checked1 = true>
                </#if>

                <td>
                    <input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1">
                </td>
                <td>
                    <input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2">
                </td>
            </tr>
            <tr>
                <td>Dependant (Spouse and/or Children)</td>
                <td><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
            </tr>
            <tr>
                <td rowspan="4"><strong>Others</strong></td>
                <td rowspan="2">Group Outpatient</td>
                <td>Employee only</td>
                <#-- 默认两个框都不勾 -->
                <#assign checked1 = false>
                <#assign checked2 = false>
                <#assign hasBenefit = false>

                <#list userPlanDutyList as item>
                    <#if item.benefit == "GP">
                        <#assign hasBenefit = true>
                        <#if item.participation == "2">
                            <#assign checked2 = true>
                        </#if>
                    </#if>
                </#list>

                <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                <#if hasBenefit && !checked2>
                    <#assign checked1 = true>
                </#if>

                <td>
                    <input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1">
                </td>
                <td>
                    <input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2">
                </td>
            </tr>
            <tr>
                <td>Dependant (Spouse and/or Children)</td>
                <td><input type="checkbox" id="myCheckbox19" name="myCheckbox19"></td>
                <td><input type="checkbox" id="myCheckbox20" name="myCheckbox20"></td>
            </tr>
            <tr>
                <td rowspan="2">Dental</td>
                <td>Employee only</td>
                <#-- 默认两个框都不勾 -->
                <#assign checked1 = false>
                <#assign checked2 = false>
                <#assign hasBenefit = false>

                <#list userPlanDutyList as item>
                    <#if item.benefit == "GD">
                        <#assign hasBenefit = true>
                        <#if item.participation == "2">
                            <#assign checked2 = true>
                        </#if>
                    </#if>
                </#list>

                <#-- 如果存在该险种且没有勾第二个，就勾第一个 -->
                <#if hasBenefit && !checked2>
                    <#assign checked1 = true>
                </#if>

                <td>
                    <input type="checkbox" <#if checked1>checked</#if> id="myCheckbox1" name="myCheckbox1">
                </td>
                <td>
                    <input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2">
                </td>
            </tr>
            <tr>
                <td>Dependant (Spouse and/or Children)</td>
                <td><input type="checkbox" id="myCheckbox23" name="myCheckbox23"></td>
                <td><input type="checkbox" id="myCheckbox24" name="myCheckbox24"></td>
            </tr>
        </table>
        <p>Note: Participation is voluntary if employees or dependants are given the choice to opt for the cover(s), subject to minimum participation level</p>
        <div class="page-footer">
            <div class="page-info">INCOME/GB/GIFFF/07/2024 • Page 1 of 12</div>
        </div>
    </div>
    <!-- 第二页 -->
    <div class="page">
        <div class="mb-36">
            <table class="q-title-table">
                <tr>
                    <td>Q1.</td>
                    <td>
                        Is there any member currently in hospital or require frequent admission to hospital <br>
                        (for example, hospital admission more than 2 times per year)?
                    </td>
                    <td>
                        <#assign hasYes=false>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="1">
                                <#assign hasYes=true>
                            </#if>
                        </#list>
                        <label class="label-item" for="myCheckbox1">
                            <input type="checkbox" id="myCheckbox1" name="myCheckbox1"
                                   <#if hasYes>checked</#if>
                            >
                            Yes
                        </label>

                        <label class="ml-30 label-item" for="myCheckbox2">
                            <input type="checkbox" id="myCheckbox2" name="myCheckbox2"
                                   <#if !hasYes>checked</#if>
                            >
                            No
                        </label>
                    </td>
                </tr>
            </table>


            <p class="mb-8">If “Yes”, please provide the following details:</p>

            <table class="common-table text-center">
                <tr>
                    <th style="width: 50px;">S/N</th>
                    <th style="width: 120px;">Number of members or age</th>
                    <th>Reason for hospitalisation or nature of illness</th>
                    <th style="width: 200px;">Total sum assured or plan</th>
                </tr>

                <#-- 遍历数据 -->
                <#assign count=0>
                <#list healthInfo as info>
                    <#if info.disclosureType=="1">
                        <#if count lt 3>
                            <tr>
                                <td>${count + 1}</td>
                                <td>${info.number!""}</td>
                                <td class="text-left">${info.content!""}</td>
                                <td>${(info.totalSumInsured.amount)!""}</td>
                            </tr>
                        </#if>
                        <#assign count=count+1>
                    </#if>
                </#list>

                <#-- 补空行到3行 -->
                <#if count lt 3>
                    <#list 1..(3-count) as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>

                <#-- 超过3条数据，增加提示行 -->
                <#if count gt 3>
                    <tr>
                        <td colspan="4" class="text-left italic">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
            </table>

            <p>Note: Income will not reimburse the hospital claims for any member in hospital at the time of application.</p>
        </div>

        <div class="mb-36">
            <table class="q-title-table">
                <tr>
                    <td>Q2.</td>
                    <td>
                        Has any member suffered or is suffering from any serious condition such as cancer, organ failure, diabetes,
                        heart disease, stroke, kidney disorder, liver disorder, arthritis or any other disorder that causes progressive
                        irreversible functional or physical disability?
                    </td>
                    <td>
                        <#assign hasYes=false>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="2">
                                <#assign hasYes=true>
                            </#if>
                        </#list>
                        <label for="myCheckbox1">
                            <input type="checkbox" id="myCheckbox1" name="myCheckbox1"
                                   <#if hasYes>checked</#if>
                            >
                            Yes
                        </label>

                        <label class="ml-30 label-item" for="myCheckbox2">
                            <input type="checkbox" id="myCheckbox2" name="myCheckbox2"
                                   <#if !hasYes>checked</#if>
                            >
                            No
                        </label>
                    </td>
                </tr>
            </table>
            <p class="mb-8">If “Yes”, please provide the following details:</p>
            <table class="common-table text-center">
                <tr>
                    <th style="width: 50px;">S/N</th>
                    <th style="width: 120px;">Number of members or age</th>
                    <th>Nature of illness</th>
                    <th style="width: 200px">Total sum assured or plan</th>
                </tr>

                <#-- 遍历数据 -->
                <#assign count=0>
                <#list healthInfo as info>
                    <#if info.disclosureType=="2">
                        <#if count lt 3>
                            <tr>
                                <td>${count + 1}</td>
                                <td>${info.number!""}</td>
                                <td class="text-left">${info.content!""}</td>
                                <td>${(info.totalSumInsured.amount)!""}</td>
                            </tr>
                        </#if>
                        <#assign count=count+1>
                    </#if>
                </#list>

                <#-- 补空行到3行 -->
                <#if count lt 3>
                    <#list 1..(3-count) as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>

                <#-- 超过3条数据，增加提示行 -->
                <#if count gt 3>
                    <tr>
                        <td colspan="4" class="text-left italic">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
            </table>
        </div>
        <div class="mb-36">
            <table class="q-title-table">
                <tr>
                    <td>Q3.</td>
                    <td>Is there any member based outside Singapore?</td>
                    <td>
                        <#assign hasYes=false>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="3">
                                <#assign hasYes=true>
                            </#if>
                        </#list>
                        <label for="myCheckbox1">
                            <input type="checkbox" id="myCheckbox1" name="myCheckbox1"
                                   <#if hasYes>checked</#if>
                            >
                            Yes
                        </label>

                        <label class="ml-30" for="myCheckbox2">
                            <input type="checkbox" id="myCheckbox2" name="myCheckbox2"
                                   <#if !hasYes>checked</#if>
                            >
                            No
                        </label>
                    </td>
                </tr>
            </table>
            <p class="mb-8">If “Yes”, please provide the following details:</p>
             <table class="common-table text-center">
                <tr>
                    <th style="width: 50px">S/N</th>
                    <th style="width: 120px">Number of members or age</th>
                    <th>Country based in</th>
                    <th style="width: 200px">Total sum assured or plan</th>
                </tr>
                <#-- 遍历数据 -->
                <#assign count=0>
                <#list healthInfo as info>
                    <#if info.disclosureType=="3">
                        <#if count lt 3>
                            <tr>
                                <td>${count + 1}</td>
                                <td>${info.number!""}</td>
                                <td class="text-left">${info.content!""}</td>
                                <td>${(info.totalSumInsured.amount)!""}</td>
                            </tr>
                        </#if>
                        <#assign count=count+1>
                    </#if>
                </#list>

                <#-- 补空行到3行 -->
                <#if count lt 3>
                    <#list 1..(3-count) as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>

                <#-- 超过3条数据，增加提示行 -->
                <#if count gt 3>
                    <tr>
                        <td colspan="4" class="text-left italic">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
            </table>
        </div>
        <div class="page-footer">
            <div class="page-info">INCOME/GB/GIFFF/07/2024 • Page 2 of 12</div>
        </div>
    </div>
    <!-- 第三页 -->
    <div class="page">
        <div class="mb-36">
            <table class="q-title-table">
                <tr>
                    <td>Q4.</td>
                    <td>Is there any limitation or exclusion imposed on the cover on any member?</td>
                    <td>
                        <#assign hasYes=false>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="4">
                                <#assign hasYes=true>
                            </#if>
                        </#list>
                        <label for="myCheckbox1">
                            <input type="checkbox" id="myCheckbox1" name="myCheckbox1"
                                   <#if hasYes>checked</#if>
                            >
                            Yes
                        </label>

                        <label class="ml-30 label-item" for="myCheckbox2">
                            <input type="checkbox" id="myCheckbox2" name="myCheckbox2"
                                   <#if !hasYes>checked</#if>
                            >
                            No
                        </label>
                    </td>
                </tr>
            </table>
            <p class="mb-8">If “Yes”, please provide the following details:</p>
             <table class="common-table text-center">
                <tr>
                    <th style="width: 50px;">S/N</th>
                    <th style="width: 120px;">Number of members or age</th>
                    <th>Limitations or exclusions</th>
                    <th style="width: 200px">Total sum assured or plan</th>
                </tr>
                    <#-- 遍历数据 -->
                    <#assign count=0>
                    <#list healthInfo as info>
                        <#if info.disclosureType=="4">
                            <#if count lt 4>
                                <tr>
                                    <td>${count + 1}</td>
                                    <td>${info.number!""}</td>
                                    <td class="text-left">${info.content!""}</td>
                                    <td>${(info.totalSumInsured.amount)!""}</td>
                                </tr>
                            </#if>
                            <#assign count=count+1>
                        </#if>
                    </#list>

                    <#-- 补空行到4行 -->
                    <#if count lt 4>
                        <#list 1..(4-count) as i>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#list>
                    </#if>

                    <#-- 超过4条数据，增加提示行 -->
                    <#if count gt 4>
                        <tr>
                            <td colspan="4" class="text-left italic">More information, please refer to the attachment.</td>
                        </tr>
                    </#if>
            </table>
        </div>
        <div class="mb-36">
            <table class="q-title-table">
                <tr>
                    <td>Q5.</td>
                    <td>
                        Is there any member engaged in hazardous occupation? <br>
                        (for example, welder, diver, sandblaster, offshore workers, etc.)
                    </td>
                    <td>
                        <#assign hasYes=false>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="5">
                                <#assign hasYes=true>
                            </#if>
                        </#list>
                        <label  for="myCheckbox1">
                            <input type="checkbox" id="myCheckbox1" name="myCheckbox1"
                                   <#if hasYes>checked</#if>
                            >
                            Yes
                        </label>

                        <label class="ml-30 label-item" for="myCheckbox2">
                            <input type="checkbox" id="myCheckbox2" name="myCheckbox2"
                                   <#if !hasYes>checked</#if>
                            >
                            No
                        </label>
                    </td>
                </tr>
            </table>
            <p class="mb-8">If “Yes”, please provide the following details:</p>
             <table class="common-table text-center">
                <tr>
                    <th style="width: 50px;">S/N</th>
                    <th style="width: 120px">Number of members or age</th>
                    <th>Nature of work</th>
                    <th style="width: 200px">Total sum assured or plan</th>
                </tr>
                <#-- 遍历数据 -->
                <#assign count=0>
                <#list healthInfo as info>
                    <#if info.disclosureType=="5">
                        <#if count lt 4>
                            <tr>
                                <td>${count + 1}</td>
                                <td>${info.number!""}</td>
                                <td class="text-left">${info.content!""}</td>
                                <td>${(info.totalSumInsured.amount)!""}</td>
                            </tr>
                        </#if>
                        <#assign count=count+1>
                    </#if>
                </#list>

                <#-- 补空行到4行 -->
                <#if count lt 4>
                    <#list 1..(4-count) as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>

                <#-- 超过4条数据，增加提示行 -->
                <#if count gt 4>
                    <tr>
                        <td colspan="4" class="text-left italic">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
            </table>
        </div>
        <div class="mb-36">
            <table class="q-title-table">
                <tr>
                    <td>Q6.</td>
                    <td>To the best of your knowledge, is there any member engaged in hazardous sports? <br>
                        (for example, scuba diving, motor racing, bungee jumping, etc.)
                    </td>
                    <td>
                        <#assign hasYes=false>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="6">
                                <#assign hasYes=true>
                            </#if>
                        </#list>
                        <label for="myCheckbox1">
                            <input type="checkbox" id="myCheckbox1" name="myCheckbox1"
                                   <#if hasYes>checked</#if>
                            >
                            Yes
                        </label>

                        <label class="ml-30 label-item" for="myCheckbox2">
                            <input type="checkbox" id="myCheckbox2" name="myCheckbox2"
                                   <#if !hasYes>checked</#if>
                            >
                            No
                        </label>
                    </td>
                </tr>
            </table>

            <p class="mb-8">If “Yes”, please provide the following details:</p>
             <table class="common-table text-center">
                <tr>
                    <th style="width: 50px">S/N</th>
                    <th style="width: 120px">Number of members or age</th>
                    <th>Type of sports</th>
                    <th style="width: 200px">Total sum assured or plan</th>
                </tr>
                <#-- 遍历数据 -->
                <#assign count=0>
                <#list healthInfo as info>
                    <#if info.disclosureType=="6">
                        <#if count lt 4>
                            <tr>
                                <td>${count + 1}</td>
                                <td>${info.number!""}</td>
                                <td class="text-left">${info.content!""}</td>
                                <td>${(info.totalSumInsured.amount)!""}</td>
                            </tr>
                        </#if>
                        <#assign count=count+1>
                    </#if>
                </#list>

                <#-- 补空行到4行 -->
                <#if count lt 4>
                    <#list 1..(4-count) as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>

                <#-- 超过4条数据，增加提示行 -->
                <#if count gt 4>
                    <tr>
                        <td colspan="4" class="text-left italic">More information, please refer to the attachment.</td>
                    </tr>
                </#if>
            </table>
        </div>
        <div class="page-footer">
            <div class="page-info">INCOME/GB/GIFFF/07/2024 • Page 3 of 12</div>
        </div>
    </div>
    <!-- 第四页 -->
    <div class="page">
        <div class="t-center t-bg font14 mb-36">
            Benefit: Group Term Life/Group Critical Illness/Group Personal Accident
        </div>
        <div class="mb-8"><strong>Occupational classifications</strong></div>
        <table class="mb-20 common-table">
            <tr>
                <td style="width: 80px;">Class 1</td>
                <td>Clerical, administrative or other similar non-hazardous occupations</td>
            </tr>
            <tr>
                <td>Class 2</td>
                <td>Occupations where some degree of risk is involved, e.g. supervision of manual workers, totally administrative job in an industrial environment</td>
            </tr>
            <tr>
                <td>Class 3</td>
                <td>Occupations involving regular light to medium manual work but no substantial hazard which may increase the risk of sickness or accident</td>
            </tr>
            <tr>
                <td>Class 4</td>
                <td>High risk occupations involving heavy manual work including works</td>
            </tr>
        </table>
        <div class="mb-8"><strong>(a) Basis of cover</strong></div>

        <!-- GTL 表格 -->
        <table class="mb-8 common-table text-center">
            <colgroup>
                <col style="width: 100px">
                <col style="width: 50px">
                <col>
                <col span="2" style="width: 250px;">
            </colgroup>
            <tr>
                <th class="border-none"></th>
                <th class="border-none"></th>
                <th>Category of employees or occupation <br>(refer to the examples)</th>
                <th>Basis of cover – sum assured <br>(refer to the examples)</th>
                <th>Number of employees</th>
            </tr>

            <#-- 查找数据 -->
            <#assign gtlData = "">
            <#list personOverviews as personOverview>
                <#if personOverview.tag == "gtl" && personOverview.itemKey == "basis_of_coverage">
                    <#assign gtlData = personOverview.valueMaps>
                </#if>
            </#list>

            <#-- 固定四行渲染 -->
            <#list 0..3 as i>
                <tr>
                    <#if i == 0>
                                <th rowspan="4">GTL</th>
                    </#if>
                    <td>
                        <#if i == 0>(i)
                        <#elseif i == 1>(ii)
                        <#elseif i == 2>(iii)
                        <#elseif i == 3>(iv)
                        </#if>
                    </td>

                    <#-- 判断当前行是否有数据 -->
                    <#if gtlData?has_content && i < gtlData?size>
                        <#assign info = gtlData[i]>
                        <td class="text-left">${info.category_of_employees_occupation!}</td>
                        <td>${info.basis_of_coverage_sum_insured!}</td>
                        <td>${info.no_of_employees!}</td>
                    <#else>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </#if>
                </tr>
            </#list>

            <#-- 如果数据超过四条，显示附件提示 -->
            <#if gtlData?has_content && 4 < gtlData?size>
                <tr>
                    <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                </tr>
            </#if>
        </table>

        <!-- GCI 表格 -->
        <table class="mb-8 common-table text-center">
            <colgroup>
                <col style="width: 100px">
                <col style="width: 50px">
                <col>
                <col span="2" style="width: 250px;">
            </colgroup>
            <#-- 查找数据 -->
            <#assign gtlData = "">
            <#list personOverviews as personOverview>
                <#if personOverview.tag == "gci" && personOverview.itemKey == "basis_of_coverage">
                    <#assign gtlData = personOverview.valueMaps>
                </#if>
            </#list>

            <#-- 固定四行渲染 -->
            <#list 0..3 as i>
                <tr>
                    <#if i == 0>
                        <th rowspan="4">GCI</th>
                    </#if>
                    <td>
                        <#if i == 0>(i)
                        <#elseif i == 1>(ii)
                        <#elseif i == 2>(iii)
                        <#elseif i == 3>(iv)
                        </#if>
                    </td>

                    <#-- 判断当前行是否有数据 -->
                    <#if gtlData?has_content && i < gtlData?size>
                        <#assign info = gtlData[i]>
                        <td class="text-left">${info.category_of_employees_occupation!}</td>
                        <td>${info.basis_of_coverage_sum_insured!}</td>
                        <td>${info.no_of_employees!}</td>
                    <#else>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </#if>
                </tr>
            </#list>

            <#-- 如果数据超过四条，显示附件提示 -->
            <#if gtlData?has_content && 4 < gtlData?size>
                <tr>
                    <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                </tr>
            </#if>
        </table>

        <!-- GPA 表格 -->
        <table class="mb-8 common-table text-center">
            <colgroup>
                <col style="width: 100px">
                <col style="width: 50px">
                <col>
                <col span="2" style="width: 250px;">
            </colgroup>
            <#-- 查找数据 -->
            <#assign gtlData = "">
            <#list personOverviews as personOverview>
                <#if personOverview.tag == "gpa" && personOverview.itemKey == "basis_of_coverage">
                    <#assign gtlData = personOverview.valueMaps>
                </#if>
            </#list>

            <#-- 固定四行渲染 -->
            <#list 0..3 as i>
                <tr>
                    <#if i == 0>
                        <th rowspan="4">GPA</th>
                    </#if>
                    <td>
                        <#if i == 0>(i)
                        <#elseif i == 1>(ii)
                        <#elseif i == 2>(iii)
                        <#elseif i == 3>(iv)
                        </#if>
                    </td>

                    <#-- 判断当前行是否有数据 -->
                    <#if gtlData?has_content && i < gtlData?size>
                        <#assign info = gtlData[i]>
                        <td class="text-left">${info.category_of_employees_occupation!}</td>
                        <td>${info.basis_of_coverage_sum_insured!}</td>
                        <td>${info.no_of_employees!}</td>
                    <#else>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </#if>
                </tr>
            </#list>

            <#-- 如果数据超过四条，显示附件提示 -->
            <#if gtlData?has_content && 4 < gtlData?size>
                <tr>
                    <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                </tr>
            </#if>
        </table>

        <table class="mb-8 padding" cellspacing="0">
            <tr style="text-align: left;">
                <th></th>
                <th style="width: 250px">Example 1 </th>
                <th style="width: 100px;">Example 2</th>
            </tr>
            <tr>
                <th style="text-align: left;">Category of employees or occupation</th>
                <th colspan="2">Basis of cover – sum assured</th>
            </tr>
            <tr>
                <td>(i) Senior Management (Director, General Manager, Senior Manager)</td>
                <td>S$100,000</td>
                <td>24 x BMS <small><sup>#</sup></small></td>
            </tr>
            <tr>
                <td>(ii) All others</td>
                <td>S$25,000</td>
                <td>12 x BMS  <small><sup>#</sup></small></td>
            </tr>
        </table>
        <p class="mb-8" style="margin: 30px 0;"><small><sup>#</sup></small> Please provide salary information if the basis of cover is in terms of Basic Monthly Salary (BMS).</p>
        <div class="mb-8">
            <table class="q-title-table">
                <tr>
                    <td class="font-bold">(b)</td>
                    <td class="font-bold">Are there any members with sum assured exceeding S$2 million?</td>
                    <td>
                        <label for="myCheckbox1" class="label-item"><input type="checkbox" id="myCheckbox1" name="myCheckbox1">Yes</label>
                        <label for="myCheckbox2" class="ml-30 label-item"><input type="checkbox" id="myCheckbox2" name="myCheckbox2">No</label>
                    </td>
                </tr>
            </table>
        </div>
        <table class="mb-36" cellspacing="0">
            <tr>
                <td style="width: 280px;">If “Yes”, please provide details on:</td>
                <td style="width: 50px">(i)</td>
                <td>
                    Number of members
                    <div class="table-input table-text inline-block" style="width: 314px;"></div>
                </td>
            </tr>
            <tr>
                <td></td>
                <td>(ii)</td>
                <td>
                    Age of members
                    <div class="table-input table-text inline-block" style="width: 344px;"></div>
                </td>
            </tr>
            <tr>
                <td></td>
                <td>(iii)</td>
                <td>
                    Individual sum assured
                    <div class="table-input table-text inline-block" style="width: 300px;"></div>
                </td>
            </tr>
        </table>
        <div class="page-footer">
            <div class="page-info">INCOME/GB/GIFFF/07/2024 • Page 4 of 12</div>
        </div>
    </div>
    <!-- 第五页 -->
    <div class="page">
        <div class="mb-8">
            <strong>(c) Please provide current non-medical limit (if applicable)</strong>
        </div>
        <table class="mb-36 nowrap" cellspacing="0">
            <tr>
                <td>Group Term Life:</td>
                <td class="text-right">
                    S$
                    <div class="table-input table-text text-center" style="width: 200px">
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gtl" && personOverview.itemKey == "current_non_medical_limit">
                                ${personOverview.value!}
                            </#if>
                        </#list>
                    </div>
                    up to age
                    <div class="table-input table-text text-center" style="width: 200px">
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gtl" && personOverview.itemKey == "up_to_age">
                                ${personOverview.value!}
                            </#if>
                        </#list>
                    </div>
                </td>
            </tr>
            <tr>
                <td>Group Critical Illness:</td>
                <td class="text-right">
                    S$
                    <div class="table-input table-text text-center" style="width: 200px">
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gci" && personOverview.itemKey == "current_non_medical_limit">
                                ${personOverview.value!}
                            </#if>
                        </#list>
                    </div>
                    up to age
                    <div class="table-input table-text text-center" style="width: 200px">
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gci" && personOverview.itemKey == "up_to_age">
                                ${personOverview.value!}
                            </#if>
                        </#list>
                    </div>
                </td>
            </tr>
        </table>
        <div class="mb-8">
            <strong>(d) Group Critical Illness: Basis of cover</strong>
        </div>
         <table class="mb-36" cellspacing="0">
             <tr>
                 <td style="width: 600px;">Is this an accelerated or additional benefit to the Group Term Life?</td>
                 <#assign personOverviewValue = "">
                 <#list personOverviews as personOverview>
                     <#if personOverview.tag == "gci" && personOverview.itemKey == "amount_to_the_term_life_option">
                         <#assign personOverviewValue = (personOverview.value)!"" >
                     </#if>
                 </#list>
                 <td>
                     <label for="myCheckbox1" class="label-item">
                         <input type="checkbox" id="myCheckbox1" name="myCheckbox1"
                         <#if personOverviewValue == "Advanced">checked</#if>>
                         Accelerated
                     </label>
                     <label for="myCheckbox2" class="label-item">
                         <input type="checkbox" id="myCheckbox2" name="myCheckbox2"
                         <#if personOverviewValue == "Additional">checked</#if>>
                         Additional
                     </label>
                 </td>
             </tr>
             <tr>
                 <td>
                     If it is an accelerated benefit, please indicate the percentage of acceleration on the Group Term Life sum assured.
                 </td>
                 <td>
                     <#assign personOverviewValue = "">
                     <#list personOverviews as personOverview>
                         <#if personOverview.tag == "gci" && personOverview.itemKey == "acceleration_percentage_on_sum_assured">
                             <#assign personOverviewValue = (personOverview.value)!"" >
                         </#if>
                     </#list>

                     <label for="myCheckbox1" class="label-item">
                         <input type="checkbox" id="myCheckbox1" name="myCheckbox1"
                          <#if personOverviewValue == "25%">checked</#if>>
                         25%
                     </label>

                     <label for="myCheckbox2" class="label-item">
                         <input type="checkbox" id="myCheckbox2" name="myCheckbox2"
                         <#if personOverviewValue == "50%">checked</#if>>
                         50%
                     </label>

                     <label for="myCheckbox3" class="label-item">
                         <input type="checkbox" id="myCheckbox3" name="myCheckbox3"
                         <#if personOverviewValue == "100%">checked</#if>>
                         100%
                     </label>
                 </td>
             </tr>
             <tr>
                 <td>Please provide a list of critical illnesses covered (if currently insured)</td>
                 <td></td>
             </tr>
        </table>
        <div class="mb-36">
            <strong>(e) Details of employees</strong>
        </div>
        <table class="common-table text-center">
            <tr>
                <th class="border-none"></th>
                <th colspan="4">GTL</th>
                <th colspan="4">GCI (additional)</th>
            </tr>
            <tr>
                <th rowspan="2">Age Band <br>(age next <br>birthday)</th>
                <th colspan="2">Number of employees</th>
                <th colspan="2">Total Sum assured (S$)</th>
                <th colspan="2">Number of employees</th>
                <th colspan="2">Total Sum assured (S$)</th>
            </tr>
            <tr>
                <th>Male</th>
                <th>Female</th>
                <th>Male</th>
                <th>Female</th>
                <th>Male</th>
                <th>Female</th>
                <th>Male</th>
                <th>Female</th>
            </tr>

            <#-- 固定年龄段列表 -->
            <#assign ageBands = ["16-30","31-35","36-40","41-45","46-50","51-55","56-60","61-65","66-70","71-75","Above 75", "Total"]>

            <#list ageBands as ageBand>
                <tr>
                    <th>${ageBand}</th>

                    <#-- 查找 GTL 数据 -->
                    <#assign gtlFound = false>
                    <#list personOverviews?filter(p -> p.tag=="gtl" && p.itemKey=="age_profile_of_employees") as gtlOverview>
                        <#list gtlOverview.valueMaps as info>
                            <#if info.age_band == ageBand>
                                <#assign gtlFound = true>
                                <td>${info.no_of_employees_male!""}</td>
                                <td>${info.no_of_employees_female!""}</td>
                                <td>${info.total_sum_insured_male!""}</td>
                                <td>${info.total_sum_insured_female!""}</td>
                            </#if>
                        </#list>
                    </#list>
                    <#if !gtlFound>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </#if>

                    <#-- 查找 GCI 数据 -->
                    <#assign gciFound = false>
                    <#list personOverviews?filter(p -> p.tag=="gci" && p.itemKey=="age_profile_of_employees") as gciOverview>
                        <#list gciOverview.valueMaps as info>
                            <#if info.age_band == ageBand>
                                <#assign gciFound = true>
                                <td>${info.no_of_employees_male!""}</td>
                                <td>${info.no_of_employees_female!""}</td>
                                <td>${info.total_sum_insured_male!""}</td>
                                <td>${info.total_sum_insured_female!""}</td>
                            </#if>
                        </#list>
                    </#list>
                    <#if !gciFound>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </#if>

                </tr>
            </#list>
        </table>
        <div class="page-footer">
            <div class="page-info">INCOME/GB/GIFFF/07/2024 • Page 5 of 12</div>
        </div>
    </div>
    <!-- 第六页 -->
    <div class="page">
        <div class="mb-8">
            <strong>(f) Claims experience for the past three years</strong>
        </div>
        <p class="mb-36">Income reserves the right to request for more information</p>
        <div class="mb-36"><strong>GTL</strong></div>
        <table class="mb-36 common-table text-center">
            <tr>
                <th rowspan="2">Period of insurance <br>(dd/mm/yyyy)</th>
                <th rowspan="2">Number of insured as at <br> <span class="line-4em"></span> (dd/mm/yyyy)</th>
                <th colspan="2">Paid claims</th>
                <th colspan="2">Outstanding claims</th>
            </tr>
            <tr>
                <th>Number of claims</th>
                <th>Amount (S$)</th>
                <th>Number of claims</th>
                <th>Amount (S$)</th>
            </tr>
            <#assign fixedRows = 3>

            <#if isUploadClaimAttach?? && isUploadClaimAttach>
                <tr>
                    <td>Please refer to the attachment</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <#list 1..(fixedRows - 1) as i>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#list>
            <#else>
                <#assign displayedCount = 0>
                <#list claimLogs?keys as key>
                    <#if key == "GTL">
                        <#assign gpclaimList = claimLogs[key]>
                        <#list gpclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime!}</td>
                                    <td>${claim.endTime!}</td>
                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>
                <#if displayedCount == 0>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#if>
            </#if>
        </table>
        <div class="mb-36"><strong>GCI</strong></div>
        <table class="mb-36 common-table text-center">
            <tr>
                <th rowspan="2">Period of insurance <br> (dd/mm/yyyy)</th>
                <th rowspan="2">Number of insured as at <br> <span class="line-4em"></span> (dd/mm/yyyy)</th>
                <th colspan="2">Paid claims</th>
                <th colspan="2">Outstanding claims</th>
            </tr>
            <tr>
                <th>Number of claims</th>
                <th>Amount (S$)</th>
                <th>Number of claims</th>
                <th>Amount (S$)</th>
            </tr>
            <#assign fixedRows = 3>

            <#if isUploadClaimAttach?? && isUploadClaimAttach>
                <tr>
                    <td>Please refer to the attachment</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <#list 1..(fixedRows - 1) as i>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#list>
            <#else>
                <#assign displayedCount = 0>
                <#list claimLogs?keys as key>
                    <#if key == "GCI">
                        <#assign gpclaimList = claimLogs[key]>
                        <#list gpclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime!}</td>
                                    <td>${claim.endTime!}</td>
                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>
                <#if displayedCount == 0>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#if>
            </#if>
        </table>
        <div class="mb-36"><strong>GPA</strong></div>
        <table class="mb-36 common-table text-center">
            <tr>
                <th rowspan="2">Period of insurance <br>(dd/mm/yyyy)</th>
                <th rowspan="2">Number of insured as at <br> <span class="line-4em"></span> (dd/mm/yyyy)</th>
                <th colspan="2">Paid claims</th>
                <th colspan="2">Outstanding claims</th>
            </tr>
            <tr>
                <th>Number of claims</th>
                <th>Amount (S$)</th>
                <th>Number of claims</th>
                <th>Amount (S$)</th>
            </tr>
            <#assign fixedRows = 3>

            <#if isUploadClaimAttach?? && isUploadClaimAttach>
                <tr>
                    <td>Please refer to the attachment</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <#list 1..(fixedRows - 1) as i>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#list>
            <#else>
                <#assign displayedCount = 0>
                <#list claimLogs?keys as key>
                    <#if key == "GPA">
                        <#assign gpclaimList = claimLogs[key]>
                        <#list gpclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime!}</td>
                                    <td>${claim.endTime!}</td>
                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>
                <#if displayedCount == 0>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#if>
            </#if>
        </table>

        <div class="page-footer">
            <div class="page-info">INCOME/GB/GIFFF/07/2024 • Page 6 of 12</div>
        </div>
    </div>
    <!-- 第七页 -->
    <div class="page">
        <div class="t-center t-bg font14 mb-36">
            Benefit: Group Hospital and Surgical/Group Major Medical
        </div>
        <div class="mb-8">
            <strong>(a) Basis of cover</strong>
        </div>
        <table class="mb-36 common-table text-center">
            <colgroup>
                <col style="width: 40px">
                <col style="width: 300px;">
            </colgroup>
            <tr class="text-left">
                <th colspan="2">Category of Employees / Occupation</th>
                <th>Room & Board Benefit Plan (S$)</th>
                <th>Currently with TMIS Yes / No</th>
                <th>Proposal with TMIS Yes / No</th>
                <th>Medical Insurance for S Pass and Work Permit holders Yes / No</th>
            </tr>

            <#-- 定义数据列表，避免空指针 -->
            <#assign filteredList = []>
            <#list personOverviews as personOverview>
                <#if personOverview.tag == "ghs" && personOverview.itemKey == "basis_of_coverage">
                    <#assign filteredList = personOverview.valueMaps![]>
                </#if>
            </#list>

            <#-- 固定输出3行，不足补空 -->
            <#list 0..2 as idx>
                <tr>
                    <#if idx == 0>
                        <td>(i)</td>
                    <#elseif idx == 1>
                        <td>(ii)</td>
                    <#elseif idx == 2>
                        <td>(iii)</td>
                    </#if>

                    <#if idx lt filteredList?size>
                        <td class="text-left">${filteredList[idx].category_of_employees_occupation!""}</td>
                        <td>${filteredList[idx].room_and_board_benefit_plan!""}</td>
                        <td>
                            <#if filteredList[idx].currently_with_tmis?string == "1">
                                Yes
                            <#elseif filteredList[idx].currently_with_tmis?string == "0">
                                No
                            <#else>
                                &nbsp;
                            </#if>
                        </td>

                        <td>
                            <#if filteredList[idx].proposal_with_tmis?string == "1">
                                Yes
                            <#elseif filteredList[idx].proposal_with_tmis?string == "0">
                                No
                            <#else>
                                &nbsp;
                            </#if>
                        </td>

                        <td>
                            <#if filteredList[idx].medical_insurance_for_pass_and_work?string == "1">
                                Yes
                            <#elseif filteredList[idx].medical_insurance_for_pass_and_work?string == "0">
                                No
                            <#else>
                                &nbsp;
                            </#if>
                        </td>
                    <#else>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </#if>
                </tr>
            </#list>

            <#-- 如果有超过3行数据，显示提示 -->
            <#if filteredList?size gt 3>
                <tr>
                    <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                </tr>
            </#if>
        </table>
        <div class="mb-8"><strong>Important note:</strong></div>
        <p>(1) Dependants can be covered under Group Hospital and Surgical plan. Their cover should be the same as the employee's cover.</p>
        <p class="mb-36">(2) Please provide the deductible or co-insurance for respective employee category or occupation, if applicable.</p>
        <table class="mb-36 padding">
            <tr style="text-align: left;">
                <th></th>
                <th style="width: 250px">Example 1 </th>
                <th style="width: 100px;">Example 2</th>
            </tr>
            <tr>
                <th style="text-align: left;">Category of employees or occupation</th>
                <th colspan="2" style="text-align: left; padding-left: 3em;">Room and board benefit plan (S$)</th>
            </tr>
            <tr>
                <td>(i) Senior Management (Director, General Manager, Senior Manager)</td>
                <td>360</td>
                <td>1 bedded</td>
            </tr>
            <tr>
                <td>(ii) Manager and Executive</td>
                <td>200</td>
                <td>4 bedded</td>
            </tr>
            <tr>
                <td>(iii) All others</td>
                <td>100</td>
                <td>6 bedded</td>
            </tr>
        </table>
        <div class="mb-8">
            <strong>(b) Age profile of employees</strong>
        </div>
        <table class="border collapse text-center">
            <tr>
                <th rowspan="2" class="text-left" style="width: 40%">Age Band (age next birthday)</th>
                <th colspan="2">Number of employees</th>
            </tr>
            <tr>
                <th>Male</th>
                <th>Female</th>
            </tr>

            <#-- 固定年龄段列表 -->
            <#assign ageBands = ["16-30","31-35","36-40","41-45","46-50","51-55","56-60","61-65","66-70","71-75","Above 75","Total"]>

            <#-- 查找数据 -->
            <#assign ghsData = "">
            <#list personOverviews as personOverview>
                <#if personOverview.tag=="ghs" && personOverview.itemKey=="age_profile_of_employees">
                    <#assign ghsData = personOverview.valueMaps>
                </#if>
            </#list>

            <#-- 渲染每个年龄段 -->
            <#list ageBands as ageBand>
                <tr>
                    <th class="text-left">${ageBand}</th>

                    <#-- 判断当前年龄段是否有数据 -->
                    <#assign found = false>
                    <#if ghsData?has_content>
                        <#list ghsData as info>
                            <#if info.age_band == ageBand>
                                <td>${info.no_of_employees_male!}</td>
                                <td>${info.no_of_employees_female!}</td>
                                <#assign found = true>
                            </#if>
                        </#list>
                    </#if>

                    <#-- 如果没找到数据，显示空格 -->
                    <#if !found>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </#if>
                </tr>
            </#list>
        </table>

        <div class="page-footer">
            <div class="page-info">INCOME/GB/GIFFF/07/2024 • Page 7 of 12</div>
        </div>
    </div>
    <!-- 第八页 -->
    <div class="page page-eight">
        <div class="mb-8">
            <strong>(c) Details of insured members</strong>
        </div>
        <div class="underline-bold">
            <strong>For GHS and GMM</strong>
        </div>
        <table class="common-table text-center">
            <colgroup>
                <col style="width: 30%;">
                <col style="width: 17.5%;">
                <col style="width: 17.5%;">
                <col style="width: 17.5%;">
                <col style="width: 17.5%;">
            </colgroup>
            <tr>
                <th rowspan="2" class="border-none"></th>
                <th colspan="4">Number of employees (Singaporeans and SPRs¹)</th>
            </tr>

            <#-- 收集所有 no_of_employees_plan 值 -->
            <#assign noOfEmployeesPlanList = []>
            <#list personOverviews as personOverview>
                <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_sgp">
                    <#if personOverview.valueMaps?has_content>
                        <#list personOverview.valueMaps as info>
                            <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                            </#if>
                        </#list>
                    </#if>
                </#if>
            </#list>

            <tr>
                <#-- 固定四列，如果数据不足显示空格 -->
                <#list 0..3 as i>
                    <th>
                        <#if i < noOfEmployeesPlanList?size>
                            ${noOfEmployeesPlanList[i]}
                        <#else>
                            &nbsp;
                        </#if>
                    </th>
                </#list>
            </tr>

            <#-- 固定四行员工类型 -->
            <#assign employeeTypes = ["Employee only", "Employee and spouse", "Employee and children", "Employee and family"]>

            <#list employeeTypes as type>
                <tr>
                    <th class="text-left">${type}</th>
                    <#list 0..3 as i>
                        <td>
                            <#-- 尝试获取对应数据，如果不存在则显示空格 -->
                            <#assign value = "">
                            <#list personOverviews as personOverview>
                                <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_sgp">
                                    <#if personOverview.valueMaps?has_content && i < personOverview.valueMaps?size>
                                        <#if type == "Employee only">
                                            <#assign value = (personOverview.valueMaps[i].employee_only)!"" >
                                        <#elseif type == "Employee and spouse">
                                            <#assign value = (personOverview.valueMaps[i].employee_and_spouse)!"" >
                                        <#elseif type == "Employee and children">
                                            <#assign value = (personOverview.valueMaps[i].employee_and_children)!"" >
                                        <#elseif type == "Employee and family">
                                            <#assign value = (personOverview.valueMaps[i].employee_and_family)!"" >
                                        </#if>
                                    </#if>
                                </#if>
                            </#list>
                            ${value?if_exists! "&nbsp;"}
                        </td>
                    </#list>
                </tr>
            </#list>

            <#-- 如果列数超过四，显示附件提示 -->
            <#if 4 < noOfEmployeesPlanList?size>
                <tr>
                    <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                </tr>
            </#if>
        </table>

        <p class="mb-36"> <small style="padding-right: 20px;"><sup>1</sup></small> refers to Singapore Permanent Residents</p>
        <table class="common-table text-center">
            <colgroup>
                <col style="width: 30%;">
                <col style="width: 17.5%;">
                <col style="width: 17.5%;">
                <col style="width: 17.5%;">
                <col style="width: 17.5%;">
            </colgroup>
            <tr>
                <th rowspan="2" class="border-none"></th>
                <th colspan="4">Number of employees (foreigners² only)</th>
            </tr>
            <#-- 收集所有 no_of_employees_plan 值 -->
            <#assign noOfEmployeesPlanList = []>
            <#list personOverviews as personOverview>
                <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_foreigners">
                    <#if personOverview.valueMaps?has_content>
                        <#list personOverview.valueMaps as info>
                            <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                            </#if>
                        </#list>
                    </#if>
                </#if>
            </#list>

            <tr>
                <#-- 固定四列，如果数据不足显示空格 -->
                <#list 0..3 as i>
                    <th>
                        <#if i < noOfEmployeesPlanList?size>
                            ${noOfEmployeesPlanList[i]}
                        <#else>
                            &nbsp;
                        </#if>
                    </th>
                </#list>
            </tr>

            <#-- 固定四行员工类型 -->
            <#assign employeeTypes = ["Employee only", "Employee and spouse", "Employee and children", "Employee and family"]>

            <#list employeeTypes as type>
                <tr>
                    <th class="text-left">${type}</th>
                    <#list 0..3 as i>
                        <td>
                            <#-- 尝试获取对应数据，如果不存在则显示空格 -->
                            <#assign value = "">
                            <#list personOverviews as personOverview>
                                <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_foreigners">
                                    <#if personOverview.valueMaps?has_content && i < personOverview.valueMaps?size>
                                        <#if type == "Employee only">
                                            <#assign value = (personOverview.valueMaps[i].employee_only)!"" >
                                        <#elseif type == "Employee and spouse">
                                            <#assign value = (personOverview.valueMaps[i].employee_and_spouse)!"" >
                                        <#elseif type == "Employee and children">
                                            <#assign value = (personOverview.valueMaps[i].employee_and_children)!"" >
                                        <#elseif type == "Employee and family">
                                            <#assign value = (personOverview.valueMaps[i].employee_and_family)!"" >
                                        </#if>
                                    </#if>
                                </#if>
                            </#list>
                            ${value?if_exists! "&nbsp;"}
                        </td>
                    </#list>
                </tr>
            </#list>

            <#-- 如果列数超过四，显示附件提示 -->
            <#if 4 < noOfEmployeesPlanList?size>
                <tr>
                    <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                </tr>
            </#if>
        </table>

        <p class="mb-36"> <small style="padding-right: 20px;"><sup>2</sup></small> refers to all foreigners holding Employment Pass, S Pass and work permit, working in Singapore</p>
         <div class="underline-bold">
            <strong>For GMM (if the basis of coverage differs from GHS)</strong>
        </div>
        <table class="common-table text-center">
            <colgroup>
                <col style="width: 30%;">
                <col style="width: 17.5%;">
                <col style="width: 17.5%;">
                <col style="width: 17.5%;">
                <col style="width: 17.5%;">
            </colgroup>
            <tr>
                <th rowspan="2" class="border-none"></th>
                <th colspan="4">Number of employees (Singaporeans and SPRs¹)</th>
            </tr>

            <#assign sameToGhs = false>
            <#list personOverviews as personOverview>
                <#if personOverview.tag == "gmm" && personOverview.itemKey == "same_to_the_ghs" && personOverview.value?string == "1">
                    <#assign sameToGhs = true>
                </#if>
            </#list>

            <#-- 收集 gmm 数据 -->
            <#assign noOfEmployeesPlanList = []>
            <#if !sameToGhs>
                <#list personOverviews as personOverview>
                    <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_sgp">
                        <#if personOverview.valueMaps?has_content>
                            <#list personOverview.valueMaps as info>
                                <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                    <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                </#if>
                            </#list>
                        </#if>
                    </#if>
                </#list>
            </#if>

            <tr>
                <#-- 固定四列，如果没有数据或 same_to_the_ghs 为 1，则显示空格 -->
                <#list 0..3 as i>
                    <th>
                        <#if !sameToGhs && i < noOfEmployeesPlanList?size>
                            ${noOfEmployeesPlanList[i]}
                        <#else>
                            &nbsp;
                        </#if>
                    </th>
                </#list>
            </tr>

            <#-- 固定四行员工类型 -->
            <#assign employeeTypes = ["Employee only", "Employee and spouse", "Employee and children", "Employee and family"]>

            <#list employeeTypes as type>
                <tr>
                    <th class="text-left">${type}</th>
                    <#list 0..3 as i>
                        <td>
                            <#if sameToGhs>
                                &nbsp;
                            <#else>
                                <#-- 尝试获取对应数据，如果不存在则显示空格 -->
                                <#assign value = "">
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_sgp">
                                        <#if personOverview.valueMaps?has_content && i < personOverview.valueMaps?size>
                                            <#if type == "Employee only">
                                                <#assign value = (personOverview.valueMaps[i].employee_only)!"" >
                                            <#elseif type == "Employee and spouse">
                                                <#assign value = (personOverview.valueMaps[i].employee_and_spouse)!"" >
                                            <#elseif type == "Employee and children">
                                                <#assign value = (personOverview.valueMaps[i].employee_and_children)!"" >
                                            <#elseif type == "Employee and family">
                                                <#assign value = (personOverview.valueMaps[i].employee_and_family)!"" >
                                            </#if>
                                        </#if>
                                    </#if>
                                </#list>
                                ${value?if_exists! "&nbsp;"}
                            </#if>
                        </td>
                    </#list>
                </tr>
            </#list>

            <#-- 如果列数超过四，显示附件提示 -->
            <#if !sameToGhs && 4 < noOfEmployeesPlanList?size>
                <tr>
                    <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                </tr>
            </#if>
        </table>
        <p class="mb-36"> <small style="padding-right: 20px;"><sup>1</sup></small>refers to Singapore Permanent Residents</p>
        <table class="common-table text-center">
            <colgroup>
                <col style="width: 30%;">
                <col style="width: 17.5%;">
                <col style="width: 17.5%;">
                <col style="width: 17.5%;">
                <col style="width: 17.5%;">
            </colgroup>
            <tr>
                <th rowspan="2" class="border-none"></th>
                <th colspan="4">Number of employees (foreigners² only)</th>
            </tr>
            <#assign sameToGhs = false>
            <#list personOverviews as personOverview>
                <#if personOverview.tag == "gmm" && personOverview.itemKey == "same_to_the_ghs" && personOverview.value?string == "1">
                    <#assign sameToGhs = true>
                </#if>
            </#list>

            <#-- 收集 gmm 数据 -->
            <#assign noOfEmployeesPlanList = []>
            <#if !sameToGhs>
                <#list personOverviews as personOverview>
                    <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_foreigners">
                        <#if personOverview.valueMaps?has_content>
                            <#list personOverview.valueMaps as info>
                                <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                    <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                </#if>
                            </#list>
                        </#if>
                    </#if>
                </#list>
            </#if>

            <tr>
                <#-- 固定四列，如果没有数据或 same_to_the_ghs 为 1，则显示空格 -->
                <#list 0..3 as i>
                    <th>
                        <#if !sameToGhs && i < noOfEmployeesPlanList?size>
                            ${noOfEmployeesPlanList[i]}
                        <#else>
                            &nbsp;
                        </#if>
                    </th>
                </#list>
            </tr>

            <#-- 固定四行员工类型 -->
            <#assign employeeTypes = ["Employee only", "Employee and spouse", "Employee and children", "Employee and family"]>

            <#list employeeTypes as type>
                <tr>
                    <th class="text-left">${type}</th>
                    <#list 0..3 as i>
                        <td>
                            <#if sameToGhs>
                                &nbsp;
                            <#else>
                                <#-- 尝试获取对应数据，如果不存在则显示空格 -->
                                <#assign value = "">
                                <#list personOverviews as personOverview>
                                    <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_foreigners">
                                        <#if personOverview.valueMaps?has_content && i < personOverview.valueMaps?size>
                                            <#if type == "Employee only">
                                                <#assign value = (personOverview.valueMaps[i].employee_only)!"" >
                                            <#elseif type == "Employee and spouse">
                                                <#assign value = (personOverview.valueMaps[i].employee_and_spouse)!"" >
                                            <#elseif type == "Employee and children">
                                                <#assign value = (personOverview.valueMaps[i].employee_and_children)!"" >
                                            <#elseif type == "Employee and family">
                                                <#assign value = (personOverview.valueMaps[i].employee_and_family)!"" >
                                            </#if>
                                        </#if>
                                    </#if>
                                </#list>
                                ${value?if_exists! "&nbsp;"}
                            </#if>
                        </td>
                    </#list>
                </tr>
            </#list>

            <#-- 如果列数超过四，显示附件提示 -->
            <#if !sameToGhs && 4 < noOfEmployeesPlanList?size>
                <tr>
                    <td colspan="5" class="text-left italic">More information, please refer to the attachment.</td>
                </tr>
            </#if>
        </table>
        <p class="mb-36"> <small style="padding-right: 20px;"><sup>2</sup></small>refers to all foreigners holding Employment Pass, S Pass and work permit, working in Singapore</p>
        <div class="mb-8">
            <strong>(d) Claims experience for the past three years</strong>
        </div>
        <table class="mb-36 common-table text-center">
            <tr>
                <th rowspan="2">Period of insurance <br> (dd/mm/yyyy)</th>
                <th rowspan="2">Number of insured as at <br> <span class="line-4em"></span> (dd/mm/yyyy)</th>
                <th colspan="2">Paid claims</th>
                <th colspan="2">Outstanding claims</th>
            </tr>
            <tr>
                <th>Number of claims</th>
                <th>Amount (S$)</th>
                <th>Number of claims</th>
                <th>Amount (S$)</th>
            </tr>
            <#assign fixedRows = 3>

            <#if isUploadClaimAttach?? && isUploadClaimAttach>
                <tr>
                    <td>Please refer to attachment</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <#list 1..(fixedRows - 1) as i>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#list>
            <#else>
                <#assign displayedCount = 0>
                <#list claimLogs?keys as key>
                    <#if key == "GHS">
                        <#assign gpclaimList = claimLogs[key]>
                        <#list gpclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime!}</td>
                                    <td>${claim.endTime!}</td>
                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>
                <#if displayedCount == 0>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#if>
            </#if>
        </table>

        <p class="mb-36">Note: Income reserves the right to request for more information</p>
        <div class="mb-8">
            <strong>(e) Please attach a copy of the Schedule of Benefits, if currently insured.</strong>
        </div>
        <div class="page-footer">
            <div class="page-info">INCOME/GB/GIFFF/07/2024 • Page 8 of 12</div>
        </div>
    </div>
    <!-- 第九页 -->
    <div class="page">
        <div class="t-center t-bg font14 mb-36">
            Benefit: Group Outpatient
        </div>
        <div class="mb-8">
            <strong>(a) Category of employees to be insured (please tick as appropriate)</strong>
        </div>
        <table class="mb-36 border collapse text-center">
            <tr>
                <th colspan="2" class="text-left">Category of Employees</th>
                <th>Clinical General <br> Practitioner</th>
                <th>Specialist</th>
                <th>Diagnostic X-ray or <br> laboratory test</th>
                <th>Dental</th>
            </tr>

            <#-- 去重categoryEmployee -->
            <#assign uniqueCategories = []>
            <#list userPlanDutyList as item>
                <#if !(uniqueCategories?seq_contains(item.categoryEmployee))>
                    <#assign uniqueCategories = uniqueCategories + [item.categoryEmployee]>
                </#if>
            </#list>

            <#-- 序号映射 -->
            <#assign romanNumerals = ["(i)", "(ii)", "(iii)"]>

            <#-- 遍历去重后的类别，最多显示三行 -->
            <#list 0..2 as i>
                <tr>
                    <#if i < uniqueCategories?size>
                        <#assign category = uniqueCategories[i]>

                        <!-- 第一列序号 -->
                        <td>${romanNumerals[i]}</td>
                        <!-- 第二列员工类别 -->
                        <td class="text-left">${category}</td>

                        <!-- 判断GP列是否勾选 -->
                        <#assign gpChecked = false>
                        <#list userPlanDutyList as item>
                            <#if item.categoryEmployee == category && item.benefit == "GP">
                                <#assign gpChecked = true>
                            </#if>
                        </#list>
                        <td><input type="checkbox" <#if gpChecked>checked</#if>></td>

                        <!-- 判断SP列是否勾选 -->
                        <#assign spChecked = false>
                        <#list userPlanDutyList as item>
                            <#if item.categoryEmployee == category && item.benefit == "SP">
                                <#assign spChecked = true>
                            </#if>
                        </#list>
                        <td><input type="checkbox" <#if spChecked>checked</#if>></td>

                        <!-- Diag X-Ray/Lab Tests 空列 -->
                        <td><input type="checkbox"></td>

                        <!-- 判断GD列是否勾选 -->
                        <#assign gdChecked = false>
                        <#list userPlanDutyList as item>
                            <#if item.categoryEmployee == category && item.benefit == "GD">
                                <#assign gdChecked = true>
                            </#if>
                        </#list>
                        <td><input type="checkbox" <#if gdChecked>checked</#if>></td>
                    <#else>
                        <!-- 空行补齐 -->
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </#if>
                </tr>
            </#list>

            <!-- 提示行 -->
            <#if 3 < uniqueCategories?size>
                <tr>
                    <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                </tr>
            </#if>

            <!-- Dependant行 -->
            <tr>
                <td colspan="2" class="text-left">Dependant (where applicable)</td>
                <#assign gpDependantsIncluded = "No">
                <#assign spDependantsIncluded = "No">
                <#assign gdDependantsIncluded = "No">
                <#assign gpHeadcount = "">
                <#assign spHeadcount = "">
                <#assign gdHeadcount = "">
                <#list personOverviews as personOverview>
                    <#if personOverview.itemKey == "dependants_included">
                        <#if personOverview.tag == "gp">
                            <#if (personOverview.value!"") == "1">
                                <#assign gpDependantsIncluded = "Yes">
                            </#if>
                        <#elseif personOverview.tag == "sp">
                            <#if (personOverview.value!"") == "1">
                                <#assign spDependantsIncluded = "Yes">
                            </#if>
                        <#elseif personOverview.tag == "gd">
                            <#if (personOverview.value!"") == "1">
                                <#assign gdDependantsIncluded = "Yes">
                            </#if>
                        </#if>
                    </#if>

                    <#if personOverview.itemKey == "no_of_headcount">
                        <#if personOverview.tag == "gp">
                            <#if personOverview.value??>
                                <#assign gpHeadcount = personOverview.value!>
                            </#if>
                        <#elseif personOverview.tag == "sp">
                            <#if personOverview.value??>
                                <#assign spHeadcount = personOverview.value!>
                            </#if>
                        <#elseif personOverview.tag == "gd">
                            <#if personOverview.value?? >
                                <#assign gdHeadcount = personOverview.value!>
                            </#if>
                        </#if>
                    </#if>
                </#list>

                <td>${gpDependantsIncluded}</td>
                <td>${spDependantsIncluded}</td>
                <td></td>
                <td>${gdDependantsIncluded}</td>
            </tr>
            <tr>
                <td colspan="2" class="text-left">Number of headcount</td>
                <td>${gpHeadcount}</td>
                <td>${spHeadcount}</td>
                <td></td>
                <td>${gdHeadcount}</td>
            </tr>
        </table>
        <div class="mb-8">
            <strong>(b) Age profile of employees</strong>
        </div>
        <table class="collapse border text-center">
            <tr>
                <th rowspan="2" class="text-left">Age Band (age next birthday)</th>
                <th colspan="2">Number of employees</th>
            </tr>
            <tr>
                <th>Male</th>
                <th>Female</th>
            </tr>
            <#-- 固定年龄段列表 -->
            <#assign ageBands = ["16-30","31-35","36-40","41-45","46-50","51-55","56-60","61-65","66-70","71-75","Above 75","Total"]>

            <#-- 查找数据 -->
            <#assign data = "">
            <#list personOverviews as personOverview>
                <#if personOverview.tag=="group_outpatient_insurance" && personOverview.itemKey=="age_profile_of_employees">
                    <#assign data = personOverview.valueMaps>
                </#if>
            </#list>

            <#-- 渲染每个年龄段 -->
            <#list ageBands as ageBand>
                <tr>
                    <th class="text-left">${ageBand}</th>

                    <#-- 判断当前年龄段是否有数据 -->
                    <#assign found = false>
                    <#if data?has_content>
                        <#list data as info>
                            <#if info.age_band == ageBand>
                                <td>${info.no_of_employees_male!}</td>
                                <td>${info.no_of_employees_female!}</td>
                                <#assign found = true>
                            </#if>
                        </#list>
                    </#if>

                    <#-- 如果没找到数据，显示空格 -->
                    <#if !found>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </#if>
                </tr>
            </#list>
        </table>
        <div class="page-footer">
            <div class="page-info">INCOME/GB/GIFFF/07/2024 • Page 9 of 12</div>
        </div>
    </div>
    <!-- 第十页 -->
    <div class="page">
        <div class="mb-8">
            <strong>(c) Claims experience for the past three years</strong>
        </div>
        <div class="underline-bold">
            <strong>Paid claims</strong>
        </div>
        <table class="common-table text-center">
            <colgroup>
                <col span="2" style="width: 18%;">
                <col span="8" style="width: 8%;">
            </colgroup>
            <tr>
                <th class="border-none"></th>
                <th class="border-none"></th>
                <th colspan="2">Clinical General <br> Practitioner</th>
                <th colspan="2">Specialist</th>
                <th colspan="2">Diagnostic X-ray or laboratory test</th>
                <th colspan="2">Dental</th>
            </tr>
            <tr>
                <th>Period of insurance <br>(dd/mm/yyyy)</th>
                <th>Number of insured as at <span class="line-4em"></span> <br> (dd/mm/yyyy)</th>
                <th>Number of visits</th>
                <th>Amount <br>(S$)</th>
                <th>Number of visits</th>
                <th>Amount <br>(S$)</th>
                <th>Number of visits</th>
                <th>Amount <br>(S$)</th>
                <th>Number of visits</th>
                <th>Amount <br>(S$)</th>
            </tr>
            <#if isUploadClaimAttach?? && isUploadClaimAttach>
                <tr>
                    <td>Please refer to attachment</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <#list 1..2 as i>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#list>
            </#if>

            <#assign displayedCount = 0>
            <#list claimLogs?keys as key>
                <#if key == "GP">
                    <#assign gpclaimList = claimLogs[key]>
                    <#list gpclaimList as claim>
                        <#if displayedCount < 3>
                            <tr>
                                <td>${claim.startTime}</td>
                                <td>${claim.endTime}</td>
                                <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                <#assign spclaimList = claimLogs["SP"]![]>
                                <#assign spshowflg = false>
                                <#list spclaimList as spclaim>
                                    <#if spclaim.startTime == claim.startTime>
                                        <#assign spshowflg = true>
                                        <td>${(spclaim.paidClaimsNum??)?then(spclaim.paidClaimsNum, "")}</td>
                                        <td>${(spclaim.paidAmount?? && spclaim.paidAmount.amount??)?then(spclaim.paidAmount.amount, "")}</td>
                                    </#if>
                                </#list>
                                <#if !spshowflg>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </#if>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <#assign gdclaimList = claimLogs["GD"]![]>
                                <#assign gdshowflg = false>
                                <#list gdclaimList as gdclaim>
                                    <#if gdclaim.startTime == claim.startTime>
                                        <#assign gdshowflg = true>
                                        <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                        <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                    </#if>
                                </#list>
                                <#if !gdshowflg>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </#if>
                            </tr>
                            <#assign displayedCount = displayedCount + 1>
                        <#else>
                            <tr>
                                <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                            </tr>
                            <#break>
                        </#if>
                    </#list>
                </#if>
            </#list>

            <#list claimLogs?keys as key>
                <#if key == "SP" && displayedCount < 3>
                    <#assign spclaimList = claimLogs[key]>
                    <#list spclaimList as claim>
                        <#if displayedCount < 3>
                            <tr>
                                <td>${claim.startTime}</td>
                                <td>${claim.endTime}</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <#assign gdclaimList = claimLogs["GD"]![]>
                                <#assign gdshowflg = false>
                                <#list gdclaimList as gdclaim>
                                    <#if gdclaim.startTime == claim.startTime>
                                        <#assign gdshowflg = true>
                                        <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                        <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                    </#if>
                                </#list>
                                <#if !gdshowflg>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </#if>
                            </tr>
                            <#assign displayedCount = displayedCount + 1>
                        <#else>
                            <tr>
                                <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                            </tr>
                            <#break>
                        </#if>
                    </#list>
                </#if>
            </#list>


            <#list claimLogs?keys as key>
                <#if key == "GD" && displayedCount < 3>
                    <#assign gdclaimList = claimLogs[key]>
                    <#list gdclaimList as claim>
                        <#if displayedCount < 3>
                            <tr>
                                <td>${claim.startTime}</td>
                                <td>${claim.endTime}</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                            </tr>
                            <#assign displayedCount = displayedCount + 1>
                        <#else>
                            <tr>
                                <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                            </tr>
                            <#break>
                        </#if>
                    </#list>
                </#if>
            </#list>
            <#if displayedCount < 3 && !isUploadClaimAttach>
                <#list displayedCount..2 as i>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#list>
            </#if>
        </table>
        <p> <small style="padding-right: 20px;"><sup>^</sup></small> all figures provided should include visits to non-panel clinics.</p>
        <p class="mb-36">Note: Income reserves the right to request for more information</p>
        <div class="underline-bold">
            <strong>Outstanding claims</strong>
        </div>
        <table class="common-table text-center">
            <colgroup>
                <col span="2" style="width: 18%;">
                <col span="8" style="width: 8%;">
            </colgroup>
            <tr>
                <th class="border-none"></th>
                <th class="border-none"></th>
                <th colspan="2">Clinical General Practitioner</th>
                <th colspan="2">Specialist</th>
                <th colspan="2">Diagnostic X-ray or laboratory test</th>
                <th colspan="2">Dental</th>
            </tr>
            <tr>
                <th>Period of insurance <br>(dd/mm/yyyy)</th>
                <th>Number of insured as at <span class="line-4em"></span> <br> (dd/mm/yyyy)</th>
                <th>Number of visits</th>
                <th>Amount <br>(S$)</th>
                <th>Number of visits</th>
                <th>Amount <br>(S$)</th>
                <th>Number of visits</th>
                <th>Amount <br>(S$)</th>
                <th>Number of visits</th>
                <th>Amount <br>(S$)</th>
            </tr>
            <#if isUploadClaimAttach?? && isUploadClaimAttach>
                <tr>
                    <td>Please refer to attachment</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                    <td>&nbsp;</td>
                </tr>
                <#list 1..2 as i>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#list>
            </#if>

            <#assign displayedCount = 0>
            <#list claimLogs?keys as key>
                <#if key == "GP">
                    <#assign gpclaimList = claimLogs[key]>
                    <#list gpclaimList as claim>
                        <#if displayedCount < 3>
                            <tr>
                                <td>${claim.startTime}</td>
                                <td>${claim.endTime}</td>
                                <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                <#assign spclaimList = claimLogs["SP"]![]>
                                <#assign spshowflg = false>
                                <#list spclaimList as spclaim>
                                    <#if spclaim.startTime == claim.startTime>
                                        <#assign spshowflg = true>
                                        <td>${(spclaim.outstandingClaimsNum??)?then(spclaim.outstandingClaimsNum, "")}</td>
                                        <td>${(spclaim.outstandingAmount?? && spclaim.outstandingAmount.amount??)?then(spclaim.outstandingAmount.amount, "")}</td>
                                    </#if>
                                </#list>
                                <#if !spshowflg>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </#if>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <#assign gdclaimList = claimLogs["GD"]![]>
                                <#assign gdshowflg = false>
                                <#list gdclaimList as gdclaim>
                                    <#if gdclaim.startTime == claim.startTime>
                                        <#assign gdshowflg = true>
                                        <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                        <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                    </#if>
                                </#list>
                                <#if !gdshowflg>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </#if>
                            </tr>
                            <#assign displayedCount = displayedCount + 1>
                        <#else>
                            <tr>
                                <td colspan="10" class="italic text-left">More information, please refer to the attachment.</td>
                            </tr>
                            <#break>
                        </#if>
                    </#list>
                </#if>
            </#list>

            <#list claimLogs?keys as key>
                <#if key == "SP" && displayedCount < 3>
                    <#assign spclaimList = claimLogs[key]>
                    <#list spclaimList as claim>
                        <#if displayedCount < 3>
                            <tr>
                                <td>${claim.startTime}</td>
                                <td>${claim.endTime}</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <#assign gdclaimList = claimLogs["GD"]![]>
                                <#assign gdshowflg = false>
                                <#list gdclaimList as gdclaim>
                                    <#if gdclaim.startTime == claim.startTime>
                                        <#assign gdshowflg = true>
                                        <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                        <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                    </#if>
                                </#list>
                                <#if !gdshowflg>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </#if>
                            </tr>
                            <#assign displayedCount = displayedCount + 1>
                        <#else>
                            <tr>
                                <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                            </tr>
                            <#break>
                        </#if>
                    </#list>
                </#if>
            </#list>

            <#list claimLogs?keys as key>
                <#if key == "GD" && displayedCount < 3>
                    <#assign gdclaimList = claimLogs[key]>
                    <#list gdclaimList as claim>
                        <#if displayedCount < 3>
                            <tr>
                                <td>${claim.startTime}</td>
                                <td>${claim.endTime}</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                            </tr>
                            <#assign displayedCount = displayedCount + 1>
                        <#else>
                            <tr>
                                <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                            </tr>
                            <#break>
                        </#if>
                    </#list>
                </#if>
            </#list>

            <#if displayedCount < 3 && !isUploadClaimAttach>
                <#list displayedCount..2 as i>
                    <tr>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                        <td>&nbsp;</td>
                    </tr>
                </#list>
            </#if>
        </table>
        <p> <small style="padding-right: 20px;"><sup>^</sup></small> all figures provided should include visits to non-panel clinics.</p>
        <p class="mb-36">Note: Income reserves the right to request for more information</p>
        <div class="mb-8">
            <strong>(d) Please attach a copy of the Schedule of Benefits, if currently insured.</strong>
        </div>
        <p>If currently self-insured, please provide the following details:</p>
        <p class="mb-36">Please indicate “Unlimited” if there is no cap and “NA” if it is Not Applicable.</p>
        <table class="collapse border text-center">
            <colgroup>
                <col style="width: 25%">
                <col span="6" style="width: 12.5%">
            </colgroup>
            <tr>
                <th rowspan="2" class="text-left">Benefits</th>
                <th colspan="2">Maximum limit per visit <br>(S$)</th>
                <th colspan="2">Maximum limit per policy <br>(S$)</th>
                <th colspan="2">Co-payment (S$) or co-insurance</th>
            </tr>
            <tr>
                <th>Clinic on company's panel</th>
                <th>Non-panel clinic</th>
                <th>Clinic on company's panel</th>
                <th>Non-panel clinic</th>
                <th>Clinic on company's panel</th>
                <th>Non-panel clinic</th>
            </tr>
            <tr>
                <th class="text-left">Clinical General Practitioner</th>
                <td colspan="2"></td>
                <td colspan="2"></td>
                <td colspan="2"></td>
            </tr>
           <tr>
                <th class="text-left">Specialist</th>
                <td colspan="2"></td>
                <td colspan="2"></td>
                <td colspan="2"></td>
            </tr>
            <tr>
                <th class="text-left">Diagnostic X-ray or laboratory tests</th>
                <td colspan="2"></td>
                <td colspan="2"></td>
                <td colspan="2"></td>
            </tr>
            <tr>
                <th class="text-left">Dental</th>
                <td colspan="2"></td>
                <td colspan="2"></td>
                <td colspan="2"></td>
            </tr>
            <tr>
                <th class="text-left">Others, please specify</th>
                <td colspan="2"></td>
                <td colspan="2"></td>
                <td colspan="2"></td>
            </tr>
        </table>
        <div class="page-footer">
            <div class="page-info">INCOME/GB/GIFFF/07/2024 • Page 10 of 12</div>
        </div>
    </div>
    <!-- 第十一页 -->
    <div class="page">
        <div class="t-center t-bg font14">
            Needs analysis and product recommendation
        </div>
        <div style="border: 1px solid #333;" class="mb-8">
            <table cellspacing="0">
            <tr class="text-left">
                <th style="width: 400px;" class="text-left">Company's Priorities</th>
                <th style="width: 40px;">Low</th>
                <th style="width: 40px;">Med</th>
                <th style="width: 40px;">High</th>
                <th class="text-left">Advisor's recommendation</th>
            </tr>
            <tr>
                <td>Cover for Group Outpatient medical expenses</td>
                <#assign isChecked = false>
                <#list userPlanDutyList as item>
                    <#if item.benefit == "GP" && item.participation == "1">
                        <#assign isChecked = true>
                    </#if>
                </#list>
                <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                <td>
                    <div class="table-input"></div>
                </td>
            </tr>
            <tr>
                <td>Cover for Group Hospital and Surgical expenses</td>
                <#assign isChecked = false>
                <#list userPlanDutyList as item>
                    <#if item.benefit == "GHS" && item.participation == "1">
                        <#assign isChecked = true>
                    </#if>
                </#list>
                <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                <td>
                    <div class="table-input"></div>
                </td>
            </tr>
            <tr>
                <td>Cover for Dental expenses</td>
                <#assign isChecked = false>
                <#list userPlanDutyList as item>
                    <#if item.benefit == "GD" && item.participation == "1">
                        <#assign isChecked = true>
                    </#if>
                </#list>
                <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                <td>
                    <div class="table-input"></div>
                </td>
            </tr>
            <tr>
                <td>Cover for Group Major Medical <br>(for example, cancer, kidney failure, etc.)</td>
                <#assign isChecked = false>
                <#list userPlanDutyList as item>
                    <#if item.benefit == "GCI" && item.participation == "1">
                        <#assign isChecked = true>
                    </#if>
                </#list>
                <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                <td>
                    <div class="table-input"></div>
                </td>
            </tr>
            <tr>
                <td>Cover for loss of income due to sickness or accident</td>
                <#assign isChecked = false>
                <#list userPlanDutyList as item>
                    <#if item.benefit == "GDI" && item.participation == "1">
                        <#assign isChecked = true>
                    </#if>
                </#list>
                <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                <td>
                    <div class="table-input"></div>
                </td>
            </tr>
            <tr>
                <td>Cover for long term medical treatment</td>
                <#assign isChecked = false>
                <#list userPlanDutyList as item>
                    <#if item.benefit == "GTL" && item.participation == "1">
                        <#assign isChecked = true>
                    </#if>
                </#list>
                <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                <td>
                    <div class="table-input"></div>
                </td>
            </tr>
            <tr>
                <td>Others:<div class="table-input" style="width: 300px;"></div></td>
                <td><input type="checkbox" id="myCheckbox45" name="myCheckbox45"></td>
                <td><input type="checkbox" id="myCheckbox46" name="myCheckbox46"></td>
                <td><input type="checkbox" id="myCheckbox47" name="myCheckbox47"></td>
                <td><div class="table-input"></div></td>
            </tr>
        </table>
        </div>
        <div class="t-center t-bg font14">
            Personal Data Use Statement
        </div>
        <div style="border: 1px solid #333;padding: 10px;">
            <p class="mb-36">
                By providing the information and submitting this application or transaction, I/we consent and agree to Income Insurance Limited (“Income”), its
                representatives, agents, relevant third parties (referred to in Income's Privacy Policy at https://www.income.com.sg/privacy-policy), Income's appointed
                insurance intermediaries and their respective third party service providers and representatives (collectively “Income Parties”) to collect, use, and
                disclose any personal data in this form or obtained from other sources, including existing personal data provided, any future updates and subsequent
                information on my/our health or financial situation (collectively “personal data”) for the purposes of processing and administering my/our insurance
                application or transaction, managing my/our relationship and policies with Income including providing me/us with financial advice/ financial planning
                services, sending me/us corporate communication and information on products and/or services related to my/our ongoing relationship with Income,
                conducting consumer profiling/data analytic/research, which includes data matching based on personal data collected by Income, its affiliates, business
                partners and/ or NTUC Enterprise group of social enterprises (“NE Group”) where required for Income, its affiliates, business partners and/or NE Group,
                to develop, improve and/ or customise their products/ services and/ or to provide me/us with their respective products /services, and in the manner and
                for other purposes described in Income's Privacy Policy.
            </p>
            <p class="mb-8">
                Where the personal data of another person(s) (for example, personal data of the insured person, my family member, employee, payee/payor or
                beneficiary) is provided by me/us (whether in this or subsequent submissions) or from other sources to Income Parties, I/we represent and warrant that:
            </p>
            <ul style="padding-left: 20px;">
                <li>I/we have obtained their consent for the collection, use and disclosure of their personal data; and</li>
                <li>I am/we are authorised to give any authorisation and approval on their behalf </li>
            </ul>
            <p class="mb-36">for the purposes as set out in this Personal Data Use Statement.</p>
            <p class="mb-36">
                I/We agree that if my/our policy(ies) premiums are paid by third-party payor(s), I/We consent to the use and disclosure of my/our name(s) and
                relevant policy(ies) information by Income to such third-party payor(s) for the purposes of processing and/or administering premiums payments for
                my/our policy(ies).
            </p>
            <p>
                Please refer to Income's Privacy Policy (<span class="underline">https://www.income.com.sg/privacy-policy</span>) for more information, including access and correction to personal
                data and consent withdrawal.
            </p>
        </div>
        <div class="page-footer">
            <div class="page-info">INCOME/GB/GIFFF/07/2024 • Page 11 of 12</div>
        </div>
    </div>
    <!-- 第十二页 -->
    <div class="page">
        <div class="t-center t-bg font14">
            Declarations by company
        </div>
        <div style="border: 1px solid #333;padding: 16px;" class="mb-8">
            <p class="mb-36">
                We cannot alter any of the wordings in this application form. Any attempt to do so will have no effect.
            </p>
            <p>We confirm:</p>
            <p>
                a. that we understand and agree to the collection, use and disclosure of our personal data as stated in the “Personal Data Use Statement” (PDUS);
            </p>
            <p class="mb-36">
                b. on the representation and warranty made in the PDUS.
            </p>
            <p class="mb-36">
                We declare that to the best of our knowledge and belief, the information given here is true, correct and complete. We accept full responsibility for them,
                whether written by us or by anyone else on our behalf. We have not withheld any information. 
            </p>
            <p class="mb-36">
                We agree that this form may be signed by electronic or digital signature, whether encrypted or not, which will be considered as an original signature for all
                purposes and shall have the same force and effect as an original signature. Electronic signature may include electronically scanned and transmitted
                versions (e.g., via pdf) of an original signature.
            </p>
            <p class="mb-36">
                <strong>
                    We agree that this form together with any other written answers, statements, information or declaration made by us or on our behalf shall form the
                    basis of the contract between us and Income. If anything is untrue, incorrect or incomplete, the insurance policy will not be valid.
                </strong>
            </p>
            <div class="mb-36 clearfix">
                <div class="fl" style="width: 350px;margin-right: 250px;">
                    <div class="table-input"></div>
                    <div class="t-center">Signature of authorised officer</div>
                </div>
                <div class="fl" style="width: 350px;">
                    <div class="table-input"></div>
                    <div class="t-center">Company stamp (if applicable) </div>
                </div>
            </div>
            <div class="mb-36 clearfix">
                <div class="fl">Name:</div>
                <div class="fl table-input" style="width: 400px;"></div>
                <div class="fl">NRIC number: </div>
                <div class="fl table-input" style="width: 300px;"></div>
            </div>
             <div class="clearfix">
                <div class="fl">Designation:</div>
                <div class="fl table-input" style="width: 360px;"></div>
                <div class="fl">Date: </div>
                <div class="fl table-input"style="width: 360px;"></div>
            </div>
        </div>
        <div class="t-center t-bg font14">
            Declaration by intermediary
        </div>
        <div style="border: 1px solid #333;padding: 16px;" class="mb-8">
            <p class="mb-36">
                I/We declare and acknowledge that I/we have reviewed this Group Insurance Fact Finding Form with the authorised officer of the company, and I/we have
                explained all the requirements of this Group Insurance Fact Finding Form to him or her.
            </p>
            <div class="mb-36 clearfix">
                <div class="fl" style="width: 350px;margin-right: 250px;">
                    <div class="table-input"></div>
                    <div class="t-center">Signature of intermediary</div>
                </div>
                <div class="fl" style="width: 350px;">
                    <div class="table-input"></div>
                    <div class="t-center">Company stamp (if applicable) </div>
                </div>
            </div>
            <div class="mb-36 clearfix">
                <div class="fl">Name:</div>
                <div class="fl table-input" style="width: 400px;"></div>
                <div class="fl">Representative code: </div>
                <div class="fl table-input" style="width: 300px;"></div>
            </div>
            <div class="clearfix mb-36">
                <div class="fl">Designation:</div>
                <div class="fl table-input" style="width: 200px;"></div>
                <div class="fl">Contact number:</div>
                <div class="fl table-input" style="width: 260px;"></div>
                <div class="fl">Date: </div>
                <div class="fl table-input" style="width: 200px;"></div>
            </div>
            <p>
                This policy is protected under the Policy Owners' Protection Scheme which is administered by the Singapore Deposit Insurance Corporation (SDIC). Coverage
                for your policy is automatic and no further action is required from you. For more information on the types of benefits that are covered under the scheme
                as well as the limits of coverage, where applicable, please contact your insurer or visit the GIA or SDIC websites (www.gia.org.sg or www.sdic.org.sg).
            </p>
        </div>
        <div class="page-footer">
            <div class="page-info">INCOME/GB/GIFFF/07/2024 • Page 12 of 12</div>
        </div>
    </div>
</body>
</html>