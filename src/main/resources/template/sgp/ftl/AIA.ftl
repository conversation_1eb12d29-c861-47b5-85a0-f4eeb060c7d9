<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>AIA Singapore Group Insurance Fact-Finding Form</title>
        <style>
            body,ol,ul,h1,h2,h3,h4,h5,h6,p,th,td,dl,dd,form,fieldset,legend,input,textarea,select{margin:0;padding:0;}
            .clearfix::after {
            content: "";
            display: table;
            clear: both;
            }
            body{
            background: #ccc;
            }
            .page{
            width: 990px;
            height: 1579px;
            background: #fff;
            margin: 0 auto;
            padding: 50px 80px;
            position: relative;
            }
            .page-footer{
            position: absolute;
            bottom: 20px;
            left: 70px;
            }
            .page-info{
            text-align: right;
            font-size: 14px;
            margin-bottom: 6px;
            width: 940px;
            }
            .page-num{
            margin-left: 60px;
            }
            .page-remark{
            font-size: 12px;
            }
            .header img{
            margin-right: 30px;
            display: inline-block;
            float: left;
            width: 80px;
            }
            .header .title{
            display: inline-block;
            float: left;
            text-align: center;
            margin-top: -30px;
            width: 700px;
            }
            .font12{
            font-size: 12px;
            }
            .font14{
            font-size: 14px;
            }
            .font16{
            font-size: 16px;
            }
            .font18{
            font-size: 18px;
            }
            .describe{
            border: 1px solid #333;
            padding: 4px 10px;
            margin-top: 10px;
            }
            .form{
            margin-top: 12px;
            }
            .form-item{
            margin-bottom: 16px;
            }
            .sub-item{
            display: inline-block;
            float: left;
            }
            .sub-item-content{
            border-bottom: 1px solid #333;
            position: relative;
            }
            .item-describle{
            position: absolute;
            bottom: -16px;
            left: 50%;
            margin-left: -30px;
            }
            .fl{
            display: inline-block;
            float: left;
            }
            .form-item-content{
            padding-left: 10px;
            }
            .mb-8{
            margin-bottom: 12px;
            }
            table{
            width: 100%;
            table-layout: fixed;
            }
            table td{
            padding: 2px 12px;
                height: 20px;
            }
            .mt-12{
            margin-top: 12px;
            }
            .table-index{
            margin-right: 10px;
            }
            .table-list{
            margin: 50px 0;
            }
            .big-td td{
            padding: 16px;
            }
            .mb-20{
            margin-bottom: 20px;
            }
            .page-five .table-list{
            margin: 8px 0;
            }
            .example-list table td{
            padding: 8px 0;
            }
            .table-input{
            border-bottom: 2px solid #333;
            height: 24px;
            width: 100%;
            }
            .page-fifth .table-list{
            margin: 30px 0;
            }
            .page-fifth td {
            padding: 10px 0;
            }
            .text-center {
            text-align: center;
            }
            .text-left {
            text-align: left;
            }
            .text-right {
            text-align: right;
            }
            .font-bold {
            font-weight: bold;
            }
            .underline {
            text-decoration: underline;
            }
            .underline-bold {
            text-decoration: underline;
            text-decoration-thickness: 2px;
            }
            .nowrap {
            white-space: nowrap;
            }
            .wrap {
            white-space: normal;
            }
            .italic {
            font-style: italic;
            }
            .line-4em {
            display: inline-block;
            border-bottom: 2px solid #333;
            width: 4em;
            }
            .line-6em {
            display: inline-block;
            border-bottom: 2px solid #333;
            width: 6em;
            }
            table.border th,
            table.border td {
            border: 2px solid #333;
            }
            table.padding td,
            table.padding th {
            padding: 4px 8px;
            }
            table.collapse {
            border-collapse: collapse;
            }
            .border-none {
            border: none !important;
            }
            .border-bottom-bold {
            border-bottom: 2px solid #333;
            }
            .overflow-hidden {
            overflow: hidden;
            }
            .inline-block {
            display: inline-block;
            }
            .py-0 {
            padding-top: 0 !important;
            padding-bottom: 0 !important;
            }
        </style>
    </head>
    <body>
        <!-- 第一页 -->
        <div class="page">
            <div class="header clearfix">
                <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIcAAACJCAYAAAAG7S
					t6AAABVmlDQ1BJQ0MgUHJvZmlsZQAAKJF1kLFLQlEUxj9LMULQoaCh4Q3R0ivEClrVIYKChxVU2/
					NpGujzct+LCBpzaBOa2sKaHauxP6BoSBoimiJojlxKXt/VSi06cO758fHdj8MB+gZMIQp+AEXblan5hLa
					2vqEFX+BDBGFMYNi0HBE3jEVa8D17q3FHN+t2UmXV86FnJy5rgXq5Wrt+Ovjr76nBTNaxOD/YUUtIF/DpZG
					PHFYr3yEOSS5ErinNtPlWcbvNFy7OSSpKvyBErb2bID2Q93aXnurhY2La+dlDbh7L26rLKYY9iCVvQ+JZgk1xO
					+Y9/puVP0iGwC0l3Dnn+0BCnIlBAlrzAHAtT0MkxRNmz6s6/79fRSlVg7g3or3S09BFwXgZG7jva2DEQ3gfOboQpz
					Z+r+hp+Z3M61uZQAgg8et7rOBA8BJoVz3uvel7zhPm80aX9Cf7lZko3HtSqAAAAOGVYSWZNTQAqAAAACAABh2kABAAA
					AAEAAAAaAAAAAAACoAIABAAAAAEAAACHoAMABAAAAAEAAACJAAAAAEbEOHcAAEAASURBVHgB7Z0JkJzleeef7+rumdEFAgkhEB
					JCSOgASVzivgUYG+Mzjo+s4zhZ59pNbaUqtbW7tWdtNru1u0lVKnaysR3H5cTENqe5L3EJ0IVAgNB9oFsIdM1MX9/X+/u/PT2a6
					eme6Z5DjBy90OqePr7j/Z7vOf/P//VKDDs9Ts9AjRnwa7x3+q3TM+Bm4LRwnBaEujMQ1v3kV+GDQtFKuZxZe4dZrmClfJ5HwYyHey
					7yeYJV7WlZfc88n3smDM1LRe5h7jlllubR1mJeJmOm73i/CpNU/xx+pYWjlM1Zaf9BK23fZaUPP7bSocMWf3zYko+PWnL4qJWOH0dIYrM
					4Rj5KCAWCEUgoUuaPazNvwljzz5jAY5x5E3meNNG8aVOtNGVyWWi8X23pOPWFg4ta+ogLvnu/JVs/sHjfAYv3HrLivkMWHz1ipWPHnOYo5
					fJdGqNLexTQHmgW69Ic8srdpZZG4OGFQQ+tEZkhMF4mbdbaat64sRadeYYFk8+0YMpZ5k+dbMH088077xzzWlrMgl8Na33qCYdMQ3unJdI
					EHx2xmEeyb7/FaIdky04E44AV93xoxb0fWtKBZiggFMM60C5RygpnjLdw8kQLzj3bfIQiuHCa+dPPs+DsieafKW0z3vyzzjBrwQRFp940a8q8Uy2
					ULR36GCHYYYVXVlv+9bctv+Y9Kxw8gCB0ONPg/IeKD1F5Hlbh6NqYTIpTNXou+ynSGumLLrDUZZdY+spLLbrhSszQuWbjxozEEYz4Nke9cLg0zMGP
					LNm8w/JrEYT1m62wZbsl+w857ZHgRyTZrJXwGz7p4QWB+WPHoDnGW3DWmfgoZ1s0a7qlFlxs0aK55ktQ0Cqnyhh9wqHgIUnMjh5zTmQRIYjRFMm6DZZ
					b+ZYVNm2zAqZjMMPz8SNCIpAoKPsFOKDlqIM7n7vfCaJ8EO1fvkwxsVInvkkJ38R4r8nhWWDh+edYat4sS111mYWXXGT+jPPxVXBsERJvlGuU0SccXJy
					E8LOEMBRfeN06nnzVchu3WHzow3JUoQunCziI4aczFo7jjh7Xan4bzqV8gRTRCWGrcyKLRC1yUnkkecLc41kr7iKyKeK7WHYQe+Qncm7l5OLgRlPPsfT82
					dZy100WXnu5eWgUCeVoHaNKOJKN+BIr3rXsqvetuHWrlfbstOIH+yw+QtipfEW/Q7bft2D8WAu4K8OzcArPRhBwCj2cRO/sM8yfwHttY4hCEAZpDxeZlP0F+Q1OW0j44rI
					AlgoIS3vekna02NHDVsK86SFnONbjII9DOMWd7QjswGbNb22xgNA4mDbFwoumWzTvYkvfdo0Fs2aYx3GPtvHJC4fu1mMdVty8y4qvvmX5F1Zax6vvMOn7mSuSVyaVXktTeOY
					TCfjKRTgVPda8MW0WTDzTwkk8zjnLgnOIJPSYcrZ5PGwC+Yo0GqPZQRKtRG6khDlL9h0kXNajHBHF+z+y+BiCcwQBQogTRU/H2p0f1N9ugvHjLCRn0vLZOyx17WIL515UF
					uJWoptRMj5x4Sgdbbfiu9us/b/9veXWoDE+Jjfh8g9kMk3aoiIgvWfMs9Ci6VMtc+U8S994lQUL55rH3eiRxXRmosufcCpdeQdpCWmHwahxRT0VXwTN4nyiLg1TUpb1eIclG
					zB9q9ZZ9sWVlnt3oxV27el9wNV/cRxyYL10m2WuutTGfOkuC+66kXB46qgxNZ+YcEhbxG/iZL7+jmWXv4M5WY+qlmBIKDSkLaQ1ZOsREj+2YOw4iy4416I5F1owe6YFOHfhBVPIN
					ZCxxIR4aIZuIeBXJ2VIcBDmEkKdHPjQ4l37rbhzt8XbSMi9v8UKCH5h8x7cWZ1HLafWR9NNtOhizMyVl1n6ussthbA7Z1Xp+k9wnFzh0ERiRuLdB522KDy/0jpfWGO5NzcyBTUc
					M4+UdlAioZQiE0lWcvo0S83H8188z0JCQ2/KJJJMgzATIzzhJUVaexCS1e9Y/o31llu7yZKjB0nbY4IwO6XOGqE3mi06b4qlr7ncWu693ZkZH99EUc0nNU6ucEgFHzlunT983Dr+7nEX
					kibHO8v1jT4zgCBhGkIcyMwNC6zlC9fh4S90/oNFXQUxmYvBmIk++xrmN2SCVK9RkY/6TiKtsuYdy7200rLLVlh+x05LjuPEVo2ymcFpHTvBxvz6XZb5zC0W3Hpt1bdO3p8nTThU+Cqu3
					26dj7zCJL3lNEeiO6hmWOpZ+pJpll4y1wIctXAuKvfi88wjVe2NIoetocskbakq8H5Mzgd7rUiKv0C+Jqfs7qr1GJo8m+lpbhQ9hZaaM8NpyJTMDAISzJxGuE2EdRIHMd0ID91B7eQL3t5s2
					Sdes/YfPUkIeLjmTr105KqhwfmTLXPTQmu95xrzF88xj6roKTuk2eQkkx0N9bhigUVc6GAi0ZOlLT6wh0ox2V4inPIg+ZYULPfeRitSUVYCUPmYFHmSgNrNoJ3qQUzgiGsOOZ6ljdvt2N88Y
					u0/ex7bS/ShPEKfUcLmnkVN4hJr+f0vWTgfp3Nsm1kGp0yRxq/KkCbJkTtRyLtzj+V+9rhln19unWvf7XuGmE2fanDm8gWWuW8p8/INsrtUiE9S1XdEhcOZknVbrOOnzxDikfresLPPBJCtIFc
					xzjJ3LEKNUoO4FDNy1TwcMXISv0pCUX3m8r86Oi1+d5MVqBnlCYNzr6214o7dFBErWqT8o5A6TWrRPGv5zO0W3YHvNWv6STExI2NWpBlwxIrvbCmbkl+8aMmho72mx9NdMbaVAtVZFl0yw9q+
					upRQDhMCNuKfxRDSDFxIeM1iC1TJJa3uE6rnXnvTCmSHk6MCIpXD+uKHZGZXve3C5VY0qXAlwflTRjxsHxnNoaTQll127HsPYkpeAHV1vMqUlCygvtGCMGS+eJtF91yP9iCtLRNyimIfhiTQyh
					KDU5GpKSxfbbmfPmqdb6BFCIe7hyq+mJQMUIAMoW7L736N+SKbOoImZvg1B0Cc4vvbLPvjxy338ho0hpzPnj5DyVIzz7X0FQjG3ddauGQ+dwH5in/OQ6izkBCW1L63ZJH5aAblN3ISFCAKbuD
					YJzzy+lsOLqWD6NZrLLx4xoiZmOETDpfgKloMVC+/bCUa40krHqhEJThRJLkE2A0mjrXMNfNJ9Fxv4e1Xn9qRyHALtEwtEU0K0+rwrGjRmIRaJXGm3TkTs3qdlcDAtgr43IZplikegTB3+IRD
					mEwOuPNnT1nHPz5mxUN7CMmU/tYuiDrANkgwxnzxFkvfg1N19Xzz8DlOjxozgCMe3ni1tVCcC8aPseMPPWP5DVu7vyh/JPfeJvN+8jDV4uOW+c5XTRXf4U4IDptwJAcOWf6JFy3/6kpqCzu
					pkVAPIWpzGoMXqYUXWfrmy8uCMW+mQ3Z3n+3pF71nQEU5gEAB4KA0GiEBiuC/8JrlVqAxShT+lDsi0slLQKhKRyQKjcKjr3LCMI5hEY4SJe140w60xhOWf/d9gDKd7hAd8qqllcLSudaylD
					vhi7eaP4/S9KmW5RzGCW9mUx5YlICWiBbMi4/zGe8TjgTgtPpwuOGKBw+Z9xa1m4eftTTmxZ8IoFkO/TCVFIZFOBLF6suQ7HXvYx8/7j5/H8FIzZttY37/a2QG55tPBdVFJN3fOP1iwBngQ
					vtohjROaED19tgPHrScwlrrCnP3HrTjDzxrPoCh8ELaI+ipsaBGEXPAHfX9wtCEQ7gLpDj3yhrrfHo5ggHoRe8RnQhvkb56MWAWQtUbFjvQzWisoPadktH3jpxOYU8jopgW+nHoGbDs6rWUZD
					AxZFuLBw9Sr1rhkG/pL9yJZh4e/2NIwlEGuWzlwFZZlkJSRZrVMRaeMcnSd95gmV+7m24x1B3h2qgfirgcqAfIn7z/UZSh9SgleGNnWObTN3GMha5EWXsX/qVEIe9NF7lES6hcq7lKAjLE0TM
					B0dSmhNSOP9hjHX97P4CW9V2C4TxQC6mejv32fSC0ANEC0x/JRE1TBz3Al0tKRqEJBQXU82gcPk5q6qYl1nozOQ6gkJWhNs8C/l7hoadJQPYtU1S+18zzoDVHadsuK5DFy762mtZDtQqUBcMBVq
					6+zFJLryMtTJlZhaJTZJS2grOgrZIYnCopBUPUdjCZdkdUunCow+XoDWU6HE72kplo5Hss7gArQvY5Vs8vqYTirn3W8egL1orf4Z87iRwSN+YQtF/zwtEFZIkBrxSef83yTKgaljVxwm6mFgC9vw
					38gTCdo7wvo/siCQ9KHUOddMnm7Q7oW1y3EcTafu7Osy248Uqa7heAEKcYqGhgCBPevc8hvFDImrr3DgqZO9De+yxZv4nwlgQk6feOV1ZZeNkcmqkuMF/XQP29gxzNCwctAgmFoM7nV1gHj0pxSCn
					fcDIHfet1Ft1186kVrlILKiEY/phW88BxqufVXzDHimvetY4fPWze+1stWvm2tfz6p81TwYtm6k906EZUneWemx3ssvg/91rSCTTRYW4Tyy173fkcbTMvKDeDD1KYg//EaOZEZY8LJGQ6nyThpTw/K
					ljZz3DqVGu973bnhIaoPfFbnAqjdJhJRR0nCICnFkYaotXnopYH3XWJakWUBIpvb+SmoGO/I2u+il3kagTr+0RMjfIYEpAxLRwLxwD0Mjl+jEpuufJd6sw5uENq1nTXnqloZzCjOeFA/cakcbM//BnJL
					iar62D81nGWXrTAxv6b36TlD8FQZ/loH3I+mcRkM34GjxLaUK0N/ozzMBtMPsLt02ik8ym8u9k6n3jFCvTT2JEj5qe5IG2UBPQ9/a87s+uCnczTVhuGD5lMdM4ZZKXp+wE85MJbKrw6nAhIgHwPPQYzmr
					q9S0DZijv2WOeKd4itP+reX8sNi6z1c7eT5KJReBhCqO4Nj+ALda7Fr71N5XOdE4TMN+4yn77WXkOZSTKU0YXnWeqicy2/5QPrXLUGgPAOC370FBhXemaAMwZXEz6idRzzT68NjPwfcjp99p9+813nI6
					lnRrkmVcM7n1lu3mXgcC+fP6gDaUo45LDFZEGVti1lkU7dXdjf6EpwkSS6XEvfaM5nkDAqHWm3AiCkZPMHZjicOmb/IqIRBLtPWl8agbtT55f5kOTTE8ussH03RbAtpPngAzlMi8XeXRa+tYnS+XQLZ+K
					r0F6gLvuTdpOoMkseST0vKQDMuY3byr2+qr1s2mppHGx16slUNptralw4yGsU1663AnA2i5W6padEmILzISxZcIl5xN+fiP1t5J7g2Ev4CqUD4vbYbblfPG/xjr2Wuvh8S/36na5C3N9mQmkGLrinqIxoo
					LSRPl5e5zZv4mJQ/LLnLLOIKI3CYngD3XezL3Rp7DKnGFMs/0t6fgRHuPASiwAk+w8+QyM6FXKFth99ZMUNmy3GNwzUCtrkjduYz0EVULC/jr//pVNVSTvILoQjxHMf87mllroeqBtdZ6NyAFlURJX7p2
					fpl3nCOu9fbtF8Mo2fv9Ei8CTBhVMHrPc4bIVwEwBrotk8VP0EKO2akyQwtBYI91nYvovKKVhZHvFbG8zoV3EcYzRPd/slIzRJLsTGhzLMfgLVVXJM14hQgajGh1QmAoOqm7mZ0ZDmEGZAoV5xK2YFx0
					2T4UFnEEydAhppiQWka0fdICeXMFFq0C6shw5q9XvOpARAB8Jr51t01WyikwmN3dGYFweqAespJzVAOLzJZ1tIR1sBM5uHWEYYiwRgjgEQFuCpuGEbz/gm67eApJ9D/meW82lGrIONkoWuQwYAcgGzX1
					Ayjxu4wPEEb6+3NK2agarhTeQ9GhMOnLcCXBnJPgA8rgkHiRw33qIZ0y3A1o06+gD1rnIXFXE4s48ut+OPLLfMwgvBXt5gLX/0lXLs36w0Cy2uTnvIX/y5s6wF+oTUpXOs8ORLlhx5ArznPisVy5VSh95
					CUGTz/edep+Viqo355mctdTvJwcsIK0cokaZ+4ehTN1mA5vLIyygxVkQo8hDeKKUulL9HUq/R0ZBwxAdIej33Gs7Xwe7tpq+YS5siTigSO9L2tHunDb4orN1o2e8/Yvl3QE/BxTH+9z8LynuBhQvAkgw
					y/xLv2G/tf34/fCGEvXGnyx+kZkyljjTJ2j59C4XHNZYla1w9SmBbCnt227G//4Vltn9Ae8Ft4GYXucbv6u8O9W+FtnYOBU+Q7Mm8DZZdT+SibjsQeoWXV6Apz3AZ30b3M6BwuN4KHJ0cmA1xdzoAT5S
					2CFK0CIyGI0JpdG8j/D0Vzgp07Ocee8U6H1tu/mT1e8yiIQhqA9LJzowM9hiE7ySDKiHJv7OB1yTI5k0nTQ2GQrwtxXJtqXrzDrWFj5bfgA8gghf8txa2ExJiuqJk9Q+G8jcJMVVjI5xTdcrleEg4EpJ
					k+dfXoeUX4nw3voMBhUNOVYLKLO7f7+JnPy12GohR5s0B1TWr7Ik3vr8R+6a7CKTBO//6Ycs+vtyKsO2M//JtNGDfbMGSS/tqNzmqePSuRK9sozx5ZT7rpJqDc8+ytt/7HI5nlpoGbEMkADuXU3h8Yw2d
					eWd0o9/6O8H8pu0uP6S2SJG3DLtwdO3cRS5CiT38jMPbqFk9C5NBBAVnWnWkOudYfewDCkfMCcXEzo5MjV8H50y0MXfi6ePljwTiufoAG/07xoTkHnje8qS5vQmtNuaWBZa6BZDRnOl9BQO+r+K7ZHp/
					+jSVzI/pzx1jLXdcRrLoEodWq7lP/ATvbCB7X1sKqGa8Hf+rB4Dt4WeArYjb6VDjuZEhX0jAKG/SJGuBSmIkhgqE6qvNoN1zb72H30HCrwPWIR3v7n34HURb8nsGGAN+IxbxK/jQCkmbOLbStxDLCw4/
					wrH7AMfOMZWrqQWSULlnVliODn41BkVzp4NXxbZfOqv33emSYKjYNRuhl8JHeOglSG0PW0gSLHPjJeyutmlwx6G7TXmdSwFHZyKL93xkuRdWuYSYA1O73hxNp2xM/e1IW+WpokaYpoyScGRgm4kg3LEM
					9I/S6iS9UioeiieEDv9SkVLB7r0WE1kFsC+70HeA7QwoHAXCsoLAI0JJMZSuDa64tJxxG2DjI/qxjkf2VGni72NKnnoDx++QpejQT19xiaW+elfv3cuMUGRL3ttq7f/nfvI1K7mECBeQu2B82vxFtGLi7
					Q84EJCQ9s1x/+7rdizlQ335AdpD0ygqhS4WIicgdbak+tTH0FFsIYLAgfWhoBxKWb3OXijKgd+dfzFwQpKWXUMUEPF7JMQo5Yvgf6BRXzg4CbHpFYhQCpCjSTgC2PhCiFdNIBLoEj6xQagac/cVlr9Fh/
					pKy2NPvbRvYz51taXuucGia/ExKkMwfjRG/pGXgTO+CZx/uxXeJ+LourujqWdbahZp74lkQBs9J/wT3fFpmrOSbaDhXn4LZ50cR7f2kKDooRuqthYpYNaO/eBhaxtPSgBiW1V5h3M4UNCl9N9CnlcZIr
					nTzR5xUzWSr60vHEyoiSZAdNJdLDQhqKhQCS+diErFJ3NIU0goUMWOa2vFWiuQ2Cq8ux3qRnjCcI4zV19q4c1XlAtofD2hDhJv30siaJ/lfvmK5Vfw/V0SdB04TigXM7UQusfr0ITcaQ2fU5eJCWEESEO
					im127GeHA73BTjvlxQqJn+SEyM2ioqiHujRLtjgFaSAcULiZ6Ue9rkynuqs12/+myoRcg9JNAspEhFcuzUhJFygYVDE73l+u8qCscwlCWtmFOXKq8fHLhtHNIfJFubtDbrbPPwb2NcJQQ0sKLb1jHd39i
					OYpfKpRlCAlbf+/raAtyLgIyVwbfLy5bZZ0/f8GOPbGizw0s0RBCPnX7Ekvfd/OAKfTKZns+q2AXQEzjESGVd6D7UTeNtIA0a8XMSIv0HvJTih/us/a//qnjNh0D7taUeQ0R0uEYcqDRbgGaPjhzIsxCR
					Fgfku/YiVDikDs3YQCfsb5wEBYqVhbCvDKC8wnBxC7Tj3CoAuge0DzJP1FGzmXlGvCOK/upfhbHZ0LdovOnT9EGsQJNsAtapAup6VxpaSWVZk13wJuSmPyA9xXe2gIJHelrflP8gDRylWaPJk+w1OWktL8
					IOIlUuqvGDjBR1cekv8vLbigJKIHoqah7Cok+00MCIi3SYyDA8fEjoOrIPn943Fo/f4tF1+CDkKYfrhEROKQungaEkGaoIlXpPIh1ML8lSHsHgnEOIBzY5m5iM4/cPbSOtYRD/olQ28rnkxcxCRQmoAjVY
					gmElQhY1SCsDF1TEQ5JrQRPOwajkFdvDHF7cugjt8ZJBp6s1NIbHU20Qu0CdY4Y0HORgld+LWHtGvkV1UP9NIGlroAI5Qs3WOrXbik7g/0Ie/UWev3N75QEDMa1WSzUmCvCVb4hAdH06lkmRs8yM9y1lS
					OTNsyjQajBJJg7zycshsY7oEgmAltHnylzN4QRnDfZkfN3rsExFQdbnseuvWZcj4FWc6gvHFxswePKmkMqGCcM4fAE6BECqsdQZjLhri08+JRDaYfYf2EwO//iR5b/5fPWAglrdC93+PVXlO16g3epyuI
					xdYKO+x+34794mrR1nhrJHBsDBVLqK+A5Z053ldHc/VSLH33OsoSHTl266EGTKtWuu7Y8dA6BtcAJstQy37iNj3TRhjYkHKKrilUN3asKbfXQ/rUfTbXMjDRx73BX/T5x/rAde+gJC5evsPTFM63lG/dZ
					6iZYCGZN5/uDH24tGLbRHboypyWKcSUgFg4P28+m6wsHGyni3SaAenzK1RGJE6GitGJR992vpSwoyuVfXQ056z6LVLXUgUgq+Z6Ye4QxbX/uFQsJo1KsjZK++wYcRrQIVNQ1h+4mhC1+m2on6Pbci69bHl
					PhRb613na9ZZZeD/XRDWW8hC4uYJcU65oILpd+n7QxDd1F7sLCdo69k20VpD/Kwuy1hkRbIKcIXYdDMHT8ajZK33q5Q34X9gII6tqXPjsxKtpDSHAJikyMtEhV4syFuUfh5Nhkxb/9qaVwWNPwrYbXXU
					GbB/M6CDS/El7ehZipSDcLhUM0esI1a6Qvpx/hyDPRtDdKOLjIEUgnVfUqHr0ggyI8K7wBdgHb7rVRwscP0BpojkmYQ3HsedQ0Op+jVfLVVU6AFFZGIplVrYO8gvOqK8UwEkSO7hqcao7f5B5/gdATxx
					Mi2jS9MCpaRaKypv2hMmT3Q6qj6iON0FYJ9lTRSbhpl+Xf2obPgZAc67qjuUalEIHprfgqmxrUs0C+KULaPGyJ/ivrSJSiFRDwvkM7rWgRCUjlIHpHM1oAoHhQD5YSUR8NFdUIAv6I3JKwJOJw9xT6Nqh
					9HdKNiCUYNw6TjPBSXY4x/cKiDDTqC0dOEoakgSoKJp1hqUtg9wORXRmyWwUqtcf+6n5rpRwtVe2fPxUJPbHJCFpFFZoSGIvVZJ2ljJwj7Gu5cTE0kqTg6W+R7XMnqw3jqyQIRgfmSFXOolK9SWCZ666
					ysd/5chkegBD0GrL7vCd/RqV0XZgIzWNMcvuf/R1+CvmNDXt4n/878sT5ZAuPDTwxvfbRzx8SXA+shnhCw7FjrYDzLD702kMCoYc0iARFcyUzI2HqK1BFrczw8ZvmsYpEau7FNlYOK6RxnmpajaYS0K66
					cUNqQ/GH1Md0PYAT9PaP2H2NceJKVn3oYGasAqBnt1oiqxEIT+lWB0CiO35OXWLTToDFt3HnUIKu5Ot7SLTLpooMDQxmEQxmQbF9DPHI2+9ZfPgj85e9QUvDOe4RUPouklDKrRSA5l0E85gFbWfjON5q
					mbvongMkW7e1UvvUZGm+Gc6+ktDKfHkpOZk2K33vIQj3AeNkUeMl4HPsozhzMqyF87k+XT8q/7T5f92+PUsR9ZS+dbcd+8lTpNbJpXRrhnqb1H4x0W7IzEi7VQlJl4ktFTtcb+yxn2cttRPzjC+ShkRf
					Jq2RjkLNRzBxHPmODK0WaHy1Y/RynstHUf1vbeFQVpGF85Is1NMl7kLsugPNihoRwpD808D0V65zfkPmU5TDge/X7I3AgxfySYkzFbfKJx/jyxBy8jBbZ6E+B2IYwvNdVDl8/Ta2y8ngl6TmQg/15bss
					upwEUSOp7crZdQlLuJi11kjRJDsPOJR5YStOI5owv/JdFupjsvDkfUK64ej+D+deQDRwtXU89pqru5TPtXJAtZ6lQTT9FW0iwZAPopxStRYh5X6YZU/X8lARDTMD/o/SPOaUeXO1mR43JRvoNWR6A1F3
					ouXcsh+U8EudEsj+R03hkF8g21dGfQEklnCQsyixEkAOkPEREjetaIuWO6+l5Y6aRH9NM7K/Ko0jcH1HYkWdNOVvrcbkCml4/yl6R1o+d6e1KCIRP9YQ2h2iy2ZZ+H//tXn/9ruutpKHACW7egN+h4cN
					p4FpCW2OmuAhDtl2D+3nRWO43CFzpwvdyJAGkZnRpVCWVRet1lzxNqOATxU/w825aqON/eNvglW51S1Z2p8P4jQH18/XIgJy9iUcg9Ycupg0xlSG61eRk0jOooBJCdECEWFpePNVSCMFnP7yBApzAQnVP
					RjlSHjoDkpdPJ0U+EKL6CCX06owzCqrP1cOptlnVKomJ/O1O83g1ypByB9/RKYQh/Uomc0WiHNTNy4CKUY9hptg0ANh81rTaLspJLQI63cfaGJTclA1mEunSeQTSXtUaxDe0nyxEmYxv9fa4V6TFmn9in
					puEPB60Yy7QRG4rrmW1lDdbKBRU3MogSWvtjJEyiJUdfat951DkwHmFsGH6V98IecitVhn4BEneN158iXykGsPEFYCK08/1zIIW8vdN5MPubJcyq79g0G9GwETVHid7DtkuTfAOODfaDkPkwOpO4nFAQM0
					iTMz/Z1TP3tX4S415zxAzSxH1pRwaKMSkIpw6mapZ2IQGRBlpTykOXByKLoLxuJwkksKFAFKg4nZQP4K2kFV6+I7ZI13wIwsfnW9j2k1EQkPMPoKhxNYNtDjt+puO/78crRJgUIVtYwv3Q0GApU8wCSqr
					1a9Lh2wzsTKytUYKHcLJ06xsd/6sqVuW+IiDoeFrPHdob4VLrjQxv6vPzT/f/zIOn+xzPLb91l2HdnVvR9ZGoyHwMdiOnQ9sIPYmYQjnDm1VyW0uc1UBET+mVoLpL1raI+ujcrs5zdttMN/+v+s7VMQ5dwO
					ZwfFR1MrhCAK8KfkSQlkAYdnyZC6Aiqpfi9mmzVXq+jacNdTX+Fw/hEqsqLp9EU5qJ2xhQBIIno3QkUOav+rNzAlwp7mwFioHpLgRPUM77T2angW20JbBBfNLEP3CW/dElZN9lbUO4Ra77smozOJYoiAHF
					MOvSzFD7DhFKRyefAV332EZT92WcunEVKIc5tOOnHs/kLK5K+9y+51UfvRqrUO0L1XmfhqE1PrB9zEBA6K/DqXrwLPssf8Z14qV3elIWiXiNEYRRKQ0hoKY92yqV09vrW22PO9vsKhT+VD1PAjIjrQUwL
					GKtHVT/+Dsm8ifM9RUBIlVMkvulURfZbGUIIsJOxV7SA9F8HQYrzUXsQDrvmUf2MIljxwl/+oStX3PPjBvo6upsFnLJPPvjoef51j3U6ozSqPT75B4mkvp57Q10KPKQUrnxxPozkFtyTIRXQAAqEMJ03A
					lGKuGrDtfc9DAqIwVwIm30Amvocq568To/ydwnbWceEx4EDbl0K230DpoLZwuJxB3/g/RZYyjSM6ELw/ASCU/fGDVEfXWuLnLMjAWrxovomHNLwBvIVqNALY4gA6EyIbiRCURDgntBKNQCrU+eRHtEhwd
					S1nwAlo4AsB5fbWP/kN7jbRJalIh7bjUSAiO/xnP4ZWiWjs09dZqkvLNLBJ53/pnFIsHNTK6lLtL77ttFJDv+3zpYqA6LkSxUgQhjiYZ9cx0ADjYG3hUGKoVwYOpxFJDmaUayf9Sl1XyCp+iwxNPJnPwC
					FxLuuosuphOIMLLq2j5A2TqKGeCi1FrqpqAYyG8v5p0SwpLNQxDEYzNzB/TjBBf6XmT7d4/jQiMQRE5hBnvMQKDzkWJNTQsYbADkWT2dDgzlR/TPpLtwPsZTtvs2okzduDGxIMzVMFJZYb3GZ6/Ep+oqK
					3Rvyq+sLh6h3lK+OHCEYrcfI5sNRVp6977Ni9xNHxKIJFOK4pQiuFWK76h3bw+MwttakIgVBZFd8YtFaRdr3cEy9RMNvjElJpNSL3qONU72I4/05ddiEA6jn0eOx2OYDytj0r4ItIUJQbyKh+ApzQ3TADO
					OH6vQQpAoiUXg8ehshASDHnc9VxAj3UvCIM14DUK4pwDiBbJD/hTEzFvAxBg0hz4Dj3vvl11H1HbeGQmo9g5kdqlczxxjE5dKT7QP4HHGgdgXu0RqoBw/cUEgvfwWv5E26JTcyH2gcLhMbi8FbuRIKSpo
					DWxlLe0T23OJK2Afc1DF8IaUeI9n9sHmvCqG3ghKrynEk4/uir5s+f6XwQ5xc1oI51WD4ZyZY/+BJAXhqhyNAWya0kRHt9R4mKMqh2MrWKmhKQZdxCVV+TiZcPIv9Dx3gizcAfzQ18SVWwG2lGqykcUj3K
					w4ekrOMjh+HggO8LVLc0Qt0hc0Jeo7yWKsmyzcTVx48SruNcKskldU3mVbRJqgiq+BPjm6gxW3doShhQ9cPcc2s5odOPw1v3GAbxgeMOBxU29g+/iM9z1Irb9xP6ARjmGJ2ZOUJdYyXUEzia0R3gKwD2NDQ
					wiT6N2umlV7mwseNnz2E2qbLSYFQ9Iloo2n7jbiIKWifXbbb8sytwkBEmJ6yVb0tApEEkIHJO6zmofNTPkDkNAB2re2+gUVM4lNjyUYvheZMoQXe6yCSgVCyB6TWUeVPIJI2gCiJFoQL4i9yLq0g0QXXdS
					WbUxeq9ftXrD7H9i8qhZemN8IndSNsDyaqTOZgs9a20/fZnQbHR/EwYWly30/V6JKC0NIpvccEm02d6HRC+RoWj6xxC0vceJknFLpkPZWZLx9GiMh9yKfBzgmkseMgSI0pgFVa+ZwH77XjpbUswbSeGTIz
					8Dz0qAnLi00ZfOeHQtZTfN8CoLRz8SLjKiINWG6Qqf1ovXlXZXkMaAW0Q0xuRe/Y1ODBfJp27H9WIT+FqMzqJ/kcKvu6W266zzJc+BcPO9P6/PFKfqglIhK9oygBuz/Rl06y0Gu3WhewqfHDQPCKaFvyHw
					QxBHdr+1ZcdL0j+58/h7JJTgefUMp4V8b/i7eBimLsQzRQuutha/+NvW+4P/rfLwZwwMNX+x+COxQkHhU5nIgc4mX6FQ3e0B35TaHOV7R3qXOoWUxAD9Im37kZVwkWxebNjkCmA5UzAKDai8uQt+2Oo0dD
					cm/7sHY6orV+zNcCJDOlj5VLQIPHaDVZ8hZaHnfvoiT2h/lXJjQEqixEofSeELywJ0tTAD/On0IFGC4SPcxvdylKhgKgs9FzdyZXTieR0Q2oOfG7K8JwzrTAOf0WrafYaMi/SHjIxEpAmzYsQepDYNpLgqy
					scMiFaZE4VUVelVWQBgDjp7DQBcgpvakXD92AwZuHcPMmeAcwHZ9F7IBzi+AhnTjetKyKHVbSOzj9RxCMQjaCECnlrJOR6b2xof4kSKtkP3JGMbpY7OIdvUALsdMI5JQNCiK3eWq8lZWnZbK11q5C/ieGTE
					U7x6B4yyfJB5LQrxOz26UC7nglJzBljawhHJbytRDASkBP6pXvbdV5Ic7g0Qb0iXY/f1RUOEZ65ZBXP6pTKkYcwIHta6yO/dTsnBWhEzmWBh7OBPbba8MuSiVSkCKdpCcFQilf5fwmjEOsRqzHrGEaafC2m
					pbHzz//Rsqs2kGWk2Tivu1Fq/MRIMDfZ97ab9+gr5OSoIJPH8MiTDGlIINAW3Re3chOw62B8GxiYek6jBETCIQHWo3HhkGvgoAVDEQ5pDNdOwLP4pbJaFQgfQ158ERJ2OaJDGkQ3MUiz3CqWLt9LFxZqVg
					85aFK9JXFasb/oFlalBi3Wx98Z0s5P/LgguklWys6yhHphH6EkQlBz4EAm7TmwoludVslt2+8wtT5N1cbDR6NohcuQqMYtqy5GwUZGjTS2E0v901s+e2xNH1TMi/w65q0BAfFTWIM2MtPCoEojDzDqag5
					JtH8eKhAqSWmIpGM3m6oc7RAFgy2pCCR6pARmPn8n23brpcrm4ofQuZaAOZBfE+KwmRJv1c7wACfW52MJcyWkFpAZPGlCmJqj074TU5InF6HQtf9Bcmw3yHYe3vNvuugtQJC98XCWkAMSh0cabEh0Pdyk
					C7nryUQOJvWv2U3auVk4xvpD14IAgScv4Beql1BKd62OfW5cfVf5lDZX43IYGWWfBxh1hYOlgcwmA9zNCNkEdpTkixBOOpqm/YuaB4GTi2pMQVWZXkABDqhhMAe4IUBdnyKcCnvK5DmCNQF+hjgkjCbiOz
					ryEoDLhTWslP0QIOZd9NOyupQLLZvYhxJVCRFZAg+Yh5Yr7ON8tgIBQLO0kqNoI8ehELa/AmXd3eFzFXfS17oLDd3vIOGYgiVoAswHZ7Yi3JTwt5GV7SXkMkESDjQyCzAGU89ynXr9brbrw/rCIXXnQ9B
					K6brISebhtUoDDYzoeBNhibAa8R4ee+lI66Svtossrf5OBeohmQb5i7g9xEToTaEBmlRzOJ1OOt7zFWJJS7g7Tic1uOHYD/fiPJN70SP+ECiiajgCHB2mOZwMbbztgOVX02kGXRPub40d6f7V++W7rqy2
					K9/TZ/qIc0KzhheQEZ5+DucwiYwo+SGSamrMFkVWsyNhTour1zuqajU/9zc0n9FsuEXuA2LANSlCUJf/AC3sTDPtJKpj8R0lHfN01YXkNyJyOnJKGxn1hYNfK1Mqzqvi7GmW37uHbvSF1kqiSjssCF20lsY
					j+jWKH9Nj0YGPQKZTB+bwolLh/N6pVYXCEfYO0pA0MP705fMADYMJIV3u4u0mvf7+TkwaIAHlHkMmW4SctQDlYx6UfAESkzI3ZyX0k/Dp9JUMknPXdeHlJOp4J+AQqinL9cGirp1ZQij0NX1Hp5YigUdu
					JH3tbEtdT4c/gGax/zRiz9lK78H2S+Q+iiK7+8kThM50pTlns+u4en5b++fmDRHGzE2XW9sffd2MGlUOxz5grb0SghkC0G6FMFd6XpTkBYA/amcNFRk2aKL7FQ4dTzR7phXVOvfSG7Q8Ev9DyxCyEnKkPg
					36TjLUAkTQWuKOdCp7D2qbu1WYDFduJ/Wuu8in60orEgT4E4qxtaasAybXcMh6zkNTr3UB2a9bqfIRKKBgJHJUB4TfZdqBimBoq3qtMLDigCrBx6RTcAwJoVt/6zM0aV/HBHCX6foQbop4zXn7OicJEJrBV
					TiJKnwyp26dXGm9wQxurMKTy60TqogO/JnE5TdqCAbb1p0fgodt/Z17LfO5m1zlOHEtJJG1XA5rInRPorsOiPhyDzxlRbjLJGf+dFgBaAhrFGk34Jn4ZDD9mTO4U1i6ctte+md3WfgZJmOi7rauobqJLgC
					C4zAZ2HZlTlX5cwciZ1PtlNxlTmp1243E0B2lhBMmSkt5e0xEJIcWs+cKf2AqVQ02ZTpVFaYQVipyLCWOk+XC5agFINzEY56+51omeE45l6HjxWdRPsSZPC6OSwgO0zmo/JCQZMu+wPLksCHGB+VrIHxOK
					rvMq6qpCG5q7vnc/RdYOJtjvIuE3EWYDgmquFPA9bqiJzducAHvI+Qx+SmtYi1KTLV7uBaPBjX1gMKh1oBAEtc2waVz85u2W0YXXnaLg3JD+IAIJ1JoLnXhf5JDBUPuHD0cqp28jB3qEloEpaQlr1QLYt
					LcA4FRWC5GZg/HzmeFhBCuD0VPvYYEvYF6RK/fNPhHiZqO6KjEOBRjWgJqWiKWKQtI2W9xdA846W0IbfrOq8ynztM9//omwqBH9wVVJpv8lHqYY3JHrSwMqOKpCzQaPK7ubdX9PpPiU3TK3LQA4AqOEpgL
					obU8DsRGaLLqHkuTHzgANHkaZxpoAnfwAUUt+CXOo9dr+UYaEnTuTNe7S+/MSR34aR7at/Xuq631125D9c/q2r00bJeWlVZEewQ0gnvAAZzT089BqjMxhv1INKEqsqXotQ3OJV/UxBh4FjioQAyC1y8iF/
					CBazUoQl8Yyo8Y5cLhJlDxvO76KghA15Q3MVUj91VVev1Z4HMVSSDEvmg8hzgShCO3gsounf/eWHC7cH64RQyb2G6XXej/Fzrg1BJWRph0luP/LkCkIpzo6TE8MyBmZR+68OBKLuAwCIY0YwJPu0oeMZDH
					4AzyVXC1uzVXmjjkxoSDdKs6u4OzJiMcBWgaWS6UvtbeyZYm9nr6qyM7AyL5g88tD9d6QOIrDXeqShLWZGQ4sFnRacj5jMbB2HcRMP7Nri2yCBg4tYO4Xg6oVPdIDPwBFfhKELJIU6kiTNju8iyBOK3oo3H
					et6KJ0TAIdx0MEobBRMnBPTjAxYSQHfTV+STKLqChnObtkR5FCqT510GzET0GOOYpGp1cv3GT89SYcHA2cu7SV0MzQKa0c8N7CMl6i9fMIEUM6HgkhEPhMZFGIp6v1W9bgda/mEnX0KpDkQhbiOl9JdOUah
					9iVtVteLD/KIGlsBg7n4B/KcJAkH+Nx8vAArOk16eMtZZbF1rqriUuh+LgCBE3VCXaG+x+q38nADNOdo7955atxNmGwlLh/DUwLVb5XNU/rfV3w8KhH/sLLrYATEf4yDIypNssO/lNG3M7iaKeYW2tvTTzn
					iYamxmzoF2BbvJOhKK4g2yhkmxdSCyXeAJ9Fjz1skvStbBaVKhu//668Jo5hma+q8iHsFFLVuSfeomIDnik6jesFJ0cI+SP0bp7SRQ+ucI6V2+28B9etLZv3QlTD8cr+odhHCobJO/T3rl2E+WCDmsBcpli
					eQ1PKLcGcxs9D6cp4RDPZ0hxrIXGptzqd+HS2GQC/vhqJaDFf8hD2oIyfuHN9XCTQ/v03KuYMDAkSqpVj90IK2nhmGU/LRdbmu6y1FISXy50HeC0lElVCFsJY6u3rfDR1ZYGcMn4veo1hZfBzD65DC50Vugm
					TV1mFJCp0++x9e0gvsCreHCAFjfswszAVkR9R5pY5f3hYi9OQNHnHl4OSHkHN2yrpe+43hU0B4uHGWAWq2YNNRiwvGbbl++kYQcCFnoziy+ttIhM3JCFQ/6FEOnQPHR87x/oIF/t6iFVR9DrzwQqgvwOYIrff
					4ji2jGXOXRNU3Bk9DuUHVW2VBlPOTE9B+bTpcUVplcDqnt+j9cyJepFbf/ePzpcSsyCeyeGtksepRuQI84ONCKLAx//0eNALHdZgMYJboXsTkuWDjbtXtkh8xcjfO0/fh4+sQNkS6lhwaEWzDy/8o2mn5sTD
					javZiORuUdwVJWIoTt++ZK1QsWQUr/rYG2oLhaZvCx1hU4AuPktqGburMaGqqoAkLbiKN//mKW/ei/ww4v6/WlM0TBPYa7j2VdxGEml9xiucgzjb+br97L0+jU9Pun7MgEzW0TDFbZtB8FWQ7u5n1RqOD2dd
					s9xsB/5m0esBRRaaukS+NGu6ruDJt5JoK0WH2uRpTP8M9qoyOILoumHsiJD08LhGpyhYU7hCIpbPItXHK3bAJMfcfQF57osYxPn5GoxCf2peRquO3/5smVfXsn9RVTi7rrGtqTqZXHPXss+/4aFt18PJrX/36
					mNoojjmH3mZQdJ7PltleBT54L1HEAw9Jt4B4sLQoIn4lxXs+m5oe7XEg5pkN4jPoTzemSTq0EJfVbKw2KwWET2XNAmowptubByA2aNgh0RnbKhAh15FOeGEkk2Lxxd5xhds5AEyyGahVkVaTVQP4CzEcUfr41
					NNhJZdtl99cnmH19mR7//C0jr97D1vhPpdqkJk2Zyv5PK7m0OYuonWo2oBUdQ5XWXOu861pF6imFJFBVmuVOuai/O5ZC24IWiCB131VCYm32bu12+yOvvWet//12oNFnDVg5+owKi7eIUZ59aYx0PLOd3cKjc
					fiVkebd14VOrdtrEn4MWDg/tEcL72YKJKQLl73j8JRvL36riDgh7l39BoqYACW0WpzOLihetM7NY89ADWhhClupMSyDhRs2+TIZWHN6sfnhisM0S0QE23VRsE3J9hEfyEYj8nURR3PXVQ2i21rtvpsgn3CkCc
					ICqNY5zrREDe8huwbn+Lz+ms3+LtX5zKeabImYDuIsYhqICGregdecg303BVaqFCY3yRsMCVuugeG/wwsHkhxdN52Rus/afPYWHrNWSXrXUnTdAxjKrfJdX71RSDu5A666qqz736LPE5G+DUiLiqDXInyinkS
					L1m756MaCaRfTXkkd4f7sVDpBX6CUcujOZfBJRXi+YXK0ND8974t5woGhphl6DYmXbeBM/uwnUOx0O1mcJycGp9m5xLP9I7aCxKrOvvOWgBGJTTF01D2eSsns/QGVXXHtvm3U+9CKNUfCKAOtsue8mIsoLGkZ
					79Trsqj8GLRzajgjd0t/8Ag7kHsvCS9rx/Z87mF8Ioa2T2mrVKEwE5fI8a7F2/OgBxyDYs3mo6ticek3DJNRKN1zmN7/onKuEymR4AdDF4wCKsmiI6iF2AD1Oxshg5sZgOo5iOnooMWFt/Qy4zoULyGfMtYgb
					pnT8L3nQfrG7Ai7qe4DynbIrSGARyk/446/AqAiQRykCmagaI0FjF9583zowSV4WVvfLFlj6dz5PdyJaZxjG0GZR1U7YBDN3EU+Tuy9u3ub4LmuqM1K5RUhZOtEyLkwl7+8aq+ucRAQkICPH6vMkjACxOBwCP
					ofsewImtIzs6vljjsUjUyp2w2osRs+vDeNrdcerWy3uONJLOMRMkJTI7iK8jsaBloXW3/uihRTVsg+TKFOTeT9IL4Gh20HFF0nBty6lPgKa3YW7VcdeEk4FYJAHX3frV26i3H87fkYPEFbV95v9c2jCIc1AfB
					4tugT1P8aC96Y6BFIvraG0svwLSPHzL8AR9jA8XHvhqMopIuk7HAQOxFLm2svhOcVEoZp9HF23TcySchNFJxw9blU2I5SUT1tDebWjoZ1W36Oq/Y4TDu5Sb09Q5R7j/xTLYBuR0/iT4D/DTAj3KUynB4thHu6
					OIkmrmmoBM6XPBUby0LQpbghRYYZzZvT6uny7YPYFEOSwYMAXYSi4ccGwmJPK2Q7LLAqipnJ+cNVCbA0C08OcOAjcqnXW8YMHrf3RF0BnkXjqSVVYOZKuZ/XPiqMjA0ltePOS3tvjjopxNgtQZeNw9Pql35oC2
					Q6gR8QkJ2mI5yJA7Qul1WeQ6lcquzRzernvhi+Ei6kHzQRpNvlMIotldvxp6h/1bAbva8mxwp6XLbPxA2vZRWLr33+719wGi2ZbRkuJfeveMpZVNa46JqjP8TXwxrAIR1lTaG8njkzhZLyWNdWgmeyk5qBlMU
					qx7G2tiES/I6UMKr3lzustRdrX9c/2LDGrhoFpMtLrZcHo7QSqsOQqtSdROASQdrTfvQqPOhdIb/IpgM5oSExDt+hw06gfNiTh1aLf4kB2PA/8gSapnnPHH+XR5WMX1HD181dIcn1smd/CzF5DXUaFNG1P/pXM
					u35xYvq7NjC0p+ERjp7HoDAVVaiIRIWoLMWxrOBqPbrWe35dQuG3aMmOGdaCxmj57G0sFDyzLwRRS32L6EXCUaXEtT1NVqgyvnIEJ2m4jnhRU3RrDhxUFwBy4QqBi04EFu51RJhhrQ2Xwi8KQK0n3EQ5OEEKO
					PX1RkxnXgJfaryOAuQZGUJ2yG6gatByHjLrI5XTGXbhEABIy2llv/cT60Rr5HFS++smK5PUnmvjvvNVEOMAZ0FO1yxlS3PAZ2ognGoNj8n2qcqKKuJkjYrmKJuVsvajisbu6RAE1V7cd5jqbG2yODUzhyDsxlI
					xDSY+bUf/4mc1RP7EmbgsMKH68b950DEkT/ivv8Vc0Zerbv8RGhL14RkCuoBnyD/ynHX85Y+tg1UcC7QdOrSY8ht1RgqYfetnrqVQNN9RA7h0bw+fpfIz16wEVXZC6b7WqJiVk7nerTSHa9cMJJBq2VTiTa+Z1
					gIV273kLj6q7Xg734wkl7RImqzomPuI+ODk6H/gkFOHypPrOfKff2DZB16wZOMOlyHt/3eD+3R4NIfCS9oj1QGXffAZ+i9IqR/Q2iq9ncaah8iqzl4r3r6KbyxpWddsYq4SzIpI9msNCYfTHA1kFWv9flDvET
					Z7rIAE0So/VwipR/l+U1tEfJS8BnNTdyj6UnUYB92R5nabp7q/4AN8OVfwfNW9Vn9QCm3tq52hAVqF/rZc/dmwCEeJwlPx5dV2/E9/aPldO4j7uYBMTiMjD9NxjKMpqkWVmH2hlmpoDiGcZL+15HitIeHwJnHn
					nUSz4tLbbSScfEEEdFw9FbEK9Ah8z+xY1YG7BY9eYtXLB5dZ+yNUiGuk4at+0uNPzy25HpMIGwuMMrrvFpJus3t8PvSXQxIOcWiU5F88usyyT78K0GU7dwo2tkHB0OGLn1OsOe2PvcDv91kKOH2aBe9cjQabXB
					m6E9W5FdcRDlWLfWFKT6LmcPzpEki1TNbUefhJCLXOsUy4i17U37AIFVagZZ8mUoGCorCZhCBwwtrbqMxA32exDxUAeh9/+BVLg/xK33bAoqVXD9scDFo4SizVEHNiea0Q/RA1EnIZ5buktn/hlnkg5BKRXHWF
					skQ3udaAK4LpcGvRkjQLF8+n1Y/+Wtdbiy1X/eGA1jyrozkA5jjo/ckUDnF4aQmLXqFs1UUkSis57CtoMKgkYwQj2UbTMwj+zgcIT+FQ0xIZzQpGZS/iGMmuBS13nO2IKVm5l7kzLCCX4lIMlS8O4nnQwiGMZ/
					aRZ+3YPz3moHL9CYaOS1VKrale3C9m4N4Am8pxi+knWQdEECqB1IxpLJs5zzI0a2sR3hI4ixjmnXrC4XAmZCJPpuZwzrMQXLXMoDsp8qFUkZPV71MthigGzEX2+TUUDTkP1klx5YN+nPXKvJwI3et5ZCTM4E+N
					Dy+3/Dtbre1PvlFusO5PaE9svO6rpoXDcZWzjHkny3p2gqRKuMsddXPNXdAtR1UxA6G+WAOFTCoCxM2tead2JRbTIT4u1U1UqRVHWB5AjU+53iJoHzZu5i7rK1gKh71IziG2/2QV3brPl0QUuQ3OFM3Z18/KA/Y
					9+l2ywjjbEu7iTqggcFL7C++1aZ8IKAWHaeZLN7JQAKZn3RY0xCbkpLaAKCoUqFk8p9n7nzWPfaQ/f7NLunUfapMvmhYOqXf1ZsQib1FSqqZ/oRNgukirpy+dzQqSpMLFFUFbZUxWUJQMWmNNqyjKpLhFBqsOXF
					waefGCQToiR0+XoDz5VWaL7XgITrngpmjh5A4PAhePxfiEO601FwXhLXhoPhoZop0WL0hqOjUTVm1o/c59Fr8BvOH5VU6oisxZQhRUawg8lBwhQkKLJJTwHVdKrS82+F7TwqG4PsBhbEU4PACyx59YVqO6qksZs
					SzFNVQLP+W61l3pWXwW0Fe3TQM1NnO6tf/wl5CKsMRXAVvZ70Cb1Psc4QjOpAA1XjmGT2CouZlKaFHN186prD6GxoSi/KuSO5cMHWotf/gV2BRpRqKPNgDyl4FdSbjQIz98DOT/Br7ed7vqzA/Is2TuhvT3G3cN
					SWvoeJoWDkH21Yke0jubQYUrQ5eld1ZEKQLbiD8sBIPZwrJSadaDDemd8FhWqoKudkQutDKkYAfyJk2CeomGpTfBMFDOd8tM1RcDHW+fobBXLH4iTxmWkUc/fYh27OxrImptX5TV/kQR0ZD8YqnVwQyZJLEEZe
					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" alt=""/>
                <div class="title">
                    <h1 class="font18">AIA SINGAPORE <br/>GROUP INSURANCE FACT-FINDING FORM</h1>
                    <h6>Corporate Solutions Department</h6>
                    <p class="font12">3 Tampines Grande, AIA Tampines #07-00, Singapore 528799 Fax : 6538 5601</p>
                    <h3 class="font16">(KINDLY PROVIDE FULL INFORMATION WHERE REQUIRED, LEGIBLY IN INK)</h3>
                </div>
            </div>
            <div class="describe font16">
                In accordance with Section 25(5) of Insurance Act, as may be amended from time to time, you are to fully and faithfully disclose in this Application Form all facts which you know, or ought to know, faiing which you may receive nothing from the policy and/or the policy issued may be void.
            </div>
            <div class="form overflow-hidden nowrap">
                <div class="form-item clearfix">
                    <div class="label fl"><strong>PERIOD OF INSURANCE</strong></div>
                    <div class="form-item-content clearfix inline-block">
                        <div class="sub-item inline-block">
                            <div class="sub-label inline-block">from:</div>
                            <div class="sub-item-content inline-block text-center border-bottom-bold" style="width: 360px;">
                                &nbsp; &nbsp; ${baseInfo.expectStartTime!}
                                <div class="item-describle font12">(dd/mm/yyyy)</div>
                            </div>
                        </div>
                        <div class="sub-item inline-block">
                            <div class="sub-label inline-block">to</div>
                            <div class="sub-item-content inline-block text-center border-bottom-bold" style="width: 360px;">
                                &nbsp; &nbsp; ${baseInfo.expectEndTime!}
                                <div class="item-describle font12">(dd/mm/yyyy)</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-item clearfix">
                    <div class="label fl"><strong>REQUEST FOR QUOTATION</strong></div>
                    <div class="form-item-content clearfix inline-block">
                        <div class="sub-item inline-block">
                            <div class="sub-label inline-block">was submitted on</div>
                            <div class="sub-item-content inline-block text-center border-bottom-bold" style="width: 620px;">
                                &nbsp; &nbsp;   ${baseInfo.quoteTime!}
                                <div class="item-describle font12">(dd/mm/yyyy)</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-item clearfix">
                    <div class="label fl"><strong>REQUEST FROM:</strong></div>
                    <div class="form-item-content clearfix inline-block">
                        <div class="sub-item inline-block">
                            <div class="sub-item-content inline-block border-bottom-bold wrap" style="width: 840px;">
                                <div class="item-describle font12">(Name of Insurance Company)</div>
                            </div>
                        </div>
                    </div>
                </div>
                <h4 class="mb-8">GENERAL INFORMATION</h4>
                <div class="form-item clearfix">
                    <div class="label fl">Name of Company: </div>
                    <div class="form-item-content clearfix inline-block">
                        <div class="sub-item inline-block">
                            <div class="sub-label inline-block"></div>
                            <div class="sub-item-content inline-block wrap border-bottom-bold text-center" style="width: 826px;">
                                &nbsp; &nbsp;  ${baseInfo.companyName!}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-item clearfix">
                    <div class="label fl">Nature of Business:</div>
                    <div class="form-item-content clearfix inline-block">
                        <div class="sub-item inline-block">
                            <div class="sub-label inline-block"></div>
                            <div class="sub-item-content inline-block text-center border-bottom-bold wrap" style="width: 824px;">
                                &nbsp; &nbsp;   ${baseInfo.companyNature!}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mb-8">Presently insured?</div>
                <div class="form-item clearfix">
                    <div class="label fl">If <strong>Yes</strong>, name of current insurer:  </div>
                    <div class="form-item-content clearfix inline-block">
                        <div class="sub-item inline-block">
                            <div class="sub-label inline-block"></div>
                            <div class="sub-item-content inline-block wrap border-bottom-bold text-center" style="width: 738px;">
                                &nbsp; &nbsp;    ${baseInfo.currentEbInsurer!}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-item clearfix">
                    <div class="label fl">Type of Policy:  </div>
                    <div class="form-item-content clearfix inline-block">
                        <div class="sub-item inline-block">
                            <div class="sub-label inline-block"></div>
                            <div class="sub-item-content inline-block text-center wrap border-bottom-bold" style="width: 859px;">
                                &nbsp; &nbsp;   Employee Benefit
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-item clearfix">
                    <div class="label fl">Period of Insurance:</div>
                    <div class="form-item-content clearfix inline-block">
                        <div class="sub-item inline-block">
                            <div class="sub-label inline-block">From:</div>
                            <div class="sub-item-content inline-block border-bottom-bold text-center" style="width: 376px;">
                                &nbsp; &nbsp;    ${baseInfo.insuranceStartTime!}
                                <div class="item-describle font12">(dd/mm/yyyy)</div>
                            </div>
                        </div>
                        <div class="sub-item inline-block">
                            <div class="sub-label inline-block">To</div>
                            <div class="sub-item-content inline-block border-bottom-bold text-center" style="width: 376px;">
                                &nbsp; &nbsp;   ${baseInfo.insuranceEndTime!}
                                <div class="item-describle font12">(dd/mm/yyyy)</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="form-item clearfix">
                    <div class="label fl"></div>
                    <div class="form-item-content clearfix inline-block" style="padding-left: 0;">
                        <div class="sub-item inline-block">
                            <div class="sub-label inline-block">Total No. of Employees:</div>
                            <div class="sub-item-content inline-block text-center border-bottom-bold" style="width: 276px;">
                                &nbsp; &nbsp;   ${baseInfo.totalEmployees!}
                            </div>
                        </div>
                        <div class="sub-item inline-block">
                            <div class="sub-label inline-block">No. of Employees to be insured:</div>
                            <div class="sub-item-content inline-block text-center border-bottom-bold" style="width: 276px;">
                                &nbsp; &nbsp;   ${baseInfo.insuredEmployees!}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="p-content mb-8">
                <strong>Participation: The insurer will assume that participation of the group insurance program is on compulsory basis unless otherwise stated.</strong>
                Please tick [√] accordingly to the choice of the insurance product that you like to have a quote from us.
            </div>
            <table border="1" cellspacing="0">
                <colgroup>
                    <col style="width: 100px;" />
                    <col style="width: 45px;">
                    <col style="width: 200px">
                    <col>
                    <col span="2" style="width: 140px;">
                </colgroup>
                <tr>
                    <th rowspan="2">Benefits</th>
                    <th rowspan="2" colspan="3">Insurance Coverage</th>
                    <th colspan="2">Participation</th>
                </tr>
                <tr>
                    <th>Compulsory</th>
                    <th>Voluntary</th>
                </tr>
                <tr>
                    <td rowspan="4" class="font-bold">Life Insurance</td>
                    <th rowspan="3">1</th>
                    <td colspan="2">Group Term Life (GTL)</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>
                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GTL">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>
                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>
                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td colspan="2">Group Personal Accident (GPA)</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GPA">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td colspan="2">Group Critical Illness (GCI)</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GCI">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <th>2</th>
                    <td colspan="2">Group Disability Income (GDI)</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GDI">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td rowspan="4" class="font-bold">Medical</td>
                    <th rowspan="4">3</th>
                    <td rowspan="2">Group Hospital & Surgical (GHS)</td>
                    <td>Employee only</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GHS">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>

                    <td>Dependant (Spouse and/or Children)</td>
                    <td class="text-center"><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox"  id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td rowspan="2">Group MajorMedical (GMM)</td>
                    <td>Employee only</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GMM">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td>Dependant (Spouse and/or Children)</td>
                    <td class="text-center"><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox"  id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td rowspan="6" class="font-bold">Others</td>
                    <th rowspan="4">4</th>
                    <td rowspan="2">Group Outpatient</td>
                    <td>Employee only</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>
                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GP">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td>Dependant (Spouse and/or Children)</td>
                    <td class="text-center"><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox"  id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td rowspan="2">Dental</td>
                    <td>Employee only</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GD">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td>Dependant (Spouse and/or Children)</td>
                    <td class="text-center"><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox"  id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <th rowspan="2">5</th>
                    <td rowspan="2">Maternity</td>
                    <td>Employee only</td>
                    <#-- 默认两个框都不勾 -->
                    <#assign checked1 = false>
                    <#assign checked2 = false>
                    <#assign hasBenefit = false>

                    <#list userPlanDutyList as item>
                        <#if item.benefit == "GM">
                            <#assign hasBenefit = true>
                            <#if item.participation == "2">
                                <#assign checked2 = true>
                            <#else>
                                <#assign checked1 = true>
                            </#if>
                        </#if>
                    </#list>

                    <#-- 如果没有该险种，则两个框都不勾 -->
                    <#if hasBenefit && !checked2>
                        <#assign checked1 = true>
                    </#if>

                    <td class="text-center"><input type="checkbox" <#if checked1 && !checked2>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox" <#if checked2>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
                <tr>
                    <td>Dependant (Spouse)</td>
                    <td class="text-center"><input type="checkbox" id="myCheckbox1" name="myCheckbox1"></td>
                    <td class="text-center"><input type="checkbox"  id="myCheckbox2" name="myCheckbox2"></td>
                </tr>
            </table>
            <p class="mt-12 italic">Note: Participation is voluntary if employees or dependants are given the choice to opt for the cover(s), subject to a minimum participation level.</p>
            <div class="page-footer">
                <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 1</span></div>
                <p class="page-remark">[AIA-INTERNAL]</p>
            </div>
        </div>
        <!-- 第二页 -->
        <div class="page">
            <div class="table-list big-td clearfix">
                <div class="fl table-index">1</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Are any members currently in hospital or requires frequent admission (e.g. hospital admission more than 2 times per year) to hospital?</div>
                    <div class="mb-8">If <strong>Yes</strong>, kindly provide the following details:</div>
                    <table border="1" cellspacing="0">
                        <tr>
                            <th style="width: 60px;">S/N</th>
                            <th style="width: 140px"># of members /Age</th>
                            <th>Reason of hospitalisation / Nature of illness</th>
                            <th style="width: 160px">Total Sum Insured / Plan</th>
                        </tr>
                        <#assign displayedCount = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="1">
                                <#if displayedCount < 3>
                                    <tr>
                                        <td class="text-center">${displayedCount + 1}</td>
                                        <td class="text-center">${info.number!""}</td>
                                        <td>${info.content!""}</td>
                                        <td class="text-center">${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="4" class="italic text-left py-0">
                                            More information, please refer to the attachment.
                                        </td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#if>
                        </#list>
                        <#list 1..(3 - displayedCount) as i>
                            <#if displayedCount!=0 && displayedCount!=3>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#if>
                        </#list>
                        <#if displayedCount == 0 >
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                        <tr>
                            <td colspan="4" class="italic py-0">
                                Note: The insurer will not reimburse the hospital claims for any member in hospital at the time of application.
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="table-list big-td clearfix">
                <div class="fl table-index">2</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Has any member suffered or is suffering from any serious condition such as cancer, organ failure, heart disease, stroke, liver disorder, arthritis or any other disorder that causes progressive irreversible functional or physical disability?</div>
                    <div class="mb-8">If <strong>Yes</strong>, kindly provide the following details:</div>
                    <table border="1" cellspacing="0">
                        <tr>
                            <th style="width: 60px;">S/N</th>
                            <th style="width: 140px"># of members /Age</th>
                            <th>Reason of hospitalisation / Nature of illness</th>
                            <th style="width: 160px">Total Sum Insured / Plan</th>
                        </tr>
                        <#assign displayedCount = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="2">
                                <#if displayedCount < 3>
                                    <tr>
                                        <td class="text-center">${displayedCount + 1}</td>
                                        <td class="text-center">${info.number!""}</td>
                                        <td>${info.content!""}</td>
                                        <td class="text-center">${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="4" class="italic text-left py-0">
                                            More information, please refer to the attachment.
                                        </td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#if>
                        </#list>
                        <#list 1..(3 - displayedCount) as i>
                            <#if displayedCount!=0 && displayedCount!=3>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#if>
                        </#list>
                        <#if displayedCount == 0 >
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                        <tr>
                            <td colspan="4" class="italic py-0">
                                Note: The insurer will not reimburse the hospital claims for any member in hospital at the time of application.
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="page-footer">
                <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 2</span></div>
                <p class="page-remark">[AIA-INTERNAL]</p>
            </div>
        </div>
        <!-- 第三页 -->
        <div class="page">
            <div class="table-list big-td clearfix">
                <div class="fl table-index">3</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Is there any member based outside Singapore?</div>
                    <div class="mb-8">If <strong>Yes</strong>, kindly provide the following details:</div>
                    <table border="1" cellspacing="0">
                        <tr>
                            <th style="width: 60px">S/N</th>
                            <th style="width: 140px"># of members /Age</th>
                            <th>Country based in</th>
                            <th style="width: 160px">Total Sum Insured / Plan</th>
                        </tr>
                        <#assign displayedCount = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="3">
                                <#if displayedCount < 3>
                                    <tr>
                                        <td class="text-center">${displayedCount + 1}</td>
                                        <td class="text-center">${info.number!""}</td>
                                        <td>${info.content!""}</td>
                                        <td class="text-center">${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="4" class="italic text-left py-0">
                                            More information, please refer to the attachment.
                                        </td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#if>
                        </#list>
                        <#list 1..(3 - displayedCount) as i>
                            <#if displayedCount!=0 && displayedCount!=3>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#if>
                        </#list>
                        <#if displayedCount == 0 >
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                        <tr>
                            <td colspan="4" class="italic py-0">
                                Note: The insurer will not reimburse the hospital claims for any member in hospital at the time of application.
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="table-list big-td clearfix">
                <div class="fl table-index">4</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Are there any limitations or exclusions imposed on the coverage on any members?</div>
                    <div class="mb-8">If <strong>Yes</strong>, kindly provide the following details:</div>
                    <table border="1" cellspacing="0">
                        <tr>
                            <th style="width: 60px">S/N</th>
                            <th style="width: 140px"># of members /Age</th>
                            <th>Limitations / Exclusions</th>
                            <th style="width: 160px">Total Sum Insured / Plan</th>
                        </tr>
                        <#assign displayedCount = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="4">
                                <#if displayedCount < 3>
                                    <tr>
                                        <td class="text-center">${displayedCount + 1}</td>
                                        <td class="text-center">${info.number!""}</td>
                                        <td>${info.content!""}</td>
                                        <td class="text-center">${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="4" class="italic text-left py-0">
                                            More information, please refer to the attachment.
                                        </td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#if>
                        </#list>
                        <#list 1..(3 - displayedCount) as i>
                            <#if displayedCount!=0 && displayedCount!=3>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#if>
                        </#list>
                        <#if displayedCount == 0 >
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                        <tr>
                            <td colspan="4" class="italic py-0">
                                Note: The insurer will not reimburse the hospital claims for any member in hospital at the time of application.
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="page-footer">
                <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 3</span></div>
                <p class="page-remark">[AIA-INTERNAL]</p>
            </div>
        </div>
        <!-- 第四页 -->
        <div class="page">
            <div class="table-list big-td clearfix">
                <div class="fl table-index">5</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Is there any member engaged in hazardous occupation? <br> (Hazardous occupation eg. welder, diver, sandblaster, offshore workers etc.)</div>
                    <div class="mb-8">If <strong>Yes</strong>, kindly provide the following details:</div>
                    <table border="1" cellspacing="0">
                        <tr>
                            <th style="width: 60px">S/N</th>
                            <th style="width: 140px;"># of members /Age</th>
                            <th>Nature of work</th>
                            <th style="width: 160px">Total Sum Insured / Plan</th>
                        </tr>
                        <#assign displayedCount = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="5">
                                <#if displayedCount < 3>
                                    <tr>
                                        <td class="text-center">${displayedCount + 1}</td>
                                        <td class="text-center">${info.number!""}</td>
                                        <td>${info.content!""}</td>
                                        <td class="text-center">${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="4" class="italic text-left py-0">
                                            More information, please refer to the attachment.
                                        </td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#if>
                        </#list>
                        <#list 1..(3 - displayedCount) as i>
                            <#if displayedCount!=0 && displayedCount!=3>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#if>
                        </#list>
                        <#if displayedCount == 0 >
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                        <tr>
                            <td colspan="4" class="italic py-0">
                                Note: The insurer will not reimburse the hospital claims for any member in hospital at the time of application.
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="table-list big-td clearfix">
                <div class="fl table-index">6</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">To the best of your knowledge, is there any member engaged in hazardous sports? <br> (Hazardous sports eg. scuba diving, motor racing, bungee jumping etc.)</div>
                    <div class="mb-8">If <strong>Yes</strong>, kindly provide the following details:</div>
                    <table border="1" cellspacing="0">
                        <tr>
                            <th style="width: 60px">S/N</th>
                            <th style="width: 140px"># of members /Age</th>
                            <th>Type of sports</th>
                            <th style="width: 160px">Total Sum Insured / Plan</th>
                        </tr>
                        <#assign displayedCount = 0>
                        <#list healthInfo as info>
                            <#if info.disclosureType=="6">
                                <#if displayedCount < 3>
                                    <tr>
                                        <td class="text-center">${displayedCount + 1}</td>
                                        <td class="text-center">${info.number!""}</td>
                                        <td>${info.content!""}</td>
                                        <td class="text-center">${(info.totalSumInsured.amount)!""}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="4" class="italic text-left py-0">
                                            More information, please refer to the attachment.
                                        </td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#if>
                        </#list>
                        <#list 1..(3 - displayedCount) as i>
                            <#if displayedCount!=0 && displayedCount!=3>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#if>
                        </#list>
                        <#if displayedCount == 0 >
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                        <tr>
                            <td colspan="4" class="italic py-0">
                                Note: The insurer will not reimburse the hospital claims for any member in hospital at the time of application.
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="page-footer">
                <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 4</span></div>
                <p class="page-remark">[AIA-INTERNAL]</p>
            </div>
        </div>
        <!-- 第五页 -->
        <div class="page page-five">
            <div class="table-list clearfix mb-20">
                <div class="fl table-index font-bold">1</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8 underline-bold"><strong>BENEFIT: GROUP TERM LIFE / GROUP PERSONAL ACCIDENT / GROUP CRITICAL ILLNESS
                        <br> INSURANCE</strong></div>
                    <div class="mb-8"><strong>Occupational Classifications</strong></div>
                    <table border="1" cellspacing="0">
                        <tr>
                            <td style="width: 80px;">Class 1</td>
                            <td>Clerical, administrative or other similar non-hazardous occupations</td>
                        </tr>
                        <tr>
                            <td>Class 2</td>
                            <td>Occupations where some degree of risk is involved, e.g. supervision of manual workers, totally administrative job in an industrial environment</td>
                        </tr>
                        <tr>
                            <td>Class 3</td>
                            <td>Occupations involving regular light to medium manual work but no substantial hazard which may increase the risk of sickness or accident</td>
                        </tr>
                        <tr>
                            <td>Class 4</td>
                            <td>High risk occupations involving heavy manual work including works</td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">a)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Basis of Coverage</div>
                    <!-- GTL 表格 -->
                    <table class="mb-20 table-align border collapse text-center">
                        <colgroup>
                            <col style="width: 90px" />
                            <col style="width: 50px;" />
                            <col span="3" />
                        </colgroup>
                        <tr>
                            <th colspan="2" class="border-none"></th>
                            <th>Category of <br> Employees/Occupation <br> (refer to the examples)</th>
                            <th>Basis of Coverage - Sum <br> Insured (refer to the examples)</th>
                            <th># of Employees</th>
                        </tr>
                        <#assign hasData = false >
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gtl" && personOverview.itemKey == "basis_of_coverage">
                                <#assign total = personOverview.valueMaps?size />
                                <#assign valueMapList = personOverview.value?eval>
                                <#list valueMapList as info>
                                    <#if info_index < 3 && info ??>
                                        <#assign hasData = true >
                                        <tr>
                                            <#if info_index == 0>
                                                <th rowspan="4">GTL</th>
                                            </#if>
                                            <td>
                                                <#if info_index == 0>(i)
                                                <#elseif info_index == 1>(ii)
                                                <#elseif info_index == 2>(iii)
                                                <#elseif info_index == 3>(iv)
                                                </#if>
                                            </td>
                                            <td>${info.category_of_employees_occupation!}</td>
                                            <td>${info.basis_of_coverage_sum_insured!}</td>
                                            <td>${info.no_of_employees!}</td>
                                        </tr>
                                    </#if>
                                    <#if info_index == 3>
                                        <tr>
                                            <td colspan="4" style="text-align:left; font-style:italic;">More information, please refer to attachment.</td>
                                        </tr>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>
                        <#if !hasData>
                            <tr>
                                <th rowspan="4">GTL</th>
                                <td>
                                    (i)
                                </td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>(ii)</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>(iii)</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>(iv)</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                    </table>

                    <!-- GPA 表格 -->
                    <table class="mb-20 table-align border collapse text-center">
                        <colgroup>
                            <col style="width: 90px" />
                            <col style="width: 50px;" />
                            <col span="3" />
                        </colgroup>
                        <#assign hasData = false >
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gpa" && personOverview.itemKey == "basis_of_coverage">
                                <#assign total = personOverview.valueMaps?size />
                                <#assign valueMapList = personOverview.value?eval>
                                <#list valueMapList as info>
                                    <#if info_index < 3 && info ??>
                                        <#assign hasData = true >
                                        <tr>
                                            <#if info_index == 0>
                                                <th rowspan="4">GPA</th>
                                            </#if>
                                            <td>
                                                <#if info_index == 0>(i)
                                                <#elseif info_index == 1>(ii)
                                                <#elseif info_index == 2>(iii)
                                                <#elseif info_index == 3>(iv)
                                                </#if>
                                            </td>
                                            <td>${info.category_of_employees_occupation!}</td>
                                            <td>${info.basis_of_coverage_sum_insured!}</td>
                                            <td>${info.no_of_employees!}</td>
                                        </tr>
                                    </#if>
                                    <#if info_index == 3>
                                        <tr>
                                            <td colspan="4" style="text-align:left; font-style:italic;">More information, please refer to attachment.</td>
                                        </tr>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>
                        <#if !hasData>
                            <tr>
                                <th rowspan="4">GPA</th>
                                <td>
                                    (i)
                                </td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>(ii)</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>(iii)</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                            <tr>
                                <td>(iv)</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#if>
                    </table>

                    <!-- GCI 表格 -->
                    <table class="mb-20 table-align border collapse text-center">
                        <colgroup>
                            <col style="width: 90px" />
                            <col style="width: 50px;" />
                            <col span="3" />
                        </colgroup>
                            <#assign hasData = false >
                            <#list personOverviews as personOverview>
                                <#if personOverview.tag == "gci" && personOverview.itemKey == "basis_of_coverage">
                                    <#assign total = personOverview.valueMaps?size />
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <#if info_index < 3 && info ??>
                                            <#assign hasData = true >
                                            <tr>
                                                <#if info_index == 0>
                                                    <th rowspan="4">GCI</th>
                                                </#if>
                                                <td>
                                                    <#if info_index == 0>(i)
                                                    <#elseif info_index == 1>(ii)
                                                    <#elseif info_index == 2>(iii)
                                                    <#elseif info_index == 3>(iv)
                                                    </#if>
                                                </td>
                                                <td>${info.category_of_employees_occupation!}</td>
                                                <td>${info.basis_of_coverage_sum_insured!}</td>
                                                <td>${info.no_of_employees!}</td>
                                            </tr>
                                        </#if>
                                        <#if info_index == 3>
                                            <tr>
                                                <td colspan="4" style="text-align:left; font-style:italic;">More information, please refer to attachment.</td>
                                            </tr>
                                        </#if>
                                    </#list>
                                </#if>
                            </#list>
                            <#if !hasData>
                                <tr>
                                    <th rowspan="4">GCI</th>
                                    <td>
                                        (i)
                                    </td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>(ii)</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>(iii)</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                                <tr>
                                    <td>(iv)</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#if>
                    </table>
                </div>
            </div>
            <div class="example-list">
                <h4 class="underline-bold">Example 1</h4>
                <table class="mb-20" cellspacing="0">
                    <tr>
                        <th style="text-align: left;">Category of <br>Employees / <br>Occupation</th>
                        <th style="width: 260px; vertical-align: bottom;" class="text-left">Basis of Coverage</th>
                    </tr>
                    <tr>
                        <td>(i) Senior Management (Director, General Manager, Senior Manager)</td>
                        <td> 100,000</td>
                    </tr>
                    <tr>
                        <td>(ii) Manager & Executive</td>
                        <td>50,000</td>
                    </tr>
                    <tr>
                        <td>(iii) All Others</td>
                        <td>25,000</td>
                    </tr>
                </table>
            </div>
            <div class="example-list">
                <h4 class="underline-bold">Example 2</h4>
                <table class="mb-20" cellspacing="0">
                    <tr>
                        <th style="text-align: left;">Category of Employees / Occupation</th>
                        <th style="width: 260px;" class="text-left">Basis of Coverage</th>
                    </tr>
                    <tr>
                        <td>(i) All Employees</td>
                        <td> 24 X Basic Monthly Salary*</td>
                    </tr>
                </table>
            </div>
            <i>* Please provide salary information if the basis of coverage is in terms of basic monthly salary.</i>
            <div class="page-footer">
                <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 5</span></div>
                <p class="page-remark">[AIA-INTERNAL]</p>
            </div>
        </div>
        <!-- 第六页 -->
        <div class="page">
            <div class="table-list clearfix">
                <div class="fl table-index">b)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Please provide Current Non-Medical Limit (if applicable)</div>
                    <div class="form">
                        <div class="form-item clearfix">
                            <div class="label fl" style="width: 200px;">Group Term Life:</div>
                            <div class="form-item-content clearfix fl">
                                <div class="sub-item clearfix">
                                    <div class="sub-label fl">S$</div>
                                    <div class="sub-item-content fl text-center border-bottom-bold" style="width: 200px;">
                                        <#list personOverviews as personOverview>
                                            <#if personOverview.tag == "gtl" && personOverview.itemKey == "current_non_medical_limit">
                                                ${personOverview.value!}
                                            </#if>
                                        </#list>
                                        &nbsp;
                                    </div>
                                </div>
                                <div class="sub-item clearfix">
                                    <div class="sub-label fl"> up to age</div>
                                    <div class="sub-item-content fl text-center border-bottom-bold" style="width: 200px;">
                                        <#list personOverviews as personOverview>
                                            <#if personOverview.tag == "gtl" && personOverview.itemKey == "up_to_age">
                                                ${personOverview.value!}
                                            </#if>
                                        </#list>
                                        &nbsp;
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-item clearfix">
                            <div class="label fl" style="width: 200px;">Group Critical Illness:</div>
                            <div class="form-item-content clearfix fl">
                                <div class="sub-item clearfix">
                                    <div class="sub-label fl">S$</div>
                                    <div class="sub-item-content fl text-center border-bottom-bold" style="width: 200px;">
                                        <#list personOverviews as personOverview>
                                            <#if personOverview.tag == "gci" && personOverview.itemKey == "current_non_medical_limit">
                                                ${personOverview.value!}
                                            </#if>
                                        </#list>
                                        &nbsp;
                                    </div>
                                </div>
                                <div class="sub-item clearfix">
                                    <div class="sub-label fl"> up to age</div>
                                    <div class="sub-item-content fl text-center border-bottom-bold" style="width: 200px;">
                                        <#list personOverviews as personOverview>
                                            <#if personOverview.tag == "gci" && personOverview.itemKey == "up_to_age">
                                                ${personOverview.value!}
                                            </#if>
                                        </#list>
                                        &nbsp;
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">c)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Group Critical Illness: Basis of Coverage</div>
                    <div class="mb-8 clearfix">
                        <div class="fl">Is this benefit an advance of or an additional amount to the Term Life? </div>
                        <div class="fl text-center border-bottom-bold" style="width: 200px;height: 20px;">
                            <#list personOverviews as personOverview>
                                <#if personOverview.tag == "gci" && personOverview.itemKey == "amount_to_the_term_life_option">
                                    ${personOverview.value!}
                                </#if>
                            </#list>
                        </div>
                    </div>
                    <div class="mb-8">If it is an advance benefit, what percentage on the Term Life sum insured you want us to quote? Please circle as appropriate:
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gci" && personOverview.itemKey == "acceleration_percentage_on_sum_assured">
                                ${personOverview.value!}
                            </#if>
                        </#list>
                        <br><br>Please provide a list of critical illnesses covered (if currently insured).</div>
                </div>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">d)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Details of Employees</div>
                    <table class="text-center border collapse">
                        <tr>
                            <th class="border-none"></th>
                            <th colspan="4">GTL</th>
                            <th colspan="4">GCI</th>
                        </tr>
                        <tr>
                            <th rowspan="2">Age <br>Band <br>(Age <br>Next <br>Birthday)</th>
                            <th colspan="2"># of Employees</th>
                            <th colspan="2">Total Sum Insured (S$)</th>
                            <th colspan="2"># of Employees</th>
                            <th colspan="2">Total Sum Insured (S$)</th>
                        </tr>
                        <tr>
                            <th>Male</th>
                            <th>Female</th>
                            <th>Male</th>
                            <th>Female</th>
                            <th>Male</th>
                            <th>Female</th>
                            <th>Male</th>
                            <th>Female</th>
                        </tr>
                        <#assign hasData6= false>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag=="gtl" && personOverview.itemKey=="age_profile_of_employees">
                                <#assign valueMapList = personOverview.value?eval>
                                <#list valueMapList  as info>
                                    <#if info.age_band??>
                                        <#assign hasData6= true>
                                        <tr>
                                            <th>${info.age_band!}</th>
                                            <td>${info.no_of_employees_male!}</td>
                                            <td>${info.no_of_employees_female!}</td>
                                            <td>${info.total_sum_insured_male!}</td>
                                            <td>${info.total_sum_insured_female!}</td>
                                            <#assign gciInfoFound = false>
                                            <#list personOverviews as personOverview>
                                                <#if personOverview.tag=="gci" && personOverview.itemKey=="age_profile_of_employees">
                                                    <#assign gcivalueMapList = personOverview.value?eval>
                                                    <#list gcivalueMapList as info1>
                                                        <#if info1.age_band?? && info1.age_band==info.age_band>
                                                            <#assign gciInfoFound = true>
                                                            <td>${info1.no_of_employees_male!}</td>
                                                            <td>${info1.no_of_employees_female!}</td>
                                                            <td>${info1.total_sum_insured_male!}</td>
                                                            <td>${info1.total_sum_insured_female!}</td>
                                                        </#if>
                                                    </#list>
                                                </#if>
                                            </#list>
                                            <#if !gciInfoFound >
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </#if>
                                        </tr>
                                        <#if info.age_band?? && info.age_band=="66-70">
                                            <tr>
                                                <th>71-75</th>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                            <tr>
                                                <th>Above 75</th>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                                <td></td>
                                            </tr>
                                        </#if>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>
                        <#if !hasData6>
                            <tr>
                                <th>16-30</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>31-35</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>36-40</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>41-45</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>46-50</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>51-55</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>56-60</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>61-65</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>66-70</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>

                            <tr>
                                <th>71-75</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>Above 75</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                            <tr>
                                <th>Total</th>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                                <td></td>
                            </tr>
                        </#if>

                    </table>
                </div>
            </div>
            <div class="page-footer">
                <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 6</span></div>
                <p class="page-remark">[AIA-INTERNAL]</p>
            </div>
        </div>
        <!-- 第七页 -->
        <div class="page">
            <div class="table-list clearfix">
                <div class="fl table-index">e)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Claims Experience for the past 3 years</div>
                    <h5 style="font-size: 1em">Paid Claims</h5>
                    <table class="mb-20 text-center" border="1" cellspacing="0">
                        <tr>
                            <th rowspan="2">Period of <br>Coverage <br>From / To <br> <span style="display: inline-block; width: 6em; border-bottom: 2px solid #333;"></span> <br /> ( dd/mm/yyyy)</th>
                            <th rowspan="2"># of Insured as <br>at <span style="display: inline-block; width: 4em; border-bottom: 2px solid #333;"></span>
                                <br> (dd/mm/yyyy)</th>
                            <th colspan="2">GTL</th>
                            <th colspan="2">GPA</th>
                            <th colspan="2">GCI</th>
                        </tr>
                        <tr>
                            <th># of Claims</th>
                            <th>Amount(S$)</th>
                            <th># of Claims</th>
                            <th>Amount(S$)</th>
                            <th># of Claims</th>
                            <th>Amount(S$)</th>
                        </tr>

                        <#if isUploadClaimAttach?? && isUploadClaimAttach>
                            <tr>
                                <td  colspan="8" class="text-left italic">Please refer to attachment</td>
                            </tr>
                            <#list 1..2 as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>

                        <#assign displayedCount = 0>
                        <#list claimLogs?keys as key>
                            <#if key == "GTL">
                                <#assign gpclaimList = claimLogs[key]>
                                <#list gpclaimList as claim>
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${claim.startTime}</td>
                                            <td>${claim.endTime}</td>
                                            <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                            <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                            <#assign gpclaimList = claimLogs["GPA"]![]>
                                            <#assign spshowflg = false>
                                            <#list gpclaimList as spclaim>
                                                <#if spclaim.startTime == claim.startTime && spclaim.endTime == claim.endTime>
                                                    <#assign spshowflg = true>
                                                    <td>${(spclaim.paidClaimsNum??)?then(spclaim.paidClaimsNum, "")}</td>
                                                    <td>${(spclaim.paidAmount?? && spclaim.paidAmount.amount??)?then(spclaim.paidAmount.amount, "")}</td>
                                                </#if>
                                            </#list>
                                            <#if !spshowflg>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </#if>
                                            <#assign gdclaimList = claimLogs["GCI"]![]>
                                            <#assign gdshowflg = false>
                                            <#list gdclaimList as gdclaim>
                                                <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                    <#assign gdshowflg = true>
                                                    <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                                    <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                                </#if>
                                            </#list>
                                            <#if !gdshowflg>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </#if>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>


                        <#list claimLogs?keys as key>
                            <#if key == "GPA" && displayedCount < 3>
                                <#assign gpclaimList = claimLogs[key]>
                                <#list gpclaimList as claim>
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${claim.startTime}</td>
                                            <td>${claim.endTime}</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                            <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                            <#assign gdclaimList = claimLogs["GCI"]![]>
                                            <#assign gdshowflg = false>
                                            <#list gdclaimList as gdclaim>
                                                <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                    <#assign gdshowflg = true>
                                                    <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                                    <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                                </#if>
                                            </#list>
                                            <#if !gdshowflg>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </#if>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                        </tr>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>
                        <#list claimLogs?keys as key>
                            <#if key == "GCI" && displayedCount < 3>
                                <#assign gpclaimList = claimLogs[key]>
                                <#list gpclaimList as claim>
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${claim.startTime}</td>
                                            <td>${claim.endTime}</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>&nbsp;</td>
                                            <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                            <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                        </tr>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>

                        <#if displayedCount < 3 && !isUploadClaimAttach>
                            <#list displayedCount..2 as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>
                        <tr>
                            <td colspan="8" class="text-left">
                                <i>Note: The insurer reserves the right to request for more information.</i>
                            </td>
                        </tr>
                    </table>
                    <h5 style="font-size: 1em">Outstanding Claims</h5>
                    <table class="mb-20 text-center" border="1" cellspacing="0">
                        <tr>
                            <th rowspan="2">Period of <br>Coverage <br>From / To <br> <span class="line-6em"></span> <br> ( dd/mm/yyyy)</th>
                            <th rowspan="2"># of Insured as <br>at <span class="line-4em"></span> <br> (dd/mm/yyyy)</th>
                            <th colspan="2">GTL</th>
                            <th colspan="2">GPA</th>
                            <th colspan="2">GCI</th>
                        </tr>
                        <tr>
                            <th># of Claims</th>
                            <th>Amount(S$)</th>
                            <th># of Claims</th>
                            <th>Amount(S$)</th>
                            <th># of Claims</th>
                            <th>Amount(S$)</th>
                        </tr>
                        <#--判断是否上传文件-->
                        <#if isUploadClaimAttach?? && isUploadClaimAttach>
                            <tr>
                                <td  colspan="8" class="text-left italic">Please refer to attachment</td>
                            </tr>
                            <#list 1..2 as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>
                        <#assign displayedCount = 0>
                        <#list claimLogs?keys as key>
                            <#if key == "GTL">
                                <#assign gpclaimList = claimLogs[key]>
                                <#list gpclaimList as claim>
                                    <#if displayedCount < 3>
                                        <tr>
                                            <td>${claim.startTime}</td>
                                            <td>${claim.endTime}</td>
                                            <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                            <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                            <#assign gpclaimList = claimLogs["GPA"]![]>
                                            <#assign spshowflg = false>
                                            <#list gpclaimList as spclaim>
                                                <#if spclaim.startTime == claim.startTime && spclaim.endTime == claim.endTime>
                                                    <#assign spshowflg = true>
                                                    <td>${(spclaim.outstandingClaimsNum??)?then(spclaim.outstandingClaimsNum, "")}</td>
                                                    <td>${(spclaim.outstandingAmount?? && spclaim.outstandingAmount.amount??)?then(spclaim.outstandingAmount.amount, "")}</td>
                                                </#if>
                                            </#list>
                                            <#if !spshowflg>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </#if>
                                            <#assign gdclaimList = claimLogs["GCI"]![]>
                                            <#assign gdshowflg = false>
                                            <#list gdclaimList as gdclaim>
                                                <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                    <#assign gdshowflg = true>
                                                    <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                                    <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                                </#if>
                                            </#list>
                                            <#if !gdshowflg>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                            </#if>
                                        </tr>
                                        <#assign displayedCount = displayedCount + 1>
                                    <#else>
                                        <tr>
                                            <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                        </tr>
                                        <#break>
                                    </#if>
                                </#list>
                            </#if>
                        </#list>
                        <#list claimLogs?keys as key>
                                <#if key == "GPA" && displayedCount < 3 >
                                    <#assign gpclaimList = claimLogs[key]>
                                    <#list gpclaimList as claim>
                                        <#if displayedCount < 3>
                                            <tr>
                                                <td>${claim.startTime}</td>
                                                <td>${claim.endTime}</td>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                                <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                                <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                                <#assign gdclaimList = claimLogs["GCI"]![]>
                                                <#assign gdshowflg = false>
                                                <#list gdclaimList as gdclaim>
                                                    <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                                        <#assign gdshowflg = true>
                                                        <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                                        <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                                    </#if>
                                                </#list>
                                                <#if !gdshowflg>
                                                    <td>&nbsp;</td>
                                                    <td>&nbsp;</td>
                                                </#if>
                                            </tr>
                                            <#assign displayedCount = displayedCount + 1>
                                        <#else>
                                            <tr>
                                                <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                            </tr>
                                            <#break>
                                        </#if>
                                    </#list>
                                </#if>
                        </#list>
                        <#list claimLogs?keys as key>
                                <#if key == "GCI" && displayedCount < 3 >
                                    <#assign gpclaimList = claimLogs[key]>
                                    <#list gpclaimList as claim>
                                        <#if displayedCount < 3>
                                            <tr>
                                                <td>${claim.startTime}</td>
                                                <td>${claim.endTime}</td>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                                <td>&nbsp;</td>
                                                <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                                <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                            </tr>
                                            <#assign displayedCount = displayedCount + 1>
                                        <#else>
                                            <tr>
                                                <td colspan="8" class="text-left italic">More information, please refer to the attachment.</td>
                                            </tr>
                                            <#break>
                                        </#if>
                                    </#list>
                                </#if>
                        </#list>
                        <#-- 不存在数据时显示空行-->
                        <#if displayedCount < 3 && !isUploadClaimAttach>
                            <#list displayedCount..2 as i>
                                <tr>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </tr>
                            </#list>
                        </#if>
                        <tr>
                            <td colspan="8" class="text-left italic">
                                <i>Note: The insurer reserves the right to request for more information.</i>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="page-footer">
                <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 7</span></div>
                <p class="page-remark">[AIA-INTERNAL]</p>
            </div>
        </div>
        <!-- 第八页 -->
        <div class="page page-five">
            <div class="table-list clearfix">
                <div class="fl table-index font-bold">2</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8 underline-bold underline"><strong>BENEFIT: GROUP DISABILITY INCOME INSURANCE</strong></div>
                </div>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">a)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">If currently insured, please attach a copy of the definition of Disability.</div>
                </div>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">b)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8 nowrap">
                        <div class="fl">What is the waiting period required? Please circle as appropriate: 3 or 6 months or</div>
                        <div class="fl wrap border-bottom-bold" style="width: 200px; height: 20px;"></div>
                    </div>
                </div>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">c)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8 clearfix nowrap">
                        <div class="fl">What the benefit duration required?</div>
                        <div class="fl wrap border-bottom-bold wrap" style="width: 574px;height: 20px;"></div>
                    </div>
                    <div style="text-align: left;">(ie. 2 years, or 5 years, or up to retirement age 60 or 62, or 65)</div>
                </div>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">d)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8 nowrap">
                        <div class="fl">What is the escalation benefit required? Please circle as appropriate: 0% or 3% or 5% or</div>
                        <div class="fl wrap border-bottom-bold" style="width: 200px;height: 20px;"></div>
                    </div>
                </div>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">e)</div>
                <div class="fl" style="width: 940px;">
                    <div class="fl">Please provide Current Non-Medical Limit (if applicable): S$</div>
                    <div class="fl border-bottom-bold" style="width: 150px;height: 20px;"></div>
                    <div class="fl">up to age</div>
                    <div class="fl border-bottom-bold" style="width: 150px;height: 20px;"></div>
                </div>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">f)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Any requirement for partial disability benefits?</div>
                </div>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">g)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Basis of Coverage </div>
                </div>
                <table class="mb-8 text-center" border="1" cellspacing="0">
                    <colgroup>
                        <col style="width: 4em;">
                        <col />
                        <col span="2" style="width: 7em" />
                        <col style="width: 200px;" />
                    </colgroup>
                    <tr>
                        <th colspan="2" rowspan="2">Category of Employees / Occupation</th>
                        <th colspan="2">Monthly Salary (S$)</th>
                        <th rowspan="2">Basis of Coverage i.e. % (e.g. 50%) of monthly salary</th>
                    </tr>
                    <tr>
                        <th>Highest*</th>
                        <th>Average*</th>
                    </tr>
                    <tr>
                        <td>(i)</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>(ii)</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>(iii)</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>(iv)</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td colspan="5" class="text-left">
                            <i>* Applicable to the category of employees as stated. Monthly salary will be basic pay + fixed bonus if any. It excludesvariable bonus, commissions, etc.</i>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">h)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Details of Employees </div>
                </div>
                <table class="mb-8 text-center" border="1" cellspacing="0">
                    <tr>
                        <th rowspan="2">Age Band (Age <br>Next Birthday)</th>
                        <th colspan="2"># of Employees</th>
                        <th colspan="2"> Sum Insured (S$)</th>
                    </tr>
                    <tr>
                        <th>Male</th>
                        <th>Female</th>
                        <th>Male</th>
                        <th>Female</th>
                    </tr>
                    <tr>
                        <th>16-30</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>31-35</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>36-40</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>41-45</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>46-50</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>51-55</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>56-60</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>61-65</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>66-70</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>Total</th>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                </table>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">i)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Claims Experience for past 3 years </div>
                </div>
                <table class="mb-8 text-center" border="1" cellspacing="0">
                    <tr>
                        <td rowspan="2" class="text-left font-bold">Date of Disability <br> <span style="width: 6em; border-bottom: 2px solid #333; display: inline-block;"></span> (dd/mm/yyyy)</td>
                        <td rowspan="2" class="text-left font-bold">Cause of Disability / <br /> Nature of Illness</td>
                        <th colspan="2">Claims Amount (S$)</th>
                    </tr>
                    <tr>
                        <td class="text-center font-bold">Paid</td>
                        <td class="text-center font-bold">Outstanding</td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td>&nbsp;</td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <td colspan="4" class="text-left">
                            <i>Note: The Insurer reserves the right to request for more information.</i>
                        </td>
                    </tr>
                </table>
            </div>
            <div class="page-footer">
                <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 8</span></div>
                <p class="page-remark">[AIA-INTERNAL]</p>
            </div>
        </div>
        <!-- 第九页 -->
        <div class="page">
            <div class="table-list clearfix">
                <div class="fl table-index font-bold">3</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8 font-bold underline" style="text-decoration-thickness: 2px;">BENEFIT: GROUP HOSPITAL & SURGICAL INSURANCE / MAJOR MEDICAL INSURANCE</div>
                </div>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">a)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Basis of Coverage</div>
                </div>
                <table class="mb-20 text-center" border="1" cellspacing="0">
                    <colgroup>
                        <col style="width: 4em;">
                        <col style="width: 30%;">
                    </colgroup>
                    <tr>
                        <th colspan="2">Category of Employees / Occupation</th>
                        <th>Room & Board Benefit Plan (S$)</th>
                        <th>Currently with TMIS <br /> Yes / No</th>
                        <th>Proposal with TMIS <br /> Yes / No</th>
                        <th>Medical Insurance for S Pass and Work Permit holders <br /> Yes / No</th>
                    </tr>
                    <#assign hasData = false>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag=="ghs" && personOverview.itemKey=="basis_of_coverage">
                            <#assign hasData = true>
                            <#assign valueMapList = personOverview.value?eval>
                            <#list valueMapList as info>
                                <#if info_index < 3>
                                    <tr>
                                        <#if info_index == 0>
                                            <td>(i)</td>
                                        <#elseif info_index == 1>
                                            <td>(ii)</td>
                                        <#elseif info_index == 2>
                                            <td>(iii)</td>
                                        </#if>
                                        <td>${info.category_of_employees_occupation!""}</td>
                                        <td>${info.room_and_board_benefit_plan!""}</td>
                                        <td>${(info.currently_with_tmis?? && info.currently_with_tmis=="1")?then("Yes", "No")}</td>
                                        <td>${(info.proposal_with_tmis?? && info.proposal_with_tmis=="1")?then("Yes", "No")}</td>
                                        <td>${(info.medical_insurance_for_pass_and_work?? && info.medical_insurance_for_pass_and_work=="1")?then("Yes", "No")}</td>
                                    </tr>
                                <#else>
                                    <tr>
                                        <td colspan="6"> More information, please refer to the attachment.</td>
                                    </tr>
                                    <#break>
                                </#if>

                            </#list>
                            <#if valueMapList?size < 3>
                                <#list valueMapList?size..2 as i>
                                    <tr>
                                        <#if i == 0>
                                            <td>(i)</td>
                                        <#elseif i == 1>
                                            <td>(ii)</td>
                                        <#elseif i == 2>
                                            <td>(iii)</td>
                                        </#if>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </tr>
                                </#list>
                            </#if>
                        </#if>
                    </#list>
                    <#if !hasData>
                        <#list 1..3 as i>
                            <tr>
                                <#if i == 1>
                                    <td>(i)</td>
                                <#elseif i == 2>
                                    <td>(ii)</td>
                                <#elseif i == 3>
                                    <td>(iii)</td>
                                </#if>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#list>
                    </#if>
                    <#if !hasData>
                        <tr>
                            <td>(i)</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>(ii)</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                        <tr>
                            <td>(iii)</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#if>
                </table>
                <div><strong>Important Note:</strong></div>
                <p><strong>(1) Dependants can be covered under Group Hospital & Surgical Plan. Their cover should be the same as theemployee's cover.</strong></p>
                <p><strong>(2) Please provide the Deductible /Co-insurance for respective employee category or occupation, if applicable.</strong></p>
            </div>
            <div class="example-list">
                <h4 class="underline underline-bold">Example 1</h4>
                <table class="mb-20" cellspacing="0">
                    <tr>
                        <th style="text-align: left;">Category of Employees / Occupation</th>
                        <th style="width: 200px; text-align: left;"> R&B Benefit Plan (S$)</th>
                    </tr>
                    <tr>
                        <td>(i) Senior Management (Director, General Manager, Senior Manager)</td>
                        <td>360</td>
                    </tr>
                    <tr>
                        <td>(ii) Manager & Executive</td>
                        <td>200</td>
                    </tr>
                    <tr>
                        <td>(iii) All Others</td>
                        <td>100</td>
                    </tr>
                </table>
            </div>
            <div class="table-list clearfix">
                <div class="fl table-index">b)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Age Profile of Employees</div>
                </div>
                <table border="1" cellspacing="0">
                    <tr>
                        <td rowspan="2" class="font-bold text-center">Age Band (Age Next Birthday)</td>
                        <td colspan="2" class="font-bold text-center"># of Employees</td>
                    </tr>
                    <tr>
                        <td class="text-center font-bold">Male</td>
                        <td class="font-bold text-center">Female</td>
                    </tr>
                    <#assign  hasData9=false>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag=="ghs" && personOverview.itemKey=="age_profile_of_employees">
                            <#assign valueMapList = personOverview.value?eval>
                            <#list valueMapList as info>
                                <#if info.age_band??>
                                    <#assign  hasData9=true>
                                    <tr>
                                            <td class="font-bold text-center">${info.age_band!}</td>
                                            <td class="text-center">${info.no_of_employees_male!}</td>
                                            <td class="text-center">${info.no_of_employees_female!}</td>

                                    </tr>
                                    <#if info.age_band?? && info.age_band=="66-70">
                                        <tr>
                                            <th>71-75</th>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                        <tr>
                                            <th>Above 75</th>
                                            <td></td>
                                            <td></td>
                                        </tr>
                                    </#if>
                                </#if>
                            </#list>
                        </#if>
                    </#list>
                    <#if !hasData9>
                        <tr>
                            <th>16-30</th>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <th>31-35</th>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <th>36-40</th>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <th>41-45</th>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <th>46-50</th>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <th>51-55</th>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <th>56-60</th>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <th>61-65</th>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <th>66-70</th>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <th>71-75</th>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <th>Above 75</th>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <th>Total</th>
                            <td></td>
                            <td></td>
                        </tr>
                    </#if>
                </table>
            </div>
            <div class="page-footer">
                <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 9</span></div>
                <p class="page-remark">[AIA-INTERNAL]</p>
            </div>
        </div>
        <!-- 第十页 -->
        <div class="page">
            <div class="table-list clearfix">
                <div class="fl table-index">c)</div>
                <div class="fl" style="width: 940px;">
                    <div class="mb-8">Details of Insured Members</div>
                </div>
                <h3 class="mb-20 underline-bold">For GHS and GMM:</h3>
                <table class="mb-20 text-center border collapse">
                    <tr>
                        <td class="border-none"></td>
                        <th colspan="4"># of Employees (Singaporeans & SPRs*)</th>
                    </tr>
                    <tr>
                        <#assign hasData = false />
                        <#assign noOfEmployeesPlanList = []>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_sgp">
                                <#if personOverview.valueMaps?has_content>
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                            <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                            <#assign hasData = true />
                                        </#if>
                                    </#list>
                                </#if>
                            </#if>
                        </#list>
                        <#if hasData>
                            <tr>
                                <td></td>
                                <#list noOfEmployeesPlanList as info>
                                    <#if info_index < 4>
                                        <th>${info} </th>
                                    </#if>
                                </#list>
                                <#if noOfEmployeesPlanList?size < 4>
                                    <#list 1..(4 - noOfEmployeesPlanList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                        <#else>
                            <tr>
                                <td>&nbsp;</td>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                        </#if>
                    </tr>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_sgp">
                            <#-- 检查valueMaps是否有数据（避免空表格） -->
                            <#if personOverview.valueMaps?has_content>
                                <tr>
                                    <td class="text-left font-bold">Employee Only</td>
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_only)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                                <tr>
                                    <td class="font-bold text-left">Employee & Spouse</td>
                                    <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_and_spouse)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                                <tr>
                                    <td class="text-left font-bold">Employee & Child(ren)</td>
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_and_children)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                                <tr>
                                    <td class="font-bold text-left">Employee & Family</td>
                                        <#assign valueMapList = personOverview.value?eval>
                                        <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_and_family)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                            </#if>
                        </#if>
                    </#list>
                    <#if !hasData>
                        <tr>
                            <td class="text-left font-bold" >Employee Only</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="text-left font-bold" >Employee & Spouse</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="text-left font-bold">Employee & Child(ren)</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="text-left font-bold">Employee & Family</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                    </#if>
                    <tr>
                        <td colspan="5" class="text-left"><i>* refers to Singapore Permanent Residents</i></td>
                    </tr>
                </table>
                <table class="mb-20 text-center border collapse">
                    <tr>
                        <td class="border-none"></td>
                        <th colspan="4"># of Employees (Foreigners* only)</th>
                    </tr>
                    <tr>
                        <#assign hasData = false />
                        <#assign noOfEmployeesPlanList = []>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_foreigners">
                                <#if personOverview.valueMaps?has_content>
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                            <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                            <#assign hasData = true />
                                        </#if>
                                    </#list>
                                </#if>
                            </#if>
                        </#list>
                        <#if hasData>
                            <tr>
                                <td></td>
                                <#list noOfEmployeesPlanList as info>
                                    <#if info_index < 4>
                                        <th>${info} </th>
                                    </#if>
                                </#list>
                                <#if noOfEmployeesPlanList?size < 4>
                                    <#list 1..(4 - noOfEmployeesPlanList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                        <#else>
                            <tr>
                                <td>&nbsp;</td>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                        </#if>
                    </tr>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "ghs" && personOverview.itemKey == "details_of_insured_members_foreigners">
                            <#-- 检查valueMaps是否有数据（避免空表格） -->
                            <#assign valueMapList = personOverview.value?eval>
                            <#if personOverview.valueMaps?has_content>
                                <tr>
                                    <td class="text-left font-bold">Employee Only</td>

                                        <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_only)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                                <tr>
                                    <td class="font-bold text-left">Employee & Spouse</td>
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_and_spouse)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                                <tr>
                                    <td class="text-left font-bold">Employee & Child(ren)</td>
                                    <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_and_children)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                                <tr>
                                    <td class="font-bold text-left">Employee & Family</td>
                                    <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_and_family)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                            </#if>
                        </#if>
                    </#list>
                    <#if !hasData>
                        <tr>
                            <td class="text-left font-bold" >Employee Only</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="text-left font-bold" >Employee & Spouse</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="text-left font-bold" >Employee & Child(ren)</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="text-left font-bold" >Employee & Family</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                    </#if>
                    <tr>
                        <td colspan="5" class="text-left italic"><i>* refers to all foreigners holding Employment Pass, S Pass and Work Permit, working in Singapore</i></td>
                    </tr>
                </table>
                <h3 class="mb-20 underline-bold">For GMM (if the basis of coverage differs from GHS):</h3>
                <table class="mb-20 text-center border collapse">
                    <tr>
                        <td class="border-none"></td>
                        <th colspan="4"># of Employees (Singaporeans & SPRs*)</th>
                    </tr>
                        <#assign hasData = false />
                        <#assign noOfEmployeesPlanList = []>
                        <#list personOverviews as personOverview>
                            <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_sgp">
                                <#if personOverview.valueMaps?has_content>
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                            <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                            <#assign hasData = true />
                                        </#if>
                                    </#list>
                                </#if>
                            </#if>
                        </#list>
                        <#if hasData>
                            <tr>
                                <td></td>
                                <#list noOfEmployeesPlanList as info>
                                    <#if info_index < 4>
                                        <th>${info} </th>
                                    </#if>
                                </#list>
                                <#if noOfEmployeesPlanList?size < 4>
                                    <#list 1..(4 - noOfEmployeesPlanList?size) as i>
                                        <td></td>
                                    </#list>
                                </#if>
                            </tr>
                        <#else>
                            <tr>
                                <td>&nbsp;</td>
                                <th></th>
                                <th></th>
                                <th></th>
                                <th></th>
                            </tr>
                        </#if>
                </tr>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_sgp">
                            <#-- 检查valueMaps是否有数据（避免空表格） -->
                            <#if personOverview.valueMaps?has_content>
                                <tr>
                                    <td class="text-left font-bold">Employee Only</td>
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_only)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                                <tr>
                                    <td class="font-bold text-left">Employee & Spouse</td>
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_and_spouse)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                                <tr>
                                    <td class="text-left font-bold">Employee & Child(ren)</td>
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_and_children)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                                <tr>
                                    <td class="font-bold text-left">Employee & Family</td>
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_and_family)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                            </#if>
                        </#if>
                    </#list>
                    <#if !hasData>
                        <tr>
                            <td class="text-left font-bold" >Employee Only</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="text-left font-bold" >Employee & Spouse</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="text-left font-bold">Employee & Child(ren)</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="text-left font-bold">Employee & Family</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                    </#if>


                <tr>
                    <td colspan="5" class="text-left"><i>* refers to Singapore Permanent Residents</i></td>
                </tr>
            </table>
            <table class="mb-20 text-center border collapse">
                <tr>
                    <td class="border-none"></td>
                    <th colspan="4"># of Employees (Foreigners* only)</th>
                </tr>
                <tr>
                    <#assign hasData = false />
                    <#assign noOfEmployeesPlanList = []>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_foreigners">
                            <#if personOverview.valueMaps?has_content>
                                <#assign valueMapList = personOverview.value?eval>
                                <#list valueMapList as info>
                                    <#if !noOfEmployeesPlanList?seq_contains(info.no_of_employees_plan!0)>
                                        <#assign noOfEmployeesPlanList = noOfEmployeesPlanList + [info.no_of_employees_plan!0]>
                                        <#assign hasData = true />
                                    </#if>
                                </#list>
                            </#if>
                        </#if>
                    </#list>
                    <#if hasData>
                        <tr>
                            <td></td>
                            <#list noOfEmployeesPlanList as info>
                                <#if info_index < 4>
                                    <th>${info} </th>
                                </#if>
                            </#list>
                            <#if noOfEmployeesPlanList?size < 4>
                                <#list 1..(4 - noOfEmployeesPlanList?size) as i>
                                    <td></td>
                                </#list>
                            </#if>
                        </tr>
                    <#else>
                        <tr>
                            <td>&nbsp;</td>
                            <th></th>
                            <th></th>
                            <th></th>
                            <th></th>
                        </tr>
                    </#if>
                </tr>
                    <#list personOverviews as personOverview>
                        <#if personOverview.tag == "gmm" && personOverview.itemKey == "details_of_insured_members_foreigners">
                            <#-- 检查valueMaps是否有数据（避免空表格） -->
                            <#assign valueMapList = personOverview.value?eval>
                            <#if personOverview.valueMaps?has_content>
                                <tr>
                                    <td class="text-left font-bold">Employee Only</td>

                                    <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_only)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                                <tr>
                                    <td class="font-bold text-left">Employee & Spouse</td>
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_and_spouse)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                                <tr>
                                    <td class="text-left font-bold">Employee & Child(ren)</td>
                                    <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_and_children)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                                <tr>
                                    <td class="font-bold text-left">Employee & Family</td>
                                    <#list valueMapList as info>
                                        <#if info_index < 4>
                                            <td>${(info.employee_and_family)!""}</td>
                                        </#if>
                                    </#list>
                                    <#if valueMapList?size < 4>
                                        <#list 1..(4 - valueMapList?size) as i>
                                            <td></td>
                                        </#list>
                                    </#if>
                                </tr>
                            </#if>
                        </#if>
                    </#list>
                    <#if !hasData>
                        <tr>
                            <td class="text-left font-bold" >Employee Only</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="text-left font-bold" >Employee & Spouse</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="text-left font-bold" >Employee & Child(ren)</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td class="text-left font-bold" >Employee & Family</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                    </#if>
                <tr>
                    <td colspan="5" class="text-left"><i>* refers to all foreigners holding Employment Pass, S Pass and Work Permit, working in Singapore</i></td>
                </tr>
            </table>
        </div>
        <div class="page-footer">
            <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 10</span></div>
            <p class="page-remark">[AIA-INTERNAL]</p>
        </div>
    </div>
    <!-- 第十一页 -->
    <div class="page">
        <div class="table-list clearfix">
            <div class="fl table-index">d)</div>
            <div class="fl" style="width: 940px;">
                <div class="mb-8">Claims Experience for the past 3 years</div>
            </div>
            <table class="mb-20 text-center" border="1" cellspacing="0">
                <tr>
                    <th rowspan="2">Period of Coverage From / To <br> <span class="line-6em"></span> <br> (dd/mm/yyyy)</th>
                    <th rowspan="2"># of Insured as at <br> <span class="line-4em"></span> <br> (dd/mm/yyyy) </th>
                    <th colspan="2">Paid Claims</th>
                    <th colspan="2">Outstanding Claims</th>
                </tr>
                <tr>
                    <th># of Claims</th>
                    <th>Amount (S$)</th>
                    <th># of Claims</th>
                    <th>Amount (S$)</th>
                </tr>
                <#if isUploadClaimAttach?? && isUploadClaimAttach>
                    <tr>
                        <td colspan="6" class="text-left">Please refer to attachment</td>
                    </tr>
                    <#list 1..2 as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>

                <#assign displayedCount = 0>
                <#list claimLogs?keys as key>
                    <#if key == "GHS">
                        <#assign gpclaimList = claimLogs[key]>
                        <#list gpclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime}</td>
                                    <td>${claim.endTime}</td>
                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>

                <#if displayedCount < 3 && !isUploadClaimAttach >
                    <#list displayedCount..2 as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>
                <tr>
                    <td colspan="6" class="text-left"><i>Note: The insurer reserves the right to request for more information.</i></td>
                </tr>
            </table>
        </div>
        <div class="table-list clearfix">
            <div class="fl table-index">e)</div>
            <div class="fl" style="width: 940px;">
                <div class="mb-8">Kindly attach a copy of the Schedule of Benefits, if the benefits are on insured basis (i.e. currently insured).</div>
            </div>
        </div>
        <div class="page-footer">
            <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 11</span></div>
            <p class="page-remark">[AIA-INTERNAL]</p>
        </div>
    </div>
    <!-- 第十二页 -->
    <div class="page">
        <div class="table-list clearfix">
            <div class="fl table-index font-bold">4</div>
            <div class="fl" style="width: 940px;">
                <div class="mb-8 underline-bold"><strong>BENEFIT: GROUP OUTPATIENT INSURANCE</strong></div>
            </div>
        </div>
        <div class="table-list clearfix">
            <div class="fl table-index">a)</div>
            <div class="fl" style="width: 940px;">
                <div class="mb-8">Category of Employees to be insured (please tick as appropriate)</div>
            </div>
            <table class="mb-20 text-center" border="1" cellspacing="0">
                <colgroup>
                    <col style="width: 4em;">
                    <col style="width: 300px;">
                </colgroup>
                <tr>
                    <td colspan="2" class="text-left font-bold">Category of Employees</td>
                    <th>Clinical GP</th>
                    <th>Specialist</th>
                    <th>Diag X-Ray/Lab Tests</th>
                    <th>Dental</th>
                </tr>

                <#assign groupedItems = {}>
                <#list userPlanDutyList as item>
                    <#if !groupedItems[item.categoryEmployee]??>
                        <#assign groupedItems = groupedItems + {item.categoryEmployee: []}>
                    </#if>
                    <#assign groupedItems = groupedItems + {item.categoryEmployee: groupedItems[item.categoryEmployee] + [item]}>
                </#list>

                <#assign index = 0>
                <#list groupedItems?keys as category>
                    <#assign itemsInCategory = groupedItems[category]>
                    <#if index < 3>
                        <tr>
                            <#if index == 0>
                                <td>(i)</td>
                            <#elseif index == 1>
                                <td>(ii)</td>
                            <#elseif index == 2>
                                <td>(iii)</td>
                            <#elseif index == 3>
                                <td>(iv)</td>
                            </#if>
                            <td class="text-left">${category}</td>
                            <#assign hasGtl = false>
                            <#assign hasSp = false>
                            <#assign hasGd = false>
                            <#list itemsInCategory as item>
                                <#if item.benefit?? && item.benefit == "GTL">
                                    <#assign hasGtl = true>
                                </#if>
                                <#if item.benefit?? && item.benefit == "SP">
                                    <#assign hasSp = true>
                                </#if>
                                <#if item.benefit?? && item.benefit == "GD">
                                    <#assign hasGd = true>
                                </#if>
                            </#list>
                            <td><input type="checkbox" <#if hasGtl>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                            <td><input type="checkbox" <#if hasSp>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                            <td></td>
                            <td><input type="checkbox" <#if hasGd>checked</#if> id="myCheckbox2" name="myCheckbox2"></td>
                        </tr>
                        <#assign index = index + 1>
                    <#else>
                        <tr>
                            <td colspan="6"> More information, please refer to the attachment.</td>
                        </tr>
                        <#break>
                    </#if>
                </#list>
                <#-- 补充空行到3行 -->
                <#if index < 3>
                    <#list index..2 as i>
                        <tr>
                            <#if i == 0>
                                <td>(i)</td>
                            <#elseif i == 1>
                                <td>(ii)</td>
                            <#elseif i == 2>
                                <td>(iii)</td>
                            </#if>
                            <td class="text-left">&nbsp;</td>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                    </#list>
                </#if>
                <tr>
                    <td colspan="2" class="text-left font-bold">Dependant (where applicable)</td>
                    <#assign gpDependantsIncluded = "No">
                    <#assign spDependantsIncluded = "No">
                    <#assign gdDependantsIncluded = "No">
                    <#assign gpHeadcount = "">
                    <#assign spHeadcount = "">
                    <#assign gdHeadcount = "">
                    <#list personOverviews as personOverview>
                        <#if personOverview.itemKey == "dependants_included">
                            <#if personOverview.tag == "gp">
                                <#if (personOverview.value!"") == "1">
                                    <#assign gpDependantsIncluded = "Yes">
                                </#if>
                            <#elseif personOverview.tag == "sp">
                                <#if (personOverview.value!"") == "1">
                                    <#assign spDependantsIncluded = "Yes">

                                </#if>
                            <#elseif personOverview.tag == "gd">
                                <#if (personOverview.value!"") == "1">
                                    <#assign gdDependantsIncluded = "Yes">
                                </#if>
                            </#if>
                        </#if>

                        <#if personOverview.itemKey == "no_of_headcount">
                            <#if personOverview.tag == "gp">
                                <#if personOverview.value??>
                                    <#assign gpHeadcount = personOverview.value!>
                                </#if>
                            <#elseif personOverview.tag == "sp">
                                <#if personOverview.value??>
                                    <#assign spHeadcount = personOverview.value!>
                                </#if>
                            <#elseif personOverview.tag == "gd">
                                <#if personOverview.value?? >
                                    <#assign gdHeadcount = personOverview.value!>
                                </#if>
                            </#if>
                        </#if>
                    </#list>


                    <td>${gpDependantsIncluded}</td>
                    <td>${spDependantsIncluded}</td>
                    <td></td>
                    <td>${gdDependantsIncluded}</td>
                </tr>
                <tr>
                    <td colspan="2" class="text-left font-bold"># of Headcount</td>
                    <td>${gpHeadcount}</td>
                    <td>${spHeadcount}</td>
                    <td></td>
                    <td>${gdHeadcount}</td>
                </tr>
            </table>
        </div>
        <div class="table-list clearfix">
            <div class="fl table-index">b)</div>
            <div class="fl" style="width: 940px;">
                <div class="mb-8">Age Profile of Employees</div>
            </div>
            <table class="text-center" border="1" cellspacing="0">
                <colgroup>
                    <col style="width: 150px;">
                </colgroup>
                <tr>
                    <th rowspan="2">Age Band (Age Next Birthday)</th>
                    <th colspan="2"># of Employees</th>
                </tr>
                <tr>
                    <th>Male</th>
                    <th>Female</th>
                </tr>
                <#assign totalMale = 0>
                <#assign totalFemale = 0>
                <#assign hasData12 = false>
                <#list personOverviews as personOverview>
                    <#if personOverview.itemKey=="age_profile_of_employees">
                        <#if personOverview.tag=="group_outpatient_insurance">
                            <#if personOverview.valueMaps?has_content>
                                    <#assign hasData12 = true>
                                    <#assign valueMapList = personOverview.value?eval>
                                    <#list valueMapList as info>
                                        <tr>
                                            <th>${info.age_band!}</th>
                                            <td>${(info.no_of_employees_male?has_content && info.no_of_employees_male?number != 0)?then(info.no_of_employees_male, "")}</td>
                                            <td>${(info.no_of_employees_female?has_content && info.no_of_employees_female?number != 0)?then(info.no_of_employees_female, "")}</td>
                                        </tr>
                                        <#if info.age_band ??&& info.age_band=="66-70">
                                                <tr>
                                                    <th>71-75</th>
                                                    <td></td>
                                                    <td></td>
                                                </tr>
                                                <tr>
                                                    <th>Above 75</th>
                                                    <td></td>
                                                    <td></td>
                                                </tr>
                                        </#if>
                                    </#list>
                                </#if>
                        </#if>
                    </#if>
                </#list>
                <#if !hasData12>
                    <tr>
                        <th>16-30</th>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>31-35</th>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>36-40</th>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>41-45</th>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>46-50</th>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>51-55</th>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>56-60</th>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>61-65</th>
                    <td></td>
                    <td></td>
                    </tr>
                    <tr>
                        <th>66-70</th>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>71-75</th>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>Above 75</th>
                        <td></td>
                        <td></td>
                    </tr>
                    <tr>
                        <th>Total</th>
                        <td></td>
                        <td></td>
                    </tr>
                </#if>
            </table>
        </div>
        <div class="page-footer">
            <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 12</span></div>
            <p class="page-remark">[AIA-INTERNAL]</p>
        </div>
    </div>
    <!-- 第十三页 -->
    <div class="page">
        <div class="table-list clearfix">
            <div class="fl table-index">b)</div>
            <div class="fl" style="width: 940px;">
                <div class="mb-8">Claims Experience for the past 3 years</div>
            </div>
            <h4 class="mb-20 underline-bold" style="font-size: 1em;">Paid Claims</h4>
            <table class="mb-20 text-center padding border collapse">
                <colgroup>
                    <col span="2" style="width: 170px;">
                </colgroup>
                <tr>
                    <th class="border-none"></th>
                    <th class="border-none"></th>
                    <th colspan="2">Clinical*</th>
                    <th colspan="2"> Specialist * </th>
                    <th colspan="2">Diagnostic X-Ray / Lab Tests*</th>
                    <th colspan="2">Dental*</th>
                </tr>
                <tr>
                    <th class="text-left">Period of Coverage From / To <span class="line-6em"></span> (dd/mm/yyyy)</th>
                    <th class="text-left"># of Insured as at <span class="line-6em"></span> (dd/mm/yyyy)</th>
                    <th># of Visits</th>
                    <th>Amt (S$)</th>
                    <th># of Visits</th>
                    <th>Amt (S$)</th>
                    <th># of Visits</th>
                    <th>Amt (S$)</th>
                    <th># of Visits</th>
                    <th>Amt (S$)</th>
                </tr>
                <#if isUploadClaimAttach?? && isUploadClaimAttach>
                    <tr>
                        <td colspan="10" >Please refer to attachment</td>
                    </tr>
                    <#list 1..2 as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>

                <#assign displayedCount = 0>
                <#list claimLogs?keys as key>
                    <#if key == "GP">
                        <#assign gpclaimList = claimLogs[key]>
                        <#list gpclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime}</td>
                                    <td>${claim.endTime}</td>
                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                    <#assign gpclaimList = claimLogs["SP"]![]>
                                    <#assign spshowflg = false>
                                    <#list gpclaimList as spclaim>
                                        <#if spclaim.startTime == claim.startTime && spclaim.endTime == claim.endTime>
                                            <#assign spshowflg = true>
                                            <td>${(spclaim.paidClaimsNum??)?then(spclaim.paidClaimsNum, "")}</td>
                                            <td>${(spclaim.paidAmount?? && spclaim.paidAmount.amount??)?then(spclaim.paidAmount.amount, "")}</td>
                                        </#if>
                                    </#list>
                                    <#if !spshowflg>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </#if>
                                    <#assign gdclaimList = claimLogs["GD"]![]>
                                    <#assign gdshowflg = false>
                                    <#list gdclaimList as gdclaim>
                                        <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                            <#assign gdshowflg = true>
                                            <td></td>
                                            <td></td>
                                            <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                            <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                        </#if>
                                    </#list>
                                    <#if !gdshowflg>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </#if>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>

                <#list claimLogs?keys as key>
                    <#if key == "SP" && displayedCount < 3>
                        <#assign gpclaimList = claimLogs[key]>
                        <#list gpclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime}</td>
                                    <td>${claim.endTime}</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                    <#assign gdclaimList = claimLogs["GD"]![]>
                                    <#assign gdshowflg = false>
                                    <#list gdclaimList as gdclaim>
                                        <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                            <#assign gdshowflg = true>
                                            <td>${(gdclaim.paidClaimsNum??)?then(gdclaim.paidClaimsNum, "")}</td>
                                            <td>${(gdclaim.paidAmount?? && gdclaim.paidAmount.amount??)?then(gdclaim.paidAmount.amount, "")}</td>
                                        </#if>
                                    </#list>
                                    <#if !gdshowflg>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </#if>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>


                <#list claimLogs?keys as key>
                    <#if key == "GD" && displayedCount < 3>
                        <#assign gpclaimList = claimLogs[key]>
                        <#list gpclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime}</td>
                                    <td>${claim.endTime}</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                    <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>
                <#if displayedCount < 3 && !isUploadClaimAttach>
                    <#list displayedCount..2 as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>
                <tr>
                    <td colspan="10" class="text-left"><i>* inclusive of visits to non-panel clinics <br> Note: The insurer reserves the right to request for more information.</i></td>
                </tr>
            </table>
            <h4 class="mb-20 underline-bold">Outstanding Claims</h4>
            <table class="mb-20 border text-center padding collapse">
                <colgroup>
                    <col span="2" style="width: 170px;">
                </colgroup>
                <tr>
                    <th class="border-none"></th>
                    <th class="border-none"></th>
                    <th colspan="2">Clinical*</th>
                    <th colspan="2"> Specialist * </th>
                    <th colspan="2">Diagnostic X-Ray / Lab Tests*</th>
                    <th colspan="2">Dental*</th>
                </tr>
                <tr>
                    <th class="text-left">Period of Coverage From / To <span class="line-6em"></span> (dd/mm/yyyy)</th>
                    <th class="text-left"># of Insured as at <span class="line-6em"></span> (dd/mm/yyyy)</th>
                    <th># of Visits</th>
                    <th>Amt (S$)</th>
                    <th># of Visits</th>
                    <th>Amt (S$)</th>
                    <th># of Visits</th>
                    <th>Amt (S$)</th>
                    <th># of Visits</th>
                    <th>Amt (S$)</th>
                </tr>
                    <#if isUploadClaimAttach?? && isUploadClaimAttach>
                        <tr>
                            <td colspan="10" >Please refer to attachment</td>
                        </tr>
                        <#list 1..2 as i>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#list>
                    </#if>

                <#assign displayedCount = 0>
                <#list claimLogs?keys as key>
                    <#if key == "GP">
                        <#assign gpclaimList = claimLogs[key]>
                        <#list gpclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime}</td>
                                    <td>${claim.endTime}</td>
                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                    <#assign gpclaimList = claimLogs["SP"]![]>
                                    <#assign spshowflg = false>
                                    <#list gpclaimList as spclaim>
                                        <#if spclaim.startTime == claim.startTime && spclaim.endTime == claim.endTime>
                                            <#assign spshowflg = true>
                                            <td>${(spclaim.outstandingClaimsNum??)?then(spclaim.outstandingClaimsNum, "")}</td>
                                            <td>${(spclaim.outstandingAmount?? && spclaim.outstandingAmount.amount??)?then(spclaim.outstandingAmount.amount, "")}</td>
                                        </#if>
                                    </#list>
                                    <#if !spshowflg>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </#if>
                                    <#assign gdclaimList = claimLogs["GD"]![]>
                                    <#assign gdshowflg = false>
                                    <#list gdclaimList as gdclaim>
                                        <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                            <#assign gdshowflg = true>
                                            <td></td>
                                            <td></td>
                                            <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                            <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                        </#if>
                                    </#list>
                                    <#if !gdshowflg>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </#if>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>

                <#list claimLogs?keys as key>
                    <#if key == "SP" && displayedCount < 3>
                        <#assign gpclaimList = claimLogs[key]>
                        <#list gpclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime}</td>
                                    <td>${claim.endTime}</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                    <#assign gdclaimList = claimLogs["GD"]![]>
                                    <#assign gdshowflg = false>
                                    <#list gdclaimList as gdclaim>
                                        <#if gdclaim.startTime == claim.startTime && gdclaim.endTime == claim.endTime>
                                            <#assign gdshowflg = true>
                                            <td>${(gdclaim.outstandingClaimsNum??)?then(gdclaim.outstandingClaimsNum, "")}</td>
                                            <td>${(gdclaim.outstandingAmount?? && gdclaim.outstandingAmount.amount??)?then(gdclaim.outstandingAmount.amount, "")}</td>
                                        </#if>
                                    </#list>
                                    <#if !gdshowflg>
                                        <td>&nbsp;</td>
                                        <td>&nbsp;</td>
                                    </#if>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>

                <#list claimLogs?keys as key>
                    <#if key == "GD" && displayedCount < 3>
                        <#assign gpclaimList = claimLogs[key]>
                        <#list gpclaimList as claim>
                            <#if displayedCount < 3>
                                <tr>
                                    <td>${claim.startTime}</td>
                                    <td>${claim.endTime}</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                    <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                    <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                </tr>
                                <#assign displayedCount = displayedCount + 1>
                            <#else>
                                <tr>
                                    <td colspan="10" class="text-left italic">More information, please refer to the attachment.</td>
                                </tr>
                                <#break>
                            </#if>
                        </#list>
                    </#if>
                </#list>

                <#if displayedCount < 3 && !isUploadClaimAttach>
                    <#list displayedCount..2 as i>
                        <tr>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>
                <tr>
                    <td colspan="10" class="text-left"><i>* inclusive of visits to non-panel clinics <br> Note: The insurer reserves the right to request for more information.</i></td>
                </tr>
            </table>
        </div>
        <div class="table-list clearfix">
            <div class="fl table-index">c)</div>
            <div class="fl" style="width: 940px;">
                <div class="mb-8">Kindly attach a copy of the Schedule of Benefits if the benefits are on insured basis.</div>
                <div class="mb-8">If currently self-insured, kindly provide the following details:</div>
                <div class="mb-8"> Please indicate “Unlimited” if there is no cap and “NA” if it is not applicable.</div>
            </div>
            <table class="mb-20 border collapse padding text-center">
                <tr>
                    <th style="width: 180px;" rowspan="2" class="text-left">Benefits</th>
                    <th colspan="2">Maximum Limit per Visit (S$)</th>
                    <th colspan="2">Maximum Limit per Policy <br> Year (S$)</th>
                    <th colspan="2">Co-Payment (S$) / Co- <br /> Insurance (%)</th>
                </tr>
                <tr>
                    <th>Clinic on Company's panel</th>
                    <th>Non-panel Clinic</th>
                    <th>Clinic on Company's panel</th>
                    <th>Non-panel Clinic</th>
                    <th>Clinic on Company's panel</th>
                    <th>Non-panel Clinic</th>
                </tr>
                <tr>
                    <th class="text-left">Clinical GP</th>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <th class="text-left">Specialist</th>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <th class="text-left">Diagnostic X-Ray / Lab Tests</th>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <th class="text-left">Dental</th>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <th class="text-left">Others</th>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            </table>
        </div>
        <div class="page-footer">
            <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 13</span></div>
            <p class="page-remark">[AIA-INTERNAL]</p>
        </div>
    </div>
    <!-- 第十四页 -->
    <div class="page">
        <div class="table-list clearfix">
            <div class="fl table-index font-bold">5</div>
            <div class="fl" style="width: 940px;">
                <div class="mb-8 underline-bold"><strong>BENEFIT: MATERNITY INSURANCE</strong></div>
            </div>
        </div>
        <div class="table-list clearfix">
            <div class="fl table-index">a)</div>
            <div class="fl" style="width: 940px;">
                <div class="mb-8">Basis of Coverage</div>
            </div>
            <table class="mb-20 border padding text-center collapse">
                <colgroup>
                    <col style="width: 120px;">
                    <col>
                    <col style="width: 30%;">
                </colgroup>
                <tr>
                    <th colspan="2" class="text-left">Category of Employees (refer to the example)</th>
                    <th> # of Headcount</th>
                </tr>
                <#assign hasData = false>
                <#list personOverviews as personOverview>
                    <#if personOverview.tag == "gm" && personOverview.itemKey == "basis_of_coverage">
                        <#assign hasData = true>
                        <#assign total = personOverview.valueMaps?size />
                        <#list 0..3 as i>
                            <tr>
                                <td>
                                    <#if i == 0>(i)
                                    <#elseif i == 1>(ii)
                                    <#elseif i == 2>(iii)
                                    <#elseif i == 3>(iv)
                                    </#if>
                                </td>
                                <#if i < total>
                                    <#assign info = personOverview.valueMaps[i] />
                                    <td>${info.category_of_employees_occupation!}</td>
                                    <td>${info.no_of_headcount!}</td>
                                <#else>
                                    <td>&nbsp;</td>
                                    <td>&nbsp;</td>
                                </#if>
                            </tr>
                        </#list>
                        <#if total gt 4>
                            <tr>
                                <td colspan="3" style="text-align:center; font-style:italic;">
                                    More information, please refer to attachment.
                                </td>
                            </tr>
                        </#if>
                    </#if>
                </#list>
                <#if !hasData>
                    <#list 1..3 as i>
                        <tr>
                            <td>
                                <#if i == 1>(i)
                                <#elseif i == 2>(ii)
                                <#elseif i == 3>(iii)
                                </#if>
                            </td>
                            <td>&nbsp;</td>
                            <td>&nbsp;</td>
                        </tr>
                    </#list>
                </#if>
            </table>
        </div>
        <div class="example-list">
            <h4 class="underline-bold">Example 1</h4>
            <table class="mb-20" cellspacing="0">
                <tr>
                    <th style="text-align: left;">Category of Employees/Occupation</th>
                </tr>
                <tr>
                    <td>(i) Senior Management (Director, General Manager, Senior Manager)</td>
                </tr>
                <tr>
                    <td>(ii) Manager & Executive</td>
                </tr>
                <tr>
                    <td>(iii) All Others</td>
                </tr>
            </table>
        </div>
        <div class="example-list">
            <h4 class="underline-bold">Example 2</h4>
            <table class="mb-20" cellspacing="0">
                <tr>
                    <td>(i) All Employees</td>
                </tr>
            </table>
        </div>
        <div class="table-list clearfix">
            <div class="fl table-index">b)</div>
            <div class="fl" style="width: 940px;">
                <div class="mb-8">Claims Experience for past 3 years</div>
            </div>
            <table class="text-center border padding collapse">
                <tr>
                    <th rowspan="2" class="text-left" style="width: 180px;">Period of Coverage From / To <span class="line-4em"></span> <br> (dd/mm/yyyy)</th>
                    <th rowspan="2" class="text-left" style="width: 180px;"># of Insured as at <br> <span class="line-4em"></span> <br> (dd/mm/yyyy)</th>
                    <th colspan="2">Paid Claims</th>
                    <th colspan="2"> Outstanding Claims</th>
                </tr>
                <tr>
                    <th># of Claims</th>
                    <th>Amount (S$)</th>
                    <th># of Claims</th>
                    <th>Amount (S$)</th>
                </tr>
                    <#if isUploadClaimAttach?? && isUploadClaimAttach>
                        <tr>
                            <td colspan="6" class="text-left">Please refer to attachment</td>
                        </tr>
                        <#list 1..2 as i>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#list>
                    </#if>

                    <#assign displayedCount = 0>
                    <#list claimLogs?keys as key>
                        <#if key == "GM">
                            <#assign gpclaimList = claimLogs[key]>
                            <#list gpclaimList as claim>
                                <#if displayedCount < 3>
                                    <tr>
                                        <td>${claim.startTime}</td>
                                        <td>${claim.endTime}</td>
                                        <td>${(claim.paidClaimsNum??)?then(claim.paidClaimsNum, "")}</td>
                                        <td>${(claim.paidAmount?? && claim.paidAmount.amount??)?then(claim.paidAmount.amount, "")}</td>
                                        <td>${(claim.outstandingClaimsNum??)?then(claim.outstandingClaimsNum, "")}</td>
                                        <td>${(claim.outstandingAmount?? && claim.outstandingAmount.amount??)?then(claim.outstandingAmount.amount, "")}</td>
                                    </tr>
                                    <#assign displayedCount = displayedCount + 1>
                                <#else>
                                    <tr>
                                        <td colspan="6" class="text-left italic">More information, please refer to the attachment.</td>
                                    </tr>
                                    <#break>
                                </#if>
                            </#list>
                        </#if>
                    </#list>

                    <#if displayedCount < 3 && !isUploadClaimAttach >
                        <#list displayedCount..2 as i>
                            <tr>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                        </#list>
                    </#if>
                <tr>
                    <td colspan="6" class="text-left italic"><i>Note: The insurer reserves the right to request for more information.</i></td>
                </tr>
            </table>
        </div>
        <div class="table-list clearfix">
            <div class="fl table-index">c)</div>
            <div class="fl" style="width: 940px;">
                <div class="mb-8">Kindly attach a copy of the Schedule of Benefits if the benefits are on insured basis.</div>
                <div class="mb-8"> If currently self-insured, kindly provide the following details:</div>
                <div class="mb-8"> Please indicate “Unlimited” if there is no cap and “NA” if it is not applicable.</div>
            </div>
            <table class="mb-20 border padding collapse text-center">
                <tr>
                    <th class="text-left">Benefits</th>
                    <th colspan="2">Maximum Limit per Policy Year (S$)</th>
                    <th colspan="2">Deductible / Co-insurance (S$)</th>
                </tr>
                <tr>
                    <th class="text-left">Normal Delivery</th>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <th class="text-left">Caesarian Delivery</th>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
                <tr>
                    <th class="text-left">Others:</th>
                    <td></td>
                    <td></td>
                    <td></td>
                    <td></td>
                </tr>
            </table>
        </div>
        <div class="page-footer">
            <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 14</span></div>
            <p class="page-remark">[AIA-INTERNAL]</p>
        </div>
    </div>
    <!-- 第十五页 -->
    <div class="page page-fifth" style="text-align: justify;">
        <div class="table-list clearfix">
            <div class="fl table-index font-bold">6</div>
            <div class="fl" style="width: 940px;">
                <div class="mb-8 underline-bold"><strong>NEEDS ANALYSIS & PRODUCT RECOMMENDATION</strong></div>
            </div>
        </div>
        <p class="mb-8">Please tick appropriate box to indicate the priority of your company's needs:</p>
        <table cellspacing="0" class="padding">
            <tr>
                <th style="width: 400px;" class="underline-bold text-left">Company's Priorities</th>
                <th style="width: 40px; border-bottom: 2px solid #333;">Low</th>
                <th style="width: 40px; border-bottom: 2px solid #333;">Med</th>
                <th style="width: 40px; border-bottom: 2px solid #333;">High</th>
                <th class="underline-bold">Advisor's Recommendation</th>
            </tr>
            <tr>
                <td>Cover for Outpatient medical <br>expenses</td>
                <#assign isChecked = false>
                <#list userPlanDutyList as item>
                    <#if item.benefit == "GP" && item.participation == "1">
                        <#assign isChecked = true>
                    </#if>
                </#list>
                <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                <td>
                    <div class="table-input"></div>
                </td>
            </tr>
            <tr>
                <td>Cover for Hospital & Surgical <br>expenses</td>
                <#assign isChecked = false>
                <#list userPlanDutyList as item>
                    <#if item.benefit == "GHS" && item.participation == "1">
                        <#assign isChecked = true>
                    </#if>
                </#list>
                <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                <td>
                    <div class="table-input"></div>
                </td>
            </tr>
            <tr>
                <td>Cover for Dental expenses</td>
                <#assign isChecked = false>
                <#list userPlanDutyList as item>
                    <#if item.benefit == "GD" && item.participation == "1">
                        <#assign isChecked = true>
                    </#if>
                </#list>
                <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                <td>
                    <div class="table-input"></div>
                </td>
            </tr>
            <tr>
                <td>Cover for Major illnesses <br>(e.g. cancer, kidney failure, etc.)</td>
                <#assign isChecked = false>
                <#list userPlanDutyList as item>
                    <#if item.benefit == "GCI" && item.participation == "1">
                        <#assign isChecked = true>
                    </#if>
                </#list>
                <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                <td>
                    <div class="table-input"></div>
                </td>
            </tr>
            <tr>
                <td>Cover for Loss of Income due to <br>sickness or accident</td>
                <#assign isChecked = false>
                <#list userPlanDutyList as item>
                    <#if item.benefit == "GDI" && item.participation == "1">
                        <#assign isChecked = true>
                    </#if>
                </#list>
                <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                <td>
                    <div class="table-input"></div>
                </td>
            </tr>
            <tr>
                <td>Cover for long term medical treatment</td>
                <#assign isChecked = false>
                <#list userPlanDutyList as item>
                    <#if item.benefit == "GTL" && item.participation == "1">
                        <#assign isChecked = true>
                    </#if>
                </#list>
                <td><input type="checkbox" <#if !isChecked>checked</#if> id="myCheckbox1" name="myCheckbox1"></td>
                <td><input type="checkbox" id="myCheckbox2" name="myCheckbox2"></td>
                <td><input type="checkbox" <#if isChecked>checked</#if> id="myCheckbox3" name="myCheckbox3"></td>
                <td>
                    <div class="table-input"></div>
                </td>
            </tr>
            <tr>
                <td>Others:</td>
                <td colspan="4"><div class="table-input"></div></td>
            </tr>
        </table>
        <div class="table-list clearfix">
            <div class="fl table-index font-bold">7</div>
            <div class="fl" style="width: 940px;">
                <div class="mb-8 underline-bold"><strong>DECLARATION AND AUTHORISATION BY POLICYHOLDER /APPLICANT</strong></div>
            </div>
        </div>
        <div class="mb-20"> <strong> I/We </strong> hereby declare that, to the best of my / our knowledge and belief, the information given here are true and complete, and agree that if a contract of insurance is effected, all information submitted in connection with this application shall form the basis of such contract between the Company and the Insurer.</div>
        <div class="mb-20"> <strong> I/We </strong> hereby authorise, agree and consent to:</div>
        <div class="table-list clearfix">
            <div class="fl table-index">(a)</div>
            <div class="fl" style="width: 940px;">
                <div>persons and organisations. whether within or outside Singapore, including but not limited to medical sources, hospitals, doctors. other healthcare professionals, laboratories, regulator, dispute resolution centres and insurers, their associated persons/organisations, my/our or the insured person's employers or financial service providers, or their third party service providers or representatives (collectively <strong>"Third Parties"</strong>) disclosing and releasing to AIA Singapore, its associated persons/organisations, its and their third party service providers and its and their representatives, whether within or outside Singapore (collectively <strong>"AIA Persons"</strong>), any information concerning the policy owner and the insured person(s) at any time, including all personal data and information, medical Information, medical history. consultation history and notes, prescriptions, treatments, descriptions of medical services rendered, and any employment and financial information, including the taking of copies of such records (collectively <strong>"Personal Data" </strong>), relevant for the Purpose (defined below); </div>
            </div>
        </div>
        <div class="table-list clearfix">
            <div class="fl table-index">(b)</div>
            <div class="fl" style="width: 940px;">
                <div>the AIA Persons sharing the scope of sub-clause (a) above, along with any of the Personal Data, with any relevant Third Parties to procure their disclosure and release of additional relevant Persona Data for the Purpose; </div>
            </div>
        </div>
        <div class="table-list clearfix">
            <div class="fl table-index">(c)</div>
            <div class="fl" style="width: 940px;">
                <div>the AIA Persons. including their approved medical examiners or laboratories, performing any necessary medical assessments and examinations and tests to determine, assess and evaluate the health of the insured person(s); </div>
            </div>
        </div>
        <div class="table-list clearfix">
            <div class="fl table-index">(d)</div>
            <div class="fl" style="width: 940px;">
                <div>the AIA Persons collecting, using, disdosing, storing, retaining and/or processing (collectively <strong>"Using"/"Use"</strong>) the Personal Data for the Purpose; and </div>
            </div>
        </div>
        <div class="table-list clearfix">
            <div class="fl table-index">(e)</div>
            <div class="fl" style="width: 940px;">
                <div>waive any right (on my own behalf and on behalf of the insured person(s) where applicable, in respect of which I/We represent and warrant that the insured person(s) have granted me/use authority to so waive) to bring a claim of any nature against any of the AIA Persons in respect of any above-mentioned Use and/or any Use of any Personal Data for the Purpose. </div>
            </div>
        </div>
        <div class="page-footer">
            <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 15</span></div>
            <p class="page-remark">[AIA-INTERNAL]</p>
        </div>
    </div>
    <!-- 第十六页 -->
    <div class="page" style="height: 1500px; text-align: justify;">
        <div class="mb-20">Where <strong>I</strong>/we are not the insured person, <strong>I</strong>/we represent and warrant that <strong>I</strong>/we have obtained the consent of the insured person(s), except to the extent such consent is not required under relevant laws: (i) to collect their Personal Data; (ii) to disclose their Personal Data to the AIA Persons; and (iii) for the AIA Persons and Third Parties to Use any of their Personal Data in the manner and for the purposes described in this Clause. I/We hereby agree to indemnify AIA Persons for all losses and damages that AIA Persons may suffer in the event that <strong>I</strong>/we are in breach of any representation and warranty provided by me/us herein. In this Clause, <strong>"Purpose"</strong> means any of the purposes described in the AIA Personal Data Policy, including but not limited to processing of this form, to provide subsequent advice or services to me/us or the insured person in relation to any existing or future policy/policies/programmes that <strong>I</strong>/we may hold/participate with AIA Singapore. This authorisation shall bind my/our successors and assignees, and remains valid, notwithstanding death, irrespective of whether or not my/our Application/form is accepted by AIA Singapore. A photocopy of this authorisation shall be valid and effective as the original.</div>
        <div class="clearfix">
            <div class="fl" style="width: 480px;">
                <div class="input-item mb-20">
                    <div class="table-input" style="width: 80%;"></div>
                    <div>Signature of Authorised Officer</div>
                </div>
                <div class="input-item">Name:</div>
                <div class="input-item">NRIC/ Fin No.</div>
                <div class="input-item">Designation:</div>
                <div class="input-item">Date:</div>
            </div>
            <div class="fl" style="width: 480px;padding-top: 118px;">
                <div class="input-item">Company Stamp (if applicable):</div>
            </div>
        </div>
        <div class="table-list clearfix">
            <div class="fl table-index font-bold">8</div>
            <div class="fl" style="width: 940px;">
                <div class="mb-8 underline-bold"><strong>DECLARATION BY INSURANCE REPRESENTATIVE</strong></div>
            </div>
        </div>
        <div class="mb-20">I/We declare and acknowledge that <strong>I</strong> / we have reviewed this Group Insurance Fact-Finding Form with the authorised officer of the Company, and that <strong>I</strong> / we have explained all the requirements of this Fact-Finding form to him / her.</div>
        <div class="clearfix">
            <div class="fl" style="width: 480px;">
                <div class="input-item mb-20">
                    <div class="table-input" style="width: 80%;"></div>
                    <div>Signature of Authorised Representative</div>
                </div>
                <div class="input-item">Name:</div>
                <div class="input-item">NRIC/ Fin No.</div>
                <div class="input-item">Designation:</div>
                <div class="input-item">Date:</div>
            </div>
            <div class="fl" style="width: 480px;padding-top: 118px;">
                <div class="input-item">Company Stamp (if applicable):</div>
            </div>
        </div>
        <div class="page-footer">
            <div class="page-info"><span>Effective 1 July 2023</span><span class="page-num">Page 16</span></div>
            <p class="page-remark">[AIA-INTERNAL]</p>
        </div>
    </div>
</body>
</html>