<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>
    <style>
        * {
            margin: 0;
            padding: 0;
        }

        .box {
            width: 1110px;
            margin: 0 auto;
        }

        .mf-20 {
            margin-left: 20px;
        }

        .ft-12 {
            font-size: 12px;
        }

        .ft-14 {
            font-size: 14px;
        }

        .ft-16 {
            font-size: 16px;
        }

        .ft-18 {
            font-size: 18px;
        }

        .fot-rt {
            float: right;
        }

        .header {
            height: 36px;
            margin: 40px 0 64px 0;
            border-top: 2px solid #000
        }

        .header p {
            text-align: right;
        }

        .pay {
        }

        .pay-header {
            font-size: 36px;
            font-weight: 400;
            text-align: center;
            margin-bottom: 90px;
        }

        .pay-block {
            margin-left: 90px;
            margin-bottom: 158px;
        }
        .pay-block p {
            height: 34px;
            margin-bottom: 34px;
        }

        .pay-block-second {
            margin-bottom: 800px;
        }

        .pay-block-title {
            font-size: 24px;
            font-weight: 400;
            color: #666666;
        }

        .pay-block-content {
            font-size: 24px;
            font-weight: 400;
            color: #000;
        }

        .pay-bottom {
            margin-right: 76px;
            height: 100px;
            text-align: right;
            position: relative;
        }
        .pay-bottom img{
            position: absolute;
            width: 389px;
            top: -77px;
            right: -20px;
        }

        .line {
            display: inline-block;
            border-bottom: 1px solid #999;
            margin: 0 4px;
            text-align: center;
        }

        .footer {
            border-top: 2px solid #DBDEE0;
        }

        .footer p {
            text-align: right;
            margin-top: 2px;
        }

        .confirm-header {
            font-size: 36px;
            font-weight: 400;
            text-align: center;
            margin-bottom: 90px;
        }

        .confirm-block {
            padding: 0 38px;
            margin-bottom: 48px;
        }

        .confirm-block-title {
            font-size: 20px;
            font-weight: 600;
            color: #000;
            height: 28px;
            margin-bottom: 24px;
        }

        .confirm-block-title span {
            vertical-align: middle;
        }

        .prefix {
            width: 4px;
            height: 18px;
            display: inline-block;
            background: #999999;
        }

        .confirm-block-content {
            padding: 0 12px;
            font-size: 0;
        }

        .confirm-block-content-item {
            display: inline-block;
            width: 33%;
            margin-bottom: 24px;
        }

        .confirm-block-content-item-title {
            font-size: 16px;
            font-weight: 400;
            color: #666666;
            margin-right: 12px;
        }

        .confirm-block-content-item-content {
            font-size: 16px;
            font-weight: 400;
            color: #000000;
        }

        .circle {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            border: 1px solid #666666;
            line-height: 12px;
            text-align: center;
            margin-left: 4px;
            color: #666666;
        }

        .table1 {
            margin-top: 16px;
            border-radius: 16px;
            border: 3px solid #F3F3F3;
            overflow: hidden;
        }

        .table1 table {
            font-size: 16px;
            border-collapse: collapse;
            width: 100%;
        }

        .table1 table th {
            background: #DBDBDB;
            font-size: 18px;
            font-weight: bold;
            color: #000;
            height: 56px;
        }

        .table1 table td {
            padding: 12px 0 12px 24px;
            background: white;
        }

        .border-color-gray {
            border-color: #F3F3F3;
        }

        .border-color-white {
            border-color: #fff;
        }

        .border-r {
            border-width: 0px 3px 0px 0px;
            border-style: solid;
        }

        .border-rb {
            border-width: 0px 3px 3px 0px;
            border-style: solid;
        }

        .border-b {
            border-width: 0px 0px 3px 0px;
            border-style: solid;
        }

        .confirm-block-text {
            font-size: 16px;
            font-weight: 400;
            line-height: 26px;
            color: #000;
        }

        .confirm-block-text-num {
            display: inline-block;
            width: 20px;
            vertical-align: top
        }

        .confirm-block-text-content {
            display: inline-block;
            width: 96%;
        }

        .underline {
            text-decoration: underline;
        }

        .table2 {
            margin-top: 0px;
            border: 0px solid #F3F3F3;
            overflow: hidden;
        }

        .table2 table {
            font-size: 16px;
            border-collapse: collapse;
            width: 100%;
        }

        .table2 table th {
            background: #DBDBDB;
            font-size: 18px;
            font-weight: 500;
            color: #000;
            height: 42px;
        }

        .table2 table td {
            padding: 8px 0 8px 0px;
            text-align: center;
        }
        .box-1{
            height: 1300px;
        }
        .box-2{
            margin-top: 500px;
        }
    </style>
    <script>

    </script>
</head>
<body>
<div class="box">
    <div class="box-1">
        <div class="pay">
            <p class="pay-header">付款通知书</p>
            <div class="pay-block">
                <p><span class="pay-block-title">付款内容：</span><span class="pay-block-title">${configName}</span></p>
                <p><span class="pay-block-title">付款企业：</span><span class="pay-block-title">${companyName}</span></p>
                <p><span class="pay-block-title">付款金额（人民币）：</span><span
                            class="pay-block-title">${totalAmount}元</span></p>
            </div>
            <div class="pay-block pay-block-second">
                <p><span class="pay-block-title">收款账户：</span><span class="pay-block-title">${payeeName}</span></p>
                <p><span class="pay-block-title">收款账号：</span><span class="pay-block-title">${payeeBankNo}</span></p>
                <p><span class="pay-block-title">账户开户行：</span><span class="pay-block-title">${payeeBankName}</span>
                </p>
            </div>
            <div class="pay-bottom">
                <p style="margin-bottom: 30px">
                    用印时间：
                    <span style="width: 56px" class="line">${year}</span>年
                    <span style="width: 56px" class="line">${month}</span>月
                    <span style="width: 56px" class="line">${day}</span>日
                </p>
                <p>用&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;印：<span style="width: 234px" class="line"></span></p>
                <img src=${lbz}/>
            </div>
        </div>
    </div>
    <div class="box-2">
        <!--<div class="header"></div>-->
        <div class="confirm">
            <p class="confirm-header">保险方案确认书</p>
            <div class="confirm-block">
                <div class="confirm-block-title">
                    <span class="prefix"></span>
                    <span>订单信息</span>
                </div>
                <div class="confirm-block-content">
                    <div class="confirm-block-content-item">
                        <span class="confirm-block-content-item-title">支付金额</span>
                        <span class="confirm-block-content-item-content">${totalAmount}元</span>
                    </div>
                    <div class="confirm-block-content-item">
                        <span class="confirm-block-content-item-title">保险起期</span>
                        <span class="confirm-block-content-item-content">${startTime}</span>
                    </div>
                    <div class="confirm-block-content-item">
                        <span class="confirm-block-content-item-title">保险止期</span>
                        <span class="confirm-block-content-item-content">${endTime}</span>
                    </div>
                </div>
            </div>
            <div class="confirm-block">
                <div class="confirm-block-title">
                    <span class="prefix"></span>
                    <span>投保人信息</span>
                </div>
                <div class="confirm-block-content">
                    <div class="confirm-block-content-item">
                        <span class="confirm-block-content-item-title">团体名称</span>
                        <span class="confirm-block-content-item-content">${groupName}</span>
                    </div>
                    <div class="confirm-block-content-item">
                        <span class="confirm-block-content-item-title">团体证件类型</span>
                        <span class="confirm-block-content-item-content">统一社会信用代码（营业执照）</span>
                    </div>
                    <div class="confirm-block-content-item">
                        <span class="confirm-block-content-item-title">团体证件号码</span>
                        <span class="confirm-block-content-item-content">${groupSocialCode}</span>
                    </div>
                    <div class="confirm-block-content-item">
                        <span class="confirm-block-content-item-title">企业地址</span>
                        <span class="confirm-block-content-item-content">${groupAddress}</span>
                    </div>
                </div>
            </div>
            <div class="confirm-block">
                <div class="confirm-block-title">
                    <span class="prefix"></span>
                    <span>保障详情</span>
                </div>
                <#list planList as plan>
                    <div class="confirm-block-content">
                        <div style="width: 100%;" class="confirm-block-content-item">
                            <span class="confirm-block-content-item-title">方案名称</span>
                            <span class="confirm-block-content-item-content">${plan.planName}</span>
                        </div>
                        <div class="confirm-block-content-item">
                            <span class="confirm-block-content-item-title">被保人职业类型</span>
                            <span class="confirm-block-content-item-content">${plan.jobCategory}</span>
                        </div>
                        <div class="confirm-block-content-item">
                            <span class="confirm-block-content-item-title">保险期间</span>
                            <span class="confirm-block-content-item-content">${insurance_period}个月</span>
                        </div>
                        <div style="width: 100%;" class="confirm-block-content-item">
                            <span class="confirm-block-content-item-title">保障计划</span>
                        </div>
                        <div class="table1">
                            <table>
                                <thead>
                                <tr>
                                    <#--<th style="width: 257px;" class="border-r border-color-white">保障内容</th>-->
                                    <th style="width: 345px;" class="border-r border-color-white">保障责任</th>
                                    <th style="width: 402px;">保障详情</th>
                                </tr>
                                </thead>
                                <tbody>
                                <#list plan.groupList as group>
                                    <#--<tr>
                                        <td rowspan="${group.count}"
                                            class="border-rb border-color-gray">${group.name}</td>
                                    </tr>-->
                                    <#list group.dutys as product>
                                        <tr>
                                            <td class="border-rb border-color-gray">${product.businessFields.productName}</td>
                                            <td class="border-rb border-color-gray">
                                                <#list product.instance as instance>
                                                    <#if "single" == instance.type>
                                                        <#list instance.list as item>
                                                            <#list item.fields as field>
                                                                <#if field.value ??>
                                                                    ${field.title}
                                                                    <#if field.value?is_hash>
                                                                        ${field.value.amount}
                                                                    <#else>
                                                                        ${field.value}
                                                                    </#if>
                                                                    ${field.unit}；
                                                                </#if>
                                                            </#list>
                                                        </#list>
                                                    <#else>
                                                        <br/>
                                                        <#list instance.list as item>
                                                            <#list item.condition as condition>
                                                            <#--${condition.title}-->
                                                                <#list condition.list as cl>
                                                                    <#if cl.value ??>
                                                                        ${cl.title} ${cl.value?default('-')} ${cl.unit}；
                                                                    </#if>
                                                                </#list>
                                                            </#list>
                                                            <#list item.fields as field>
                                                                <#if field.value??>
                                                                    ${field.title}
                                                                    <#if field.value?is_hash>
                                                                        ${field.value.amount}
                                                                    <#else>
                                                                        ${field.value}
                                                                    </#if>
                                                                    ${field.unit}；
                                                                </#if>
                                                            </#list>
                                                            <br/>
                                                        </#list>
                                                    </#if>
                                                </#list>
                                            </td>
                                        </tr>
                                    </#list>
                                </#list>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </#list>
            </div>
            <div class="confirm-block">
                <div class="confirm-block-title">
                    <span class="prefix"></span>
                    <span>特别约定</span>
                </div>
                ${tbxz}
            </div>
            <div class="confirm-block" style="margin: 80px 0 0 -38px;">
                <div class="confirm-block-title">
                    <span class="prefix"></span>
                    <span>联系人信息</span>
                </div>
                <div class="confirm-block-content">
                    <div class="confirm-block-content-item">
                        <span class="confirm-block-content-item-title">联系人姓名</span>
                        <span class="confirm-block-content-item-content">${order.contactPerson}</span>
                    </div>
                    <div class="confirm-block-content-item">
                        <span class="confirm-block-content-item-title">手机号</span>
                        <span class="confirm-block-content-item-content">${order.contactMobile}</span>
                    </div>
                    <div class="confirm-block-content-item">
                        <span class="confirm-block-content-item-title">电子邮箱</span>
                        <span class="confirm-block-content-item-content">${order.contactEmail}</span>
                    </div>
                </div>
            </div>
            <div class="confirm-block" style="margin: 80px 0 0 -38px;">
                <div class="confirm-block-title">
                    <span class="prefix"></span>
                    <span>被保人信息</span>
                </div>
                <#list planList as plan>
                    <div class="confirm-block-content">
                        <div style="width: 100%;" class="confirm-block-content-item">
                            <span class="confirm-block-content-item-title">方案名称</span>
                            <span class="confirm-block-content-item-content">${plan.planName}</span>
                        </div>
                        <div class="confirm-block-content-item">
                            <span class="confirm-block-content-item-title">被保人职业类型</span>
                            <span class="confirm-block-content-item-content">${plan.jobCategory}</span>
                        </div>
                        <div class="table2">
                            <table>
                                <thead>
                                <tr>
                                    <th class="border-r border-color-white">序号</th>
                                    <th class="border-r border-color-white">姓名</th>
                                    <th class="border-r border-color-white">性别</th>
                                    <th class="border-r border-color-white">证件类型</th>
                                    <th class="border-r border-color-white">证件号码</th>
                                    <th class="border-r border-color-white">出生日期</th>
                                    <th class="border-r border-color-white">医保所在地</th>
                                    <th class="border-r border-color-white">从事职业</th>
                                </tr>
                                </thead>
                                <tbody>
                                <#list plan.personalList as personal>
                                    <#if personal.number % 2 == 1>
                                        <tr>
                                            <td class="border-r border-color-gray">${personal.number}</td>
                                            <td class="border-r border-color-gray">${personal.realName.value}</td>
                                            <#if personal.sex.value == 'MALE'>
                                                <td class="border-r border-color-gray">男</td>
                                            </#if>
                                            <#if personal.sex.value == 'FEMALE'>
                                                <td class="border-r border-color-gray">女</td>
                                            </#if>
                                            <#if personal.certType.value == 'ID_NUMBER'>
                                                <td class="border-r border-color-gray">身份证</td>
                                            </#if>
                                            <#if personal.certType.value == 'PASSPORT'>
                                                <td class="border-r border-color-gray">护照</td>
                                            </#if>
                                            <td class="border-r border-color-gray">${personal.certCode.value}</td>
                                            <td class="border-r border-color-gray">${personal.birth.value}</td>
                                            <td class="border-r border-color-gray">
                                                <#if personal.medicareCity?exists && personal.medicareCity.value??>
                                                    ${personal.medicareCity.value}
                                                <#else>
                                                </#if>
                                            </td>
                                            <td class="border-r border-color-gray">${personal.jobPosition.value}</td>
                                        </tr>
                                    </#if>
                                    <#if personal.number % 2 != 1>
                                        <tr style="background:#F3F3F3;">
                                            <td class="border-r border-color-white">${personal.number}</td>
                                            <td class="border-r border-color-white">${personal.realName.value}</td>
                                            <#if personal.sex.value == 'MALE'>
                                                <td class="border-r border-color-white">男</td>
                                            </#if>
                                            <#if personal.sex.value == 'FEMALE'>
                                                <td class="border-r border-color-white">女</td>
                                            </#if>
                                            <#if personal.certType.value == 'ID_NUMBER'>
                                                <td class="border-r border-color-white">身份证</td>
                                            </#if>
                                            <#if personal.certType.value == 'PASSPORT'>
                                                <td class="border-r border-color-white">护照</td>
                                            </#if>
                                            <td class="border-r border-color-white">${personal.certCode.value}</td>
                                            <td class="border-r border-color-white">${personal.birth.value}</td>
                                            <td class="border-r border-color-white">
                                                <#if personal.medicareCity?exists && personal.medicareCity.value??>
                                                    ${personal.medicareCity.value}
                                                <#else>
                                                </#if>
                                            </td>
                                            <td class="border-r border-color-white">${personal.jobPosition.value}</td>
                                        </tr>
                                    </#if>
                                </#list>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </#list>
            </div>
            <div class="confirm-block">

                <div class="pay-bottom" style="margin: 2500px 0 104px 0;">
                    <p style="margin-bottom: 30px">
                        用&nbsp;&nbsp;印&nbsp;&nbsp;时&nbsp;&nbsp;间：
                        <span style="width: 56px" class="line">${year}</span>年
                        <span style="width: 56px" class="line">${month}</span>月
                        <span style="width: 56px" class="line">${day}</span>日
                    </p>
                    <p>用&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;印：<span style="width: 234px" class="line"></span></p>
                    <img style="margin: 60px 0 0 0;" src=${lbz}/>
                </div>
            </div>
        </div>
    </div>
</div>
</body>
</html>