<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<?mso-application progid="Excel.Sheet"?>
<Workbook xmlns="urn:schemas-microsoft-com:office:spreadsheet" xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:x="urn:schemas-microsoft-com:office:excel" xmlns:ss="urn:schemas-microsoft-com:office:spreadsheet" xmlns:html="http://www.w3.org/TR/REC-html40" xmlns:dt="uuid:C2F41010-65B3-11d1-A29F-00AA00C14882"><DocumentProperties xmlns="urn:schemas-microsoft-com:office:office"><LastAuthor>lzs</LastAuthor><Created>2006-09-16T00:00:00Z</Created><LastSaved>2022-03-21T06:03:00Z</LastSaved></DocumentProperties><CustomDocumentProperties xmlns="urn:schemas-microsoft-com:office:office"><ICV dt:dt="string">E7E88242E0334516948DC8A33CADFF38</ICV><KSOProductBuildVer dt:dt="string">2052-11.1.0.11365</KSOProductBuildVer></CustomDocumentProperties><ExcelWorkbook xmlns="urn:schemas-microsoft-com:office:excel"><WindowWidth>18530</WindowWidth><WindowHeight>7130</WindowHeight><ProtectStructure>False</ProtectStructure><ProtectWindows>False</ProtectWindows></ExcelWorkbook><Styles><Style ss:ID="Default" ss:Name="Normal"><Alignment ss:Vertical="Bottom"/><Borders/><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000"/><Interior/><NumberFormat/><Protection/></Style><Style ss:ID="s11" ss:Name="百分比"><NumberFormat ss:Format="0%"/></Style><Style ss:ID="s49"><Alignment ss:Vertical="Bottom"/><Borders/><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000"/><Interior/><NumberFormat/><Protection/></Style><Style ss:ID="s50"><Alignment ss:Vertical="Bottom" ss:WrapText="1"/><Protection ss:Protected="0"/></Style><Style ss:ID="s51"><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s52"><Protection ss:Protected="0"/></Style><Style ss:ID="s53"><Alignment ss:Vertical="Center"/><Protection ss:Protected="0"/></Style><Style ss:ID="s54"><Alignment ss:Horizontal="Center" ss:Vertical="Center"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000" ss:Bold="1"/><Interior ss:Color="#DAEEF3" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s55"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="9" ss:Color="#000000"/><Protection ss:Protected="0"/></Style><Style ss:ID="s56"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="9" ss:Color="#000000"/><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/><NumberFormat/><Protection ss:Protected="0"/></Style><Style ss:ID="s57"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="9" ss:Color="#000000"/><Protection ss:Protected="0"/></Style><Style ss:ID="s58"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="9" ss:Color="#000000"/><NumberFormat/><Protection ss:Protected="0"/></Style><Style ss:ID="s59"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="9" ss:Color="#000000"/><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/><NumberFormat/><Protection ss:Protected="0"/></Style><Style ss:ID="s60"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="9" ss:Color="#000000"/><NumberFormat ss:Format="0.00%"/><Protection ss:Protected="0"/></Style><Style ss:ID="s61"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="9" ss:Color="#000000"/><Interior/><Protection ss:Protected="0"/></Style><Style ss:ID="s62" ss:Parent="s11"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="9" ss:Color="#000000"/><Protection ss:Protected="0"/></Style><Style ss:ID="s63"><Alignment ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="9"/><Interior/><NumberFormat/><Protection ss:Protected="0"/></Style><Style ss:ID="s64"><Alignment ss:Vertical="Center" ss:WrapText="1"/><Protection ss:Protected="0"/></Style><Style ss:ID="s65"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders/><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000" ss:Bold="1"/><Interior/><Protection ss:Protected="0"/></Style><Style ss:ID="s66"><Alignment ss:Vertical="Top" ss:WrapText="1"/><Borders/><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11"/><Protection ss:Protected="0"/></Style><Style ss:ID="s67"><Alignment ss:Horizontal="Center" ss:Vertical="Center"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="14" ss:Color="#000000" ss:Bold="1"/><Interior/><Protection ss:Protected="0"/></Style><Style ss:ID="s68"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="12"/><Interior ss:Color="#DAEEF3" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s69"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="Arial" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s70"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="Arial" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><NumberFormat ss:Format="#,##0"/><Protection ss:Protected="0"/></Style><Style ss:ID="s71"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s72"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s73"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="Arial" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><NumberFormat ss:Format="0%"/><Protection ss:Protected="0"/></Style><Style ss:ID="s74"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Protection ss:Protected="0"/></Style><Style ss:ID="s75"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s76"><Alignment ss:Horizontal="Center" ss:Vertical="Center"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="16" ss:Color="#000000" ss:Bold="1"/><Interior ss:Color="#DAEEF3" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s77"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s78"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s79"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s80"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/></Style><Style ss:ID="s81"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11"/><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/></Style><Style ss:ID="s82"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s83"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000" ss:Bold="1"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s84"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11"/><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/></Style><Style ss:ID="s85"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000" ss:Bold="1"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s86"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s87"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s88"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000" ss:Bold="1"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s89"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000" ss:Bold="1"/><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/></Style><Style ss:ID="s90"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s91"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11"/><Protection ss:Protected="0"/></Style><Style ss:ID="s92"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s93"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s94"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000" ss:Bold="1"/><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s95"><Alignment ss:Horizontal="Center" ss:Vertical="Center"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000" ss:Bold="1"/><Interior ss:Color="#DAEEF3" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s96"><Alignment ss:Horizontal="Center" ss:Vertical="Center"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="14" ss:Color="#000000" ss:Bold="1"/><Interior/><Protection ss:Protected="0"/></Style><Style ss:ID="s97"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s98"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="Arial" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s99"><Alignment ss:Horizontal="Center" ss:Vertical="Center"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="16" ss:Color="#000000" ss:Bold="1"/><Interior ss:Color="#DAEEF3" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s100"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s101"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s102"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s103"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/></Style><Style ss:ID="s104"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11"/><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/></Style><Style ss:ID="s105"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s106"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s107"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s108"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s109"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000" ss:Bold="1"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s110"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s111"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11"/><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/></Style><Style ss:ID="s112"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000" ss:Bold="1"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s113"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000" ss:Bold="1"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s114"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s115"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s116"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s117"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000" ss:Bold="1"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s118"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000" ss:Bold="1"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s119"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s120"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000" ss:Bold="1"/><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/></Style><Style ss:ID="s121"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s122"><Alignment ss:Horizontal="Left" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11"/><Protection ss:Protected="0"/></Style><Style ss:ID="s123"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#FF0000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s124"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s125"><Alignment ss:Horizontal="Left" ss:Vertical="Center"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="11" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/></Style><Style ss:ID="s126"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="Arial" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s127"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s128"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Protection ss:Protected="0"/></Style><Style ss:ID="s129"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="Arial" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s130"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s131"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s132"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s133"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s134"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s599"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="Arial" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFFFF" ss:Pattern="Solid"/><NumberFormat ss:Format="#,##0"/><Protection ss:Protected="0"/></Style><Style ss:ID="s600"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="10" ss:Color="#000000"/><Interior ss:Color="#FFFF00" ss:Pattern="Solid"/><Protection ss:Protected="0"/></Style><Style ss:ID="s740"><Alignment ss:Horizontal="Center" ss:Vertical="Center"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"></Border><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"></Border><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"></Border></Borders></Style><Style ss:ID="s550"><Alignment ss:Horizontal="Center" ss:Vertical="Center" ss:WrapText="1"/><Borders><Border ss:Position="Bottom" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Left" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Right" ss:LineStyle="Continuous" ss:Weight="1"/><Border ss:Position="Top" ss:LineStyle="Continuous" ss:Weight="1"/></Borders><Font ss:FontName="宋体" x:CharSet="134" ss:Size="9" ss:Color="#000000"/><Protection ss:Protected="0"/></Style></Styles>
    <Worksheet ss:Name="第一次询价">
        <Table x:FullColumns="1" x:FullRows="1" ss:StyleID="s52"
               ss:DefaultColumnWidth="48.9" ss:DefaultRowHeight="25.35">
            <Column ss:StyleID="s52" ss:AutoFitWidth="0" ss:Width="115.5" ss:Span="3"/>
            <Column ss:StyleID="s52" ss:AutoFitWidth="0" ss:Width="65.4"/>
            <Column ss:StyleID="s52" ss:AutoFitWidth="0" ss:Width="59.9" ss:Span="3"/>
            <Row ss:Height="25.2">
                <Cell ss:StyleID="s95" ss:MergeAcross="1">
                    <Data ss:Type="String">询价重点信息</Data>
                </Cell>
            </Row>
            <#if basicInfo?? && (basicInfo?size > 0)>
                <#list basicInfo as basic>
                    <Row ss:Height="25.2">
                        <Cell ss:StyleID="s55"><ss:Data ss:Type="String" xmlns="http://www.w3.org/TR/REC-html40"><Font html:Color="#000000" >${(basic.key)!}</Font><Font html:Color="#FF0000"><#if basic.isRequired == true >*</#if></Font></ss:Data></Cell>
                        <#assign itemValue = basic.value >
                        <#if basic.isRequired == true >
                            <Cell ss:StyleID="s56"><Data ss:Type="String">${(itemValue)!}</Data></Cell>
                        <#else>
                            <Cell ss:StyleID="s550"><Data ss:Type="String">${(itemValue)!}</Data></Cell>
                        </#if>
                    </Row>
                </#list>
            </#if>
            <Row ss:Height="25.2">
                <Cell ss:StyleID="s65">
                    <Data ss:Type="String">备注：标红星（*）都是必填项目，对于确实未获得信息的，可以填写不清楚，报价会适当上浮，对于未填写的，将不予报价</Data>
                </Cell>
            </Row>
            <Row ss:Height="25.2"/>
            <Row ss:Height="25.2">
                <Cell ss:StyleID="s96" ss:MergeAcross="8">
                    <Data ss:Type="String">${(planName)!}</Data>
                </Cell>
            </Row>
            <Row ss:Height="25.2">
                <Cell ss:StyleID="s68">
                    <Data ss:Type="String">保障内容</Data>
                </Cell>
                <Cell ss:StyleID="s68">
                    <Data ss:Type="String">保障责任</Data>
                </Cell>
                <Cell ss:StyleID="s68">
                    <Data ss:Type="String">类型（此列为说明）</Data>
                </Cell>
                <Cell ss:StyleID="s68">
                    <Data ss:Type="String">保障额度</Data>
                </Cell>
                <Cell ss:StyleID="s68">
                    <Data ss:Type="String">年免赔额</Data>
                </Cell>
                <Cell ss:StyleID="s68">
                    <Data ss:Type="String">赔付比例</Data>
                </Cell>
                <Cell ss:StyleID="s68">
                    <Data ss:Type="String">其它</Data>
                </Cell>
                <Cell ss:StyleID="s68">
                    <Data ss:Type="String">分项费用</Data>
                </Cell>
                <Cell ss:StyleID="s68">
                    <Data ss:Type="String">标准费用</Data>
                </Cell>
            </Row>
            <#if prodList?? && (prodList?size > 0)>
                <#list prodList as prod>
                    <#assign dutyList = prod.dutyList/>
                    <#assign mergeSize = dutyList?size -1/>
                    <#if dutyList[0].other?? && (dutyList[0].other?size > 0)>
                        <#assign height = 25.2 * (dutyList[0].other?size + 1)>
                    <#else >
                        <#assign height = 25.2>
                    </#if>
                    <Row ss:Height=${"'" + height + "'"}>
                        <Cell ss:StyleID="s126" ss:MergeDown= ${"'" + mergeSize + "'"}>
                            <ss:Data ss:Type="String" xmlns="http://www.w3.org/TR/REC-html40"><Font html:Color="#000000"></Font><Font
                                        html:Face="宋体" html:Color="#000000">${(prod.prodName)!}</Font></ss:Data>
                        </Cell>
                        <Cell ss:StyleID="s70">
                            <ss:Data ss:Type="String" xmlns="http://www.w3.org/TR/REC-html40"><Font html:Color="#000000"></Font><Font
                                        html:Face="宋体" html:Color="#000000">${(dutyList[0].dutyName)!}</Font></ss:Data>
                        </Cell>
                        <Cell ss:StyleID="s599">
                            <Data ss:Type="String">${(dutyList[0].dutyType)!}</Data>
                        </Cell>
                        <#assign cellType = "Number">
                        <#assign dutyValue = dutyList[0].amount>
                        <#if dutyValue?is_number && dutyValue lt 0>
                            <#assign cellType = "String">
                            <#assign dutyValue = "-">
                        </#if>
                        <Cell ss:StyleID="s70">
                            <Data ss:Type=${"'" + cellType + "'"}>${dutyValue}</Data>
                        </Cell>
                        <#assign cellType = "Number">
                        <#assign dutyValue = dutyList[0].deduction>
                        <#if dutyValue?is_number && dutyValue lt 0>
                            <#assign cellType = "String">
                            <#assign dutyValue = "-">
                        </#if>
                        <Cell ss:StyleID="s70">
                            <Data ss:Type=${"'" + cellType + "'"}>${dutyValue}</Data>
                        </Cell>

                        <#assign cellType = "Number">
                        <#assign dutyValue = dutyList[0].payment>
                        <#if dutyValue?is_number && dutyValue lt 0>
                            <#assign cellType = "String">
                            <#assign dutyValue = "-">
                        <#else >
                            <#assign cellType = "String">
                            <#assign dutyValue = dutyValue + "%">
                        </#if>
                        <Cell ss:StyleID="s70">
                            <Data ss:Type=${"'" + cellType + "'"}>${dutyValue}</Data>
                        </Cell>
                        <Cell ss:StyleID="s70">
                            <Data xmlns="http://www.w3.org/TR/REC-html40" ss:Type="String">
                                <#if (dutyList[0].other?? && (dutyList[0].other?size > 0))>
                                    <#list dutyList[0].other as otherInfo>
                                        <Font>${(otherInfo)!}</Font>
                                    </#list>
                                </#if>
                            </Data>
                        </Cell>
                        <Cell ss:StyleID="s600">
                            <Data ss:Type="String">${(dutyList[0].price)!}元/人/年</Data>
                        </Cell>
                        <Cell ss:StyleID="s70" ss:MergeDown=${"'" + mergeSize + "'"}>
                            <Data ss:Type="String">${(prod.configPrice)!}元/人/年</Data>
                        </Cell>
                    </Row>
                    <#if (dutyList?size > 1)>
                        <#list dutyList as duty>
                            <#if duty.other?? && (duty.other?size > 0)>
                                <#assign duty_height = 25.2 * (duty.other?size + 1)>
                            <#else >
                                <#assign duty_height = 25.2>
                            </#if>
                            <#if duty_index != 0>
                                <Row ss:Height=${"'" + duty_height + "'"}>
                                    <Cell ss:Index="2" ss:StyleID="s72">
                                        <Data ss:Type="String">${(duty.dutyName)!}</Data>
                                    </Cell>
                                    <Cell ss:StyleID="s599">
                                        <Data ss:Type="String">${(duty.dutyType)!}</Data>
                                    </Cell>
                                    <Cell ss:StyleID="s70">
                                        <Data ss:Type="Number">${(duty.amount)!}</Data>
                                    </Cell>
                                    <#assign cellType = "Number">
                                    <#assign dutyValue = duty.deduction>
                                    <#if dutyValue?is_number && dutyValue lt 0>
                                        <#assign cellType = "String">
                                        <#assign dutyValue = "-">
                                    </#if>
                                    <Cell ss:StyleID="s70">
                                        <Data ss:Type=${"'" + cellType + "'"}>${dutyValue}</Data>
                                    </Cell>

                                    <#assign cellType = "Number">
                                    <#assign dutyValue = duty.payment>
                                    <#if dutyValue?is_number && dutyValue lt 0>
                                        <#assign cellType = "String">
                                        <#assign dutyValue = "-">
                                    <#else >
                                        <#assign cellType = "String">
                                        <#assign dutyValue = dutyValue + "%">
                                    </#if>
                                    <Cell ss:StyleID="s70">
                                        <Data ss:Type=${"'" + cellType + "'"}>${dutyValue}</Data>
                                    </Cell>
                                    <Cell ss:StyleID="s70">
                                        <Data xmlns="http://www.w3.org/TR/REC-html40" ss:Type="String">
                                            <#if (duty.other?? && (duty.other?size > 0))>
                                                <#list duty.other as otherInfo>
                                                    <Font>${(otherInfo)!}</Font>
                                                </#list>
                                            </#if>
                                        </Data>
                                    </Cell>
                                    <Cell ss:StyleID="s600">
                                        <Data ss:Type="String">${(duty.price)!}元/人/年</Data>
                                    </Cell>
                                </Row>
                            </#if>
                        </#list>
                    </#if>
                </#list>
            </#if>

            <Row ss:Height="25.2">
                <Cell ss:Index="9999" ss:StyleID="s52"/>
            </Row>
            <Row ss:Height="25.2">
                <Cell ss:StyleID="s99" ss:MergeAcross="6">
                    <Data ss:Type="String">报价申明（不可删除）</Data>
                </Cell>
            </Row>
            <#assign specialIndex = 1>
            <#if (duty?? && duty?size > 0)>
                <#list duty as dutySpecial>
                    <Row ss:Height="19.95">
                        <Cell ss:StyleID="s100" ss:MergeAcross="6">
                            <Data ss:Type="String">注${specialIndex}：${(dutySpecial)}</Data>
                            <#assign specialIndex = specialIndex + 1>
                        </Cell>
                    </Row>
                </#list>
            </#if>
            <#if (duty?? && qoute?size > 0)>
                <#list qoute as qouteSpecial>
                    <#if qouteSpecial_index != qoute?size - 1>
                        <Row ss:Height="19.95">
                            <Cell ss:StyleID="s100" ss:MergeAcross="6">
                                <Data ss:Type="String">注${specialIndex}：${(qouteSpecial)}</Data>
                                <#assign specialIndex = specialIndex + 1>
                            </Cell>
                        </Row>
                    <#else >
                        <Row ss:Height="25.2">
                            <Cell ss:StyleID="s125" ss:MergeAcross="6">
                                <Data ss:Type="String">注${specialIndex}：${(qouteSpecial)}</Data>
                            </Cell>
                        </Row>
                    </#if>
                </#list>
            </#if>
            <Row ss:Height="25.2">
                <Cell ss:StyleID="s94">
                    <Data ss:Type="String">不承担中医理赔赔付责任。</Data>
                </Cell>
            </Row>
            <Row ss:Height="25.2" ss:Span="7"/>
            <Row ss:Index="74" ss:Height="24.9"/>
            <Row ss:Index="80" ss:Height="24.9" ss:Span="4"/>
        </Table>
    </Worksheet>
</Workbook>