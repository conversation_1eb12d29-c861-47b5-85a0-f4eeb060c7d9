spring:
  cloud:
    nacos:
      discovery:
        metadata:
          group: discovery-guide-group
          version: 1.0
        server-addr: http://nacos-fat.internal.insgeek
        namespace: platform
      username: nacos
      password: nacos
      config:
        server-addr: http://nacos-fat.internal.insgeek
        namespace: platform
        group: insgeek-business-quote
        prefix: ${spring.application.name}
        file-extension: yaml
        extension-configs:
          - data-id: insgeek-ops-common-platform.yaml
            group: insgeek-ops
            refresh: true
          - data-id: insgeek-business-insurance-duty-platform.yaml
            group: insgeek-business-insurance
            refresh: true
          - data-id: insgeek-business-quote-special.yaml
            group: insgeek-business-quote
            refresh: true
          - data-id: ins-common-platform.yaml
            group: insgeek-business-common
            refresh: true
          - data-id: insgeek-ops-secretkey-platform.yaml
            group: insgeek-ops
            refresh: true
        shared-configs:
          - data-id: insgeek-ops-actuator-platform.yaml
            group: insgeek-ops
            refresh: true
        refresh-enabled: true
  profiles:
    active: platform

nacos:
  password: nacos
  server-addr: http://nacos-fat.internal.insgeek
  username: nacos