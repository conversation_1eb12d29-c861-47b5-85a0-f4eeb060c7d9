#2024-02-18T15:50:34.252
b_b_quote_1=Enterprise query error
b_b_quote_2=Enterprise account already exists
b_b_quote_3=The company name and account number do not match
b_b_quote_4=scene cannot be empty
b_b_quote_5=item_type cannot be empty
b_b_quote_6=No inquiry data
b_b_quote_7=Quotation information quotationinfoid not found
b_b_quote_8=The value-added service information has been saved, please refresh the page and edit it again.
b_b_quote_9=The current data is not the latest version, please return to the list to view the latest data!
b_b_quote_10=No inquiry data
b_b_quote_11=Failed to upload oss file
b_b_quote_12=No liability needs to be calculated
b_b_quote_13=Data exception: configuration information does not exist
b_b_quote_14=Data anomaly: Product liability information does not exist
b_b_quote_15=resulterror.append(premium required).tostring()
b_b_quote_16=resulterror.append(premium and commission ratio required).tostring()
b_b_quote_17=Email is required
b_b_quote_18=Customer mobile phone number is required
b_b_quote_19=Customer mobile phone number and email address are required
b_b_quote_20=Failed to query special offer
b_b_quote_21=Failed to generate special offer
b_b_quote_22=Query configuration information error
b_b_quote_23=The configuration information of the associated quotation is empty
b_b_quote_24=Enterprise query error
b_b_quote_25=Data exception: configuration information does not exist
b_b_quote_26=Data anomaly: Product liability information does not exist
b_b_quote_27=resulterror.append(premium required).tostring()
b_b_quote_28=resulterror.append(premium and commission ratio required).tostring()
b_b_quote_29=Email is required
b_b_quote_30=Customer mobile phone number is required
b_b_quote_31=Customer mobile phone number and email address are required
b_b_quote_32=Query configuration information error
b_b_quote_33=The configuration information of the associated quotation is empty
b_b_quote_34=Enterprise query error
b_b_quote_35=The corresponding return node was not found, and the process flow was abnormal!
b_b_quote_36=Name cannot be empty
b_b_quote_37=Certificate type cannot be empty
b_b_quote_38=ID number cannot be empty
b_b_quote_39=Medical insurance type cannot be empty
b_b_quote_40=Medical insurance address cannot be empty
b_b_quote_41=Data value cannot be null
b_b_quote_42=Field name encoding cannot be empty
b_b_quote_43=Field name cannot be empty
b_b_quote_44=Field type cannot be empty (1-number, 2-string, 3-date, 4-time)
b_b_quote_45=Field length cannot be empty, 0 means no limit
b_b_quote_46=Quotation id cannot be empty
b_b_quote_47=The id of the brokerage company’s corresponding standard product relationship cannot be empty.
b_b_quote_48=Insurance start period cannot be empty
b_b_quote_49=Insurance end date cannot be empty
b_b_quote_50=Insurance period cannot be empty
b_b_quote_51=Enterprise information cannot be empty
b_b_quote_52=Enterprise expansion information cannot be empty
b_b_quote_53=The number of insured persons cannot be empty
b_b_quote_54=The total number of people in the enterprise cannot be empty
b_b_quote_55=File information cannot be empty
b_b_quote_56=Company Name
b_b_quote_57=The id of the brokerage company’s corresponding standard product relationship cannot be empty.
b_b_quote_58=responsevo can not null!
b_b_quote_59=responsevo can not null!
b_b_quote_60=format failed!
b_b_quote_61=offset is out of range [-**********000 ~ **********000]!
b_b_quote_62=offset is out of range [-********** ~ **********]!
b_b_quote_63=offset is out of range [-******** ~ ********]!
b_b_quote_64=offset is out of range [-867240 ~ 867240]!
b_b_quote_65=offset is out of range [-36135 ~ 36135]!
b_b_quote_66=offset is out of range [-5148 ~ 5148]!
b_b_quote_67=offset is out of range [-1188 ~ 1188]!
b_b_quote_68=offset is out of range [-`999 ~ 999]!
b_b_quote_69=Time format failed
b_b_quote_70=The quotation data has not been generated yet, please try again later!
b_b_quote_71=Please configure the direct superior of your account first!
b_b_quote_72=The remark information is abnormal and submission is prohibited!
b_b_quote_73=Quotation group data does not exist
b_b_quote_74=Inquiry group data does not exist
b_b_quote_75=Inquiry group data does not exist
b_b_quote_76=Quotation group data does not exist
b_b_quote_77=Approval status is wrong
b_b_quote_78=Inquiry group data does not exist
b_b_quote_79=Quotation group data does not exist
b_b_quote_80=The plan has not been created, please create the plan first
b_b_quote_81=Quotation information quoteinfoid not found
b_b_quote_82=The value-added service information has been saved, please refresh the page and edit it again.
b_b_quote_83=The current case has been entered, please do not enter it again!
b_b_quote_84=The returned information does not match the uploaded invoice!
b_b_quote_85=json file download failed!
b_b_quote_86=File upload failed!
b_b_quote_87=zip compression failed!
b_b_quote_88=json file content is null!
b_b_quote_89=The quotation plan has been generated, no need to repeat the operation
b_b_quote_90=The information generated by the company is incorrect
b_b_quote_91=Excel parsing failed
b_b_quote_92=system error
b_b_quote_93=Solution generation failed
b_b_quote_94=Process start failed
b_b_quote_95=There are no tasks available for the currently logged in user
b_b_quote_96=The code for joint clauses is empty
b_b_quote_97=No corresponding insurance policy found
b_b_quote_98=Abnormal assembly plan parameters
b_b_quote_99=Post-processing failed after solution generation
b_b_quote_100=The responsibility is associated with unfinished or unclosed quotation data, and the operation is not allowed
b_b_quote_101=Duplication of quotation responsibilities
b_b_quote_102=The scope of liability selected by a joint and several quotation is not allowed to exceed the liability of its owner.
b_b_quote_103=Single outpatient liability is not allowed to be insured
b_b_quote_104=Please select at least one Product Liability
b_b_quote_105=Exception when calling data center!
b_b_quote_106=The in condition value cannot be empty or null!
b_b_quote_107=The condition value cannot be empty!
b_b_quote_108=Query condition conversion exception!
b_b_quote_109=id cannot be empty!
b_b_quote_110=There are multiple results!
b_b_quote_111=Query without permission control is not supported for the time being. Please add an interface and modify this method to support it!
b_b_quote_112=No data found
b_b_quote_113=Quotation not associated
b_b_quote_114=Quotation is empty
b_b_quote_115=Quotation person type is not applicable to me
b_b_quote_116=The quoted product is illegal
b_b_quote_117=Data combination is illegal
b_b_quote_118=When the person type is non-individual, the insurance period quoted should be within the selected insurance period for the individual.
b_b_quote_119=The customer’s mobile phone number or email address is empty
b_b_quote_120=The same quotation and several plans must be associated with the same main and quoted prices
b_b_quote_121=The main quoted price cannot be closed.
b_b_quote_122=The main quoted price cannot be in the status of pending quotation, quoted price or closed.
b_b_quote_123=Insurance liability cannot be empty
b_b_quote_124=Insurance liability cannot be repeated
b_b_quote_125=Insurance liability products cannot be duplicated
b_b_quote_126=Quote configuration does not exist
b_b_quote_127=There can be no liability under the sub-quote configuration
b_b_quote_128=Data cannot be empty
b_b_quote_129=The unique primary key of the data cannot be empty
b_b_quote_130=Environment variables cannot be empty or non-numeric values
b_b_quote_131=Rate detailed configuration cannot be empty
b_b_quote_132="Two-dimensional rule processing exception, the corresponding value does not match (fieldyval, fieldxval
b_b_quote_133=Two-dimensional rule processing exception, relational product responsibility code does not match
b_b_quote_134=Rule configuration processing error
b_b_quote_135=Rule configuration processing error, customized rules are not implemented
b_b_quote_136=Failed to save calculation results, please recalculate. [Failed to acquire lock]
b_b_quote_137=Customer does not exist
b_b_quote_138=The file osskey or download ID cannot be empty at the same time.
b_b_quote_139=File query condition id, type and data id cannot be empty at the same time
b_b_quote_140=No actionable data found
b_b_quote_141=Data is illegal
b_b_quote_142=There can only be one main enterprise
b_b_quote_143=Please save data belonging to the same type
b_b_quote_144=Enterprises cannot be bound repeatedly under the same contract
b_b_quote_145=The same enterprise cannot bind core configurations repeatedly.
b_b_quote_146=There is no one-to-many relationship between quotation customers and core customers.
b_b_quote_147=The core solution does not exist
b_b_quote_148=The core solution already exists
b_b_quote_149=There is already a relationship between the quotation customer and the core customer. Please unbind it before binding it. The binding enterprise:
b_b_quote_150=The main and quilt plan needs to generate the main plan first
b_b_quote_151=Our company has no relationship with the core
b_b_quote_152=The core enterprise does not exist
b_b_quote_153=The core enterprise does not have an account:
b_b_quote_154=Calculating floating rate failed
b_b_quote_155=Contract start date cannot be greater than end date
b_b_quote_156=Contract enterprise information core enterprise id does not exist
b_b_quote_157=Abnormal assembly plan parameters
b_b_quote_158=Solution generation failed
b_b_quote_159=Solution generation failed
b_b_quote_160=hr saas userid error:
b_b_quote_161=Query userid data exception:
b_b_quote_162=The enterprise at its core does not exist:
b_b_quote_163=The same enterprise cannot bind core solutions repeatedly.
b_b_quote_164=Failed to create customer:
b_b_quote_165=Copy special exception
b_b_quote_166=Data format is corrupted
b_b_quote_167=data disabled
b_b_quote_168=Please do not repeatedly add channel standard products
b_b_quote_169=Data cannot be repeated
b_b_quote_170=Quotation id cannot be empty
b_b_quote_171=Channel id cannot be empty
b_b_quote_172=Customer information cannot be empty
b_b_quote_173=Insurance period information cannot be empty
b_b_quote_174=Submission Failed-"
b_b_quote_175=Verification failed to add insured
b_b_quote_176=Cipher text parsing failed
b_b_quote_177=Jump failed:
b_b_quote_178=Wrong download file type
b_b_quote_179=Failed to obtain token:
b_b_quote_180=Parameter error:
b_b_quote_181=Wrong ID number->
b_b_quote_182=Duplicate ID number, please check
b_b_quote_183=Insured cannot be empty
b_b_quote_184=There is data that cannot be deleted
b_b_quote_185=Operable data is empty
b_b_quote_186=The preservation settlement period does not match the payment period
b_b_quote_187=Description of special needs up to 5000 words
b_b_quote_188=Lead fee format error
b_b_quote_189=quote_id cannot be empty
b_b_quote_190=The quotation data is being processed in the background. Please try again later.
b_b_quote_191=Only the pending submission status can modify the business type field, and this status will affect the process type.
b_b_quote_192=Solution group name and inquiry/quotation ID cannot be empty or non-numeric type
b_b_quote_193=The file type cannot be empty [bus_type] [bus_classify] ->
b_b_quote_194=Under development...
b_b_quote_195=The corresponding class does not exist-》key
b_b_quote_196=Abnormal operation
b_b_quote_197=Parameter error:
b_b_quote_198=Failed to acquire lock:
b_b_quote_199=Insurance company, the data is incomplete, please complete it and submit it.
b_b_quote_200=The quoted commission ratio, the data is incomplete, please complete it and submit it.
b_b_quote_201=Liability premium, the data is incomplete, please complete it and submit it.
b_b_quote_202=The data on insurance companies, premiums, and commission ratios are complete and are allowed to be submitted.
b_b_quote_301=In Geek+mode, commission rate is required
b_b_quote_302=The commission information is incomplete (please check the quoted commission or proposal commission)

b_b_insurance_1324=身份证
b_b_insurance_1325=出生证
b_b_insurance_1326=护照号
b_b_insurance_1985=本人
b_b_insurance_1986=子女
b_b_insurance_1987=配偶
b_b_insurance_1988=父母
b_b_insurance_1989=男
b_b_insurance_1990=女
b_b_insurance_1295=城镇职工（含在职和退休）
b_b_insurance_1296=城镇居民
b_b_insurance_1297=新型农村合作医疗
b_b_insurance_1298=均未参加
b_b_quote_203=The quoted commission ratio, the data is incomplete, please complete it and predict it.
b_b_quote_204=Coverage Period Start time, end time cannot be empty.
b_b_quote_205=The enterprise has not opened the claim prediction function, so contact the technical staff to enable it.
b_b_quote_206=Quotation companies are not allowed to delete
b_b_quote_207=The enterprise in the contract parameters must include the quoting enterprise
b_b_quote_208 = The prediction function is used by a large number of users, so please initiate a prediction later.
b_b_quote_209=The payment type of the special agreement and the scheme group is inconsistent after claims and refunds under the scheme!
b_b_quote_210=The payment method and security settlement period for all scheme groups need to be consistent!
b_b_quote_211=Each plan should have only one special agreement after claim refund!
b_b_quote_212=This quotation is for the insurance company of Bohai Property and Casualty Insurance. If the system detects that there is no "supplementary medical hospitalization" or "comprehensive medical hospitalization" in the configuration where the "disease death or total disability" occurs, the insurance company will be adjusted or the responsibility for configuration will be re selected
b_b_quote_220=Commission ratio exceeds the upper limit
b_b_quote_221=The commission ratio needs to be a multiple of {0}
b_b_quote_222 = Huanong's single payment type is not the default single payment special 1
b_b_quote_223 = Huanong's term payment type is not the default period payment special 1
b_b_quote_224=The {0} scheme group is not associated with an underwriting company
b_b_quote_225=No associated basic scheme configuration has been made, please configure it
b_b_quote_226=The commission ratio for the quotation has not been filled in
b_b_quote_227={1} is disabled in {0}, please select it again
b_b_quote_228=The quotation information may change, please try refreshing.
b_b_quote_229=The hierarchical responsibility sharing family plan is incomplete, please check
b_b_quote_230=This group code already exists. Please verify the correct number.
b_b_quote_231=The company is not exist
b_b_quote_232=Failed to download the occupation reference table.
b_b_quote_233=order_id cannot be empty
b_b_quote_234=This method currently only supports the change of status of the employer's standard product.
b_b_quote_235=This status cannot be changed.
b_b_quote_236=The source status was not found.
b_b_quote_237=The plan is being confirmed, please try again later.
b_b_quote_238=The order does not exist.
b_b_quote_239=The initial enrollment risk control parameters of the plan are not configured or are incomplete.
b_b_quote_240=The initial enrollment risk control parameter [minimum insured persons] of the plan is not configured.
b_b_quote_241=The initial enrollment risk control parameter [maximum insured persons] of the plan is not configured.
b_b_quote_242=At least {0} persons are required for insurance.
b_b_quote_243=Currently only supports {0} persons. If the company exceeds {0} persons, please contact your dedicated agent.
b_b_quote_244=Invalid group code. Please check.
b_b_quote_245=Under the current conditions, the amount must be at least {0} yuan.
b_b_quote_246=Abnormal premium calculation.
b_b_quote_247=There is an illegal premium filled in, please check.
b_b_quote_248=The total premium was miscalculated.
b_b_quote_249=Currently, only orders with standard_type set to "employer" are supported for copying.
b_b_quote_250=The list of standard plan IDs must not be empty.
b_b_quote_251=The source order detail does not exist.
b_b_quote_252=Renewal premium should be equal to or greater than the original premium.
b_b_quote_253=The source order does not exist.
b_b_quote_254=Failed to parse birthday.

b_b_quote_sgp_001=Data parsing failed. Please provide the original data for troubleshooting.
b_b_quote_sgp_002=Personnel information is missing. Please ensure that all personnel information is complete.
b_b_quote_sgp_003=Please check if "EmployeeCategory," "MemberType," and "AllSelectedPlan" are correct
b_b_quote_sgp_004=Please check if "EmployeeCategory," "MemberType," and "AllSelectedPlan" are correct
b_b_quote_sgp_005=Price interval not configured.
b_b_quote_sgp_006=No proportion data found for the price interval.
b_b_quote_sgp_007=Invalid ratio data. The total ratio exceeds 100%.
b_b_quote_sgp_008=The cart detail ID cannot be empty.
b_b_quote_sgp_009=No insurance company information was found.
b_b_quote_sgp_010=The number of insurers for the selected plan is not unique.
b_b_quote_sgp_011=You have selected a plan from {0} for {1}. For a better experience and more favorable pricing, it is recommended that you choose plans from the same insurance provider for different categories.
b_b_quote_sgp_012=Failed to retrieve estimated premium information
b_b_quote_sgp_013=Failed to retrieve plan coverage information
b_b_quote_sgp_014=The order details were not obtained
b_b_quote_sgp_015=The order does not exist.
b_b_quote_sgp_016=The channel information does not exist.
b_b_quote_sgp_017=The document is being prepared. Please try again later.
b_b_quote_sgp_018=Documents are being prepared. Please try again later.


b_b_quote_sgp_no_more_plans=Sorry, we couldn't find more plans based on your current filters.


b_sg_calc_0001=batch number is not exist
b_sg_calc_0002=no data

occupation_categor_error=The occupation category does not support the selection of 6 categories, please modify the occupation category, or select the non-standard mode quotation

b_b_quote_sgp_019=Failed to download template file.


b_sg_excel_error_001=There is no information about the plan. Please confirm
b_sg_excel_error_002=The number of schemes has exceeded the maximum limit. Please reduce the number of schemes and try again
b_sg_excel_error_003=Cannot be empty
b_sg_excel_error_min_value=The minimum limit of the field is：{0}
b_sg_excel_error_max_value=The maximum limit of the field is：{0}
b_sg_excel_error_004=The parameter format is incorrect. Numbers need to be entered
b_sg_excel_error_006=The legal input is: {0}
b_sg_excel_error_007=Additional insurance cannot be added separately; it requires the corresponding main insurance
b_sg_excel_error_008=The parameter is empty.
b_sg_excel_error_009=We detected duplicate plan names: [{0}].Please update the names or delete the duplicates to continue.
b_sg_excel_error_010=Duplicate shared benefit limits detected. Please review [{0}] and correct accordingly.
b_sg_excel_error_011=Failed to save the shared insurance amount information.
b_sg_excel_error_012=The solution information failed to be saved. Please try again later.
b_sg_excel_error_013=insurer already exists：
b_sg_excel_error_014=One or more coverage details have errors.Please review and update before saving
b_sg_excel_error_015=Non-existent shared benefit limits detected. Please review [{0}] and correct accordingly.
b_sg_excel_error_016=The configuration under Scheme [{0}] cannot be empty
b_sg_excel_error_017=The file could not be processed due to a parsing error. Please check the format and try again, or contact support if the issue persists.
b_sg_excel_error_018=We have detected mutually exclusive liability: [{0}]. Please update or delete the mutex to continue.
b_sg_excel_error_019=There is a mutual exclusion field: [{0}]


b_b_quote_sgp_020=Invalid member last name format.
b_b_quote_sgp_021=Invalid member given name format.
b_b_quote_sgp_022=Member type does not exist.
b_b_quote_sgp_023=Identity type does not exist.
b_b_quote_sgp_024=Invalid Identity Type format.
b_b_quote_sgp_025=Date of Birth cannot be empty.
b_b_quote_sgp_026=Gender does not exist.
b_b_quote_sgp_027=Employee ID No. cannot be empty.
b_b_quote_sgp_028=Nationality does not exist.
b_b_quote_sgp_029=Country of residence does not exist.
b_b_quote_sgp_030=Maritus status does not exist.
b_b_quote_sgp_031=Occupational class does not exist.
b_b_quote_sgp_032=Gender cannot be empty.
b_b_quote_sgp_033=Employee category cannot be empty.
b_b_quote_sgp_034=Employee category does not exist.
b_b_quote_sgp_035=Effective date cannot be empty.
b_b_quote_sgp_036=Please select at least one coverage item.
b_b_quote_sgp_037=%s has no corresponding main benefit. Please confirm and modify.
b_b_quote_sgp_038=Invalid date of birth format.
b_b_quote_sgp_039=Invalid effective date format.
b_b_quote_sgp_040=Invalid employment date format.
b_b_quote_sgp_041=Failed to parse Excel file.
b_b_quote_sgp_042=The list of people is empty.
b_b_quote_sgp_043=The list of benefit is empty.
b_b_quote_sgp_044=Benefit does not exist.
b_b_quote_sgp_045=Exception occurred while aggregating person information for benefit %s.
b_b_quote_sgp_046=No actionable data found.
b_b_quote_sgp_047=The selected plan is not available under the current coverage.
b_b_quote_sgp_048=Please select the correct employee category first.
b_b_quote_sgp_049=Riders selection must be identical to the Core plan.
b_b_quote_sgp_050=Template usage error. Please download the latest template.
b_b_quote_sgp_051=No plan data found.
b_b_quote_sgp_052=Some data did not pass validation. Kindly check and resolve the issues.
b_b_quote_sgp_054=Fill in at least one premium
b_b_quote_sgp_055=Failed to send insurance company email
b_b_quote_sgp_056=The Fact-finding Form file is still being generated. Please try again later. If the issue persists, contact <NAME_EMAIL>.
b_b_quote_sgp_057=Missing email information for the insurance company contact