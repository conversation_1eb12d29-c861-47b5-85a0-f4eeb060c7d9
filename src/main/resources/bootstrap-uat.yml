spring:
  cloud:
    nacos:
      discovery:
        metadata:
          group: discovery-guide-group
          version: 1.0
        server-addr: http://uat-nacos.insgeek.cn
        register-enabled: false
        port: ${server.port}
      username: nacos
      password: nacos
      config:
        server-addr: http://uat-nacos.insgeek.cn
        namespace: uat
        group: insgeek-business-quote
        prefix: ${spring.application.name}
        file-extension: yaml
        extension-configs:
          - data-id: insgeek-ops-common-uat.yaml
            group: insgeek-ops
            refresh: true
          - data-id: insgeek-business-insurance-duty-uat.yaml
            group: insgeek-business-insurance
            refresh: true
          - data-id: insgeek-business-quote-special-uat.yaml
            group: insgeek-business-quote
            refresh: true
          - data-id: ins-common-uat.yaml
            group: insgeek-business-common
            refresh: true
          - data-id: insgeek-ops-secretkey-uat.yaml
            group: insgeek-ops
            refresh: true
        shared-configs:
          - data-id: insgeek-ops-actuator-uat.yaml
            group: insgeek-ops
            refresh: true
        refresh-enabled: true
  profiles:
    active: uat

nacos:
  password: ${spring.cloud.nacos.password}
  server-addr: ${spring.cloud.nacos.discovery.server-addr}
  username: ${spring.cloud.nacos.username}
