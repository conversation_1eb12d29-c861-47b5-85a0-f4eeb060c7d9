spring:
  cloud:
    nacos:
      discovery:
        metadata:
          group: discovery-guide-group
          version: 1.0
        server-addr: http://nacos-dev.internal.insgeek
        register-enabled: false
        port: ${server.port}
      username: nacos
      password: nacos
      config:
        server-addr: http://nacos-dev.internal.insgeek
        namespace: dev
        group: insgeek-business-quote
        prefix: ${spring.application.name}
        file-extension: yaml
        extension-configs:
          - data-id: insgeek-ops-common-dev.yaml
            group: insgeek-ops
            refresh: true
          - data-id: insgeek-business-insurance-duty-dev.yaml
            group: insgeek-business-insurance
            refresh: true
          - data-id: insgeek-business-quote-special.yaml
            group: insgeek-business-quote
            refresh: true
          - data-id: ins-common-dev.yaml
            group: insgeek-business-common
            refresh: true
          - data-id: insgeek-ops-secretkey-dev.yaml
            group: insgeek-ops
            refresh: true
        shared-configs:
          - data-id: insgeek-ops-actuator-dev.yaml
            group: insgeek-ops
            refresh: true
        refresh-enabled: true
  profiles:
    active: dev

nacos:
  password: nacos
  server-addr: http://nacos-dev.internal.insgeek
  username: nacos

