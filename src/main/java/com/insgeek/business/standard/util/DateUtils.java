package com.insgeek.business.standard.util;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Validator;
import com.insgeek.boot.commons.datetime.DateTimeUtil;
import com.insgeek.boot.web.context.IdentityContext;
import com.insgeek.boot.web.util.MessageUtil;
import com.insgeek.business.quote.common.exception.QuoteException;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.ParseException;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.regex.Matcher;
import java.util.stream.Collectors;

/**
 * Class DateUtil Create Time 2021/3/19 下午4:37
 *
 * <AUTHOR> <<EMAIL>>
 * @version 1.0
 * @category JAVA
 * @package com.insgeek.business.insurance.utils
 * @copyright 2015 - 2021 insgeek.com All Rights Reserved
 * @link https://www.insgeek.com
 */
@Slf4j
public class DateUtils {

    private DateUtils() {
    }

    protected static final String[] patterns = {"yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss"};
    public final static long DAY_UNIX = 86400000L;
    public static final String FORMAT = "yyyy-MM-dd HH:mm:ss";
    public static final String FORMAT_T = "yyyy-MM-dd'T'HH:mm:ss";
    public static final String FORMAT_Z = "EEE MMM dd HH:mm:ss Z yyyy";
    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String DDMMYYYY= "dd/MM/yyyy";

    //判断字符串是否为日期格式
    public static boolean isDate(String param) {
        if (param == null) {
            return false;
        }
        try {
            Date date = org.apache.http.client.utils.DateUtils.parseDate(param, patterns);
            if (date == null) {
                return false;
            }
        } catch (ParseException e) {
            return false;
        }
        return true;
    }

    /**
     * 时间格式转换
     * <p>
     * source: 2021-04-12T09:54:31.000+0800
     * target: 2021-04-12 09:54:31
     * </p>
     *
     * <AUTHOR> href ="https://blog.janloong.com">Janloong</a>
     * @since 2021/4/12 6:52 下午
     **/
    public static String dateTimeFormat(String date) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        df.setTimeZone(TimeZone.getTimeZone("UTC"));
        try {
            Date target = df.parse(date);
            DateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return df2.format(target);
        } catch (java.text.ParseException e) {
            log.error("dateTimeFormat", e);
            throw new QuoteException(MessageUtil.get("b_b_quote_69"));
        }
    }

    public static String dateTimeFormat(String date, boolean isUTC) {
        DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
        if (!isUTC) {
            df.setTimeZone(TimeZone.getDefault());
        }
        try {
            Date target = df.parse(date);
            DateFormat df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            return df2.format(target);
        } catch (java.text.ParseException e) {
            log.error("dateTimeFormat", e);
            throw new QuoteException(MessageUtil.get("b_b_quote_69"));
        }
    }

    /**
     * 根据起止时间计算对应的天数
     *
     * <AUTHOR> href ="https://blog.janloong.com">Janloong Doo</a>
     * @since 2021/4/20 7:43 下午
     **/
    public static BigDecimal calculateDay(Long startTime, Long endTime) {
        BigDecimal mills = new BigDecimal(endTime - startTime + 1);
        BigDecimal baseTime = new BigDecimal(24 * 60 * 60 * 1000);
        return mills.divide(baseTime, 20, RoundingMode.HALF_UP);
    }

    /*
     * 时区格式改为年月日时分秒
     * @param oldDate
     * @return
     */
    /*public static String dealDateFormat(String oldDate) {
        Date date1 = null;
        DateFormat df2 = null;
        try {
            DateFormat df = new SimpleDateFormat(FORMAT_T);
            Date date = df.parse(oldDate);
            SimpleDateFormat df1 = new SimpleDateFormat(FORMAT_Z, Locale.UK);
            date1 = df1.parse(date.toString());
            df2 = new SimpleDateFormat(FORMAT);
        } catch (Exception e) {
            log.error("dealDateFormat", e);
        }
        return df2.format(date1);
    }*/

    /**
     * 根据时间点获取所有时间区间
     *
     * <AUTHOR> href ="https://blog.janloong.com">Janloong Doo</a>
     * @since 2021/10/11 3:39 下午
     **/
    public static List<Map<String, ZonedDateTime>> getInterval(List<ZonedDateTime> timePoint) {
        List<Map<String, ZonedDateTime>> result = new ArrayList<>();
        for (int i = 0; i < timePoint.size() - 1; i++) {
            ZonedDateTime start = timePoint.get(i);
            ZonedDateTime end = timePoint.get(i + 1);
            Map<String, ZonedDateTime> time = new HashMap<>();
            time.put("start", start);
            time.put("end", end);
            result.add(time);
        }
        return result;
    }

    /**
     * 获取时间区间key
     *
     * <AUTHOR> href ="https://blog.janloong.com">Janloong Doo</a>
     * @since 2021/10/11 3:42 下午
     **/
    public static List<String> getIntervalKey(List<Map<String, ZonedDateTime>> interval) {
        return interval.stream().map(v -> {
            ZonedDateTime start = v.get("start");
            ZonedDateTime end = v.get("end");
            return start.toString() + "-" + end.toString();
        }).collect(Collectors.toList());
    }

    /**
     * 是否属于区间时间
     *
     * @param s
     * @param startTime
     * @param endTime
     * <AUTHOR> href ="https://blog.janloong.com">Janloong Doo</a>
     * @since 2021/10/11 4:02 下午
     */
    public static Boolean isBetween(String s, Long startTime, Long endTime) {
        String[] split = s.split("-");
        Long start = Long.valueOf(split[0]);
        Long end = Long.valueOf(split[1]);
        if (startTime <= start && end <= endTime) {
            return true;
        }
        return false;
    }

    /**
     * 获取昨日开始时间戳
     *
     * @return
     */
    public static Long getStartTimeStampYesterday() {
        Calendar calendar = Calendar.getInstance();
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH) - 1, 0, 0, 0);
        long tt = calendar.getTime().getTime() / 1000 * 1000;
        return tt;
    }


    public static Long getStartTimeStampYesterday1() {
        // 获取昨天日期
        LocalDate yesterday = LocalDate.now(ZoneId.systemDefault()).minusDays(1);
        // 设置为昨天的开始时间（0点0分0秒）
        ZonedDateTime yesterdayStart = yesterday.atStartOfDay(IdentityContext.getTimeZone());
        // 转换为毫秒数
        return yesterdayStart.toInstant().toEpochMilli();


    }
    /**
     * 获取昨日结束时间戳
     *
     * @return
     */
    public static Long getEndTimeStampYesterday() {
        // 获取昨天日期
        LocalDate yesterday = LocalDate.now(ZoneId.systemDefault()).minusDays(1);
        // 设置为昨天的结束时间（23:59:59）
        ZonedDateTime yesterdayEnd = yesterday.atTime(LocalTime.MAX).atZone(IdentityContext.getTimeZone());
        // 转换为毫秒数
        return yesterdayEnd.toInstant().toEpochMilli();
    }

    public static int calculateAge(ZonedDateTime birthDate, ZonedDateTime now) {
        // 获取出生日期的LocalDate
        LocalDate birthLocalDate = birthDate.toLocalDate();
        // 获取当前日期的LocalDate
        LocalDate nowLocalDate = now.toLocalDate();
        // 计算年龄
        Period period = Period.between(birthLocalDate, nowLocalDate);
        return period.getYears()+1;
    }

    /**
     * 将Date对象转换为ZonedDateTime对象
     *
     * @param date 日期对象
     * @return ZonedDateTime对象
     */
    public static ZonedDateTime dateToZonedDateTime(Date date) {
        Instant instant = date.toInstant();
        ZoneId zoneId = ZoneId.systemDefault(); // 或者使用其他时区ID
        return instant.atZone(zoneId);
    }

    public static  ZonedDateTime parseDateToZonedDateTime(String dateString, ZoneId zoneId) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        try {
            LocalDate localDate = LocalDate.parse(dateString, formatter);
            return localDate.atStartOfDay(zoneId);
        } catch (DateTimeParseException e) {
            return null;
        }
    }
    /**
     * 计算保期时间
     *
     * <AUTHOR> href ="https://blog.janloong.com">Janloong Doo</a>
     * @since 2022/7/25 17:00
     **/
    public static BigDecimal getDaysBetweenStartAndEndTime(ZonedDateTime startTime, ZonedDateTime endTime) {
        return BigDecimal.valueOf(DateTimeUtil.betweenDays(startTime, endTime, true));
    }

    /**
     * 计算首期保障结束时间
     */
    public static ZonedDateTime getFirstPeriodEndTime(ZonedDateTime startTime, int periodType, int periodValue) {
        ZonedDateTime endTime = null;
        // 1 日 2 月 3 年
        if (periodType == 1) {
            endTime = DateTimeUtil.endOfDay(DateTimeUtil.offsetDay(startTime, periodValue));
        }
        if (periodType == 2) {
            endTime = DateTimeUtil.endOfDay(DateTimeUtil.offsetMonth(startTime, periodValue));
        }
        if (periodType == 3) {
            endTime = DateTimeUtil.endOfDay(DateTimeUtil.offsetYear(startTime, periodValue));
        }
        return endTime;
    }

    /**
     * 是否是合法的时间
     *
     * @param date
     * @return
     */
    public static boolean isLegalDate(String date) {
        Matcher matcher = Validator.BIRTHDAY.matcher(date);
        if (matcher.find()) {
            int year = Integer.parseInt(matcher.group(1));
            int month = Integer.parseInt(matcher.group(3));
            int day = Integer.parseInt(matcher.group(5));
            if (year >= 1900) {
                if (month >= 1 && month <= 12) {
                    if (day >= 1 && day <= 31) {
                        if (day == 31 && (month == 4 || month == 6 || month == 9 || month == 11)) {
                            return false;
                        } else if (month != 2) {
                            return true;
                        } else {
                            return day < 29 || day == 29 && DateUtil.isLeapYear(year);
                        }
                    } else {
                        return false;
                    }
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * @Description: 更换日期字符串的格式
     * @Date: 2024/10/23
     * @Param dateString:
     * @Param formatOriginal:
     * @Param formatNew:
     **/
    public static String convertSGToCHNDate(String dateString, String formatOriginal, String formatNew) throws java.text.ParseException {
        if (dateString == null || dateString.trim().isEmpty()) {
            return null;
        }

        // 只过滤明显的默认时间戳格式（带时分秒的1970年日期），而不是所有1970年日期
        // 例如："1970-01-01 00:00:00" 或 "1970-01-01T00:00:00" 这种明显是系统默认值
        // 但保留真实的1970年生日，如 "01/01/1970" 格式
        if (dateString.matches("1970-01-01[\\s T]00:00:00.*")) {
            log.warn("Detected system default timestamp: {}, returning null", dateString);
            return null;
        }

        SimpleDateFormat sfOriginal = new SimpleDateFormat(formatOriginal);
        SimpleDateFormat sfNew = new SimpleDateFormat(formatNew);
        //return sfNew.format(sfOriginal.parse(dateString));

        try {
            return sfNew.format(sfOriginal.parse(dateString));
        } catch (java.text.ParseException e) {
            // 尝试多种可能的日期格式进行解析
            String[] fallbackFormats = {
                    "yyyy-MM-dd HH:mm:ss",
                    "yyyy-MM-dd",
                    "dd/MM/yyyy HH:mm:ss",
                    "MM/dd/yyyy",
                    "yyyy/MM/dd"
            };

            for (String fallbackFormat : fallbackFormats) {
                try {
                    SimpleDateFormat fallbackSf = new SimpleDateFormat(fallbackFormat);
                    Date parsedDate = fallbackSf.parse(dateString);
                    return sfNew.format(parsedDate);
                } catch (java.text.ParseException ignored) {
                    // 继续尝试下一个格式
                }
            }

            // 如果所有格式都失败，记录错误并重新抛出原始异常
            log.error("Failed to parse date string '{}' with format '{}' and fallback formats", dateString, formatOriginal);
            throw e;
        }
    }
}
