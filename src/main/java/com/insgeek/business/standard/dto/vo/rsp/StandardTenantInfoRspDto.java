package com.insgeek.business.standard.dto.vo.rsp;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.insgeek.business.standard.dto.vo.dto.OptionItemDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 渠道信息Dto
 *
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@JsonInclude
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class StandardTenantInfoRspDto {

    /**
     * logo地址
     */
    private String logo;
    /**
     * 主颜色
     */
    private String colorPrimary;
    /**
     * 次颜色
     */
    private String colorSecondary;
    /**
     * 文字颜色
     */
    private String colorPrimaryBgText;

    /**
     * 是否显示跳转gide按钮
     */
    private Boolean showGotoGide = false;
    /**
     * 标品付款流程
     */
    private String standardPaymentProcess;
    /**
     * 支持的标品类型
     */
    private List<OptionItemDto<String>> supportStandardTypeList;

}