package com.insgeek.business.standard.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.BooleanUtil;
import com.insgeek.boot.commons.datetime.DateTimeUtil;
import com.insgeek.boot.web.context.IdentityContext;
import com.insgeek.business.standard.config.StandardSurveyConfig;
import com.insgeek.business.standard.dto.vo.req.StandardInterviewInfoReqDto;
import com.insgeek.business.standard.dto.vo.req.StandardSurveyReqDto;
import com.insgeek.business.standard.enums.StandardSurveyHistoryPopupTypeEnum;
import com.insgeek.business.standard.enums.StandardSurveyPopupTypeEnum;
import com.insgeek.business.standard.interceptor.CustomContext;
import com.insgeek.business.standard.mapper.StandardInterviewInfoMapper;
import com.insgeek.business.standard.mapper.StandardSurveyMapper;
import com.insgeek.business.standard.mapper.StandardSurveyPopupHistoryMapper;
import com.insgeek.business.standard.service.StandardSurveyService;
import com.insgeek.protocol.data.client.entity.IgStandardInterviewInfo;
import com.insgeek.protocol.data.client.entity.IgStandardSurvey;
import com.insgeek.protocol.data.client.entity.IgStandardSurveyPopupHistory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
public class StandardSurveyServiceImpl implements StandardSurveyService {

    @Resource
    private StandardSurveyMapper standardSurveyMapper;

    @Resource
    private StandardSurveyPopupHistoryMapper standardSurveyPopupHistoryMapper;

    @Resource
    private StandardInterviewInfoMapper standardInterviewInfoMapper;

    @Resource
    private StandardSurveyConfig standardSurveyConfig;

    @Override
    public void submit(List<StandardSurveyReqDto> standardSurveyReqDtoList) {
        List<IgStandardSurvey> insertSurveyList = new ArrayList<>();
        for (StandardSurveyReqDto standardSurveyReqDto : standardSurveyReqDtoList) {
            IgStandardSurvey igStandardSurvey = new IgStandardSurvey();
            igStandardSurvey.setQuestion(standardSurveyReqDto.getQuestion());
            igStandardSurvey.setAnswer(standardSurveyReqDto.getAnswer());
            igStandardSurvey.setUid(IdentityContext.getUserId());
            igStandardSurvey.setChannelId(CustomContext.getChannelId());
            insertSurveyList.add(igStandardSurvey);
        }
        if (CollectionUtil.isNotEmpty(insertSurveyList)) {
            standardSurveyMapper.insertAll(insertSurveyList);
        }
    }

    @Override
    public String isPopup() {
        // 判断问卷调查弹窗是否启用
        if (BooleanUtil.isFalse(standardSurveyConfig.getSurveyPopupEnabled())) {
            return StandardSurveyPopupTypeEnum.NON.getValue();
        }

        // 如果当前用户提交过问卷调查表，则判断线上访谈的弹窗是否该显示
        if (hasSubmittedSurveyForm()) {
            // 判断用户访谈弹窗是否启用
            if (BooleanUtil.isFalse(standardSurveyConfig.getInterviewPopupEnabled())) {
                return StandardSurveyPopupTypeEnum.NON.getValue();
            }
            // 判断是否已经提交过线上访谈信息，若提交过，则不显示任何弹窗
            if (hasSubmittedInterviewInfo()) {
                return StandardSurveyPopupTypeEnum.NON.getValue();
            }
            // 判断用户是否点击过“不再提醒”，若点击过，则不显示任何弹窗
            if (hasNotRemindPopupHistory()) {
                return StandardSurveyPopupTypeEnum.NON.getValue();
            }
            // 判断用户是否在今天点击过“考虑一下”，若点击过，则不显示任何弹窗
            if (hasConsiderPopupHistoryToday()) {
                return StandardSurveyPopupTypeEnum.NON.getValue();
            }
            // 若满足上述条件均未满足，则显示“线上访谈弹窗”
            return StandardSurveyPopupTypeEnum.INTERVIEW_POPUP.getValue();
        }
        // 若用户没有提交过问卷，判断今天是否已经显示过问卷弹窗，如果满足条件，则显示“浮标”
        if (hasSurveyPopupRecordToday()) {
            return StandardSurveyPopupTypeEnum.SURVEY_BUOY.getValue();
        }
        // 如果今天没有显示过问卷弹窗，则记录一次弹窗历史
        recordSurveyPopupHistory();
        // 返回显示问卷调查弹窗类型
        return StandardSurveyPopupTypeEnum.SURVEY_POPUP.getValue();
    }

    /**
     * 判断当前用户今日是否显示过问卷弹窗
     */
    private boolean hasSurveyPopupRecordToday() {
        return CollectionUtil.isNotEmpty(standardSurveyPopupHistoryMapper.getPopupHistoryListByUid(IdentityContext.getUserId()));
    }

    /**
     * 判断当前用户今日是否点击过“考虑一下”按钮
     */
    private boolean hasConsiderPopupHistoryToday() {
        return standardSurveyPopupHistoryMapper.getConsiderPopupHistory(IdentityContext.getUserId()) != null;
    }

    /**
     * 判断当前用户是否点击过“不再提醒”按钮
     */
    private boolean hasNotRemindPopupHistory() {
        return standardSurveyPopupHistoryMapper.getNotRemindPopupHistory(IdentityContext.getUserId()) != null;
    }

    /**
     * 判断当前用户是否提交过线上访谈信息
     */
    private boolean hasSubmittedInterviewInfo() {
        return standardInterviewInfoMapper.getInterviewInfoByUid(IdentityContext.getUserId()) != null;
    }

    /**
     * 判断当前用户是否提交过问卷调查表
     */
    private boolean hasSubmittedSurveyForm() {
        return CollectionUtil.isNotEmpty(standardSurveyMapper.getSurveyListByUid(IdentityContext.getUserId()));
    }

    private void recordSurveyPopupHistory() {
        IgStandardSurveyPopupHistory igStandardSurveyPopupHistory = new IgStandardSurveyPopupHistory();
        igStandardSurveyPopupHistory.setUid(IdentityContext.getUserId());
        igStandardSurveyPopupHistory.setRecordDate(DateTimeUtil.nowOfBusiness());
        igStandardSurveyPopupHistory.setChannelId(CustomContext.getChannelId());
        igStandardSurveyPopupHistory.setPopupType(StandardSurveyHistoryPopupTypeEnum.SURVEY_POPUP.getValue());
        standardSurveyPopupHistoryMapper.insertOne(igStandardSurveyPopupHistory);
    }


    @Override
    public void submitInterviewInfo(StandardInterviewInfoReqDto standardInterviewInfoReqDto) {
        IgStandardInterviewInfo igStandardInterviewInfo = new IgStandardInterviewInfo();
        igStandardInterviewInfo.setName(standardInterviewInfoReqDto.getName());
        igStandardInterviewInfo.setContact(standardInterviewInfoReqDto.getContact());
        igStandardInterviewInfo.setInterviewTime(standardInterviewInfoReqDto.getInterviewTime());
        igStandardInterviewInfo.setUid(IdentityContext.getUserId());
        igStandardInterviewInfo.setChannelId(CustomContext.getChannelId());
        standardInterviewInfoMapper.insertOne(igStandardInterviewInfo);
    }

    @Override
    public void report(String closeType) {
        IgStandardSurveyPopupHistory igStandardSurveyPopupHistory = new IgStandardSurveyPopupHistory();
        igStandardSurveyPopupHistory.setUid(IdentityContext.getUserId());
        igStandardSurveyPopupHistory.setRecordDate(DateTimeUtil.nowOfBusiness());
        igStandardSurveyPopupHistory.setChannelId(CustomContext.getChannelId());
        igStandardSurveyPopupHistory.setPopupType(StandardSurveyHistoryPopupTypeEnum.INTERVIEW_POPUP.getValue());
        igStandardSurveyPopupHistory.setCloseType(closeType);
        standardSurveyPopupHistoryMapper.insertOne(igStandardSurveyPopupHistory);
    }

    @Override
    public Boolean isShowCloseBtn() {
        // 判断是否存在点击考虑一下的记录
        // 如果存在，就展示关闭的按钮
        return standardSurveyPopupHistoryMapper.existedConsiderPopupHistory(IdentityContext.getUserId());
    }

}
