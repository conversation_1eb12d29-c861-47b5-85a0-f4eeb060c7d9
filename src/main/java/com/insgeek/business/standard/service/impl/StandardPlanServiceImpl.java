package com.insgeek.business.standard.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.util.CollectionUtils;
import com.alibaba.excel.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.insgeek.boot.commons.datetime.DateTimeUtil;
import com.insgeek.boot.commons.exception.InsgeekException;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.boot.web.context.IdentityContext;
import com.insgeek.boot.web.pojo.dto.CurrencyAmount;
import com.insgeek.boot.web.util.CurrencyAmountUtil;
import com.insgeek.boot.web.util.MessageUtil;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.backend.aggservice.BrokerService;
import com.insgeek.business.quote.backend.dto.quote.BrokerDto;
import com.insgeek.business.quote.common.exception.QuoteException;
import com.insgeek.business.quote.util.CommonUtil;
import com.insgeek.business.standard.aggservice.SingaporeCalcCacheService;
import com.insgeek.business.standard.config.*;
import com.insgeek.business.standard.dto.vo.dto.*;
import com.insgeek.business.standard.dto.vo.req.*;
import com.insgeek.business.standard.dto.vo.rsp.*;
import com.insgeek.business.standard.enums.*;
import com.insgeek.business.standard.interceptor.CustomContext;
import com.insgeek.business.standard.mapper.StandardOrderDetailMapper;
import com.insgeek.business.standard.mapper.StandardPlanMapper;
import com.insgeek.business.standard.mapper.StandardRecommendRatioDataMapper;
import com.insgeek.business.standard.mapper.StandardTenantInfoMapper;
import com.insgeek.business.standard.service.StandardPlanService;
import com.insgeek.business.standard.service.StandardPriceCalcService;
import com.insgeek.business.standard.util.AgeCalculatorUtil;
import com.insgeek.business.standard.util.PersonNumUtils;
import com.insgeek.components.orm.model.core.vo.Page;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.components.orm.model.querydsl.BQLQueryFactory;
import com.insgeek.protocol.common.client.FileClient;
import com.insgeek.protocol.common.client.IdGeneratorClient;
import com.insgeek.protocol.common.dto.UploadResultDto;
import com.insgeek.protocol.data.client.dto.*;
import com.insgeek.protocol.data.client.feign.QuoteRuleExecuteClient;
import com.insgeek.protocol.insurance.client.PlanConfigClient;
import com.insgeek.protocol.insurance.client.ProductUIClient;
import com.insgeek.protocol.insurance.dto.config.RecommendStandardConfig;
import com.insgeek.protocol.insurance.dto.config.RecommendStandardPlan;
import com.insgeek.protocol.insurance.dto.product.request.ProductClientUIDTO;
import com.insgeek.protocol.insurance.enums.IgPlanConfigEnums;
import com.insgeek.protocol.insurance.enums.Sex;
import com.insgeek.protocol.platform.common.dto.entity.*;
import com.insgeek.protocol.platform.rule.client.RuleClient;
import com.insgeek.protocol.platform.rule.client.RuleExecuteClient;
import com.insgeek.protocol.platform.rule.dto.RuleInstanceDto;
import com.insgeek.protocol.platform.rule.dto.RuleStreamPreData;
import com.insgeek.protocol.platform.rule.dto.RuleStreamRequest;
import com.querydsl.core.types.dsl.Expressions;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.insgeek.boot.web.auth.dto.IdentityDto.USER_GUEST;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class StandardPlanServiceImpl implements StandardPlanService {

    @Autowired
    private StandardPlanMapper standardPlanMapper;

    @Autowired
    private FileClient fileClient;

    @Autowired
    private RuleExecuteClient ruleExecuteClient;

    @Autowired
    private QuoteRuleExecuteClient quoteRuleExecuteClient;

    @Autowired
    private RuleClient ruleClient;

    @Autowired
    private StandardRuleConfig standardRuleConfig;

    @Resource
    private StandardEmployerRuleConfig standardEmployerRuleConfig;

    @Autowired
    private SingaporeStandardRuleConfig singaporeStandardRuleConfig;

    @Autowired
    ProductUIClient productUIClient;

    @Autowired
    private IdGeneratorClient idGeneratorClient;

    @Autowired
    private StandardRecommendRatioDataMapper standardRecommendRatioDataMapper;

    @Autowired
    private StandardTenantInfoMapper standardTenantInfoMapper;

    @Autowired
    private StandardChannelConfig standardChannelConfig;

    @Autowired
    private SingaporeStandardRecommendConfig singaporeStandardRecommendConfig;

    @Autowired
    private PlanConfigClient planConfigClient;

    @Autowired
    private SingaporeStandardGroupConfig singaporeStandardGroupConfig;

    @Autowired
    private SingaporeStandardPlanPriceIntervalConfig singaporeStandardPlanPriceIntervalConfig;
    @Autowired
    private StandardOrderDetailMapper standardOrderDetailMapper;

    @Autowired
    private StandardPriceCalcService standardPriceCalcService;

    @Autowired
    private BQLQueryFactory bqlQueryFactory;

    @Autowired
    private BrokerService brokerService;

    @Autowired
    private SingaporeCalcCacheService singaporeCalcCacheService;

    public Map<Long, List<ProductClientUIDTO>> getProductUIByConfigs(List<Long> planConfigIds) {
        ResponseVO<Map<Long, List<ProductClientUIDTO>>> planConfigDutyDetailBatch = productUIClient.getPlanConfigDutyDetailBatch(planConfigIds, "1");
        return planConfigDutyDetailBatch.getData();
    }


    @Override
    public List<RecommendPlanRspDto> getRecommendPlan(RecommendPlanReqDto recommendPlanReqDto) {
        List<RecommendPlanRspDto> recommendPlanRspDtoList = new ArrayList<>();
        List<StandardPlanDto> recommendPlanList = getCalcedPlanList(recommendPlanReqDto);
        List<Long> planConfigIds = recommendPlanList.stream().map(StandardPlanDto::getPlanConfigId)
                .collect(Collectors.toList());
        ResponseVO<Map<Long, List<ProductClientUIDTO>>> planConfigDutyDetailBatch = productUIClient.getPlanConfigDutyDetailBatch(planConfigIds, "1");
        Map<Long, List<ProductClientUIDTO>> planConfigDutyDetailBatchData = planConfigDutyDetailBatch.getData();
        // 查询产品描述
        List<Long> standardIds = recommendPlanList.stream().map(StandardPlanDto::getStandardPlanId)
                .collect(Collectors.toList());
        List<IgStandardPlan> standardPlanAdvantageList = standardPlanMapper.getStandardPlanAdvantage(standardIds);
        Map<Long, List<IgStandardPlan>> standardPlanMap = standardPlanAdvantageList.stream()
                .collect(Collectors.groupingBy(IgStandardPlan::getId));
        List<IgStandardPlanInfo> standardPlanAdvantageInfo = standardPlanMapper.getStandardPlanAdvantageInfo(standardIds);
        Map<Long, List<IgStandardPlanInfo>> standardPlanInfoMap = standardPlanAdvantageInfo.stream()
                .collect(Collectors.groupingBy(IgStandardPlanInfo::getStandardPlanId));
        // 查询产品责任
        for (StandardPlanDto standardPlanDto : recommendPlanList) {
            RecommendPlanRspDto recommendPlanRspDto = new RecommendPlanRspDto();
            recommendPlanRspDto.setStandardPlanId(standardPlanDto.getStandardPlanId());
            recommendPlanRspDto.setPrice(standardPlanDto.getPrice());
            recommendPlanRspDto.setOriginPrice(standardPlanDto.getOriginPrice());
            recommendPlanRspDto.setTitle(standardPlanDto.getTitle());
            recommendPlanRspDto.setDescription(standardPlanDto.getDescription());
            recommendPlanRspDto.setType(standardPlanDto.getType());
            List<IgStandardPlan> igStandardPlans = standardPlanMap.get(standardPlanDto.getStandardPlanId());
            if (CollectionUtil.isNotEmpty(igStandardPlans)) {
                ProductDescInfo productDescInfo = new ProductDescInfo();
                productDescInfo.setTitle(standardPlanDto.getProductTitle());
                productDescInfo.setDescription(standardPlanDto.getProductContent());
                List<IgStandardPlanInfo> igStandardPlanInfos = standardPlanInfoMap.get(standardPlanDto.getStandardPlanId());
                if (CollectionUtil.isNotEmpty(igStandardPlanInfos)) {
                    List<ProductDescItemInfo> productDescItemInfos = new ArrayList<>();
                    igStandardPlanInfos.forEach(igStandardPlanInfo -> {
                        ProductDescItemInfo productDescItemInfo = new ProductDescItemInfo();
                        productDescItemInfo.setTitle(igStandardPlanInfo.getTitle());
                        productDescItemInfo.setContent(igStandardPlanInfo.getContent());
                        productDescItemInfos.add(productDescItemInfo);
                    });
                    productDescInfo.setItems(productDescItemInfos);
                }
                recommendPlanRspDto.setProductDescInfo(productDescInfo);
            }
            List<ProductClientUIDTO> productClientUIDTOS = planConfigDutyDetailBatchData.get(standardPlanDto.getPlanConfigId());
            List<StandardPlanDetailInfoItemRspDto> planDetailInfoItemDtos = getProductClientUIDTOS(productClientUIDTOS);
            recommendPlanRspDto.setDuctyDescList(planDetailInfoItemDtos);
            recommendPlanRspDtoList.add(recommendPlanRspDto);
        }

        List<RecommendPlanRspDto> recommendPlanRspDtoSorted = recommendPlanRspDtoList.stream().sorted(Comparator.comparing(RecommendPlanRspDto::getPrice)).collect(Collectors.toList());
        List<RecommendPlanRspDto> closestIndices = findClosestIndices(recommendPlanRspDtoSorted, recommendPlanReqDto.getPrice(), recommendPlanReqDto.getExcludeStandardPlanId());

        // 销量数据
        List<IgStandardRecommendRatio> byProvinceAndPersonNum = standardRecommendRatioDataMapper.getByProvinceAndPersonNum(recommendPlanReqDto.getCity(), recommendPlanReqDto.getPersonNum());
        // 最高比例
        Optional<IgStandardRecommendRatio> maxRatio = byProvinceAndPersonNum.stream().max(Comparator.comparing(IgStandardRecommendRatio::getRatio));
        maxRatio.ifPresent(igStandardRecommendRatio -> recommendPlanRspDtoSorted.forEach(recommendPlanRspDto -> {
            recommendPlanRspDto.setTopSaleFlag(false);
            // recommendPlanRspDto.setTopSaleFlag(igStandardRecommendRatio.getPrice().equals(recommendPlanRspDto.getOriginPrice()));
        }));

        return closestIndices;
    }


    /**
     * 找到最接近目标价格的索引，并返回新数组的第2、5、8个元素的索引
     * 如果目标价格不在这些位置上，返回实际的索引值
     *
     * @param standardPlanDtoList 方案对象数组
     * @param targetPrice         目标价格
     * @return 新数组的第2、5、8个元素的索引
     */
    public List<RecommendPlanRspDto> findClosestIndices(List<RecommendPlanRspDto> standardPlanDtoList, CurrencyAmount targetPrice, Long excludeStandardPlanId) {
        // 方案推荐
        log.warn("目标价格:{}-更多ID-{}-{}", targetPrice, excludeStandardPlanId, JacksonUtils.writeAsString(standardPlanDtoList));

        int index = findClosestIndex(standardPlanDtoList, targetPrice);
        log.warn("目标价格:{}，最接近的索引:{}", targetPrice, index);
        List<RecommendPlanRspDto> result = new ArrayList<>();

        // 定位到的价格
        RecommendPlanRspDto recommendPlanRspDtoBase = standardPlanDtoList.get(index);
        BigDecimal basePrice = recommendPlanRspDtoBase.getOriginPrice().getAmount();
        log.warn("基础价格:{}", basePrice);

        // 计算起始索引和结束索引
        // 9个元素的一半
        int halfLength = 4;
        int listSize = standardPlanDtoList.size();

        // 计算起始索引
        int startIndex = Math.max(index - halfLength, 0);

        // 计算结束索引
        int endIndex = startIndex + 9;

        // 确保结束索引不超过列表的长度
        if (endIndex > listSize) {
            endIndex = listSize;
            startIndex = Math.max(endIndex - 9, 0);
        }

        // 分组并提取指定元素的索引
        List<RecommendPlanRspDto> indices = new ArrayList<>();
        HashMap<BigDecimal, Integer> priceMap = new HashMap<>();
        HashMap<Long, Integer> idIndexMap = new HashMap<>();
        int indexCount = 0;
        for (int i = startIndex; i <= endIndex; i++) {
            if (indices.size() > 8) {
                break;
            }
            indices.add(standardPlanDtoList.get(i));
            priceMap.put(standardPlanDtoList.get(i).getOriginPrice().getAmount(), indexCount);
            idIndexMap.put(standardPlanDtoList.get(i).getStandardPlanId(), indexCount);
            indexCount++;
        }

        log.warn("获取数组：{}", JacksonUtils.writeAsString(indices));
        log.warn("priceMap：{}", JacksonUtils.writeAsString(priceMap));
        log.warn("idIndexMap：{}", JacksonUtils.writeAsString(idIndexMap));

        // 更多还是默认
        // 更多，返回同组
        // 返回多组 1，4，7
        // 基础index
        List<Integer> group1 = Arrays.asList(0, 1, 2);
        List<Integer> group2 = Arrays.asList(3, 4, 5);
        List<Integer> group3 = Arrays.asList(6, 7, 8);
        Integer baseIndex = priceMap.getOrDefault(basePrice, 0);

        // 更多方案标志位
        if (null != excludeStandardPlanId) {
            log.warn("更多推荐逻辑开始");
            if (group1.contains(idIndexMap.get(excludeStandardPlanId))) {
                for (Integer integer : group1) {
                    result.add(indices.get(integer));
                }
            }
            if (group2.contains(idIndexMap.get(excludeStandardPlanId))) {
                for (Integer integer : group2) {
                    result.add(indices.get(integer));
                }
            }
            if (group3.contains(idIndexMap.get(excludeStandardPlanId))) {
                for (Integer integer : group3) {
                    result.add(indices.get(integer));
                }
            }

        } else {
            log.warn("主推荐逻辑开始");
            // 返回多组 1，4，7
            if (group1.contains(baseIndex)) {
                result.add(indices.get(baseIndex));
            } else {
                result.add(indices.get(1));
            }
            if (group2.contains(baseIndex)) {
                result.add(indices.get(baseIndex));
            } else {
                result.add(indices.get(4));
            }
            if (group3.contains(baseIndex)) {
                result.add(indices.get(baseIndex));
            } else {
                result.add(indices.get(7));
            }
        }
        log.warn("返回多组：{}", JacksonUtils.writeAsString(result));

        return result;
    }

    /**
     * 找到最接近目标价格的索引
     *
     * @param standardPlanDtoList 方案对象数组
     * @param targetPrice         目标价格
     * @return 最接近目标价格的索引
     */
    private static int findClosestIndex(List<RecommendPlanRspDto> standardPlanDtoList, CurrencyAmount targetPrice) {
        int closestIndex = 0;
        double minDiff = Double.MAX_VALUE;

        for (int i = 0; i < standardPlanDtoList.size(); i++) {
            double diff = Math.abs(CurrencyAmountUtil.subtract(standardPlanDtoList.get(i).getPrice(), targetPrice).getAmount().doubleValue());
            if (diff < minDiff) {
                minDiff = diff;
                closestIndex = i;
            }
        }

        return closestIndex;
    }

    @Override
    public StandardPlanDetailRspDto getPlan(Long standardPlanId, String city, Integer minAge, Integer maxAge, Integer femaleRatio, String jobCategory, Integer personNum) {
        StandardPlanDto planInfo = standardPlanMapper.getPlanInfo(standardPlanId);

        if (StandardTypeEnum.WELFARE_RECOMMEND.getValue().equals(planInfo.getStandardType())) {
            StandardPriceCalcDto standardPriceCalcDto = new StandardPriceCalcDto();
            standardPriceCalcDto.setMinAge(minAge);
            standardPriceCalcDto.setMaxAge(maxAge);
            standardPriceCalcDto.setPersonNum(personNum);
            standardPriceCalcDto.setFemaleRatio(femaleRatio);
            standardPriceCalcDto.setJobCategory(jobCategory);
            standardPriceCalcDto.setCity(city);
            standardPriceCalcDto.setChannel(CustomContext.getChannelId().toString());
            standardPriceCalcDto.setType(planInfo.getType());
            standardPriceCalcDto.setPlanConfigId(planInfo.getPlanConfigId());
            standardPriceCalcDto.setPrice(planInfo.getPrice());
            // 计算价格
            getPlanPrice(Collections.singletonList(standardPriceCalcDto));

            // TODO  不可计算提示
            planInfo.setPrice(standardPriceCalcDto.getCalculatePrice());
        }


        IgRisk5 planAgeInfo = standardPlanMapper.getPlanAgeInfo(planInfo.getPlanId());

        StandardPlanDetailRspDto standardPlanDetailRspDto = new StandardPlanDetailRspDto();
        standardPlanDetailRspDto.setStandardPlanId(planInfo.getStandardPlanId());
        ResponseVO<List<UploadResultDto>> listResponseVO = fileClient.listFile(Collections.singletonList(planInfo.getImg().toString()));
        UploadResultDto uploadResultDto = listResponseVO.getData().get(0);
        standardPlanDetailRspDto.setImg(uploadResultDto.getLargeUrl());
        standardPlanDetailRspDto.setTitle(planInfo.getTitle());
        standardPlanDetailRspDto.setDescription(planInfo.getDescription());
        standardPlanDetailRspDto.setPrice(planInfo.getPrice());
        standardPlanDetailRspDto.setStandardFlag(true);
        standardPlanDetailRspDto.setAgeMax(maxAge);
        standardPlanDetailRspDto.setAgeMin(minAge);
        standardPlanDetailRspDto.setInsuredAgeMax(null == planAgeInfo ? null : planAgeInfo.getMaxAge());
        standardPlanDetailRspDto.setInsuredAgeMin(null == planAgeInfo ? null : planAgeInfo.getMinAge());
        standardPlanDetailRspDto.setStartTime(DateTimeUtil.startOfDay(DateTimeUtil.offsetDay(DateTimeUtil.now(true), 3)));
        standardPlanDetailRspDto.setEndTime(DateTimeUtil.endOfDay(DateTimeUtil.offsetYear(DateTimeUtil.offsetDay(DateTimeUtil.now(true), 2), 1)));
        standardPlanDetailRspDto.setStandardType(planInfo.getStandardType());

        ResponseVO<List<ProductClientUIDTO>> planConfigDutyDetail = productUIClient.getPlanConfigDutyDetail(planInfo.getPlanConfigId(), "1");
        List<ProductClientUIDTO> productClientUIDTOS = planConfigDutyDetail.getData();
        List<StandardPlanDetailInfoItemRspDto> standardPlanDetailInfoItemRspDtoList = new ArrayList<>();
        //  大类汇总
        if (StandardTypeEnum.WELFARE_RECOMMEND.getValue().equals(planInfo.getStandardType())) {
            List<StandardPlanDetailInfoItemRspDto> categoryList = getProductClientUIDTOS(planConfigDutyDetail.getData());
            standardPlanDetailRspDto.setCategory(categoryList);
        }
        List<StandardPlanDetailTabItemRspDto> standardPlanDetailTabItemRspDtos = new ArrayList<>();
        //  责任信息
        StandardPlanDetailTabItemRspDto dutyPlanDetail = new StandardPlanDetailTabItemRspDto();
        dutyPlanDetail.setTag("duty");
        dutyPlanDetail.setTitle(StandardPlanDetailTypeEnum.TYPE1.getTitle());
        dutyPlanDetail.setDutyList(productClientUIDTOS);
        standardPlanDetailTabItemRspDtos.add(dutyPlanDetail);

        // 渠道信息 ig_standard_tenant_info 如果设置某些信息（理赔流程），做替换
        List<IgStandardPlanInfo> standardPlanInfo = standardPlanMapper.getStandardPlanInfo(standardPlanId);

        List<String> list = new ArrayList<>();
        list.add(StandardPlanDetailTypeEnum.TYPE3.getValue());
        list.add(StandardPlanDetailTypeEnum.TYPE5.getValue());
        list.add(StandardPlanDetailTypeEnum.TYPE6.getValue());
        list.add(StandardPlanDetailTypeEnum.TYPE7.getValue());

        List<IgStandardPlanInfo> standardPlanInfoList = standardPlanInfo.stream().filter(igStandardPlanInfo -> list.contains(igStandardPlanInfo.getType()))
                .sorted(Comparator.comparing(IgStandardPlanInfo::getOrderIndex))
                .collect(Collectors.toList());

        list.forEach(type -> {
            standardPlanDetailTabItemRspDtos.add(getTab(standardPlanInfoList, type));
        });

        standardPlanInfo.forEach(igStandardPlanInfo -> {
            // 方案亮点
            if (StandardPlanDetailTypeEnum.TYPE2.getValue().equals(igStandardPlanInfo.getType())) {
                StandardPlanDetailInfoItemRspDto standardPlanDetailInfoItemRspDto = new StandardPlanDetailInfoItemRspDto();
                standardPlanDetailInfoItemRspDto.setTitle(igStandardPlanInfo.getTitle());
                standardPlanDetailInfoItemRspDto.setContent(igStandardPlanInfo.getContent());
                standardPlanDetailInfoItemRspDtoList.add(standardPlanDetailInfoItemRspDto);
            }
        });
        if (StandardTypeEnum.WELFARE_RECOMMEND.getValue().equals(planInfo.getStandardType())) {
            standardPlanDetailRspDto.setSellingPointList(standardPlanDetailInfoItemRspDtoList);
        } else {
            standardPlanDetailRspDto.setEmployerPlanDescriptionList(standardPlanDetailInfoItemRspDtoList);
        }
        standardPlanDetailRspDto.setTabs(standardPlanDetailTabItemRspDtos);

        // 投保流程须知
        StandardPlanDetailTabItemRspDto content = getTab(standardPlanInfo, StandardPlanDetailTypeEnum.TYPE4.getValue());
        standardPlanDetailRspDto.setInsuredProcessNotice(content != null ? content.getHtmlContent().replace("{company}", planInfo.getCompanyName()) : "");

        return standardPlanDetailRspDto;
    }

    private StandardPlanDetailTabItemRspDto getTab(List<IgStandardPlanInfo> standardPlanInfoList, String type) {
        // 分组
        Map<Long, Map<String, IgStandardPlanInfo>> resultMap = standardPlanInfoList.stream()
                .collect(Collectors.groupingBy(
                        IgStandardPlanInfo::getChannelId,
                        Collectors.toMap(IgStandardPlanInfo::getType, info -> info, (existingValue, newValue) -> existingValue)
                ));

        // 条款列表
        IgStandardPlanInfo igStandardPlanInfo;
        if (resultMap.containsKey(CustomContext.getChannelId()) && resultMap.get(CustomContext.getChannelId()).containsKey(type)) {
            igStandardPlanInfo = resultMap.get(CustomContext.getChannelId()).getOrDefault(type, null);
        } else {
            igStandardPlanInfo = resultMap.get(0L).getOrDefault(type, null);
        }

        if (null != igStandardPlanInfo) {
            StandardPlanDetailTabItemRspDto standardPlanDetailTabItemRspDto = new StandardPlanDetailTabItemRspDto();
            standardPlanDetailTabItemRspDto.setTag("html");
            standardPlanDetailTabItemRspDto.setTitle(igStandardPlanInfo.getTitle());
            standardPlanDetailTabItemRspDto.setHtmlContent(igStandardPlanInfo.getContent());
            return standardPlanDetailTabItemRspDto;
        }
        return null;
    }

    private static List<StandardPlanDetailInfoItemRspDto> getProductClientUIDTOS(List<ProductClientUIDTO> productClientUIDTOS) {
        List<StandardPlanDetailInfoItemRspDto> standardPlanDetailInfoItemRspDtoList = new ArrayList<>();
        Map<Integer, CurrencyAmount> sumAmountMap = new HashMap<>();
        productClientUIDTOS.forEach(v -> {
            ProductClientUIDTO.BusinessField businessFields = v.getBusinessFields();
            String productType = businessFields.getProductType();
            Integer code = PlanConfigDutyEnum.findByTypeCode(productType).getCategory().getCode();
            if (code > PlanConfigDutyCategoryEnum.CATEGORY_0.getCode()) {
                CurrencyAmount sumAmount = calculateAmount(v);
                if (sumAmount != null) {
                    sumAmountMap.compute(code, (k, amount) -> CurrencyAmountUtil.add(amount, sumAmount));
                }
            }
        });
        standardPlanDetailInfoItemRspDtoList.add(new StandardPlanDetailInfoItemRspDto().setTitle(PlanConfigDutyCategoryEnum.CATEGORY_5.getDesc()).setAmount(sumAmountMap.get(PlanConfigDutyCategoryEnum.CATEGORY_5.getCode())));
        standardPlanDetailInfoItemRspDtoList.add(new StandardPlanDetailInfoItemRspDto().setTitle(PlanConfigDutyCategoryEnum.CATEGORY_3.getDesc()).setAmount(sumAmountMap.get(PlanConfigDutyCategoryEnum.CATEGORY_3.getCode())));
        standardPlanDetailInfoItemRspDtoList.add(new StandardPlanDetailInfoItemRspDto().setTitle(PlanConfigDutyCategoryEnum.CATEGORY_1.getDesc()).setAmount(sumAmountMap.get(PlanConfigDutyCategoryEnum.CATEGORY_1.getCode())));
        standardPlanDetailInfoItemRspDtoList.add(new StandardPlanDetailInfoItemRspDto().setTitle(PlanConfigDutyCategoryEnum.CATEGORY_2.getDesc()).setAmount(sumAmountMap.get(PlanConfigDutyCategoryEnum.CATEGORY_2.getCode())));
        standardPlanDetailInfoItemRspDtoList.add(new StandardPlanDetailInfoItemRspDto().setTitle(PlanConfigDutyCategoryEnum.CATEGORY_4.getDesc()).setAmount(sumAmountMap.get(PlanConfigDutyCategoryEnum.CATEGORY_4.getCode())));
        return standardPlanDetailInfoItemRspDtoList;
    }

    /**
     * 计算 金额
     *
     * @param v
     */
    private static CurrencyAmount calculateAmount(ProductClientUIDTO v) {
        List<CurrencyAmount> sumAmount = new ArrayList<>();
        List<ProductClientUIDTO.Instance> instance = v.getInstance();
        ProductClientUIDTO.BusinessField businessFields = v.getBusinessFields();
        Integer property = businessFields.getProperty();
        AtomicReference<CurrencyAmount> amountCurrent = new AtomicReference<>(CurrencyAmount.zero(IdentityContext.getBusinessCurrency()));
        AtomicReference<BigDecimal> payment = new AtomicReference<>(new BigDecimal(0));
        // 津贴类计算
        if (property == 5) {
            instance.forEach(item -> {
                item.getList().forEach(list -> {
                    list.getFields().forEach(field -> {
                        if ("amount".equals(field.getTag())) {
                            amountCurrent.set(JSON.parseObject(JSON.toJSONString(field.getValue()), CurrencyAmount.class));
                        }
                        if ("payment".equals(field.getTag())) {
                            payment.set(JSON.parseObject(JSON.toJSONString(field.getValue()), BigDecimal.class));
                        }
                    });
                });
            });
            return CurrencyAmountUtil.multi(amountCurrent.get(), payment.get());
        }
        instance.forEach(item -> {
            List<ProductClientUIDTO.InstanceObject> instanceObjectList = item.getList();
            instanceObjectList.forEach(instanceObject -> {
                List<ProductClientUIDTO.Field> fields = instanceObject.getFields();
                fields.forEach(field -> {
                    if ("amount".equals(field.getTag())) {
                        CurrencyAmount amount = JSON.parseObject(JSON.toJSONString(field.getValue()), CurrencyAmount.class);
                        sumAmount.add(amount);
                    }
                });
            });
        });
        return CurrencyAmountUtil.sum(sumAmount);
    }


    @Override
    public void getPlanPrice(List<StandardPriceCalcDto> standardPriceCalcDto) {

        try {
            RuleStreamRequest ruleStreamRequest = new RuleStreamRequest();
            ruleStreamRequest.setDataIds(standardPriceCalcDto.stream().map(StandardPriceCalcDto::getPlanConfigId).collect(Collectors.toList()));
            Map<Long, RuleStreamPreData> preDataMap = new HashMap<>();
            for (StandardPriceCalcDto priceCalcDto : standardPriceCalcDto) {
                RuleStreamPreData preData = new RuleStreamPreData();
                Map<String, Object> environmentMap = new HashMap<>();
                environmentMap.put("price", priceCalcDto.getPrice().getAmount());
                environmentMap.put("plan_config_id", priceCalcDto.getPlanConfigId());
                environmentMap.put("province_code", priceCalcDto.getCity());
                environmentMap.put("job_category", priceCalcDto.getJobCategory());
                environmentMap.put("channel_id", priceCalcDto.getChannel());
                environmentMap.put("female_rate", priceCalcDto.getFemaleRatio());
                environmentMap.put("insured_count", priceCalcDto.getPersonNum());
                environmentMap.put("avg_age", priceCalcDto.getMinAge());
                environmentMap.put("standard_type", priceCalcDto.getType());
                environmentMap.put("id", priceCalcDto.getPlanConfigId());
                preData.setEnvironmentMap(environmentMap);
                preDataMap.put(priceCalcDto.getPlanConfigId(), preData);
            }

            ruleStreamRequest.setPreData(preDataMap);

            log.warn("价格计算费率参数:-----{}", JacksonUtils.writeAsString(ruleStreamRequest));

            ResponseVO<List<RuleInstanceDto>> execute = ruleExecuteClient.execute(standardRuleConfig.getId(), ruleStreamRequest);
            List<RuleInstanceDto> RuleInstanceDtos = CommonUtil.getResponseData(execute);
            List<Long> ids = RuleInstanceDtos.stream().map(RuleInstanceDto::getId).collect(Collectors.toList());
            //4.查看规则流结果
            ResponseVO<Map<Long, Map<String, String>>> environmentVariablesValuePostSecond = ruleClient.findEnvironmentVariablesValuePostSecond(ids);
            Map<Long, Long> IdToDataId = RuleInstanceDtos.stream().collect(Collectors.toMap(RuleInstanceDto::getObjectDataId, RuleInstanceDto::getId));
            Map<Long, Map<String, String>> responseData = CommonUtil.getResponseData(environmentVariablesValuePostSecond);

            log.warn("价格计算费率结果:-----{}", JacksonUtils.writeAsString(responseData));

            for (StandardPriceCalcDto priceCalcDto : standardPriceCalcDto) {
                String calcPrice = responseData.get(IdToDataId.get(priceCalcDto.getPlanConfigId())).get("calc_price");
                String computedRatio = responseData.get(IdToDataId.get(priceCalcDto.getPlanConfigId())).get("computed_ratio");
                CurrencyAmount currencyAmount = CurrencyAmount.valueOf(Double.parseDouble(calcPrice), IdentityContext.getBusinessCurrency());
                CurrencyAmount divide = CurrencyAmountUtil.multi(CurrencyAmountUtil.divide(currencyAmount, BigDecimal.valueOf(10), 0, RoundingMode.HALF_UP), BigDecimal.valueOf(10L));
                priceCalcDto.setCalculatePrice("-".equals(calcPrice) ? null : divide);
                priceCalcDto.setComputedRatio(BigDecimal.valueOf(Double.parseDouble(computedRatio)));
            }
        } catch (Exception e) {
            log.warn("规则无法计算", e);
        }
    }

    @Override
    public void executeRuleAndFillEmployerPlanPrice(List<StandardEmployerPriceCalcDto> standardEmployerPriceCalcDtoList) {
        // 由于雇主标品使用的模版方案可能会是同一个，而规则流传的参数的key是configId，会存在重复情况，导致key值覆盖
        // 为了减少规则流调用次数，这里对list做切割，将list拆分成若干个子集合，每个集合内的所有对象的configId都不重复
        List<List<StandardEmployerPriceCalcDto>> splitList = splitListByUniquePlanConfigId(standardEmployerPriceCalcDtoList);
        for (List<StandardEmployerPriceCalcDto> employerPriceCalcDtoList : splitList) {
            try {
                // 构造规则流计算参数
                RuleStreamRequest ruleStreamRequest = constructRuleStreamRequestWithEmployer(employerPriceCalcDtoList);
                log.info("价格计算费率参数:-----{}", JacksonUtils.writeAsString(ruleStreamRequest));

                // 执行规则流
                ResponseVO<List<RuleInstanceDto>> execute = ruleExecuteClient.execute(standardEmployerRuleConfig.getId(), ruleStreamRequest);
                List<RuleInstanceDto> ruleInstanceDtoList = CommonUtil.getResponseData(execute);
                List<Long> ids = ruleInstanceDtoList.stream().map(RuleInstanceDto::getId).collect(Collectors.toList());

                // 获取规则流结果
                ResponseVO<Map<Long, Map<String, String>>> environmentVariablesValuePostSecond = ruleClient.findEnvironmentVariablesValuePostSecond(ids);
                // key -> 数据id，value -> 规则流结果id
                Map<Long, Long> idToDataIdMapping = ruleInstanceDtoList.stream().collect(Collectors.toMap(RuleInstanceDto::getObjectDataId, RuleInstanceDto::getId, (a, b) -> a));
                // key -> 规则流结果id，值 -> 环境变量
                Map<Long, Map<String, String>> ruleCalcResultMapping = CommonUtil.getResponseData(environmentVariablesValuePostSecond);
                log.info("价格计算费率结果:-----{}", JacksonUtils.writeAsString(ruleCalcResultMapping));

                // 处理规则流结果
                for (StandardEmployerPriceCalcDto standardEmployerPriceCalcDto : employerPriceCalcDtoList) {
                    // 计算后的价格
                    String calcPriceStr = ruleCalcResultMapping.get(idToDataIdMapping.get(standardEmployerPriceCalcDto.getPlanConfigId())).get("calc_price");
                    // 将计算后的价格转换为BigDecimal，并向下取整为整数
                    BigDecimal calcPrice = new BigDecimal(calcPriceStr).setScale(0, RoundingMode.DOWN);
                    // 设置计算后的价格
                    standardEmployerPriceCalcDto.setCalculatePrice(CurrencyAmount.valueOf(calcPrice, IdentityContext.getBusinessCurrency()));
                }
            } catch (Exception e) {
                log.error("【雇主标品】规则流计算出现异常", e);
            }
        }
    }

    /**
     * 对list做切割，将list拆分成若干个子集合，每个集合内的所有对象的configId都不重复
     */
    public List<List<StandardEmployerPriceCalcDto>> splitListByUniquePlanConfigId(List<StandardEmployerPriceCalcDto> inputList) {
        // 保存最终的子列表结果
        List<List<StandardEmployerPriceCalcDto>> result = new ArrayList<>();

        // 对应每个子列表的 planConfigId Set，用于判断是否已有重复 id
        List<Set<Long>> idSets = new ArrayList<>();

        // 遍历输入列表
        for (StandardEmployerPriceCalcDto dto : inputList) {
            Long id = dto.getPlanConfigId();
            boolean placed = false;

            // 遍历已有子列表，尝试放进去
            for (int i = 0; i < result.size(); i++) {
                Set<Long> idSet = idSets.get(i);
                if (!idSet.contains(id)) {
                    result.get(i).add(dto);
                    idSet.add(id);
                    placed = true;
                    break;
                }
            }

            // 所有子列表都有这个 id，创建新列表
            if (!placed) {
                List<StandardEmployerPriceCalcDto> newSubList = new ArrayList<>();
                Set<Long> newIdSet = new HashSet<>();

                newSubList.add(dto);
                newIdSet.add(id);

                result.add(newSubList);
                idSets.add(newIdSet);
            }
        }

        return result;
    }

    /**
     * 构造规则流参数（雇主标品）
     */
    private RuleStreamRequest constructRuleStreamRequestWithEmployer(List<StandardEmployerPriceCalcDto> standardEmployerPriceCalcDtoList) {
        RuleStreamRequest ruleStreamRequest = new RuleStreamRequest();
        // 数据id列表
        List<Long> planConfigList = standardEmployerPriceCalcDtoList.stream().map(StandardEmployerPriceCalcDto::getPlanConfigId).collect(Collectors.toList());
        ruleStreamRequest.setDataIds(planConfigList);
        // 前置数据 key为dataId
        Map<Long, RuleStreamPreData> preData = constructPreDataMapWithEmployer(standardEmployerPriceCalcDtoList);
        ruleStreamRequest.setPreData(preData);
        return ruleStreamRequest;
    }

    /**
     * 构造前置数据（雇主标品）
     */
    private Map<Long, RuleStreamPreData> constructPreDataMapWithEmployer(List<StandardEmployerPriceCalcDto> standardEmployerPriceCalcDtoList) {
        Map<Long, RuleStreamPreData> preDataMap = new HashMap<>();

        for (StandardEmployerPriceCalcDto standardEmployerPriceCalcDto : standardEmployerPriceCalcDtoList) {
            RuleStreamPreData ruleStreamPreData = new RuleStreamPreData();
            Map<String, Object> environmentMap = constructEnvironmentMapWithEmployer(standardEmployerPriceCalcDto);
            ruleStreamPreData.setEnvironmentMap(environmentMap);
            preDataMap.put(standardEmployerPriceCalcDto.getPlanConfigId(), ruleStreamPreData);
        }
        return preDataMap;
    }

    /**
     * 构造环境变量（雇主标品）
     */
    private Map<String, Object> constructEnvironmentMapWithEmployer(StandardEmployerPriceCalcDto priceCalcDto) {
        Map<String, Object> environmentMap = new HashMap<>();
        environmentMap.put("plan_config_id", priceCalcDto.getPlanConfigId());
        environmentMap.put("standard_plan_id", priceCalcDto.getStandardPlanId());
        environmentMap.put("job_category", priceCalcDto.getJobCategory());
        environmentMap.put("channel_id", CustomContext.getChannelId());
        environmentMap.put("insured_count", priceCalcDto.getPersonNum());
        environmentMap.put("company_id", priceCalcDto.getCompanyId());
        environmentMap.put("id", priceCalcDto.getPlanConfigId());
        return environmentMap;
    }


    @Override
    public Map<Long, CurrencyAmount> getSingaporePlanPrice(List<SingaporeStandardPriceCalcDto> standardPriceCalcDtoList, BigDecimal personNum) {
        List<Long> companyIds = standardPriceCalcDtoList.stream().map(SingaporeStandardPriceCalcDto::getCompanyId)
                                                     .distinct().collect(Collectors.toList());
        Map<Long, CurrencyAmount> femaleRateMap =new HashMap<>();
        // 根据配置获取对应配置的缓存
        List<Long> feiLvSgpConfigs = getFeiLvSgpConfigs();
        List<Long> configIds = standardPriceCalcDtoList.stream().map(SingaporeStandardPriceCalcDto::getId).collect(Collectors.toList());
        log.info("femalePlanConfigs: {}, configIds: {}",  feiLvSgpConfigs,configIds);
        if (configIds.stream().anyMatch(feiLvSgpConfigs::contains)){
            List<SingaporeStandardPriceCalcDto> singaporeStandardPriceCalcDtos = standardPriceCalcDtoList.stream()
                                                                                  .filter(dto -> feiLvSgpConfigs.contains(dto.getId()))
                                                                                  .collect(Collectors.toList());
            femaleRateMap = getFemaleRate(singaporeStandardPriceCalcDtos, personNum);
        }
        Map<Long, CurrencyAmount> result = new HashMap<>();
        try {
            RuleStreamRequest ruleStreamRequest = new RuleStreamRequest();
            List<Long> planConfigList = standardPriceCalcDtoList.stream()
                    .map(SingaporeStandardPriceCalcDto::getId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, String> planConfigTagsMap = standardPlanMapper.getPlanConfigTags(planConfigList);
            ruleStreamRequest.setDataIds(planConfigList);
            Map<Long, RuleStreamPreData> preDataMap = new HashMap<>();
            for (SingaporeStandardPriceCalcDto priceCalcDto : standardPriceCalcDtoList) {
                RuleStreamPreData preData = new RuleStreamPreData();
                Map<String, Object> environmentMap = new HashMap<>();
                environmentMap.put("age", priceCalcDto.getAge());
                environmentMap.put("id", priceCalcDto.getId());
                environmentMap.put("company_id", priceCalcDto.getCompanyId());
                environmentMap.put("job_category", priceCalcDto.getJobCategory());
                environmentMap.put("config_tags", planConfigTagsMap.getOrDefault(priceCalcDto.getId(), ""));
                environmentMap.put("employee_type", priceCalcDto.getEmployeeType());
                environmentMap.put("person_num", priceCalcDto.getPersonNum());
                if(feiLvSgpConfigs.contains(priceCalcDto.getId())){
                    // 该保司的男性费率
                    StandardAgeLeavelEnum standardAgeLeavelEnum = StandardAgeLeavelEnum.getAge(priceCalcDto.getAge());
                    if (standardAgeLeavelEnum != null) {
                        environmentMap.put("age", standardAgeLeavelEnum.getMin());
                    }
                    environmentMap.put("sex", Sex.MALE.getValue());
                    environmentMap.put("person_num",PersonNumUtils.getMaleNum(personNum.intValue()));
                }
                preData.setEnvironmentMap(environmentMap);
                preDataMap.put(priceCalcDto.getId(), preData);
            }
            ruleStreamRequest.setPreData(preDataMap);

            if (CollectionUtil.isEmpty(ruleStreamRequest.getPreData())){
                log.warn("新加坡没有需要计算的数据" );
                return result;
            }
            log.info("新加坡非精准算费请求入参:-----{}", JacksonUtils.writeAsString(ruleStreamRequest));
            ResponseVO<List<RuleInstanceDto>> execute = ruleExecuteClient.execute(singaporeStandardRuleConfig.getId(), ruleStreamRequest);
//            ResponseVO<List<RuleInstanceDto>> execute = quoteRuleExecuteClient.executeWithLog(singaporeStandardRuleConfig.getId(), ruleStreamRequest);
            List<RuleInstanceDto> ruleInstanceDtos = CommonUtil.getResponseData(execute);
            List<Long> ids = ruleInstanceDtos.stream().map(RuleInstanceDto::getId).collect(Collectors.toList());
            //4.查看规则流结果
            ResponseVO<Map<Long, Map<String, String>>> environmentVariablesValuePostSecond = ruleClient.findEnvironmentVariablesValuePostSecond(ids);
            Map<Long, Long> idToDataId = ruleInstanceDtos.stream().collect(Collectors.toMap(RuleInstanceDto::getObjectDataId, RuleInstanceDto::getId));
            Map<Long, Map<String, String>> responseData = CommonUtil.getResponseData(environmentVariablesValuePostSecond);

            for (SingaporeStandardPriceCalcDto priceCalcDto : standardPriceCalcDtoList) {
                Long id = priceCalcDto.getId();
                Map<String, String> resMap = responseData.get(idToDataId.get(id));
                if (null == resMap) {
                    continue;
                }
                String calcPrice = responseData.get(idToDataId.get(id)).get("price");
                BigDecimal personNums = BigDecimal.valueOf(Long.parseLong(responseData.get(idToDataId.get(id)).get("person_num")));
                if (null == calcPrice || "-".equals(calcPrice) || StringUtils.isEmpty(calcPrice)) {
                    log.error("未配置费率 - {}", JacksonUtils.writeAsString(responseData.get(idToDataId.get(id))));
                } else {
                    CurrencyAmount currencyAmount = CurrencyAmount.valueOf(BigDecimal.valueOf(Double.parseDouble(calcPrice)), IdentityContext.getBusinessCurrency());
                    result.put(id, CurrencyAmountUtil.multi(currencyAmount, personNums));
                }
            }
            log.info("femaleRateMap: {}", JacksonUtils.writeAsString(femaleRateMap));
             //合并男女价格
            for (SingaporeStandardPriceCalcDto singaporeStandardPriceCalcDto : standardPriceCalcDtoList) {
                if (configIds.contains(singaporeStandardPriceCalcDto.getId())){
                    if (result.containsKey(singaporeStandardPriceCalcDto.getId())){
                        CurrencyAmount  amount = CurrencyAmountUtil.add(result.get(singaporeStandardPriceCalcDto.getId()), femaleRateMap.get(singaporeStandardPriceCalcDto.getId()));
                        result.put(singaporeStandardPriceCalcDto.getId(), amount);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("规则无法计算", e);
        }
        log.info("保司ID:{} 新加坡费率计算结果:{}", JacksonUtils.writeAsString(companyIds),JacksonUtils.writeAsString(result));
        return result;
    }


    /**
     * 计算 女性费率
     * @param standardPriceCalcDtoList
     * @param personNum
     * @return
     */
    private Map<Long, CurrencyAmount> getFemaleRate(List<SingaporeStandardPriceCalcDto> standardPriceCalcDtoList, BigDecimal personNum) {
        Map<Long, CurrencyAmount> result = new HashMap<>();
        try {
            //指定配置按照女性进行计算
            RuleStreamRequest ruleStreamRequest = new RuleStreamRequest();
            List<Long> planConfigList = standardPriceCalcDtoList.stream().map(SingaporeStandardPriceCalcDto::getId)
                                                                .distinct()
                                                                .collect(Collectors.toList());
            Map<Long, String> planConfigTagsMap = standardPlanMapper.getPlanConfigTags(planConfigList);
            ruleStreamRequest.setDataIds(planConfigList);
            Map<Long, RuleStreamPreData> preDataMap = new HashMap<>();
            for (SingaporeStandardPriceCalcDto priceCalcDto : standardPriceCalcDtoList) {
                RuleStreamPreData preData = new RuleStreamPreData();
                Map<String, Object> environmentMap = new HashMap<>();
                Integer age = priceCalcDto.getAge();
                StandardAgeLeavelEnum standardAgeLeavelEnum = StandardAgeLeavelEnum.getMBAge(age);
                if (standardAgeLeavelEnum != null){
                    environmentMap.put("age", standardAgeLeavelEnum.getMin());
                }
                environmentMap.put("id", priceCalcDto.getId());
                environmentMap.put("company_id", priceCalcDto.getCompanyId());
                environmentMap.put("job_category", priceCalcDto.getJobCategory());
                environmentMap.put("config_tags", planConfigTagsMap.getOrDefault(priceCalcDto.getId(), ""));
                environmentMap.put("employee_type", priceCalcDto.getEmployeeType());
                environmentMap.put("person_num", PersonNumUtils.getFemaleNum(personNum.intValue()));
                environmentMap.put("sex", Sex.FEMALE.getValue());
                preData.setEnvironmentMap(environmentMap);
                preDataMap.put(priceCalcDto.getId(), preData);
            }
            ruleStreamRequest.setPreData(preDataMap);

            ResponseVO<List<RuleInstanceDto>> execute = ruleExecuteClient.execute(singaporeStandardRuleConfig.getId(), ruleStreamRequest);
            List<RuleInstanceDto> ruleInstanceDtos = CommonUtil.getResponseData(execute);
            List<Long> ids = ruleInstanceDtos.stream().map(RuleInstanceDto::getId).collect(Collectors.toList());
            //4.查看规则流结果
            ResponseVO<Map<Long, Map<String, String>>> environmentVariablesValuePostSecond = ruleClient.findEnvironmentVariablesValuePostSecond(ids);
            Map<Long, Long> idToDataId = ruleInstanceDtos.stream().collect(Collectors.toMap(RuleInstanceDto::getObjectDataId, RuleInstanceDto::getId));
            Map<Long, Map<String, String>> responseData = CommonUtil.getResponseData(environmentVariablesValuePostSecond);

            for (SingaporeStandardPriceCalcDto priceCalcDto : standardPriceCalcDtoList) {
                Long id = priceCalcDto.getId();
                String calcPrice = responseData.get(idToDataId.get(id)).get("price");
                String personNums = responseData.get(idToDataId.get(id)).get("person_num");
                if (null == calcPrice || "-".equals(calcPrice) || StringUtils.isEmpty(calcPrice)) {
                    log.error("未配置费率 - {}", JacksonUtils.writeAsString(responseData.get(idToDataId.get(id))));
                } else {
                    CurrencyAmount currencyAmount = CurrencyAmount.valueOf(BigDecimal.valueOf(Double.parseDouble(calcPrice)), IdentityContext.getBusinessCurrency());
                    result.put(id, CurrencyAmountUtil.multi(currencyAmount, new BigDecimal(personNums)));
                }
            }
        } catch (Exception e) {
            log.warn("规则无法计算", e);
        }
        return  result;
    }

    @Override
    public List<RecommendPlanPriceRspDto> getPriceList(RecommendPlanReqDto recommendPlanReqDto) {

        List<StandardPlanDto> calcedPlanList = getCalcedPlanList(recommendPlanReqDto);
        Optional<StandardPlanDto> max = calcedPlanList.stream().max(Comparator.comparing(StandardPlanDto::getPrice));
        Optional<StandardPlanDto> min = calcedPlanList.stream().min(Comparator.comparing(StandardPlanDto::getPrice));
        // 所有价格柱
        List<IgStandardRecommendRatio> byProvinceAndPersonNum = standardRecommendRatioDataMapper.getByProvinceAndPersonNum(recommendPlanReqDto.getCity(), recommendPlanReqDto.getPersonNum());
        // 最高比例
        Optional<IgStandardRecommendRatio> maxRatio = byProvinceAndPersonNum.stream().max(Comparator.comparing(IgStandardRecommendRatio::getRatio));

        List<RecommendPlanPriceRspDto> list = new ArrayList<>();
        for (IgStandardRecommendRatio igStandardRecommendRatio : byProvinceAndPersonNum) {
            RecommendPlanPriceRspDto recommendPlanPriceRspDto = new RecommendPlanPriceRspDto();
            recommendPlanPriceRspDto.setMaxFlag(maxRatio.isPresent() && igStandardRecommendRatio.getRatio().compareTo(maxRatio.get().getRatio()) == 0);
            recommendPlanPriceRspDto.setActive(max.isPresent() && min.isPresent()
                    && igStandardRecommendRatio.getPrice().compareTo(max.get().getPrice()) <= 0
                    && igStandardRecommendRatio.getPrice().compareTo(min.get().getPrice()) >= 0);
            recommendPlanPriceRspDto.setPrice(igStandardRecommendRatio.getPrice());
            recommendPlanPriceRspDto.setRatio(igStandardRecommendRatio.getRatio());
            list.add(recommendPlanPriceRspDto);
        }
        return list;
    }

    @Override
    public StandardQrCodeRspDto getQrCode() {
        StandardQrCodeRspDto standardQrCodeRspDto = new StandardQrCodeRspDto();
        IgStandardTenantInfo channelInfo = standardTenantInfoMapper.getChannelInfo(CustomContext.getChannelId());
        String qrCode = channelInfo.getAttorneyPath();

        ResponseVO<List<UploadResultDto>> listResponseVO = fileClient.listFile(Collections.singletonList(qrCode));
        UploadResultDto uploadResultDto = listResponseVO.getData().get(0);

        standardQrCodeRspDto.setUrl(uploadResultDto.getLargeUrl());

        return standardQrCodeRspDto;
    }

    @Override
    public Long getDefaultTenantId() {
        return standardChannelConfig.getDefaultId();
    }

    @Override
    public StandardTenantInfoRspDto getTenantInfo() {
        StandardTenantInfoRspDto standardTenantInfoRspDto = new StandardTenantInfoRspDto();
        IgStandardTenantInfo channelInfo = standardTenantInfoMapper.getChannelInfo(CustomContext.getChannelId());
        if (channelInfo == null) {
            throw new QuoteException(MessageUtil.get("b_b_quote_sgp_016"));
        }

        if (Boolean.FALSE.equals(standardChannelConfig.getGotoGideButtonFlag())) {
            standardTenantInfoRspDto.setShowGotoGide(false);
        } else {
            standardTenantInfoRspDto.setShowGotoGide(standardChannelConfig.getChannelIdList().contains(channelInfo.getChannelTenantId()));
        }

        // logo
        String logo = channelInfo.getLogo();
        // 主颜色
        String colorPrimary = channelInfo.getColorPrimary();
        // 次颜色
        String colorSecondary = channelInfo.getColorSecondary();
        // 文字颜色
        String colorPrimaryBgText = channelInfo.getColorPrimaryBgText();
        ResponseVO<List<UploadResultDto>> listResponseVO = fileClient.listFile(Collections.singletonList(logo));
        UploadResultDto uploadResultDto = listResponseVO.getData().get(0);
        standardTenantInfoRspDto.setLogo(uploadResultDto.getLargeUrl());
        standardTenantInfoRspDto.setColorPrimary(StrUtil.isNotBlank(colorPrimary) ? colorPrimary : "");
        standardTenantInfoRspDto.setColorSecondary(StrUtil.isNotBlank(colorSecondary) ? colorSecondary : "");
        standardTenantInfoRspDto.setColorPrimaryBgText(StrUtil.isNotBlank(colorPrimaryBgText) ? colorPrimaryBgText : "");
        // 标品付款流程
        standardTenantInfoRspDto.setStandardPaymentProcess(channelInfo.getStandardPaymentProcess());
        // 支持的标品类型
        List<String> supportStandardTypeList = channelInfo.getSupportStandardTypeList();
        if (CollectionUtil.isNotEmpty(supportStandardTypeList)) {
            List<OptionItemDto<String>> optionItemList = new ArrayList<>();
            for (String standardType : supportStandardTypeList) {
                OptionItemDto<String> optionItemDto = new OptionItemDto<>();
                optionItemDto.setValue(standardType);
                optionItemDto.setDesc(StandardTypeEnum.getDesc(standardType));
                optionItemList.add(optionItemDto);
            }
            standardTenantInfoRspDto.setSupportStandardTypeList(optionItemList);
        }
        return standardTenantInfoRspDto;
    }

    @Override
    public List<SingaporeRecommendPlanPriceRspDto> getSingaporePriceList(SingaporeRecommendPriceListReqDto singaporeRecommendPriceListReqDto) {
        int number = singaporeRecommendPriceListReqDto.getEmployeeNum() + singaporeRecommendPriceListReqDto.getChildNum() + singaporeRecommendPriceListReqDto.getSpouseNum();
        String employeeType = singaporeRecommendPriceListReqDto.getEmployeeType();
        // 获取nacos里的配置
        List<Long> priceIntervalList = singaporeStandardPlanPriceIntervalConfig.getPriceIntervalList();
        if (CollectionUtil.isEmpty(priceIntervalList)) {
            throw new QuoteException(MessageUtil.get("b_b_quote_sgp_005"));
        }
        Collections.sort(priceIntervalList);
        // 添加区间到Map中
        Map<Long, Long> intervalMap = new LinkedHashMap<>();
        for (int i = 0; i < priceIntervalList.size() - 1; i++) {
            intervalMap.put(priceIntervalList.get(i), priceIntervalList.get(i + 1));
        }
        // 处理最大区间
        intervalMap.put(priceIntervalList.get(priceIntervalList.size() - 1), Long.MAX_VALUE);
        // 获取区间对应的比例
        int ageMin = singaporeRecommendPriceListReqDto.getAgeMin();
        int ageMax = singaporeRecommendPriceListReqDto.getAgeMax();
        List<IgStandardRecommendRatio> ratioByPriceIntervalList = standardRecommendRatioDataMapper.getRatioByPriceInterval(ageMin, ageMax, employeeType, intervalMap);
        if (CollectionUtil.isEmpty(ratioByPriceIntervalList)) {
            // 未查询到价格区间对应的比例数据
            throw new QuoteException(MessageUtil.get("b_b_quote_sgp_006"));
        }
        // 找出最大的比例的数据
        BigDecimal totalRatio = ratioByPriceIntervalList.stream()
                .map(IgStandardRecommendRatio::getRatio)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        if (totalRatio.compareTo(BigDecimal.valueOf(100L)) > 0) {
            // 比例之和大于100，则比例有误
            throw new QuoteException(MessageUtil.get("b_b_quote_sgp_007"));
        }
        // 最后一列的比例
        Optional<IgStandardRecommendRatio> maxRatioOptional = ratioByPriceIntervalList.stream()
                .max(Comparator.comparing(IgStandardRecommendRatio::getRatio));
        IgStandardRecommendRatio maxRatioRecord = maxRatioOptional.get();
        Map<String, IgStandardRecommendRatio> ratioMap = ratioByPriceIntervalList.stream()
                .collect(Collectors.toMap(k -> k.getPriceMin().getAmount().longValue() + "-" + k.getPriceMax().getAmount().longValue(), Function.identity()));
        // 填充结果
        return constructResult(intervalMap, number, ratioMap, maxRatioRecord.getRatio());
    }

    private static List<SingaporeRecommendPlanPriceRspDto> constructResult(Map<Long, Long> intervalMap, int number, Map<String, IgStandardRecommendRatio> ratioMap, BigDecimal maxRatio) {
        List<SingaporeRecommendPlanPriceRspDto> singaporeRecommendPlanPriceRspDtoList = new ArrayList<>();
        intervalMap.forEach((key, value) -> {
            SingaporeRecommendPlanPriceRspDto singaporeRecommendPlanPriceRspDto = new SingaporeRecommendPlanPriceRspDto();
            singaporeRecommendPlanPriceRspDto.setPriceMin(CurrencyAmount.valueOf(key * number, IdentityContext.getBusinessCurrency()));
            if (value != Long.MAX_VALUE) {
                singaporeRecommendPlanPriceRspDto.setPriceMax(CurrencyAmount.valueOf(value * number, IdentityContext.getBusinessCurrency()));
            } else {
                value = 99999L;
            }
            IgStandardRecommendRatio igStandardRecommendRatio = ratioMap.get(key + "-" + value);
            if (igStandardRecommendRatio != null) {
                // 判断是否为最大值
                singaporeRecommendPlanPriceRspDto.setHighTradingFlag(maxRatio.compareTo(igStandardRecommendRatio.getRatio()) == 0);
                // 计算高度比例
                singaporeRecommendPlanPriceRspDto.setRatio(igStandardRecommendRatio.getRatio().divide(maxRatio, 2, RoundingMode.HALF_UP));
            }
            singaporeRecommendPlanPriceRspDtoList.add(singaporeRecommendPlanPriceRspDto);
        });
        return singaporeRecommendPlanPriceRspDtoList;
    }

    @Override
    public List<SingaporeRecommendPlanRspDto> getSingaporeRecommendPlan(List<SingaporeRecommendPlanReqDto> singaporeRecommendPlanReqDtoList) {
        List<SingaporeRecommendPlanRspDto> singaporeRecommendPlanRspDtos = new ArrayList<>();
        log.warn("新加坡标品推荐参数：{}", JacksonUtils.writeAsString(singaporeRecommendPlanReqDtoList));

        // 换取信息补全的对象
//        List<SingaporeStandardPlanDto> standardPlanDtoList = getStandardPlanInfo(Arrays.asList(2748549288690883369L));
        List<SingaporeStandardPlanDto> standardPlanDtoList = getStandardPlanInfo(new ArrayList<>());

        Map<String, Long> recommendTypeCompanyMap = new HashMap<>();
        for (SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto : singaporeRecommendPlanReqDtoList) {
            // 兼容最大价格为空的场景
            if (null == singaporeRecommendPlanReqDto.getPriceMax()) {
                singaporeRecommendPlanReqDto.setPriceMax(CurrencyAmount.valueOf(200000L, IdentityContext.getBusinessCurrency()));
            }

            // 过滤当前人员类型的标品
            List<SingaporeStandardPlanDto> singaporeStandardPlanDtoList = standardPlanDtoList.stream()
                    .filter(x -> null != x.getEmployeeCategory() && x.getEmployeeCategory().equals(singaporeRecommendPlanReqDto.getEmployeeType()))
                    .collect(Collectors.toList());

            // 获取当前人员类型的推荐方案
            List<SingaporeRecommendPlanItemRspDto> planList = this.getAllRecommendPlanList(singaporeRecommendPlanReqDto, singaporeStandardPlanDtoList, recommendTypeCompanyMap);

            // 获取当前人员类型的保司ID
            planList.forEach(x -> {
                if (!recommendTypeCompanyMap.containsKey(x.getPlanType())) {
                    recommendTypeCompanyMap.put(x.getPlanType(), x.getCompanyId());
                }
            });

            // 返回对象
            SingaporeRecommendPlanRspDto singaporeRecommendPlanRspDto = new SingaporeRecommendPlanRspDto();
            singaporeRecommendPlanRspDto.setEmployeeType(singaporeRecommendPlanReqDto.getEmployeeType());
            singaporeRecommendPlanRspDto.setEmployeeTitle(StandardEmployeeTypeEnum.getByValue(singaporeRecommendPlanReqDto.getEmployeeType()));
            singaporeRecommendPlanRspDto.setPlanList(planList);

            singaporeRecommendPlanRspDtos.add(singaporeRecommendPlanRspDto);
        }

        return singaporeRecommendPlanRspDtos;
    }

    /**
     * @param singaporeRecommendPlanReqDto
     * @param standardPlanDtoList
     * @param recommendTypeCompanyMap
     * @return
     */
    private List<SingaporeRecommendPlanItemRspDto> getAllRecommendPlanList(SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto
            , List<SingaporeStandardPlanDto> standardPlanDtoList
            , Map<String, Long> recommendTypeCompanyMap
    ) {
        String employeeType = singaporeRecommendPlanReqDto.getEmployeeType();
        List<RecommendStandardConfig> recommendStandardConfigList = standardPlanDtoList.stream()
                .map(RecommendStandardPlan::getRecommendStandardConfigList)
                .flatMap(Collection::stream)
                .filter(v -> v.getRelation().equals(IgPlanConfigEnums.Relation.ONESELF.getCode())
                        && null != v.getConfigTag()
                        && (!employeeType.equals(StandardEmployeeTypeEnum.FOREIGN_WORKERS.getValue()) || v.getConfigTag().contains(StandardEmployeeTypeEnum.FOREIGN_WORKERS.getTag())))
                .collect(Collectors.toList());

        Map<Long, Long> planConfigCompanyMap = new HashMap<>();
        standardPlanDtoList.forEach(standardPlanDto -> {
            List<Long> planConfigIdList = standardPlanDto.getRecommendStandardConfigList().stream().map(IgPlanConfig::getId)
                                                .collect(Collectors.toList());
            planConfigIdList.forEach(planConfigId -> planConfigCompanyMap.put(planConfigId,standardPlanDto.getCompanyId()));
        });
        resetRecommendPlanItem(singaporeRecommendPlanReqDto);

        List<Long> companyIdList = new ArrayList<>();
        List<SingaporeRecommendPlanItemRspDto> singaporeRecommendPlanItemRspDtoList = new ArrayList<>();
        SingaporeRecommendPlanItemRspDto singaporeRecommendPlanItemRspDto1 = this.getFriendRecommendPlanItem(planConfigCompanyMap,companyIdList, singaporeRecommendPlanReqDto, recommendStandardConfigList, standardPlanDtoList, recommendTypeCompanyMap, 1);
        if (null != singaporeRecommendPlanItemRspDto1) {
            companyIdList.add(singaporeRecommendPlanItemRspDto1.getCompanyId());
        }
        SingaporeRecommendPlanItemRspDto singaporeRecommendPlanItemRspDto2 = this.getMostRecommendPlanItem(planConfigCompanyMap,companyIdList, singaporeRecommendPlanReqDto, recommendStandardConfigList, standardPlanDtoList, recommendTypeCompanyMap, 1);
        if (null != singaporeRecommendPlanItemRspDto2) {
            companyIdList.add(singaporeRecommendPlanItemRspDto2.getCompanyId());
        }
        SingaporeRecommendPlanItemRspDto singaporeRecommendPlanItemRspDto3 = this.getBenchmarkRecommendPlanItem(planConfigCompanyMap,companyIdList, singaporeRecommendPlanReqDto, recommendStandardConfigList, standardPlanDtoList, recommendTypeCompanyMap, 1);

        if (null != singaporeRecommendPlanItemRspDto3) {
            singaporeRecommendPlanItemRspDtoList.add(singaporeRecommendPlanItemRspDto3);
        }
        if (null != singaporeRecommendPlanItemRspDto1) {
            singaporeRecommendPlanItemRspDtoList.add(singaporeRecommendPlanItemRspDto1);
        }
        if (null != singaporeRecommendPlanItemRspDto2) {
            singaporeRecommendPlanItemRspDtoList.add(singaporeRecommendPlanItemRspDto2);
        }

        return singaporeRecommendPlanItemRspDtoList;
    }

    private SingaporeRecommendPlanItemRspDto getBenchmarkRecommendPlanItem(Map<Long, Long> planConfigCompanyMap,List<Long> companyIdList
            , SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto
            , List<RecommendStandardConfig> recommendStandardConfigList
            , List<SingaporeStandardPlanDto> standardPlanDtoList
            , Map<String, Long> recommendTypeCompanyMap, Integer mode) {
        // 基础价格
        CurrencyAmount priceBenchMark = getPriceBenchMark(singaporeRecommendPlanReqDto);

        SingaporeStandardPlanTypeEnum benchmark = SingaporeStandardPlanTypeEnum.Benchmark;

        // 方案列表
        Map<Long, SingaporeStandardPlanDto> singaporeStandardPlanDtoMap = standardPlanDtoList.stream()
                .collect(Collectors.toMap(SingaporeStandardPlanDto::getPlanId, Function.identity()));

        // 方案列表
        List<RecommendStandardConfig> recommendStandardConfigs = recommendStandardConfigList.stream().filter(
                x -> null != SingaporeStandardPlanTypeEnum.getByCode(benchmark.getCode())
                        && !companyIdList.contains(singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId())
        ).collect(Collectors.toList());

        // 同一家保司的方案
        if (null != singaporeRecommendPlanReqDto.getCompanyId2() && mode == 2) {
            standardPlanDtoList = standardPlanDtoList.stream()
                    .filter(standardPlanDto -> standardPlanDto.getCompanyId().equals(singaporeRecommendPlanReqDto.getCompanyId2()))
                    .collect(Collectors.toList());

            recommendStandardConfigs = recommendStandardConfigs.stream().filter(
                            x -> singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId().equals(singaporeRecommendPlanReqDto.getCompanyId2()))
                    .collect(Collectors.toList());
        }

        // 同一家保司的方案
        if (null != singaporeRecommendPlanReqDto.getCompanyId3() && mode == 3) {
            standardPlanDtoList = standardPlanDtoList.stream()
                    .filter(standardPlanDto -> standardPlanDto.getCompanyId().equals(singaporeRecommendPlanReqDto.getCompanyId3()))
                    .collect(Collectors.toList());

            recommendStandardConfigs = recommendStandardConfigs.stream().filter(
                            x -> singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId().equals(singaporeRecommendPlanReqDto.getCompanyId3()))
                    .collect(Collectors.toList());
        }


        List<GroupPlanFinalPriceDto> groupPlanFinalPriceDtos = this.getCalcConfigList(planConfigCompanyMap,singaporeRecommendPlanReqDto, standardPlanDtoList, recommendStandardConfigs, recommendTypeCompanyMap, benchmark);

        // 获取价格最接近的
        GroupPlanFinalPriceDto groupPlanFinalPriceDto = this.getPriceMode(priceBenchMark, groupPlanFinalPriceDtos, mode);

        return this.getRspDto(groupPlanFinalPriceDto, standardPlanDtoList, benchmark, singaporeRecommendPlanReqDto, recommendStandardConfigList);
    }

    private SingaporeRecommendPlanItemRspDto getMostRecommendPlanItem(Map<Long, Long> planConfigCompanyMap,
                                                                      List<Long> companyIdList
            , SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto
            , List<RecommendStandardConfig> recommendStandardConfigList
            , List<SingaporeStandardPlanDto> standardPlanDtoList
            , Map<String, Long> recommendTypeCompanyMap, Integer mode) {
        // 基础价格
        CurrencyAmount priceMost = getPriceMost(singaporeRecommendPlanReqDto);

        // 方案列表
        Map<Long, SingaporeStandardPlanDto> singaporeStandardPlanDtoMap = standardPlanDtoList.stream()
                .collect(Collectors.toMap(SingaporeStandardPlanDto::getPlanId, Function.identity()));

        SingaporeStandardPlanTypeEnum most = SingaporeStandardPlanTypeEnum.MOST;

        // 方案列表
        List<RecommendStandardConfig> recommendStandardConfigs = recommendStandardConfigList.stream().filter(
                x -> null != SingaporeStandardPlanTypeEnum.getByCode(most.getCode())
                        && !companyIdList.contains(singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId())
        ).collect(Collectors.toList());

        // 同一家保司的方案
        if (null != singaporeRecommendPlanReqDto.getCompanyId2() && mode == 2) {
            standardPlanDtoList = standardPlanDtoList.stream()
                    .filter(standardPlanDto -> standardPlanDto.getCompanyId().equals(singaporeRecommendPlanReqDto.getCompanyId2()))
                    .collect(Collectors.toList());

            recommendStandardConfigs = recommendStandardConfigs.stream().filter(
                            x -> singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId().equals(singaporeRecommendPlanReqDto.getCompanyId2()))
                    .collect(Collectors.toList());
        }

        // 同一家保司的方案
        if (null != singaporeRecommendPlanReqDto.getCompanyId3() && mode == 3) {
            standardPlanDtoList = standardPlanDtoList.stream()
                    .filter(standardPlanDto -> standardPlanDto.getCompanyId().equals(singaporeRecommendPlanReqDto.getCompanyId3()))
                    .collect(Collectors.toList());

            recommendStandardConfigs = recommendStandardConfigs.stream().filter(
                            x -> singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId().equals(singaporeRecommendPlanReqDto.getCompanyId3()))
                    .collect(Collectors.toList());
        }

        boolean isFivePersonRule = isOnePersonSpecialRule(singaporeRecommendPlanReqDto, singaporeStandardRecommendConfig.getLessThanNotRecommendPersonNum());
        if (isFivePersonRule) {
            recommendStandardConfigs = recommendStandardConfigs.stream()
                    .filter(x -> !singaporeStandardRecommendConfig.getLessThanNotRecommendCompanyIdList().contains(singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId()))
                    .collect(Collectors.toList());
        }

        // 特殊规则：当人数为1时，budget和comprehensive只推荐Raffles和HSBC两家保司
        boolean isOnePersonRule = isOnePersonSpecialRule(singaporeRecommendPlanReqDto, singaporeStandardRecommendConfig.getLessThanRecommendPersonNum());
        if (isOnePersonRule) {
            recommendStandardConfigs = recommendStandardConfigs.stream().filter(
                    x -> singaporeStandardRecommendConfig.getLessThanRecommendCompanyIdList().contains(singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId())
            ).collect(Collectors.toList());
        }

        List<GroupPlanFinalPriceDto> groupPlanFinalPriceDtos = this.getCalcConfigList(planConfigCompanyMap,singaporeRecommendPlanReqDto, standardPlanDtoList, recommendStandardConfigs, recommendTypeCompanyMap, most);

        GroupPlanFinalPriceDto groupPlanFinalPriceDto = this.getPriceMode(priceMost, groupPlanFinalPriceDtos, mode);

        return this.getRspDto(groupPlanFinalPriceDto, standardPlanDtoList, most, singaporeRecommendPlanReqDto, recommendStandardConfigList);
    }

    private GroupPlanFinalPriceDto getPriceMode(CurrencyAmount priceBase, List<GroupPlanFinalPriceDto> groupPlanFinalPriceDtos, Integer mode) {

        // 获取价格最接近的
        GroupPlanFinalPriceDto groupPlanFinalPriceDto = groupPlanFinalPriceDtos.stream()
                .min(Comparator.comparingDouble(x -> Math.abs(priceBase.getAmount()
                        .subtract(x.getTotalAmount().getAmount()).doubleValue())))
                .orElse(new GroupPlanFinalPriceDto());

        // 如果没找到
        if (null == groupPlanFinalPriceDto.getTotalAmount()) {
            return groupPlanFinalPriceDto;
        }

        // 返回低于的价格
        if (mode == 2) {
            return groupPlanFinalPriceDtos.stream()
                    .filter(x -> x.getTotalAmount().compareTo(groupPlanFinalPriceDto.getTotalAmount()) < 0
                    )
                    .min(Comparator.comparingDouble(x -> Math.abs(groupPlanFinalPriceDto.getTotalAmount().getAmount()
                            .subtract(x.getTotalAmount().getAmount()).doubleValue())))
                    .orElse(new GroupPlanFinalPriceDto());
        }

        // 返回高于的价格
        if (mode == 3) {
            return groupPlanFinalPriceDtos.stream()
                    .filter(x -> x.getTotalAmount().compareTo(groupPlanFinalPriceDto.getTotalAmount()) > 0
                    )
                    .min(Comparator.comparingDouble(x -> Math.abs(priceBase.getAmount()
                            .subtract(x.getTotalAmount().getAmount()).doubleValue())))
                    .orElse(new GroupPlanFinalPriceDto());
        }

        return groupPlanFinalPriceDto;
    }


    /**
     * 获取经济友好的方案
     *
     * @param singaporeRecommendPlanReqDto
     * @param recommendStandardConfigList
     * @param standardPlanDtoList
     * @param recommendTypeCompanyMap
     * @return
     */
    private SingaporeRecommendPlanItemRspDto getFriendRecommendPlanItem(Map<Long, Long> planConfigCompanyMap,List<Long> companyIdList,
                                                                        SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto,
                                                                        List<RecommendStandardConfig> recommendStandardConfigList,
                                                                        List<SingaporeStandardPlanDto> standardPlanDtoList,
                                                                        Map<String, Long> recommendTypeCompanyMap, Integer mode) {
        // 基础价格
        CurrencyAmount priceFriendly = getPriceFriendly(singaporeRecommendPlanReqDto);

        SingaporeStandardPlanTypeEnum budget = SingaporeStandardPlanTypeEnum.BUDGET;

        // 方案列表
        Map<Long, SingaporeStandardPlanDto> singaporeStandardPlanDtoMap = standardPlanDtoList.stream()
                .collect(Collectors.toMap(SingaporeStandardPlanDto::getPlanId, Function.identity()));

        List<RecommendStandardConfig> recommendStandardConfigs = recommendStandardConfigList.stream().filter(
                x -> null != SingaporeStandardPlanTypeEnum.getByCode(budget.getCode())
                        && !companyIdList.contains(singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId())
        ).collect(Collectors.toList());


        // 同一家保司的方案
        if (null != singaporeRecommendPlanReqDto.getCompanyId2() && mode == 2) {
            standardPlanDtoList = standardPlanDtoList.stream()
                    .filter(standardPlanDto -> standardPlanDto.getCompanyId().equals(singaporeRecommendPlanReqDto.getCompanyId2()))
                    .collect(Collectors.toList());

            recommendStandardConfigs = recommendStandardConfigs.stream().filter(
                            x -> singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId().equals(singaporeRecommendPlanReqDto.getCompanyId2()))
                    .collect(Collectors.toList());
        }

        // 同一家保司的方案
        if (null != singaporeRecommendPlanReqDto.getCompanyId3() && mode == 3) {
            standardPlanDtoList = standardPlanDtoList.stream()
                    .filter(standardPlanDto -> standardPlanDto.getCompanyId().equals(singaporeRecommendPlanReqDto.getCompanyId3()))
                    .collect(Collectors.toList());

            recommendStandardConfigs = recommendStandardConfigs.stream().filter(
                            x -> singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId().equals(singaporeRecommendPlanReqDto.getCompanyId3()))
                    .collect(Collectors.toList());
        }

        boolean isFivePersonRule = isOnePersonSpecialRule(singaporeRecommendPlanReqDto, singaporeStandardRecommendConfig.getLessThanNotRecommendPersonNum());
        if (isFivePersonRule) {
            recommendStandardConfigs = recommendStandardConfigs.stream()
                    .filter(x -> !singaporeStandardRecommendConfig.getLessThanNotRecommendCompanyIdList().contains(singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId()))
                    .collect(Collectors.toList());
        }

        // 特殊规则：当人数为1时，budget和comprehensive只推荐Raffles和HSBC两家保司
        boolean isOnePersonRule = isOnePersonSpecialRule(singaporeRecommendPlanReqDto, singaporeStandardRecommendConfig.getLessThanRecommendPersonNum());
        if (isOnePersonRule) {
            recommendStandardConfigs = recommendStandardConfigs.stream().filter(
                    x -> singaporeStandardRecommendConfig.getLessThanRecommendCompanyIdList().contains(singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId())
            ).collect(Collectors.toList());
        }

        List<GroupPlanFinalPriceDto> groupPlanFinalPriceDtos = this.getCalcConfigList(planConfigCompanyMap,singaporeRecommendPlanReqDto, standardPlanDtoList, recommendStandardConfigs, recommendTypeCompanyMap, budget);

        // 获取价格最接近的
        GroupPlanFinalPriceDto groupPlanFinalPriceDto = this.getPriceMode(priceFriendly, groupPlanFinalPriceDtos, mode);

        return this.getRspDto(groupPlanFinalPriceDto, standardPlanDtoList, budget, singaporeRecommendPlanReqDto, recommendStandardConfigList);
    }

    private List<GroupPlanFinalPriceDto> getCalcConfigList(Map<Long, Long> planConfigCompanyMap,SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto,
                                                           List<SingaporeStandardPlanDto> standardPlanDtoList,
                                                           List<RecommendStandardConfig> recommendStandardConfigList1,
                                                           Map<String, Long> recommendTypeCompanyMap,
                                                           SingaporeStandardPlanTypeEnum typeEnum) {

        Long companyId = recommendTypeCompanyMap.getOrDefault(typeEnum.getValue(), null);

        List<String> allCoverageCode = new ArrayList<>();
        singaporeRecommendPlanReqDto.getCoverages().forEach(x -> {
            allCoverageCode.add(x.getTitle());
            x.getOptionalRiderList().forEach(y -> {
                allCoverageCode.add(y.getTitle());
            });
        });
        // 方案和保司映射map
        Map<Long, Long> planIdToCompanyIdMap = standardPlanDtoList.stream()
                .collect(Collectors.toMap(
                        SingaporeStandardPlanDto::getPlanId,
                        SingaporeStandardPlanDto::getCompanyId,
                        (existing, replacement) -> existing
                ));

        List<RecommendStandardConfig> recommendStandardConfigList = recommendStandardConfigList1.stream()
                .filter(x -> null != x.getConfigTag() && x.getConfigTag().contains(typeEnum.getCode()))
                .filter(x -> null == companyId || planIdToCompanyIdMap.get(x.getPlanId()).equals(companyId))
                .collect(Collectors.toList());

        // 获取价格费率
        List<RecommendStandardConfigDto> configList = this.getBasePriceConfigList(planConfigCompanyMap,recommendStandardConfigList, singaporeRecommendPlanReqDto);
        Map<Long, List<RecommendStandardConfigDto>> configGroupMap = configList.stream()
                .filter(x -> allCoverageCode.contains(Objects.requireNonNull(StandardCoverageEnum.getTitleByTags(x.getConfigTag())).getCode()))
                .collect(Collectors.groupingBy(RecommendStandardConfigDto::getPlanId));

        // 计算组合数据
        List<GroupPlanPriceDto> maps = calculateCombinations(configGroupMap, planIdToCompanyIdMap, configList);
        // 根据companyId进行分组
        Map<Long, List<GroupPlanPriceDto>> groupedMap = maps.stream()
                .filter(map -> map.getCompanyId() != null)
                .collect(Collectors.groupingBy(GroupPlanPriceDto::getCompanyId));

        if (singaporeStandardRecommendConfig.getChannelCompanyFirst().containsKey(CustomContext.getChannelId())
                && singaporeStandardRecommendConfig.getChannelCompanyFirst().get(CustomContext.getChannelId()).containsKey(singaporeRecommendPlanReqDto.getEmployeeType())) {
            recommendTypeCompanyMap.put(typeEnum.getValue(), singaporeStandardRecommendConfig.getChannelCompanyFirst().get(CustomContext.getChannelId()).get(singaporeRecommendPlanReqDto.getEmployeeType()));
        }

        // 获取所有主险
        List<String> allCoverage = new ArrayList<>();
        singaporeRecommendPlanReqDto.getCoverages().forEach(x -> {
            allCoverage.add(x.getTitle());
        });
        // 根据所有主险选择价格组合
        List<GroupPlanFinalPriceDto> groupPlanFinalPriceDtos = this.getFinalPriceList(allCoverage, groupedMap);

        if (groupPlanFinalPriceDtos.isEmpty()) {
            return new ArrayList<>();
        }
        return groupPlanFinalPriceDtos;
    }

    private SingaporeRecommendPlanItemRspDto getRspDto(GroupPlanFinalPriceDto groupPlanFinalPriceDto,
                                                       List<SingaporeStandardPlanDto> standardPlanDtoList,
                                                       SingaporeStandardPlanTypeEnum singaporeStandardPlanTypeEnum,
                                                       SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto,
                                                       List<RecommendStandardConfig> recommendStandardConfigList
    ) {

        SingaporeRecommendPlanItemRspDto rspDto = new SingaporeRecommendPlanItemRspDto();

        List<GroupPlanPriceDto> list = groupPlanFinalPriceDto.getList();

        if (null == list || list.isEmpty()) {
            return null;
        }

        GroupPlanPriceDto groupPlanPriceDto = list.stream().filter(x -> x.getMainInsuranceCode().equals(StandardCoverageEnum.GHS.getCode()))
                .findFirst().orElse(null);
        if (null == groupPlanPriceDto) {
            groupPlanPriceDto = list.stream().filter(x -> x.getMainInsuranceCode().equals(StandardCoverageEnum.GTL.getCode()))
                    .findFirst().orElse(null);
        }
        if (null == groupPlanPriceDto) {
            groupPlanPriceDto = list.stream().filter(x -> x.getMainInsuranceCode().equals(StandardCoverageEnum.GPA.getCode()))
                    .findFirst().orElse(null);
        }

        if (null == groupPlanPriceDto) {
            return null;
        }

        Long planId = groupPlanPriceDto.getPlanId();

        SingaporeStandardPlanDto singaporeStandardPlanDto = standardPlanDtoList.stream()
                .filter(x -> x.getPlanId().equals(planId)).findFirst().orElse(null);

        if (null == singaporeStandardPlanDto) {
            return null;
        }

        rspDto.setStandardPlanId(singaporeStandardPlanDto.getPlanId());
        rspDto.setPlanType(singaporeStandardPlanTypeEnum.getValue());
        rspDto.setPlanTitle(singaporeStandardPlanTypeEnum.getTitle());
        rspDto.setCompanyName(singaporeStandardPlanDto.getCompanyName());
        rspDto.setCompanyLogo(singaporeStandardPlanDto.getCompanyLogo());
        rspDto.setCompanyTitle(singaporeStandardPlanDto.getPlanTitle());
        rspDto.setImageUrl(singaporeStandardPlanDto.getImageUrl());
        rspDto.setCompanyId(singaporeStandardPlanDto.getCompanyId());
        rspDto.setCompanyDesc(singaporeStandardPlanDto.getItems().stream().map(ProductDescItemInfo::getContent).collect(Collectors.toList()));
        rspDto.setTotalAmount(groupPlanFinalPriceDto.getTotalAmount());
        rspDto.setPersonPrice(groupPlanFinalPriceDto.getTotalPrice());
        rspDto.setEligibility(singaporeStandardPlanDto.getEligibility());
        rspDto.setPolicyWording(singaporeStandardPlanDto.getPolicyWording());
        rspDto.setCoverages(this.getCoveragesByGroup(groupPlanFinalPriceDto, standardPlanDtoList, recommendStandardConfigList));

        return rspDto;
    }

    private List<GroupPlanFinalPriceDto> getFinalPriceList(List<String> allCoverage, Map<Long, List<GroupPlanPriceDto>> groupedMap) {
        Map<String, Integer> purchaseDemand = new HashMap<>();
        allCoverage.forEach(x -> purchaseDemand.put(x, 1));
        return standardPriceCalcService.findAllCombinations(groupedMap, purchaseDemand);
    }

    private List<SingaporeStandardPlanMainCoverageDto> getCoveragesByGroup(GroupPlanFinalPriceDto groupPlanFinalPriceDto,
                                                                           List<SingaporeStandardPlanDto> standardPlanDtoList,
                                                                           List<RecommendStandardConfig> recommendStandardConfigList) {

        log.info("获取 getCoveragesByGroup 请求参数  groupPlanFinalPriceDto:{}   standardPlanDtoList:{} recommendStandardConfigList:{}",JacksonUtils.writeAsString(groupPlanFinalPriceDto),JacksonUtils.writeAsString(standardPlanDtoList),JacksonUtils.writeAsString(recommendStandardConfigList));
        List<SingaporeStandardPlanMainCoverageDto> mainCoverageDtoList = new ArrayList<>();

        Map<Long, SingaporeStandardPlanDto> standardPlanMap = standardPlanDtoList.stream()
                .collect(Collectors.toMap(SingaporeStandardPlanDto::getPlanId, Function.identity()));

        List<Long> planConfigIdList = recommendStandardConfigList.stream().map(IgPlanConfig::getId).collect(Collectors.toList());
        Map<Long, List<ProductClientUIDTO>> planConfigDutyDetailBatchData = getProductUIByConfigs(planConfigIdList);

        groupPlanFinalPriceDto.getList().forEach(map -> {

            // 附加险列表用
            Map<String, SingaporeStandardPlanOptionalCoverageGroupDto> extendCoverMap = new HashMap<>();

            List<RecommendStandardConfig> recommendStandardConfigListPlan = recommendStandardConfigList.stream()
                    .filter(x -> x.getPlanId().equals(map.getPlanId()))
                    .collect(Collectors.toList());

            Map<String, Long> coverageMap = new HashMap<>();
            map.getList().forEach(item -> {
                item.forEach((x, configCurr) -> {
                    coverageMap.put(x, configCurr.getId());
                });
            });

            //
            Long mainConfigId = coverageMap.get(map.getMainInsuranceCode());
            log.info("获取 getCoveragesByGroup 获取主险id  mainConfigId:{}",mainConfigId);
            // recommendStandardConfigListPlan 根据id转换为map
            Map<Long, RecommendStandardConfig> recommendStandardConfigListPlanMap = recommendStandardConfigListPlan
                    .stream().collect(Collectors.toMap(RecommendStandardConfig::getId, Function.identity()));
            Map<String, List<Long>> coverageConfigIdMap = new HashMap<>();
            if (null != mainConfigId && recommendStandardConfigListPlanMap.containsKey(mainConfigId)) {
                RecommendStandardConfig recommendStandardConfig = recommendStandardConfigListPlanMap.get(mainConfigId);
                if (CollectionUtil.isNotEmpty(recommendStandardConfig.getOptionalConfigs())) {
                    recommendStandardConfig.getOptionalConfigs().forEach(optionalConfig -> {
                        if (recommendStandardConfigListPlanMap.containsKey(optionalConfig)) {
                            RecommendStandardConfig relateConfig = recommendStandardConfigListPlanMap.get(optionalConfig);
                            String code = StandardCoverageEnum.getTitleByTags(relateConfig.getConfigTag()).getCode();
                            if (!coverageConfigIdMap.containsKey(code)) {
                                coverageConfigIdMap.put(code, new ArrayList<>());
                            }
                            coverageConfigIdMap.get(code).add(optionalConfig);
                        }
                    });
                }
            }

            // 先处理附加险
            recommendStandardConfigListPlan.forEach(configFill -> {
                if ("0".equals(configFill.getIsMainConfig())) {
                    return;
                }
                SingaporeStandardPlanDto plan = standardPlanMap.get(configFill.getPlanId());
                if (null == plan) {
                    log.error("获取返回信息时方案不存在");
                    return;
                }

                StandardCoverageEnum planType = StandardCoverageEnum.getTitleByTags(plan.getPlanTag());
                if (null == planType) {
                    log.error("方案的主类型为空");
                    return;
                }


                SingaporeStandardPlanOptionalCoverageItemDto riderDto = new SingaporeStandardPlanOptionalCoverageItemDto();

                StandardCoverageEnum standardCoverageEnumExtend = StandardCoverageEnum.getTitleByTags(configFill.getConfigTag());
                if (null == standardCoverageEnumExtend) {
                    log.info("附加险的方案类型为空");
                    return;
                }
                // 是否关联配置
                String code = standardCoverageEnumExtend.getCode();
                if (coverageConfigIdMap.containsKey(code) && !coverageConfigIdMap.get(code).contains(configFill.getId())) {
                    return;
                }

                if (!extendCoverMap.containsKey(code)) {
                    SingaporeStandardPlanOptionalCoverageGroupDto groupDto = new SingaporeStandardPlanOptionalCoverageGroupDto();
                    groupDto.setCode(code);
                    groupDto.setTitle(code);
                    groupDto.setOptionalRiderList(new ArrayList<>());
                    extendCoverMap.put(code, groupDto);
                }

                riderDto.setPlanId(configFill.getPlanId());
                riderDto.setPlanConfigId(configFill.getId());

                riderDto.setCode(standardCoverageEnumExtend.getCode());
                riderDto.setType(code);
                riderDto.setTitle(String.format("%s %s", configFill.getName(), configFill.getBenefit()));
                riderDto.setPrice(configFill.getBasePrice());
                riderDto.setCoverageTitle(standardCoverageEnumExtend.getTitleText());
                riderDto.setCoverageCode(code);
                riderDto.setCoverageCoreType(standardCoverageEnumExtend.getInsuranceTypeDetail().getCode().toUpperCase());
                riderDto.setConfigName(configFill.getName());
                riderDto.setBenefit(configFill.getBenefit());
                riderDto.setIsDefault(coverageMap.containsValue(configFill.getId()));
                riderDto.setAnnualPolicyLimit(configFill.getAnnualPolicyLimit());
                riderDto.setDutyList(planConfigDutyDetailBatchData.get(configFill.getId()));

                extendCoverMap.get(code).getOptionalRiderList().add(riderDto);
            });

            map.getList().forEach(item -> {
                item.forEach((x, config) -> {
                    if ("0".equals(config.getIsMainConfig())) {
                        SingaporeStandardPlanMainCoverageDto mainCoverageDto = new SingaporeStandardPlanMainCoverageDto();

                        StandardCoverageEnum standardCoverageEnumMain = StandardCoverageEnum.getTitleByTags(config.getConfigTag());

                        mainCoverageDto.setPlanId(config.getPlanId());
                        mainCoverageDto.setPlanConfigId(config.getId());

                        mainCoverageDto.setCode(standardCoverageEnumMain.getValue());
                        mainCoverageDto.setType(standardCoverageEnumMain.getCode());
                        mainCoverageDto.setTitle(Objects.requireNonNull(standardCoverageEnumMain).getTitleText() + " " + config.getName());
                        mainCoverageDto.setCoverageTitle(standardCoverageEnumMain.getTitleText());
                        mainCoverageDto.setCoverageCode(standardCoverageEnumMain.getCode());
                        mainCoverageDto.setCoverageCoreType(standardCoverageEnumMain.getInsuranceTypeDetail().getCode().toUpperCase());

                        mainCoverageDto.setConfigName(config.getName());
                        mainCoverageDto.setBenefit(config.getBenefit());
                        mainCoverageDto.setTotalAmount(CurrencyAmountUtil.divide(config.getTotalAmount(), BigDecimal.valueOf(1), 2, RoundingMode.HALF_UP));
                        mainCoverageDto.setDutyList(planConfigDutyDetailBatchData.get(config.getId()));
                        List<SingaporeStandardPlanOptionalCoverageGroupDto> extendGroupList = new ArrayList<>();
                        if (StandardCoverageEnum.GHS.getCode().equals(standardCoverageEnumMain.getCode())) {
                            if (extendCoverMap.containsKey(StandardCoverageEnum.MM.getCode())) {
                                extendGroupList.add(extendCoverMap.get(StandardCoverageEnum.MM.getCode()));
                            }
                            if (extendCoverMap.containsKey(StandardCoverageEnum.GP.getCode())) {
                                extendGroupList.add(extendCoverMap.get(StandardCoverageEnum.GP.getCode()));
                            }
                            if (extendCoverMap.containsKey(StandardCoverageEnum.SP.getCode())) {
                                extendGroupList.add(extendCoverMap.get(StandardCoverageEnum.SP.getCode()));
                            }
                            if (extendCoverMap.containsKey(StandardCoverageEnum.GADD.getCode())) {
                                extendGroupList.add(extendCoverMap.get(StandardCoverageEnum.GADD.getCode()));
                            }
                            if (extendCoverMap.containsKey(StandardCoverageEnum.Dental.getCode())) {
                                extendGroupList.add(extendCoverMap.get(StandardCoverageEnum.Dental.getCode()));
                            }
                            if (extendCoverMap.containsKey(StandardCoverageEnum.MB.getCode())) {
                                extendGroupList.add(extendCoverMap.get(StandardCoverageEnum.MB.getCode()));
                            }
                        }
                        if (StandardCoverageEnum.GTL.getCode().equals(standardCoverageEnumMain.getCode())) {
                            if (extendCoverMap.containsKey(StandardCoverageEnum.CI.getCode())) {
                                extendGroupList.add(extendCoverMap.get(StandardCoverageEnum.CI.getCode()));
                            }
                        }
                        mainCoverageDto.setOptionalRiderGroupList(extendGroupList);
                        mainCoverageDto.setAnnualPolicyLimit(config.getAnnualPolicyLimit());
                        log.info("新加皮主险信息:{}", JacksonUtils.writeAsString(mainCoverageDto));
                        mainCoverageDtoList.add(mainCoverageDto);
                    }
                });
            });

        });
        log.info("新加坡主险费率信息:{}", JacksonUtils.writeAsString(mainCoverageDtoList));

        return mainCoverageDtoList;
    }

    /**
     * 获取经济友好总预算
     *
     * @param singaporeRecommendPlanReqDto
     * @return
     */
    private CurrencyAmount getPriceFriendly(SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto) {
        return singaporeRecommendPlanReqDto.getPriceMin();
    }

    /**
     * 获取保障全面总预算
     *
     * @param singaporeRecommendPlanReqDto
     * @return
     */
    private CurrencyAmount getPriceMost(SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto) {
        return singaporeRecommendPlanReqDto.getPriceMax();
    }

    /**
     * 获取行业标准总预算
     *
     * @param singaporeRecommendPlanReqDto
     * @return
     */
    private CurrencyAmount getPriceBenchMark(SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto) {
        BenchmarkSettingDTO benchMarkInfo = getBenchMarkInfo(singaporeRecommendPlanReqDto);
        CurrencyAmount price = benchMarkInfo.getPrice();
        singaporeRecommendPlanReqDto.setCoverages(benchMarkInfo.getCoverages());
        return price;
    }

    private List<RecommendStandardConfigDto> getBasePriceConfigList(Map<Long, Long> planConfigCompanyMap,List<RecommendStandardConfig> recommendStandardConfigList, SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto) {
        List<RecommendStandardConfigDto> recommendStandardConfigDtos = JacksonUtils.convertList(recommendStandardConfigList, RecommendStandardConfigDto.class);
        Map<Long, CurrencyAmount> totalAmountMap = this.getConfigTotalAmount(planConfigCompanyMap,recommendStandardConfigDtos, singaporeRecommendPlanReqDto);
        for (RecommendStandardConfigDto recommendStandardConfigDto : recommendStandardConfigDtos) {
            recommendStandardConfigDto.setTotalAmount(totalAmountMap.get(recommendStandardConfigDto.getId()));
            recommendStandardConfigDto.setCoverageCode(Objects.requireNonNull(StandardCoverageEnum.getTitleByTags(recommendStandardConfigDto.getConfigTag())).getCode());
        }
        return recommendStandardConfigDtos.stream().filter(x->totalAmountMap.containsKey(x.getId())).collect(Collectors.toList());
    }

    private Map<Long, CurrencyAmount> getConfigTotalAmount(Map<Long, Long> planConfigCompanyMap,List<RecommendStandardConfigDto> recommendStandardConfigList, SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto) {

        if (null == singaporeRecommendPlanReqDto.getPipelineNum()) {
            // 所有人数
            int personNum = singaporeRecommendPlanReqDto.getEmployeeNum() + singaporeRecommendPlanReqDto.getChildNum() + singaporeRecommendPlanReqDto.getSpouseNum();
            // 获取所有方案的价格 - 计算对象
            List<SingaporeStandardPriceCalcDto> singaporeStandardPriceCalcDtoList = new ArrayList<>();
            for (RecommendStandardConfig recommendStandardConfig : recommendStandardConfigList) {
                SingaporeStandardPriceCalcDto singaporeStandardPriceCalcDto = new SingaporeStandardPriceCalcDto();
                singaporeStandardPriceCalcDto.setAge(singaporeRecommendPlanReqDto.getAgeMin());
                singaporeStandardPriceCalcDto.setId(recommendStandardConfig.getId());
                if (!planConfigCompanyMap.isEmpty()){
                    Long companyId = planConfigCompanyMap.get(recommendStandardConfig.getId());
                    singaporeStandardPriceCalcDto.setCompanyId(companyId);
                }else {
                    singaporeStandardPriceCalcDto.setCompanyId(0L);
                }
                singaporeStandardPriceCalcDto.setJobCategory("1");
                singaporeStandardPriceCalcDto.setConfigTag(recommendStandardConfig.getConfigTag());
                singaporeStandardPriceCalcDto.setEmployeeType(singaporeRecommendPlanReqDto.getEmployeeType());
                singaporeStandardPriceCalcDto.setPersonNum(personNum);
                singaporeStandardPriceCalcDtoList.add(singaporeStandardPriceCalcDto);
            }

            // 配置 - 价格 map
            return getSingaporePlanPrice(singaporeStandardPriceCalcDtoList, BigDecimal.valueOf(Long.parseLong(String.valueOf(personNum))));
        }

        return getSingaporePlanPriceByPerson(recommendStandardConfigList, singaporeRecommendPlanReqDto);
    }


    @Override
    public Map<Long, CurrencyAmount> getSingaporePlanPriceByPerson(List<RecommendStandardConfigDto> recommendStandardConfigList, SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto) {
        log.warn(">>>>>>getSingaporePlanPriceByPerson-recommendStandardConfigList:{}<<<<<<", JSON.toJSONString(recommendStandardConfigList));
        log.warn(">>>>>>getSingaporePlanPriceByPerson-singaporeRecommendPlanReqDto:{}<<<<<<", JSON.toJSONString(singaporeRecommendPlanReqDto));

        Map<Long, CurrencyAmount> result = new HashMap<>();
        try {

            List<IgSgQuotePersonInfoDetail> personInfoDetailList = getPersonInfoDetailList(singaporeRecommendPlanReqDto.getPipelineNum());
            log.warn(">>>>>>getSingaporePlanPriceByPerson-getPersonInfoDetailList:{}<<<<<<", JSON.toJSONString(personInfoDetailList));

            List<IgSgQuotePersonInfoDetail> personInfoDetailListFilter = personInfoDetailList.stream()
                    .filter(x -> x.getEmployeeCategory().equals(singaporeRecommendPlanReqDto.getEmployeeType()))
                    .collect(Collectors.toList());

            // 计算费率
            List<SingaporeStandardPriceCalcDto> singaporeStandardPriceCalcDtoList = new ArrayList<>();
            List<Long> snowIds = idGeneratorClient.nextIds(recommendStandardConfigList.size() * personInfoDetailListFilter.size());
            AtomicInteger index = new AtomicInteger();

            Map<Long, List<CurrencyAmount>> configPriceMap = new HashMap<>();
            List<Long> femalePlanConfigList = recommendStandardConfigList.stream().filter(dto -> dto.getConfigTag().contains(StandardCoverageEnum.MB.getCode())).map(IgPlanConfig::getId).collect(Collectors.toList());
            // 根据配置获取对应配置的缓存
            List<Long>  errorList = new ArrayList<>();
            List<Long> feiLvSgpConfigs = getFeiLvSgpConfigs();
            for (RecommendStandardConfigDto recommendStandardConfigDto : recommendStandardConfigList) {
                for (IgSgQuotePersonInfoDetail igSgQuotePersonInfoDetail : personInfoDetailListFilter) {
                    int age = AgeCalculatorUtil.calculateAge(igSgQuotePersonInfoDetail.getBirthText());

                    if (recommendStandardConfigDto.getConfigTag().contains(StandardCoverageEnum.GPA.getCode())) {
                        log.warn(">>>>>>getSingaporePlanPriceByPerson-gpa<<<<<<");
                        String occupationalClass = null == igSgQuotePersonInfoDetail.getOccupationalClass() ? "1" : igSgQuotePersonInfoDetail.getOccupationalClass();
                        Double occupationRate = singaporeCalcCacheService.getOccupationRate(recommendStandardConfigDto.getId(), occupationalClass);
                        if (null != occupationRate) {
                            if (!configPriceMap.containsKey(recommendStandardConfigDto.getId())) {
                                configPriceMap.put(recommendStandardConfigDto.getId(), new ArrayList<>());
                            }
                            configPriceMap.get(recommendStandardConfigDto.getId()).add(CurrencyAmount.valueOf(BigDecimal.valueOf(occupationRate), IdentityContext.getBusinessCurrency()));
                            continue;
                        }
                    }

                    if (recommendStandardConfigDto.getConfigTag().contains(StandardEmployeeTypeEnum.FOREIGN_WORKERS.getTag())) {
                        log.warn(">>>>>>getSingaporePlanPriceByPerson-FOREIGN_WORKERS<<<<<<");
                        Double agePeopleRate = singaporeCalcCacheService.getAgePeopleRate(recommendStandardConfigDto.getId(), age, personInfoDetailListFilter.size());
                        if (null != agePeopleRate) {
                            if (!configPriceMap.containsKey(recommendStandardConfigDto.getId())) {
                                configPriceMap.put(recommendStandardConfigDto.getId(), new ArrayList<>());
                            }
                            configPriceMap.get(recommendStandardConfigDto.getId()).add(CurrencyAmount.valueOf(BigDecimal.valueOf(agePeopleRate), IdentityContext.getBusinessCurrency()));
                            continue;
                        }
                    }
                    if(feiLvSgpConfigs.contains(recommendStandardConfigDto.getId())){
                        if(StringUtils.isEmpty(igSgQuotePersonInfoDetail.getSex())){
                            errorList.add(recommendStandardConfigDto.getId());
                            continue;
                        }
                        StandardAgeLeavelEnum ageLeavelEnum = null;
                        if (femalePlanConfigList.contains(recommendStandardConfigDto.getId())){
                            igSgQuotePersonInfoDetail.setSex(Sex.FEMALE.getValue());
                            ageLeavelEnum = StandardAgeLeavelEnum.getMBAge(age);
                        }else {
                            ageLeavelEnum = StandardAgeLeavelEnum.getAge(age);
                        }
                        if (null == ageLeavelEnum){
                            log.info("精准算费年龄段无法计算费率，配置Id：{} ，最小年龄:{},最大年龄:{},性别:{}", recommendStandardConfigDto.getId(),ageLeavelEnum.getMin(), ageLeavelEnum.getMax(), igSgQuotePersonInfoDetail.getSex());
                            continue;
                        }
                        Double sexAgePeopleRate = singaporeCalcCacheService.getSexAndAgePeopleRate(recommendStandardConfigDto.getId(), ageLeavelEnum.getMin(), ageLeavelEnum.getMax(), igSgQuotePersonInfoDetail.getSex());
                        if (null != sexAgePeopleRate) {
                            if (!configPriceMap.containsKey(recommendStandardConfigDto.getId())) {
                                configPriceMap.put(recommendStandardConfigDto.getId(), new ArrayList<>());
                            }
                            configPriceMap.get(recommendStandardConfigDto.getId()).add(CurrencyAmount.valueOf(BigDecimal.valueOf(sexAgePeopleRate), IdentityContext.getBusinessCurrency()));
                            continue;
                        }
                        log.warn("未从缓存中获取费率：{} ，最小年龄:{},最大年龄:{},性别:{}", recommendStandardConfigDto.getId(),ageLeavelEnum.getMin(), ageLeavelEnum.getMax(), igSgQuotePersonInfoDetail.getSex());
                    }
                    Double ageOnlyRate = singaporeCalcCacheService.getAgeOnlyRate(recommendStandardConfigDto.getId(), age);
                    log.warn(">>>>>>getSingaporePlanPriceByPerson-ageOnlyRate:" + recommendStandardConfigDto.getId()
                            + "age" + age + "{}<<<<<<", JSON.toJSONString(ageOnlyRate));
                    if (null != ageOnlyRate) {
                        if (!configPriceMap.containsKey(recommendStandardConfigDto.getId())) {
                            configPriceMap.put(recommendStandardConfigDto.getId(), new ArrayList<>());
                        }
                        configPriceMap.get(recommendStandardConfigDto.getId()).add(CurrencyAmount.valueOf(BigDecimal.valueOf(ageOnlyRate), IdentityContext.getBusinessCurrency()));
                        continue;
                    }
                    SingaporeStandardPriceCalcDto singaporeStandardPriceCalcDto = new SingaporeStandardPriceCalcDto();
                    singaporeStandardPriceCalcDto.setAge(age);
                    singaporeStandardPriceCalcDto.setId(recommendStandardConfigDto.getId());
                    singaporeStandardPriceCalcDto.setEmployeeType(igSgQuotePersonInfoDetail.getEmployeeCategory());
                    singaporeStandardPriceCalcDto.setCompanyId(recommendStandardConfigDto.getId());
                    singaporeStandardPriceCalcDto.setJobCategory(StringUtils.isEmpty(igSgQuotePersonInfoDetail.getOccupationalClass()) ? "1" : igSgQuotePersonInfoDetail.getOccupationalClass());
                    singaporeStandardPriceCalcDto.setUniqueId(snowIds.get(index.getAndIncrement()));
                    singaporeStandardPriceCalcDto.setPersonNum(1);
                    singaporeStandardPriceCalcDto.setSex(igSgQuotePersonInfoDetail.getSex());
                    singaporeStandardPriceCalcDtoList.add(singaporeStandardPriceCalcDto);
                }
            }

            configPriceMap.forEach((planConfigId, priceList) -> {
                if (!errorList.contains(planConfigId)){
                    result.put(planConfigId, priceList.stream().reduce(CurrencyAmountUtil::add).orElse(CurrencyAmount.zero(IdentityContext.getBusinessCurrency())));
                }
            });

            // 如果不需要调用规则流
            if (CollectionUtil.isEmpty(singaporeStandardPriceCalcDtoList)) {
                log.warn(">>>>>>getSingaporePlanPriceByPerson-不执行规则流缓存:{}<<<<<<", JSON.toJSONString(singaporeStandardPriceCalcDtoList));
                return result;
            }

            List<Long> planConfigList = recommendStandardConfigList.stream().map(RecommendStandardConfigDto::getId)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Long, String> planConfigTagsMap = standardPlanMapper.getPlanConfigTags(planConfigList);


            RuleStreamRequest ruleStreamRequest = new RuleStreamRequest();

            ruleStreamRequest.setDataIds(singaporeStandardPriceCalcDtoList.stream().map(SingaporeStandardPriceCalcDto::getUniqueId).collect(Collectors.toList()));
            Map<Long, RuleStreamPreData> preDataMap = new HashMap<>();
            for (SingaporeStandardPriceCalcDto priceCalcDto : singaporeStandardPriceCalcDtoList) {
                RuleStreamPreData preData = new RuleStreamPreData();
                Map<String, Object> environmentMap = new HashMap<>();
                environmentMap.put("age", priceCalcDto.getAge());
                environmentMap.put("id", priceCalcDto.getId());
                environmentMap.put("company_id", priceCalcDto.getCompanyId());
                environmentMap.put("job_category", priceCalcDto.getJobCategory());
                environmentMap.put("config_tags", planConfigTagsMap.getOrDefault(priceCalcDto.getId(), ""));
                environmentMap.put("person_num", priceCalcDto.getPersonNum());
                environmentMap.put("sex", priceCalcDto.getSex());
                environmentMap.put("unique_id", priceCalcDto.getUniqueId());
                preData.setEnvironmentMap(environmentMap);
                preDataMap.put(priceCalcDto.getUniqueId(), preData);
            }

            ruleStreamRequest.setPreData(preDataMap);

            log.warn("[新加坡]价格计算费率参数:-----{}", JacksonUtils.writeAsString(ruleStreamRequest));

            ResponseVO<List<RuleInstanceDto>> execute = ruleExecuteClient.execute(singaporeStandardRuleConfig.getId(), ruleStreamRequest);
            List<RuleInstanceDto> ruleInstanceDtos = execute.getData();
            List<Long> ids = ruleInstanceDtos.stream().map(RuleInstanceDto::getId).collect(Collectors.toList());
            //4.查看规则流结果
            ResponseVO<Map<Long, Map<String, String>>> environmentVariablesValuePostSecond = ruleClient.findEnvironmentVariablesValuePostSecond(ids);
            Map<Long, Long> idToDataId = ruleInstanceDtos.stream().collect(Collectors.toMap(RuleInstanceDto::getObjectDataId, RuleInstanceDto::getId));
            Map<Long, Map<String, String>> responseData = environmentVariablesValuePostSecond.getData();

            log.warn("[新加坡]价格计算费率结果:-----{}", JacksonUtils.writeAsString(responseData));

            // 根据singaporeStandardPriceCalcDtoList的id分组
            Map<Long, List<SingaporeStandardPriceCalcDto>> idToSingaporeStandardPriceCalcDtoList = singaporeStandardPriceCalcDtoList.stream().collect(Collectors.groupingBy(SingaporeStandardPriceCalcDto::getId));

            idToSingaporeStandardPriceCalcDtoList.forEach((id, list) -> {
                CurrencyAmount currencyAmount = CurrencyAmount.zero(IdentityContext.getBusinessCurrency());
                for (SingaporeStandardPriceCalcDto priceCalcDto : list) {
                    Long uniqueId = priceCalcDto.getUniqueId();
                    String calcPrice = responseData.get(idToDataId.get(uniqueId)).get("price");
                    if ("-".equals(calcPrice)){
                        log.warn("计算错误,规则无法计算,responseData:{}", responseData.get(uniqueId));
                    }
                    currencyAmount = CurrencyAmountUtil.add(currencyAmount, CurrencyAmount.valueOf(Double.parseDouble(calcPrice), IdentityContext.getBusinessCurrency()));
                }
                result.put(id, currencyAmount);
            });
        } catch (Exception e) {
            log.error("规则无法计算", e.getMessage(),e);
        }
        return result;
    }

    public static List<GroupPlanPriceDto> calculateCombinations(Map<Long, List<RecommendStandardConfigDto>> data,
                                                                Map<Long, Long> planIdToCompanyIdMap, List<RecommendStandardConfigDto> recommendStandardConfigList) {
        List<GroupPlanPriceDto> result = new ArrayList<>();

        // recommendStandardConfigList根据id生成map
        Map<Long, RecommendStandardConfigDto> recommendStandardConfigMap = recommendStandardConfigList.stream()
                .collect(Collectors.toMap(RecommendStandardConfigDto::getId, Function.identity()));

        // 遍历每个键值对
        for (Map.Entry<Long, List<RecommendStandardConfigDto>> entry : data.entrySet()) {
            List<RecommendStandardConfigDto> plans = entry.getValue();
            // 按照类型分组
            Map<String, List<RecommendStandardConfigDto>> plansByType = new HashMap<>();
            for (RecommendStandardConfigDto plan : plans) {
                plansByType.computeIfAbsent(plan.getCoverageCode(), k -> new ArrayList<>()).add(plan);
            }

            // 生成所有可能的组合
            List<List<RecommendStandardConfigDto>> combinations = new ArrayList<>();
            List<List<RecommendStandardConfigDto>> typeLists = new ArrayList<>(plansByType.values());
            generateCombinations(combinations, typeLists, 0, new ArrayList<>());

            // 计算每种组合的价格，并存储在 Map 中
            for (List<RecommendStandardConfigDto> combination : combinations) {
                // 险种代码map
                Map<String, List<Long>> combinationCodeMap = new HashMap<>();
                for (RecommendStandardConfigDto plan : combination) {
                    if (!combinationCodeMap.containsKey(plan.getCoverageCode())) {
                        combinationCodeMap.put(plan.getCoverageCode(), new ArrayList<>());
                    }
                    combinationCodeMap.get(plan.getCoverageCode()).add(plan.getId());
                }

                // 生产
                List<Map<String, RecommendStandardConfigDto>> subResult = new ArrayList<>();
                Map<String, RecommendStandardConfigDto> combinationMap = new HashMap<>();
                GroupPlanPriceDto groupPlanPriceDto = new GroupPlanPriceDto();
                // 获取主险代码
                Optional<RecommendStandardConfigDto> first = combination.stream().filter(x ->
                        Arrays.asList(
                                StandardCoverageEnum.GHS.getCode(),
                                StandardCoverageEnum.GTL.getCode(),
                                StandardCoverageEnum.GPA.getCode()
                        ).contains(Objects.requireNonNull(StandardCoverageEnum.getTitleByTags(x.getConfigTag())).getCode())
                ).findFirst();
                AtomicReference<Boolean> skipFlag = new AtomicReference<>(false);
                first.ifPresent(recommendStandardConfigDto -> {
                    List<Long> optionalConfigs = recommendStandardConfigDto.getOptionalConfigs();
                    optionalConfigs.forEach(relateConfigId -> {
                        if (recommendStandardConfigMap.containsKey(relateConfigId)) {
                            RecommendStandardConfig optionalConfigDto = recommendStandardConfigMap.get(relateConfigId);
                            String code = Objects.requireNonNull(StandardCoverageEnum.getTitleByTags(optionalConfigDto.getConfigTag())).getCode();
                            if (combinationCodeMap.containsKey(code) && !combinationCodeMap.get(code).contains(relateConfigId)) {
                                skipFlag.set(true);
                            }
                        }
                    });
                    groupPlanPriceDto.setMainInsuranceCode(recommendStandardConfigDto.getCoverageCode());
                    groupPlanPriceDto.setPlanId(recommendStandardConfigDto.getPlanId());
                    groupPlanPriceDto.setCompanyId(planIdToCompanyIdMap.get(recommendStandardConfigDto.getPlanId()));
                    groupPlanPriceDto.setOptionalInsuranceCode(optionalConfigs);
                });

                if (skipFlag.get()) {
                    continue;
                }

                CurrencyAmount totalAmount = CurrencyAmount.zero(IdentityContext.getBusinessCurrency());
                for (RecommendStandardConfigDto plan : combination) {
                    combinationMap.put(plan.getCoverageCode(), plan);
                    totalAmount = CurrencyAmountUtil.add(totalAmount, plan.getTotalAmount());
                }
                subResult.add(combinationMap);
                groupPlanPriceDto.setList(subResult);
                groupPlanPriceDto.setTotalAmount(totalAmount);
                result.add(groupPlanPriceDto);
            }
        }

        // 按照组合价格排序
        result.sort(Comparator.comparingDouble(map -> {
            double totalPrice = 0;
            for (Map<String, RecommendStandardConfigDto> plans : map.getList()) {
                for (RecommendStandardConfigDto plan : plans.values()) {
                    totalPrice += plan.getTotalAmount().getAmount().doubleValue();
                }
            }
            return totalPrice;
        }));

        return result;
    }

    private static void generateCombinations(List<List<RecommendStandardConfigDto>> combinations, List<List<RecommendStandardConfigDto>> typeLists, int index, List<RecommendStandardConfigDto> currentCombination) {
        if (index == typeLists.size()) {
            combinations.add(new ArrayList<>(currentCombination));
            return;
        }

        List<RecommendStandardConfigDto> currentTypeList = typeLists.get(index);
        for (RecommendStandardConfigDto plan : currentTypeList) {
            currentCombination.add(plan);
            generateCombinations(combinations, typeLists, index + 1, currentCombination);
            currentCombination.remove(currentCombination.size() - 1);
        }
    }

    /**
     * 获取推荐结果
     *
     * @param singaporeRecommendPlanReqDto  请求对象
     * @param singaporeStandardPlanDtoMap   方案map
     * @param perPersonPrice                单人预算
     * @param notRecommendCompanyIds        不推荐的保司列表
     * @param recommendStandardConfigList   推荐配置列表
     * @param standardPlanDtoList           方案列表
     * @param singaporeStandardPlanTypeEnum 推荐类型
     * @param recommendTypeCompanyMap       第二个类型时，指定的保险公司结果
     * @return 推荐结果
     */
    private SingaporeRecommendPlanItemRspDto getSingaporeRecommendPlanItem(SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto
            , Map<Long, SingaporeStandardPlanDto> singaporeStandardPlanDtoMap
            , CurrencyAmount perPersonPrice
            , List<Long> notRecommendCompanyIds
            , List<RecommendStandardConfig> recommendStandardConfigList
            , List<SingaporeStandardPlanDto> standardPlanDtoList
            , SingaporeStandardPlanTypeEnum singaporeStandardPlanTypeEnum
            , Map<String, Long> recommendTypeCompanyMap) {
        SingaporeRecommendPlanItemRspDto singaporeRecommendPlanItemRspDto = new SingaporeRecommendPlanItemRspDto();

        if (CollectionUtil.isEmpty(singaporeRecommendPlanReqDto.getPlanIdList()) || CollectionUtil.isEmpty(singaporeRecommendPlanReqDto.getPlanConfigIdList())) {
            return null;
        }

        Map<Long, SingaporeStandardPlanDto> standardPlanDtoMap = standardPlanDtoList.stream().collect(Collectors.toMap(SingaporeStandardPlanDto::getPlanId, Function.identity()));

        resetRecommendPlanItem(singaporeRecommendPlanReqDto);

        // 指定保司推荐
        List<Long> recommandCompanyIdList = new ArrayList<>();
        if (recommendTypeCompanyMap.containsKey(singaporeStandardPlanTypeEnum.getValue())) {
            // 如果包含这个保司，将其他保司添加到不推荐列表中
            Long companyId = recommendTypeCompanyMap.get(singaporeStandardPlanTypeEnum.getValue());
            long count = recommendStandardConfigList.stream().filter(x ->
                    companyId.equals(singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId())
            ).count();
            if (count > 0) {
                recommandCompanyIdList.add(companyId);
            }
        }

        // 不需要过滤的方案
        List<Long> planConfigIdList = singaporeRecommendPlanReqDto.getPlanConfigIdList();
        List<RecommendStandardConfig> noFilterConfig = recommendStandardConfigList.stream()
                                                                                  .filter(x -> planConfigIdList.contains(x.getId()))
                                                                                  .collect(Collectors.toList());

        log.info("指定保司推荐方案:{}", JacksonUtils.writeAsString(noFilterConfig));
        // 过滤不推荐的保司
        List<RecommendStandardConfig> recommendStandardConfigExclude = recommendStandardConfigList.stream()
                .filter(x -> !notRecommendCompanyIds.contains(singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId())
                        && null != x.getConfigTag()
                        && x.getRelation().equals(IgPlanConfigEnums.Relation.ONESELF.getCode())
                        && x.getConfigTag().contains(singaporeStandardPlanTypeEnum.getCode()))
                .collect(Collectors.toList());

        noFilterConfig.forEach(x -> {
            List<Long> longList = recommendStandardConfigExclude.stream().map(RecommendStandardConfig::getId)
                                                               .collect(Collectors.toList());
            if (!longList.contains(x.getId())) {
                recommendStandardConfigExclude.add(x);
            }
        });
        // 行业标准的主附险重置
        if (null == perPersonPrice) {
            BenchmarkSettingDTO benchMarkInfo = getBenchMarkInfo(singaporeRecommendPlanReqDto);
            singaporeRecommendPlanReqDto.setCoverages(benchMarkInfo.getCoverages());
        }

        log.info("新加坡过滤推推荐的方案配置 recommendStandardConfigExclude:{}", JacksonUtils.writeAsString(recommendStandardConfigExclude));

        // 设置主险
        SingaporeStandardPlanDto singaporeStandardPlanDto = standardPlanDtoMap.get(singaporeRecommendPlanReqDto.getPlanIdList().get(0));

        singaporeRecommendPlanItemRspDto.setStandardPlanId(singaporeRecommendPlanReqDto.getPlanIdList().get(0));
        singaporeRecommendPlanItemRspDto.setPlanType(singaporeStandardPlanTypeEnum.getValue());
        singaporeRecommendPlanItemRspDto.setPlanTitle(singaporeStandardPlanTypeEnum.getTitle());
        singaporeRecommendPlanItemRspDto.setCompanyLogo(singaporeStandardPlanDto.getCompanyLogo());
        singaporeRecommendPlanItemRspDto.setCompanyName(singaporeStandardPlanDto.getCompanyName());
        singaporeRecommendPlanItemRspDto.setCompanyTitle(singaporeStandardPlanDto.getPlanTitle());
        singaporeRecommendPlanItemRspDto.setImageUrl(singaporeStandardPlanDto.getImageUrl());
        singaporeRecommendPlanItemRspDto.setCompanyId(singaporeStandardPlanDto.getCompanyId());
        singaporeRecommendPlanItemRspDto.setCompanyDesc(singaporeStandardPlanDto.getItems().stream().map(ProductDescItemInfo::getContent).collect(Collectors.toList()));
        singaporeRecommendPlanItemRspDto.setEligibility(singaporeStandardPlanDto.getEligibility());
        singaporeRecommendPlanItemRspDto.setPolicyWording(singaporeStandardPlanDto.getPolicyWording());
        List<SingaporeStandardPlanMainCoverageDto> coverages = this.getCoverages(singaporeRecommendPlanReqDto, recommendStandardConfigExclude, singaporeStandardPlanDtoMap);
        log.info("新加坡过滤推推荐的方案配置 coverages:{}", JacksonUtils.writeAsString(coverages));
        singaporeRecommendPlanItemRspDto.setCoverages(coverages);

        // 计算预估保费
        SingaporePlanTotalAmountReqDto singaporePlanTotalAmountReqDto = new SingaporePlanTotalAmountReqDto();
        singaporePlanTotalAmountReqDto.setPipelineNum(singaporeRecommendPlanReqDto.getPipelineNum());
        singaporePlanTotalAmountReqDto.setEmployeeType(singaporeRecommendPlanReqDto.getEmployeeType());
        singaporePlanTotalAmountReqDto.setEmployeeNum(singaporeRecommendPlanReqDto.getEmployeeNum());
        singaporePlanTotalAmountReqDto.setSpouseNum(singaporeRecommendPlanReqDto.getSpouseNum());
        singaporePlanTotalAmountReqDto.setChildNum(singaporeRecommendPlanReqDto.getChildNum());
        singaporePlanTotalAmountReqDto.setPlanConfigIdList(singaporeRecommendPlanReqDto.getPlanConfigIdList());
        singaporePlanTotalAmountReqDto.setAgeMin(singaporeRecommendPlanReqDto.getAgeMin());
        singaporePlanTotalAmountReqDto.setAgeMax(singaporeRecommendPlanReqDto.getAgeMax());
        CurrencyAmount singaporePlanTotalAmount = getSingaporePlanTotalAmount(singaporePlanTotalAmountReqDto);
        singaporeRecommendPlanItemRspDto.setTotalAmount(singaporePlanTotalAmount);

        return singaporeRecommendPlanItemRspDto;
    }

    private void resetRecommendPlanItem(SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto) {

        // 兼容最大价格为空的场景
        if (null == singaporeRecommendPlanReqDto.getPriceMax()) {
            singaporeRecommendPlanReqDto.setPriceMax(CurrencyAmount.valueOf(200000L, IdentityContext.getBusinessCurrency()));
        }

        // 如果流水线有数据
        if (null != singaporeRecommendPlanReqDto.getPipelineNum()) {
            List<IgSgQuotePersonInfoDetail> personInfoDetailList = getPersonInfoDetailList(singaporeRecommendPlanReqDto.getPipelineNum());
            List<IgSgQuotePersonInfoDetail> collect = personInfoDetailList.stream().filter(x -> x.getEmployeeCategory().equals(singaporeRecommendPlanReqDto.getEmployeeType())).collect(Collectors.toList());
            // 计算平均年龄
            double averageAge = collect.stream()
                    .filter(Objects::nonNull)  // 过滤 null 元素
                    .mapToDouble(person -> AgeCalculatorUtil.calculateAge(person.getBirthText()))
                    .filter(age -> age >= 0)  // 过滤无效年龄
                    .average()
                    .orElse(0.0);
            singaporeRecommendPlanReqDto.setEmployeeNum(collect.size());
            singaporeRecommendPlanReqDto.setChildNum(0);
            singaporeRecommendPlanReqDto.setSpouseNum(0);
            CalcAgeRangeEnum byAge = CalcAgeRangeEnum.getByAge((int) Math.round(averageAge));
            singaporeRecommendPlanReqDto.setAgeMin(byAge.getMin());
            singaporeRecommendPlanReqDto.setAgeMax(byAge.getMax());
        }
    }

    /**
     * 根据推荐方案请求参数和配置信息，组装新加坡标准计划的主险和附加险信息
     *
     * @param singaporeRecommendPlanReqDto 新加坡推荐计划请求参数，包含方案ID列表、已选择的险种等信息
     * @param recommendStandardConfigList  推荐标准配置列表，包含所有可选的保险配置
     * @param singaporeStandardPlanDtoMap  新加坡标准计划DTO映射，key为计划ID，value为对应的计划信息
     * @return 新加坡标准计划主险列表，包含主险及其对应的附加险分组信息
     */
    private List<SingaporeStandardPlanMainCoverageDto> getCoverages(SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto
            , List<RecommendStandardConfig> recommendStandardConfigList, Map<Long, SingaporeStandardPlanDto> singaporeStandardPlanDtoMap) {

        log.info("recommendStandardConfigList:{}",JacksonUtils.writeAsString(recommendStandardConfigList));
        log.info("singaporeRecommendPlanReqDto:{}",JacksonUtils.writeAsString(singaporeRecommendPlanReqDto));
        log.info("singaporeStandardPlanDtoMap:{}",JacksonUtils.writeAsString(singaporeStandardPlanDtoMap));
        // 主险列表
        Map<String, Map<String, SingaporeStandardPlanOptionalCoverageGroupDto>> configGroup = new LinkedHashMap<>();

        // 配置重置数组
        List<RecommendStandardConfigDto> recommendStandardConfigDtos = JacksonUtils.convertList(recommendStandardConfigList, RecommendStandardConfigDto.class);

        // 所有主险
        List<SingaporeStandardPlanMainCoverageDto> singaporeStandardPlanMainCoverageDtos = new ArrayList<>();

        SingaporeStandardPlanDto singaporeStandardPlanDtoFirst = singaporeStandardPlanDtoMap.get(singaporeRecommendPlanReqDto.getPlanIdList().get(0));

        // 所有配置map
        Map<Long, RecommendStandardConfigDto> recommendStandardConfigListPlanMap = recommendStandardConfigDtos.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));
        // 所有选择的主险
        List<String> mainCoverageList = singaporeRecommendPlanReqDto.getCoverages().stream().map(x -> null == x.getType() ? x.getTitle() : x.getType()).collect(Collectors.toList());
        log.warn("所有选择的主险：{}", JacksonUtils.writeAsString(mainCoverageList));

        // 所有配置的责任列表
        List<Long> planConfigList = recommendStandardConfigList.stream()
                .map(RecommendStandardConfig::getId).collect(Collectors.toList());
        Map<Long, List<ProductClientUIDTO>> planConfigDutyDetailBatchData = getProductUIByConfigs(planConfigList);

        log.warn("get-coverages-param-config-list:{}", JacksonUtils.writeAsString(recommendStandardConfigDtos));

        // 按照主附险排序（用于先添加主险，再添加附加险）
        List<RecommendStandardConfigDto> recommendStandardConfigDtoList = recommendStandardConfigDtos.stream()
                .filter(x -> null != x.getConfigTag()
                        && null != StandardCoverageEnum.getTitleByTags(x.getConfigTag())
                        && singaporeStandardPlanDtoMap.get(x.getPlanId()).getCompanyId().equals(singaporeStandardPlanDtoFirst.getCompanyId())
                )
                .sorted(Comparator.comparingInt(x -> Integer.parseInt(StandardCoverageEnum.getTitleByTags(x.getConfigTag()).getValue())))
                .collect(Collectors.toList());
        log.warn("排序后的主附险配置：{}", JacksonUtils.writeAsString(recommendStandardConfigDtoList));

        List<String> mainExclude = new ArrayList<>();

        Map<String, Long> mainCoverageTypePlanIdMap = new HashMap<>();
        singaporeRecommendPlanReqDto.getPlanIdList().forEach(x -> {
            mainCoverageTypePlanIdMap.put(StandardCoverageEnum.getTitleByTags(singaporeStandardPlanDtoMap.get(x).getPlanTag()).getCode(), x);
        });

        log.warn("所有责任组装开始：{}", JacksonUtils.writeAsString(singaporeRecommendPlanReqDto));

        Map<String, List<Long>> coverageConfigIdMap = new HashMap<>();

        recommendStandardConfigDtoList.forEach(recommendStandardConfig -> {
            StandardCoverageEnum coverageEnum = StandardCoverageEnum.getTitleByTags(recommendStandardConfig.getConfigTag());

            if (null == coverageEnum) {
                log.warn("未匹配到险种{}", JacksonUtils.writeAsString(recommendStandardConfig));
                return;
            }

            if (null == recommendStandardConfig.getPlanId()) {
                log.warn("配置的方案ID为空{}", JacksonUtils.writeAsString(recommendStandardConfig));
                return;
            }

            // 已包括的主险不做添加
            if (mainExclude.contains(coverageEnum.getCode())) {
                log.warn("已包括的主险不做添加：{}-{}", JacksonUtils.writeAsString(mainExclude), JacksonUtils.writeAsString(recommendStandardConfig));
                return;
            }

            recommendStandardConfig.setCoverageCode(coverageEnum.getCode());
            recommendStandardConfig.setIsMain(coverageEnum.getIsMaster());

            String code = coverageEnum.getCode();


            if (Boolean.TRUE.equals(coverageEnum.getIsMaster())) {
                // 未选择的不做添加
                if (!mainCoverageList.contains(coverageEnum.getCode())) {
                    log.warn("未选择的不做添加：{}-{}", JacksonUtils.writeAsString(mainCoverageList), JacksonUtils.writeAsString(coverageEnum));
                    return;
                }
                if (!singaporeRecommendPlanReqDto.getPlanConfigIdList().contains(recommendStandardConfig.getId())) {
                    log.info("添加主险:singaporeRecommendPlanReqDto{}:coverageEnum {}- recommendStandardConfig: {}", JacksonUtils.writeAsString(singaporeRecommendPlanReqDto),JacksonUtils.writeAsString(coverageEnum), JacksonUtils.writeAsString(recommendStandardConfig));
                    log.warn("未匹配到的主险配置，跳过-{}-{}", JacksonUtils.writeAsString(singaporeRecommendPlanReqDto.getPlanConfigIdList()), JacksonUtils.writeAsString(recommendStandardConfig));
                  return;
                }
                mainExclude.add(coverageEnum.getCode());

                configGroup.put(code, new HashMap<>());
                // 主险
                SingaporeStandardPlanMainCoverageDto singaporeStandardPlanMainCoverageDto = new SingaporeStandardPlanMainCoverageDto();
                singaporeStandardPlanMainCoverageDto.setPlanId(recommendStandardConfig.getPlanId());
                singaporeStandardPlanMainCoverageDto.setPlanConfigId(recommendStandardConfig.getId());
                singaporeStandardPlanMainCoverageDto.setCode(coverageEnum.getValue());
                singaporeStandardPlanMainCoverageDto.setType(coverageEnum.getCode());
                singaporeStandardPlanMainCoverageDto.setTitle(String.format("%s %s", coverageEnum.getTitleText(), recommendStandardConfig.getName()));
                singaporeStandardPlanMainCoverageDto.setConfigName(recommendStandardConfig.getName());
                singaporeStandardPlanMainCoverageDto.setBenefit(recommendStandardConfig.getBenefit());
                singaporeStandardPlanMainCoverageDto.setCoverageTitle(coverageEnum.getTitleText());
                singaporeStandardPlanMainCoverageDto.setAnnualPolicyLimit(recommendStandardConfig.getAnnualPolicyLimit());
                singaporeStandardPlanMainCoverageDto.setDutyList(planConfigDutyDetailBatchData.get(recommendStandardConfig.getId()));

                singaporeStandardPlanMainCoverageDtos.add(singaporeStandardPlanMainCoverageDto);

                // 处理主险的可选配置（附加险）
                if (CollectionUtil.isNotEmpty(recommendStandardConfig.getOptionalConfigs())) {
                    recommendStandardConfig.getOptionalConfigs().forEach(optionalConfig -> {
                        if (recommendStandardConfigListPlanMap.containsKey(optionalConfig)) {
                            RecommendStandardConfig relateConfig = recommendStandardConfigListPlanMap.get(optionalConfig);
                            String codeRelate = StandardCoverageEnum.getTitleByTags(relateConfig.getConfigTag()).getCode();
                            if (!coverageConfigIdMap.containsKey(codeRelate)) {
                                coverageConfigIdMap.put(codeRelate, new ArrayList<>());
                            }
                            coverageConfigIdMap.get(codeRelate).add(optionalConfig);
                        }
                    });
                }

                log.warn("已添加主险：{}", JacksonUtils.writeAsString(singaporeStandardPlanMainCoverageDto));
            } else {
                // 找到配置对应的主险是个什么类型的方案
                SingaporeStandardPlanDto singaporeStandardPlanDto = singaporeStandardPlanDtoMap.get(recommendStandardConfig.getPlanId());
                StandardCoverageEnum relatedMainCoverage = StandardCoverageEnum.getTitleByTags(singaporeStandardPlanDto.getPlanTag());
                if (null == relatedMainCoverage) {
                    log.info("plan_config_id related main coverage no exist {}", JacksonUtils.writeAsString(recommendStandardConfig));
                    return;
                }
                if (!singaporeStandardPlanMainCoverageDtos.stream().map(SingaporeStandardPlanMainCoverageDto::getType).collect(Collectors.toList()).contains(relatedMainCoverage.getCode())) {
                    log.warn("非关联主险-{}，跳过{}", JacksonUtils.writeAsString(relatedMainCoverage), JacksonUtils.writeAsString(singaporeStandardPlanMainCoverageDtos));
                    return;
                }

                if (!mainCoverageTypePlanIdMap.containsKey(relatedMainCoverage.getCode())) {
                    log.warn("主险{}不存在，退出{}", JacksonUtils.writeAsString(relatedMainCoverage), JacksonUtils.writeAsString(mainCoverageTypePlanIdMap));
                    return;
                }

                // 非主险方案的附加险，退出
                if (!mainCoverageTypePlanIdMap.get(relatedMainCoverage.getCode()).equals(recommendStandardConfig.getPlanId())) {
                    log.warn("非主险方案{}的附加险{}，退出", JacksonUtils.writeAsString(mainCoverageTypePlanIdMap), JacksonUtils.writeAsString(recommendStandardConfig));
                    return;
                }


                // 是否关联配置
                if (coverageConfigIdMap.containsKey(code) && !coverageConfigIdMap.get(code).contains(recommendStandardConfig.getId())) {
                    return;
                }

                // 附加险分组
                if (!configGroup.get(relatedMainCoverage.getCode()).containsKey(code)) {
                    SingaporeStandardPlanOptionalCoverageGroupDto singaporeStandardPlanOptionalCoverageGroupDto = new SingaporeStandardPlanOptionalCoverageGroupDto();
                    singaporeStandardPlanOptionalCoverageGroupDto.setCode(coverageEnum.getCode());
                    singaporeStandardPlanOptionalCoverageGroupDto.setTitle(coverageEnum.getCode());
                    singaporeStandardPlanOptionalCoverageGroupDto.setOptionalRiderList(new ArrayList<>());
                    configGroup.get(relatedMainCoverage.getCode()).put(code, singaporeStandardPlanOptionalCoverageGroupDto);
                    log.warn("首次添加附加险分组{}：{}", code, JacksonUtils.writeAsString(singaporeStandardPlanOptionalCoverageGroupDto));
                }

                SingaporeStandardPlanOptionalCoverageGroupDto singaporeStandardPlanOptionalCoverageGroupDto = configGroup.get(relatedMainCoverage.getCode()).get(coverageEnum.getCode());

                List<SingaporeStandardPlanOptionalCoverageItemDto> optionalRiderList = singaporeStandardPlanOptionalCoverageGroupDto.getOptionalRiderList();

                SingaporeStandardPlanOptionalCoverageItemDto singaporeStandardPlanOptionalCoverageItemDto = getSingaporeStandardPlanOptionalCoverageItemDto(recommendStandardConfig, coverageEnum, singaporeRecommendPlanReqDto.getPlanConfigIdList());
                singaporeStandardPlanOptionalCoverageItemDto.setDutyList(planConfigDutyDetailBatchData.get(recommendStandardConfig.getId()));

                optionalRiderList.add(singaporeStandardPlanOptionalCoverageItemDto);

                singaporeStandardPlanOptionalCoverageGroupDto.setOptionalRiderList(optionalRiderList);

                singaporeStandardPlanMainCoverageDtos.stream()
                        .filter(v -> v.getType().equals(relatedMainCoverage.getCode()))
                        .findFirst().get()
                        .setOptionalRiderGroupList(new ArrayList<>(configGroup.get(relatedMainCoverage.getCode()).values()));
            }
        });

        log.warn("所有责任组装结束：{} - {}", JacksonUtils.writeAsString(singaporeRecommendPlanReqDto), JacksonUtils.writeAsString(singaporeStandardPlanMainCoverageDtos));

        return singaporeStandardPlanMainCoverageDtos;
    }

    private SingaporeStandardPlanOptionalCoverageItemDto getSingaporeStandardPlanOptionalCoverageItemDto(RecommendStandardConfigDto recommendStandardConfig, StandardCoverageEnum coverageEnum, List<Long> selectedPlanConfigIdList) {
        SingaporeStandardPlanOptionalCoverageItemDto singaporeStandardPlanOptionalCoverageItemDto = new SingaporeStandardPlanOptionalCoverageItemDto();
        singaporeStandardPlanOptionalCoverageItemDto.setPlanId(recommendStandardConfig.getPlanId());
        singaporeStandardPlanOptionalCoverageItemDto.setPlanConfigId(recommendStandardConfig.getId());
        singaporeStandardPlanOptionalCoverageItemDto.setCode(coverageEnum.getCode());
        singaporeStandardPlanOptionalCoverageItemDto.setType(coverageEnum.getCode());
        singaporeStandardPlanOptionalCoverageItemDto.setTitle(String.format("%s %s", recommendStandardConfig.getName(), recommendStandardConfig.getBenefit()));
        singaporeStandardPlanOptionalCoverageItemDto.setConfigName(recommendStandardConfig.getName());
        singaporeStandardPlanOptionalCoverageItemDto.setBenefit(recommendStandardConfig.getBenefit());
        singaporeStandardPlanOptionalCoverageItemDto.setCoverageTitle(coverageEnum.getTitleText());
        singaporeStandardPlanOptionalCoverageItemDto.setIsDefault(selectedPlanConfigIdList.contains(recommendStandardConfig.getId()));
        return singaporeStandardPlanOptionalCoverageItemDto;
    }

    @Override
    public List<SingaporeStandardPlanDto> getStandardPlanInfo(List<Long> standardPlanIds) {

        // 查询所有标品
        ResponseVO<List<RecommendStandardPlan>> responseVO;
        if (CollectionUtil.isEmpty(standardPlanIds)) {
            responseVO = planConfigClient.getStandardConfig(singaporeStandardGroupConfig.getIdList());
        } else {
            responseVO = planConfigClient.getStandardConfigByPlanId(standardPlanIds);
        }
        if (CollectionUtil.isEmpty(responseVO.getData())) {
            throw new QuoteException(-1, "standard config is empty");
        }
        List<RecommendStandardPlan> recommendStandardPlans = responseVO.getData();

        List<RecommendStandardPlan> recommendStandardPlanList = recommendStandardPlans.stream()
                .filter(x -> null != x.getRecommendStandardConfigList() && !CollectionUtils.isEmpty(x.getRecommendStandardConfigList()))
                .collect(Collectors.toList());

        // 换取信息补全的对象
        List<SingaporeStandardPlanDto> standardPlanDtoList = JacksonUtils.convertList(recommendStandardPlanList, SingaporeStandardPlanDto.class);

        List<SingaporeStandardPlanDto> byPlanIdList = standardPlanMapper.getByPlanIdList(standardPlanDtoList);
        if (!USER_GUEST.equals(IdentityContext.getUserId())) {
            BrokerDto brokerDto = brokerService.get(IdentityContext.getUserId());

            if (brokerDto == null) {
                return byPlanIdList;
            }

            // 过滤
            return filterPlansByBrokerLicense(byPlanIdList, brokerDto.getLicenseType());

        }

        return byPlanIdList;
    }

    /**
     * 根据Broker许可证类型过滤标准计划列表
     *
     * @param standardPlans  原始标准计划列表
     * @param brokerLicenses 经纪人拥有的许可证类型集合
     * @return 过滤后的标准计划列表
     */
    @Override
    public List<SingaporeStandardPlanDto> filterPlansByBrokerLicense(List<SingaporeStandardPlanDto> standardPlans, List<String> brokerLicenses) {
        if (CollectionUtils.isEmpty(brokerLicenses)) {
            // 无许可证或有两个以上许可证时不进行过滤
            return standardPlans;
        }

        if (brokerLicenses.size() == 1) {
            String licenseType = brokerLicenses.get(0);

            // LIFE许可证处理逻辑：公司为LIFE不进行过滤，COMPOSITE才过滤GENERAL方案
            if (SingaporeCompanyTypeEnum.LIFE.getCode().equals(licenseType)) {
                return standardPlans.stream()
                        .filter(plan -> !SingaporeCompanyTypeEnum.GENERAL.getCode().equals(plan.getCompanyType()))
                        .filter(plan -> !SingaporeCompanyTypeEnum.COMPOSITE.getCode().equals(plan.getCompanyType())
                                || !plan.getPlanTag().contains(SingaporePlanTypeEnum.GENERAL.getValue()))
                        .collect(Collectors.toList());
            }

            // GENERAL许可证处理逻辑：公司为GENERAL不进行过滤，COMPOSITE才过滤LIFE方案
            if (SingaporeCompanyTypeEnum.GENERAL.getCode().equals(licenseType)) {
                return standardPlans.stream()
                        .filter(plan -> !SingaporeCompanyTypeEnum.LIFE.getCode().equals(plan.getCompanyType()))
                        .filter(plan -> !SingaporeCompanyTypeEnum.COMPOSITE.getCode().equals(plan.getCompanyType())
                                || !plan.getPlanTag().contains(SingaporePlanTypeEnum.LIFE.getValue()))
                        .collect(Collectors.toList());
            }
        }

        // 不支持超过2个许可证的场景，记录日志
        log.warn("Unsupported number of licenses: {}", brokerLicenses.size());
        return standardPlans;
    }

    @Override
    public SingaporeRecommendPlanItemRspDto getSingaporePlan(SingaporeRecommendPlanDetailReqDto singaporeRecommendPlanDetailReqDto) {
        Long cartDetailId = singaporeRecommendPlanDetailReqDto.getCartDetailId();
        List<SingaporeStandardPlanDto> standardPlanInfo = getStandardPlanInfo(singaporeRecommendPlanDetailReqDto.getPlanIdList());
        log.info("新加坡指定方案: {} 推荐的配置信息:{}", JacksonUtils.writeAsString(singaporeRecommendPlanDetailReqDto), JacksonUtils.writeAsString(standardPlanInfo));

        // 过滤人员类型
        List<SingaporeStandardPlanDto> singaporeStandardPlanDtoList = standardPlanInfo.stream()
                .filter(x -> null != x.getEmployeeCategory() && x.getEmployeeCategory().equals(singaporeRecommendPlanDetailReqDto.getEmployeeType()))
                .collect(Collectors.toList());

        // 查询保司信息
        Map<Long, SingaporeStandardPlanDto> singaporeStandardPlanDtoMap = singaporeStandardPlanDtoList.stream()
                .collect(Collectors.toMap(SingaporeStandardPlanDto::getPlanId, Function.identity()));
        List<RecommendStandardConfig> recommendStandardConfigList = singaporeStandardPlanDtoList.stream()
                .map(RecommendStandardPlan::getRecommendStandardConfigList).flatMap(Collection::stream)
                .filter(x -> x.getRelation().equals(IgPlanConfigEnums.Relation.ONESELF.getCode()))
                .collect(Collectors.toList());

        // 如果购物车进入
        if (null != cartDetailId) {
            return this.getPlanDetail(cartDetailId, standardPlanInfo);
        }

        List<Long> notRecommendCompanyIdList = new ArrayList<>();
        standardPlanInfo.stream().filter(v -> singaporeRecommendPlanDetailReqDto.getPlanIdList().contains(v.getPlanId())).findFirst().ifPresent(v -> {
            notRecommendCompanyIdList.addAll(standardPlanInfo.stream().filter(f -> !f.getCompanyId().equals(v.getCompanyId()))
                    .map(SingaporeStandardPlanDto::getCompanyId)
                    .distinct()
                    .collect(Collectors.toList()));
        });

        String planTypeReq = singaporeRecommendPlanDetailReqDto.getPlanType();
        String planType = null == planTypeReq ? SingaporeStandardPlanTypeEnum.BUDGET.getValue() : planTypeReq;
        List<SingaporeStandardPlanMainCoverageDto> coverages = singaporeRecommendPlanDetailReqDto.getCoverages();

        CurrencyAmount basePrice = CurrencyAmount.zero(IdentityContext.getBusinessCurrency());
        if (SingaporeStandardPlanTypeEnum.BUDGET.getValue().equals(planType)) {
            basePrice = singaporeRecommendPlanDetailReqDto.getPriceMin();
        }
        if (SingaporeStandardPlanTypeEnum.MOST.getValue().equals(planType)) {
            basePrice = singaporeRecommendPlanDetailReqDto.getPriceMax();
            if (null == basePrice) {
                basePrice = CurrencyAmount.valueOf(BigDecimal.valueOf(2000000), IdentityContext.getBusinessCurrency());
            }
        }
        if (SingaporeStandardPlanTypeEnum.Benchmark.getValue().equals(planType)) {
            SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto = JacksonUtils.covertObject(singaporeRecommendPlanDetailReqDto, SingaporeRecommendPlanReqDto.class);
            BenchmarkSettingDTO benchmarkSettingDTO = this.getBenchMarkInfo(singaporeRecommendPlanReqDto);
            basePrice = benchmarkSettingDTO.getPrice();
            singaporeRecommendPlanDetailReqDto.setCoverages(benchmarkSettingDTO.getCoverages());
        }

        // 获取请求对象
        SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto = getSingaporeRecommendPlanReqDto(singaporeRecommendPlanDetailReqDto, coverages);

        resetRecommendPlanItem(singaporeRecommendPlanReqDto);

        log.info("新加坡指定方案: {} 推荐的配置信息 recommendStandardConfigList :{}", JacksonUtils.writeAsString(singaporeRecommendPlanDetailReqDto), JacksonUtils.writeAsString(recommendStandardConfigList));
        return this.getSingaporeRecommendPlanItem(singaporeRecommendPlanReqDto
                , singaporeStandardPlanDtoMap
                , basePrice
                , notRecommendCompanyIdList
                , recommendStandardConfigList
                , standardPlanInfo
                , Objects.requireNonNull(SingaporeStandardPlanTypeEnum.getEnumByValue(planType))
                , new HashMap<>()
        );
    }

    /**
     * 构造详情请求参数
     *
     * @param singaporeRecommendPlanDetailReqDto
     * @param coverages
     * @return
     */
    private static SingaporeRecommendPlanReqDto getSingaporeRecommendPlanReqDto(SingaporeRecommendPlanDetailReqDto singaporeRecommendPlanDetailReqDto, List<SingaporeStandardPlanMainCoverageDto> coverages) {
        SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto = new SingaporeRecommendPlanReqDto();
        singaporeRecommendPlanReqDto.setPipelineNum(singaporeRecommendPlanDetailReqDto.getPipelineNum());
        singaporeRecommendPlanReqDto.setEmployeeType(singaporeRecommendPlanDetailReqDto.getEmployeeType());
        singaporeRecommendPlanReqDto.setAgeMax(singaporeRecommendPlanDetailReqDto.getAgeMax());
        singaporeRecommendPlanReqDto.setAgeMin(singaporeRecommendPlanDetailReqDto.getAgeMin());
        singaporeRecommendPlanReqDto.setPriceMax(singaporeRecommendPlanDetailReqDto.getPriceMax());
        singaporeRecommendPlanReqDto.setPriceMin(singaporeRecommendPlanDetailReqDto.getPriceMin());
        singaporeRecommendPlanReqDto.setCoverages(coverages);
        singaporeRecommendPlanReqDto.setEmployeeNum(singaporeRecommendPlanDetailReqDto.getEmployeeNum());
        singaporeRecommendPlanReqDto.setSpouseNum(singaporeRecommendPlanDetailReqDto.getSpouseNum());
        singaporeRecommendPlanReqDto.setChildNum(singaporeRecommendPlanDetailReqDto.getChildNum());
        singaporeRecommendPlanReqDto.setPlanIdList(singaporeRecommendPlanDetailReqDto.getPlanIdList());
        singaporeRecommendPlanReqDto.setPlanConfigIdList(singaporeRecommendPlanDetailReqDto.getPlanConfigIdList());
        return singaporeRecommendPlanReqDto;
    }

    private BenchmarkSettingDTO getBenchMarkInfo(SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto) {

        BenchmarkSettingDTO benchmarkSettingDTO = new BenchmarkSettingDTO();

        List<SingaporeStandardRecommendConfig.benchmarkAge> benchmarkAgeGroups = singaporeStandardRecommendConfig.getBenchmarkAgeGroups();

        String ageKey = singaporeRecommendPlanReqDto.getAgeMin() + "-" + singaporeRecommendPlanReqDto.getAgeMax();
        SingaporeStandardRecommendConfig.benchmarkAge benchmarkAge = benchmarkAgeGroups.stream().filter(x -> x.getAgeRange().equals(ageKey)).findFirst().orElse(null);
        if (benchmarkAge == null) {
            benchmarkSettingDTO.setIsUseful(Boolean.FALSE);
            return benchmarkSettingDTO;
        }

        String priceString = "0";
        if (StandardEmployeeTypeEnum.MIDDLE_MANAGEMENT.getValue().equals(singaporeRecommendPlanReqDto.getEmployeeType())) {
            priceString = benchmarkAge.getMidManagement();
        }
        if (StandardEmployeeTypeEnum.MANAGEMENT.getValue().equals(singaporeRecommendPlanReqDto.getEmployeeType())) {
            priceString = benchmarkAge.getManagement();
        }
        if (StandardEmployeeTypeEnum.FOREIGN_WORKERS.getValue().equals(singaporeRecommendPlanReqDto.getEmployeeType())) {
            priceString = benchmarkAge.getMidManagement();
        }
        if (StandardEmployeeTypeEnum.ALL.getValue().equals(singaporeRecommendPlanReqDto.getEmployeeType())) {
            priceString = benchmarkAge.getEmployee();
        }

        CurrencyAmount price = CurrencyAmount.valueOf(BigDecimal.valueOf(Double.parseDouble(priceString)), IdentityContext.getBusinessCurrency());

        BigDecimal personNum = BigDecimal.valueOf((long) singaporeRecommendPlanReqDto.getEmployeeNum() + singaporeRecommendPlanReqDto.getSpouseNum() + singaporeRecommendPlanReqDto.getChildNum());
        benchmarkSettingDTO.setPrice(CurrencyAmountUtil.multi(price, personNum));


        Map<String, List<SingaporeStandardPlanMainCoverageDto>> benchmarkCoverages = singaporeStandardRecommendConfig.getBenchmarkCoverages();

        List<SingaporeStandardPlanMainCoverageDto> coverages = benchmarkCoverages.get(singaporeRecommendPlanReqDto.getEmployeeType());
        benchmarkSettingDTO.setCoverages(coverages);

        return benchmarkSettingDTO;
    }

    private SingaporeRecommendPlanItemRspDto getPlanDetail(Long cartDetailId, List<SingaporeStandardPlanDto> standardPlanInfo) {

        SingaporeRecommendPlanItemRspDto singaporeRecommendPlanItemRspDto = new SingaporeRecommendPlanItemRspDto();

        IgStandardOrderDetail igStandardOrderDetail = standardOrderDetailMapper.entity(IgStandardOrderDetail.class).selectOne(cartDetailId, true);

        if (igStandardOrderDetail == null) {
            throw new QuoteException(-1, "plan is expired");
        }

        List<SingaporeStandardPlanDto> singaporeStandardPlanDtoList = standardPlanInfo.stream()
                .filter(x -> igStandardOrderDetail.getPlanIdList().contains(x.getPlanId()))
                .sorted(Comparator.comparingInt(x -> Integer.parseInt(StandardCoverageEnum.getTitleByTags(x.getPlanTag()).getValue())))
                .collect(Collectors.toList());

        Map<Long, SingaporeStandardPlanDto> singaporeStandardPlanDtoMap = singaporeStandardPlanDtoList.stream().collect(Collectors.toMap(SingaporeStandardPlanDto::getPlanId, Function.identity()));

        List<RecommendStandardConfig> recommendStandardConfigList = singaporeStandardPlanDtoList.stream()
                .map(RecommendStandardPlan::getRecommendStandardConfigList).flatMap(Collection::stream)
                .filter(x -> x.getRelation().equals(IgPlanConfigEnums.Relation.ONESELF.getCode()))
                .collect(Collectors.toList());

        SingaporeStandardPlanDto singaporeStandardPlanDto = singaporeStandardPlanDtoList.get(0);

        Optional<RecommendStandardConfig> first = singaporeStandardPlanDto.getRecommendStandardConfigList().stream()
                .filter(x -> igStandardOrderDetail.getPlanConfigIdList().contains(x.getId()) && "0".equals(x.getIsMainConfig()))
                .findFirst();

        if (!first.isPresent()) {
            throw new QuoteException(-1, "main coverage is not select");
        }

        SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto = new SingaporeRecommendPlanReqDto();
        singaporeRecommendPlanReqDto.setEmployeeType(igStandardOrderDetail.getEmployeeType());
        singaporeRecommendPlanReqDto.setAgeMax(igStandardOrderDetail.getAgeMax());
        singaporeRecommendPlanReqDto.setAgeMin(igStandardOrderDetail.getAgeMin());
        singaporeRecommendPlanReqDto.setPriceMax(igStandardOrderDetail.getPriceMax());
        singaporeRecommendPlanReqDto.setPriceMin(igStandardOrderDetail.getPriceMin());
        singaporeRecommendPlanReqDto.setCoverages(this.getRequestCoverages(igStandardOrderDetail, singaporeStandardPlanDtoList));
        singaporeRecommendPlanReqDto.setEmployeeNum(igStandardOrderDetail.getEmployeeNum());
        singaporeRecommendPlanReqDto.setSpouseNum(igStandardOrderDetail.getSpouseNum());
        singaporeRecommendPlanReqDto.setChildNum(igStandardOrderDetail.getChildNum());


        singaporeRecommendPlanItemRspDto.setStandardPlanId(igStandardOrderDetail.getPlanIdList().get(0));
        singaporeRecommendPlanItemRspDto.setPlanType(SingaporeStandardPlanTypeEnum.BUDGET.getCode());
        singaporeRecommendPlanItemRspDto.setPlanTitle(singaporeStandardPlanDto.getPlanTitle());
        singaporeRecommendPlanItemRspDto.setCompanyLogo(singaporeStandardPlanDto.getCompanyLogo());
        singaporeRecommendPlanItemRspDto.setCompanyName(singaporeStandardPlanDto.getCompanyName());
        singaporeRecommendPlanItemRspDto.setCompanyTitle(singaporeStandardPlanDto.getPlanTitle());
        singaporeRecommendPlanItemRspDto.setImageUrl(singaporeStandardPlanDto.getImageUrl());
        singaporeRecommendPlanItemRspDto.setCompanyId(singaporeStandardPlanDto.getCompanyId());
        singaporeRecommendPlanItemRspDto.setCompanyDesc(singaporeStandardPlanDto.getItems().stream().map(ProductDescItemInfo::getContent).collect(Collectors.toList()));
        singaporeRecommendPlanItemRspDto.setTotalAmount(igStandardOrderDetail.getAmount());
        singaporeRecommendPlanItemRspDto.setEligibility(singaporeStandardPlanDto.getEligibility());
        singaporeRecommendPlanItemRspDto.setPolicyWording(singaporeStandardPlanDto.getPolicyWording());
        singaporeRecommendPlanItemRspDto.setCoverages(this.getCoverages(singaporeRecommendPlanReqDto, recommendStandardConfigList, singaporeStandardPlanDtoMap));

        return singaporeRecommendPlanItemRspDto;
    }

    /**
     * 根据购物车详情获取选择的配置
     *
     * @param igStandardOrderDetail
     * @param singaporeStandardPlanDtoList
     * @return
     */
    private List<SingaporeStandardPlanMainCoverageDto> getRequestCoverages(IgStandardOrderDetail igStandardOrderDetail, List<SingaporeStandardPlanDto> singaporeStandardPlanDtoList) {

        List<SingaporeStandardPlanMainCoverageDto> singaporeStandardPlanMainCoverageDtoList = new ArrayList<>();
        Map<Long, SingaporeStandardPlanDto> singaporeStandardPlanDtoMap = singaporeStandardPlanDtoList.stream().collect(Collectors.toMap(SingaporeStandardPlanDto::getPlanId, Function.identity()));

        // 所有选择的主险
        for (Long planId : igStandardOrderDetail.getPlanIdList()) {
            SingaporeStandardPlanMainCoverageDto singaporeStandardPlanMainCoverageDto = new SingaporeStandardPlanMainCoverageDto();

            SingaporeStandardPlanDto singaporeStandardPlanDto = singaporeStandardPlanDtoMap.get(planId);

            // 主险
            Optional<RecommendStandardConfig> first = singaporeStandardPlanDto.getRecommendStandardConfigList().stream().filter(x ->
                            igStandardOrderDetail.getPlanConfigIdList().contains(x.getId())
                                    && "0".equals(x.getIsMainConfig())
                    )
                    .findFirst();

            if (first.isPresent()) {
                RecommendStandardConfig recommendStandardConfig = first.get();

                StandardCoverageEnum standardCoverageEnum = StandardCoverageEnum.getTitleByTags(recommendStandardConfig.getConfigTag());

                List<SingaporeStandardPlanOptionalCoverageItemDto> singaporeStandardPlanOptionalCoverageItemDtoList = new ArrayList<>();

                List<RecommendStandardConfig> recommendStandardConfigList = singaporeStandardPlanDto.getRecommendStandardConfigList().stream()
                        .filter(x -> igStandardOrderDetail.getPlanConfigIdList().contains(x.getId()))
                        .collect(Collectors.toList());

                // 对配置根据id转换为map
                Map<Long, RecommendStandardConfig> recommendStandardConfigMap = recommendStandardConfigList.stream()
                        .collect(Collectors.toMap(RecommendStandardConfig::getId, Function.identity()));

                List<Long> optionalConfigs = recommendStandardConfig.getOptionalConfigs();
                Map<String, List<Long>> bindCodeConfigIdList = new HashMap<>();
                if (CollectionUtil.isNotEmpty(optionalConfigs)) {
                    for (Long optionalConfig : optionalConfigs) {
                        if (recommendStandardConfigMap.containsKey(optionalConfig)) {
                            RecommendStandardConfig recommendStandardConfigExtend = recommendStandardConfigMap.get(optionalConfig);
                            StandardCoverageEnum titleByTags = StandardCoverageEnum.getTitleByTags(recommendStandardConfigExtend.getConfigTag());
                            if (null != titleByTags) {
                                String code = titleByTags.getCode();
                                if (!bindCodeConfigIdList.containsKey(code)) {
                                    bindCodeConfigIdList.put(code, new ArrayList<>());
                                }
                                bindCodeConfigIdList.get(code).add(optionalConfig);
                            }
                        }
                    }
                }


                for (RecommendStandardConfig recommendStandardConfigExtend : recommendStandardConfigList) {
                    StandardCoverageEnum standardCoverageEnumExtend = StandardCoverageEnum.getTitleByTags(recommendStandardConfigExtend.getConfigTag());
                    String code = Objects.requireNonNull(standardCoverageEnumExtend).getCode();
                    // 如果不是绑定配置。不返回
                    if (bindCodeConfigIdList.containsKey(code) && !bindCodeConfigIdList.get(code).contains(recommendStandardConfigExtend.getId())) {
                        continue;
                    }
                    SingaporeStandardPlanOptionalCoverageItemDto singaporeStandardPlanOptionalCoverageItemDto = new SingaporeStandardPlanOptionalCoverageItemDto();
                    singaporeStandardPlanOptionalCoverageItemDto.setCode(standardCoverageEnumExtend.getCode());
                    singaporeStandardPlanOptionalCoverageItemDto.setType(code);
                    singaporeStandardPlanOptionalCoverageItemDto.setTitle(code);
                    singaporeStandardPlanOptionalCoverageItemDtoList.add(singaporeStandardPlanOptionalCoverageItemDto);
                }

                singaporeStandardPlanMainCoverageDto.setPlanId(planId);
                singaporeStandardPlanMainCoverageDto.setPlanConfigId(recommendStandardConfig.getId());
                singaporeStandardPlanMainCoverageDto.setCode(Objects.requireNonNull(standardCoverageEnum).getValue());
                singaporeStandardPlanMainCoverageDto.setType(standardCoverageEnum.getCode());
                singaporeStandardPlanMainCoverageDto.setCoverageTitle(Objects.requireNonNull(standardCoverageEnum).getTitleText());
                singaporeStandardPlanMainCoverageDto.setCoverageCode(standardCoverageEnum.getCode());
                singaporeStandardPlanMainCoverageDto.setCoverageCoreType(standardCoverageEnum.getInsuranceTypeDetail().getCode().toUpperCase());
                singaporeStandardPlanMainCoverageDto.setTitle(Objects.requireNonNull(standardCoverageEnum).getTitleText() + " " + recommendStandardConfig.getName());
                singaporeStandardPlanMainCoverageDto.setConfigName(recommendStandardConfig.getName());
                singaporeStandardPlanMainCoverageDto.setBenefit(recommendStandardConfig.getBenefit());
                singaporeStandardPlanMainCoverageDto.setOptionalRiderList(singaporeStandardPlanOptionalCoverageItemDtoList);

                singaporeStandardPlanMainCoverageDtoList.add(singaporeStandardPlanMainCoverageDto);
            }
        }

        return singaporeStandardPlanMainCoverageDtoList;
    }

    @Override
    public CurrencyAmount getSingaporePlanTotalAmount(SingaporePlanTotalAmountReqDto singaporePlanTotalAmountReqDto) {

        List<Long> planConfigIdList = singaporePlanTotalAmountReqDto.getPlanConfigIdList().stream().distinct().collect(Collectors.toList());
        ResponseVO<List<RecommendStandardPlan>> standardConfigByConfigId = planConfigClient.getStandardConfigByConfigId(planConfigIdList);
        List<RecommendStandardPlan> recommendStandardPlans = standardConfigByConfigId.getData();
        List<RecommendStandardConfig> collect = recommendStandardPlans.stream().flatMap(x -> x.getRecommendStandardConfigList().stream()).collect(Collectors.toList());
        List<RecommendStandardConfig> recommendStandardConfigList = collect.stream().filter(x -> planConfigIdList.contains(x.getId())).collect(Collectors.toList());
        List<RecommendStandardConfigDto> singaporeStandardPlanDtos = JacksonUtils.convertList(recommendStandardConfigList, RecommendStandardConfigDto.class);
        Map<Long, Long> planConfigCompanyMap = new HashMap<>();
        QIgInsurance qIgInsurance = QIgInsurance.ig_insurance;
        QIgPlanConfig qIgPlanConfig = QIgPlanConfig.ig_plan_config;
        List<CompanyAndConfigDto> companyAndConfigDtos = bqlQueryFactory.select(qIgPlanConfig.id.as("plan_config_id"), qIgInsurance.company_id.as("company_id"))
                                                        .from(qIgInsurance).leftJoin(qIgPlanConfig)
                                                        .on(qIgPlanConfig.plan_id.eq(qIgInsurance.plan_id))
                                                        .where(qIgPlanConfig.id.in(planConfigIdList)
                                                                               .and(qIgPlanConfig.delete_flg.eq(false)
                                                                               .and(qIgPlanConfig.status.eq("1"))))
                                                        .findList(true, CompanyAndConfigDto.class);

        if (CollectionUtil.isNotEmpty(companyAndConfigDtos)){
            planConfigCompanyMap = companyAndConfigDtos.stream().collect(Collectors.toMap(CompanyAndConfigDto::getPlanConfigId, CompanyAndConfigDto::getCompanyId));
        }
        Map<Long, CurrencyAmount> configTotalAmountMap = getConfigTotalAmount(planConfigCompanyMap,singaporeStandardPlanDtos, JacksonUtils.covertObject(singaporePlanTotalAmountReqDto, SingaporeRecommendPlanReqDto.class));
        CurrencyAmount currencyAmount = configTotalAmountMap.values().stream()
                .map(x -> CurrencyAmount.valueOf(x.getAmount(), IdentityContext.getBusinessCurrency()))
                .reduce(CurrencyAmountUtil::add).orElseGet(() -> CurrencyAmount.zero(IdentityContext.getBusinessCurrency()));
        return CurrencyAmountUtil.divide(currencyAmount, BigDecimal.valueOf(1L), 2, RoundingMode.HALF_UP);
    }

    @Override
    public List<SingaporeRecommendPlanItemRspDto> getSingaporeRecommendPlanMore(SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto) {
        log.warn("新加坡标品推荐参数：{}", JacksonUtils.writeAsString(singaporeRecommendPlanReqDto));

        resetRecommendPlanItem(singaporeRecommendPlanReqDto);

        // 换取信息补全的对象
        List<SingaporeStandardPlanDto> standardPlanDtoList1 = getStandardPlanInfo(new ArrayList<>());

        List<SingaporeStandardPlanDto> singaporeStandardPlanDtoList = standardPlanDtoList1.stream()
                .filter(x -> null != x.getEmployeeCategory() && x.getEmployeeCategory().equals(singaporeRecommendPlanReqDto.getEmployeeType()))
                .collect(Collectors.toList());

        String employeeType = singaporeRecommendPlanReqDto.getEmployeeType();
        List<RecommendStandardConfig> recommendStandardConfigList = singaporeStandardPlanDtoList.stream()
                .map(RecommendStandardPlan::getRecommendStandardConfigList)
                .flatMap(Collection::stream)
                .filter(x -> x.getRelation().equals(IgPlanConfigEnums.Relation.ONESELF.getCode()))
                .filter(v -> !employeeType.equals(StandardEmployeeTypeEnum.FOREIGN_WORKERS.getValue()) || v.getConfigTag().contains(StandardEmployeeTypeEnum.FOREIGN_WORKERS.getValue()))
                .collect(Collectors.toList());

        Map<Long, Long> planConfigCompanyMap = new HashMap<>();
        singaporeStandardPlanDtoList.forEach(standardPlanDto -> {
            List<Long> planConfigIdList = standardPlanDto.getRecommendStandardConfigList().stream().map(IgPlanConfig::getId)
                                                         .collect(Collectors.toList());
            planConfigIdList.forEach(planConfigId -> planConfigCompanyMap.put(planConfigId,standardPlanDto.getCompanyId()));
        });
        List<SingaporeRecommendPlanItemRspDto> singaporeRecommendPlanItemRspDtoList = new ArrayList<>();

        SingaporeStandardPlanTypeEnum planTypeEnum = SingaporeStandardPlanTypeEnum.getEnumByValue(singaporeRecommendPlanReqDto.getPlanType());

        SingaporeRecommendPlanDetailReqDto singaporeRecommendPlanDetailReqDto = JacksonUtils.covertObject(singaporeRecommendPlanReqDto, SingaporeRecommendPlanDetailReqDto.class);
        switch (planTypeEnum) {
            case Benchmark:
                List<Long> companyList = new ArrayList<>();
                SingaporeRecommendPlanItemRspDto benchmarkRecommendPlanItemBase = getSingaporePlan(singaporeRecommendPlanDetailReqDto);
                if (null != benchmarkRecommendPlanItemBase && null != benchmarkRecommendPlanItemBase.getTotalAmount()) {
                    singaporeRecommendPlanItemRspDtoList.add(benchmarkRecommendPlanItemBase);
                    companyList.add(benchmarkRecommendPlanItemBase.getCompanyId());
                }

                SingaporeRecommendPlanItemRspDto benchmarkRecommendPlanItemLow = this.getBenchmarkRecommendPlanItem(planConfigCompanyMap,companyList,
                        singaporeRecommendPlanReqDto,
                        recommendStandardConfigList,
                        singaporeStandardPlanDtoList,
                        new HashMap<>(),
                        2
                );
                if (null != benchmarkRecommendPlanItemLow && null != benchmarkRecommendPlanItemLow.getTotalAmount()) {
                    benchmarkRecommendPlanItemLow.setPriceMode(2);
                    singaporeRecommendPlanItemRspDtoList.add(benchmarkRecommendPlanItemLow);
                    companyList.add(benchmarkRecommendPlanItemLow.getCompanyId());
                }

                SingaporeRecommendPlanItemRspDto benchmarkRecommendPlanItemHigh = this.getBenchmarkRecommendPlanItem(planConfigCompanyMap,companyList,
                        singaporeRecommendPlanReqDto,
                        recommendStandardConfigList,
                        singaporeStandardPlanDtoList,
                        new HashMap<>(),
                        3
                );
                if (null != benchmarkRecommendPlanItemHigh && null != benchmarkRecommendPlanItemHigh.getTotalAmount()) {
                    benchmarkRecommendPlanItemHigh.setPriceMode(3);
                    singaporeRecommendPlanItemRspDtoList.add(benchmarkRecommendPlanItemHigh);
                }

                break;
            case BUDGET:
                List<Long> companyListFriend = new ArrayList<>();
                SingaporeRecommendPlanItemRspDto friendRecommendPlanItemBase = getSingaporePlan(singaporeRecommendPlanDetailReqDto);
                if (null != friendRecommendPlanItemBase && null != friendRecommendPlanItemBase.getTotalAmount()) {
                    singaporeRecommendPlanItemRspDtoList.add(friendRecommendPlanItemBase);
                    companyListFriend.add(friendRecommendPlanItemBase.getCompanyId());
                }

                SingaporeRecommendPlanItemRspDto friendRecommendPlanItemLow = this.getFriendRecommendPlanItem(planConfigCompanyMap,companyListFriend,
                        singaporeRecommendPlanReqDto,
                        recommendStandardConfigList,
                        singaporeStandardPlanDtoList,
                        new HashMap<>(),
                        2
                );

                if (null != friendRecommendPlanItemLow && null != friendRecommendPlanItemLow.getTotalAmount()) {
                    singaporeRecommendPlanItemRspDtoList.add(friendRecommendPlanItemLow);
                    friendRecommendPlanItemLow.setPriceMode(2);
                    companyListFriend.add(friendRecommendPlanItemLow.getCompanyId());
                }

                SingaporeRecommendPlanItemRspDto friendRecommendPlanItemHigh = this.getFriendRecommendPlanItem(planConfigCompanyMap,companyListFriend,
                        singaporeRecommendPlanReqDto,
                        recommendStandardConfigList,
                        singaporeStandardPlanDtoList,
                        new HashMap<>(),
                        3
                );

                // 如果没有更多方案
                if (null == friendRecommendPlanItemLow && null == friendRecommendPlanItemHigh) {
                    throw new InsgeekException(-1, MessageUtil.get("b_b_quote_sgp_no_more_plans"));
                }

                if (null != friendRecommendPlanItemHigh && null != friendRecommendPlanItemHigh.getTotalAmount()) {
                    friendRecommendPlanItemHigh.setPriceMode(3);
                    singaporeRecommendPlanItemRspDtoList.add(friendRecommendPlanItemHigh);
                }
                break;
            case MOST:
                List<Long> companyListMost = new ArrayList<>();
                SingaporeRecommendPlanItemRspDto mostRecommendPlanItemBase = getSingaporePlan(singaporeRecommendPlanDetailReqDto);
                if (null != mostRecommendPlanItemBase && null != mostRecommendPlanItemBase.getTotalAmount()) {
                    singaporeRecommendPlanItemRspDtoList.add(mostRecommendPlanItemBase);
                    companyListMost.add(mostRecommendPlanItemBase.getCompanyId());
                }

                SingaporeRecommendPlanItemRspDto mostRecommendPlanItemLow = this.getMostRecommendPlanItem(planConfigCompanyMap,
                        companyListMost,
                        singaporeRecommendPlanReqDto,
                        recommendStandardConfigList,
                        singaporeStandardPlanDtoList,
                        new HashMap<>(),
                        2
                );

                if (null != mostRecommendPlanItemLow && null != mostRecommendPlanItemLow.getTotalAmount()) {
                    singaporeRecommendPlanItemRspDtoList.add(mostRecommendPlanItemLow);
                    mostRecommendPlanItemLow.setPriceMode(2);
                    companyListMost.add(mostRecommendPlanItemLow.getCompanyId());
                }

                SingaporeRecommendPlanItemRspDto mostRecommendPlanItemHigh = this.getMostRecommendPlanItem(planConfigCompanyMap,
                        companyListMost,
                        singaporeRecommendPlanReqDto,
                        recommendStandardConfigList,
                        singaporeStandardPlanDtoList,
                        new HashMap<>(),
                        3
                );

                // 如果没有更多方案
                if (null == mostRecommendPlanItemLow && null == mostRecommendPlanItemHigh) {
                    throw new InsgeekException(-1, MessageUtil.get("b_b_quote_sgp_no_more_plans"));
                }

                if (null != mostRecommendPlanItemHigh && null != mostRecommendPlanItemHigh.getTotalAmount()) {
                    mostRecommendPlanItemHigh.setPriceMode(3);
                    singaporeRecommendPlanItemRspDtoList.add(mostRecommendPlanItemHigh);
                }
                break;
            default:
                break;
        }

        return singaporeRecommendPlanItemRspDtoList;
    }

    /**
     * 判断是否为1人特殊规则
     */
    private boolean isOnePersonSpecialRule(SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto, int personNumTotal) {
        int personNum = null == singaporeRecommendPlanReqDto.getEmployeeNum() ? 0 : singaporeRecommendPlanReqDto.getEmployeeNum();
        if (null != singaporeRecommendPlanReqDto.getSpouseNum()) {
            personNum += singaporeRecommendPlanReqDto.getSpouseNum();
        }
        if (null != singaporeRecommendPlanReqDto.getChildNum()) {
            personNum += singaporeRecommendPlanReqDto.getChildNum();
        }
        return personNum <= personNumTotal;
    }

    private List<StandardPlanDto> getCalcedPlanList(RecommendPlanReqDto recommendPlanReqDto) {
        List<StandardPlanDto> allStandardPlan = standardPlanMapper.getPlanList();

        List<StandardPriceCalcDto> standardPriceCalcDtoList = new ArrayList<>();
        for (StandardPlanDto standardPlanDto : allStandardPlan) {
            StandardPriceCalcDto standardPriceCalcDto = new StandardPriceCalcDto();
            standardPriceCalcDto.setMinAge(recommendPlanReqDto.getMinAge());
            standardPriceCalcDto.setMaxAge(recommendPlanReqDto.getMaxAge());
            standardPriceCalcDto.setPersonNum(recommendPlanReqDto.getPersonNum());
            standardPriceCalcDto.setFemaleRatio(recommendPlanReqDto.getFemaleRatio());
            standardPriceCalcDto.setJobCategory(recommendPlanReqDto.getJobCategory());
            standardPriceCalcDto.setCity(recommendPlanReqDto.getCity());
            standardPriceCalcDto.setChannel(CustomContext.getChannelId().toString());
            standardPriceCalcDto.setType(standardPlanDto.getType());
            standardPriceCalcDto.setPlanConfigId(standardPlanDto.getPlanConfigId());
            standardPriceCalcDto.setPrice(standardPlanDto.getPrice());
            standardPriceCalcDtoList.add(standardPriceCalcDto);
        }
        getPlanPrice(standardPriceCalcDtoList);

        Map<Long, StandardPriceCalcDto> standardPriceCalcDtoMap = standardPriceCalcDtoList.stream()
                .collect(Collectors.toMap(StandardPriceCalcDto::getPlanConfigId, dto -> dto, (existing, replacement) -> existing));

        for (StandardPlanDto standardPlanDto : allStandardPlan) {
            standardPlanDto.setOriginPrice(standardPlanDto.getPrice());
            if (null != standardPriceCalcDtoMap.get(standardPlanDto.getPlanConfigId()).getCalculatePrice()) {
                standardPlanDto.setPrice(standardPriceCalcDtoMap.get(standardPlanDto.getPlanConfigId()).getCalculatePrice());
                standardPlanDto.setIsSupported(true);
            }
        }

        return allStandardPlan.stream().filter(StandardPlanDto::getIsSupported).collect(Collectors.toList());
    }

    @Override
    public Page<StandardProductInfoRspDto> getPlanListForProduct(StandardProductReqDto standardProductReqDto) {
        Page<StandardProductInfoRspDto> pageResult = standardPlanMapper.getPlanListForProduct(
                standardProductReqDto.getStandardType(),
                standardProductReqDto.getPageNum(),
                standardProductReqDto.getPageSize(),
                CustomContext.getChannelId(),
                standardProductReqDto.getProductName(),
                standardProductReqDto.getCompanyId(),
                StandardShelfStatusEnum.ON_SHELF.getValue()
        );
        List<StandardProductInfoRspDto> standardProductList = pageResult.getData();
        // 填充产品描述
        fillProductDescriptionList(standardProductList);
        // img从ossKey替换为地址
        replaceImgOssKeyToUrl(standardProductList);
        return pageResult;
    }

    /**
     * 图片从ossKey替换为url
     */
    private void replaceImgOssKeyToUrl(List<StandardProductInfoRspDto> standardProductList) {
        if (CollectionUtil.isEmpty(standardProductList)) {
            return;
        }
        //  提取所有 img 和 companyLogo 的 oss key，去除 null
        List<String> ossKeyList = standardProductList.stream()
                .flatMap(dto -> Stream.of(dto.getImg(), dto.getCompanyLogo()))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        //  若无有效 key，无需请求远程服务
        if (CollectionUtil.isEmpty(ossKeyList)) {
            return;
        }
        //  请求文件服务获取对应 URL 列表
        ResponseVO<List<UploadResultDto>> fileListResponseVO = fileClient.listFile(ossKeyList);
        //  获取结果列表
        List<UploadResultDto> fileList = CommonUtil.getResponseData(fileListResponseVO);
        if (CollectionUtil.isEmpty(fileList)) {
            return;
        }
        // 构建 key → UploadResultDto 映射，便于后续替换
        Map<String, UploadResultDto> uploadResultMapping = fileList.stream()
                .collect(Collectors.toMap(
                        UploadResultDto::getKey,
                        Function.identity(),
                        (v1, v2) -> v1
                ));
        // 替换每条数据的 img 和 companyLogo 字段为对应 URL
        for (StandardProductInfoRspDto dto : standardProductList) {
            // 替换标品img
            String imgKey = dto.getImg();
            UploadResultDto imgResult = uploadResultMapping.get(imgKey);
            if (imgResult != null) {
                dto.setImg(imgResult.getLargeUrl());
            }

            // 替换公司 logo
            String logoKey = dto.getCompanyLogo();
            UploadResultDto logoResult = uploadResultMapping.get(logoKey);
            if (logoResult != null) {
                dto.setCompanyLogo(logoResult.getLargeUrl());
            }
        }
    }


    /**
     * 填充产品描述
     */
    private void fillProductDescriptionList(List<StandardProductInfoRspDto> standardProductList) {
        if (CollectionUtil.isNotEmpty(standardProductList)) {
            List<Long> standardPlanIdList = standardProductList.stream().map(StandardProductInfoRspDto::getStandardPlanId).collect(Collectors.toList());
            // 获取产品描述
            List<IgStandardPlanInfo> standardPlanInfoList = standardPlanMapper.getStandardPlanInfo(
                    standardPlanIdList,
                    StandardPlanDetailTypeEnum.TYPE2.getValue(),
                    Boolean.TRUE);
            Map<Long, List<IgStandardPlanInfo>> standardPlanInfoMapping = standardPlanInfoList.stream().collect(Collectors.groupingBy(IgStandardPlanInfo::getStandardPlanId));
            for (StandardProductInfoRspDto standardProductInfo : standardProductList) {
                Long standardPlanId = standardProductInfo.getStandardPlanId();
                // 获取方案描述
                List<IgStandardPlanInfo> igStandardPlanInfoList = standardPlanInfoMapping.getOrDefault(standardPlanId, new ArrayList<>());
                // 设置方案描述
                standardProductInfo.setProductDescriptionList(assembleProductDescriptionList(igStandardPlanInfoList));
            }
        }
    }

    /**
     * 组装方案描述列表
     */
    private List<StandardPlanDetailInfoItemRspDto> assembleProductDescriptionList(List<IgStandardPlanInfo> igStandardPlanInfoList) {
        List<StandardPlanDetailInfoItemRspDto> standardPlanDetailInfoItemList = new ArrayList<>();
        for (IgStandardPlanInfo igStandardPlanInfo : igStandardPlanInfoList) {
            StandardPlanDetailInfoItemRspDto standardPlanDetailInfoItem = new StandardPlanDetailInfoItemRspDto();
            standardPlanDetailInfoItem.setTitle(igStandardPlanInfo.getTitle());
            standardPlanDetailInfoItem.setContent(igStandardPlanInfo.getContent());
            standardPlanDetailInfoItemList.add(standardPlanDetailInfoItem);
        }
        return standardPlanDetailInfoItemList;
    }

    @Override
    public List<StandardPlanInfoRspDto> getPlanListForInsuredInfo(String standardType, List<Long> standardPlanIdList, String companyId, String productName) {
        // 如果传了保司id，先构造标品方案id列表
        standardPlanIdList = constructStandardPlanIdListByCompany(standardType, standardPlanIdList, companyId);
        // 查询标品方案列表
        List<StandardPlanInfoRspDto> standardPlanInfoList = standardPlanMapper.getPlanListForInsuredInfo(standardPlanIdList, productName);
        // 填充责任列表
        fillUIDutyList(standardPlanInfoList);
        return standardPlanInfoList;
    }

    @Override
    public List<StandardPlanForBackendDto> getChannelStandardPlanList(Long channelId) {
        QIgStandardPlan qIgStandardPlan = QIgStandardPlan.ig_standard_plan;
        QIgPlan qIgPlan = QIgPlan.ig_plan;
        QIgInsurance qIgInsurance = QIgInsurance.ig_insurance;
        QIgCompany qIgCompany = QIgCompany.ig_company;

        List<StandardPlanForBackendDto> standardPlanForBackendDtoList = bqlQueryFactory.select(Expressions.stringPath("ig_standard_plan.*"),
                        qIgCompany.company_name.as("company_name")
                ).from(qIgStandardPlan)
                .join(qIgPlan).on(qIgStandardPlan.plan_id.eq(qIgPlan.id))
                .join(qIgInsurance).on(qIgPlan.id.eq(qIgInsurance.plan_id))
                .join(qIgCompany).on(qIgCompany.id.eq(qIgInsurance.company_id))
                .where(qIgStandardPlan.channel_id.eq(channelId))
                .findList(true, StandardPlanForBackendDto.class);
        log.debug("channel_plan_list:{}", standardPlanForBackendDtoList);

        return standardPlanForBackendDtoList;
    }

    @Override
    public Integer updateStandardPlanInfo(IgStandardPlan igStandardPlan) {
        int i = standardPlanMapper.entity(IgStandardPlan.class).updateOne(igStandardPlan, true);
        log.debug("update standard plan:{}", i);
        return i;
    }

    @Override
    public String getInsuredProcessNotice(Long standardPlanId) {
        // 获取标品方案信息
        List<IgStandardPlanInfo> standardPlanInfo = standardPlanMapper.getStandardPlanInfo(standardPlanId);
        StandardPlanDto planInfo = standardPlanMapper.getPlanInfo(standardPlanId);
        // 投保流程须知
        StandardPlanDetailTabItemRspDto content = getTab(standardPlanInfo, StandardPlanDetailTypeEnum.TYPE4.getValue());
        return content != null ? content.getHtmlContent().replace("{company}", planInfo.getCompanyName()) : "";
    }

    @Override
    public boolean getPlanShelfStatus(List<Long> standardPlanIdList) {
        List<StandardPlanShelfStatusDto> planShelfStatusList = standardPlanMapper.getPlanShelfStatusByIdList(standardPlanIdList);
        // 判断是否存在下架产品，如果存在拼接提示语，示例：xxx，xxx，xxx产品已下架
        List<String> offShelfProductNames = planShelfStatusList.stream()
                .filter(dto -> StandardShelfStatusEnum.isOffShelf(dto.getShelfStatus()))
                .map(dto -> "【" + dto.getTitle() + "】")
                .collect(Collectors.toList());
        // 判断是否存在下架产品
        if (CollectionUtil.isNotEmpty(offShelfProductNames)) {
            throw new QuoteException(-1, String.join("，", offShelfProductNames) + "产品已下架");
        }
        // 构造返回结果
        return true;
    }

    @Override
    public List<CalcRecommendPlanPriceRspDto> getCalcSingaporePriceList(String pipeLineNumber) {

        List<CalcRecommendPlanPriceRspDto> calcRecommendPlanPriceRspDtoList = new ArrayList<>();

        List<IgSgQuotePersonInfoDetail> personInfoDetailList = getPersonInfoDetailList(pipeLineNumber);

        // 按照emplye_category进行分组
        Map<String, List<IgSgQuotePersonInfoDetail>> personInfoDetailMap = personInfoDetailList.stream().collect(Collectors.groupingBy(IgSgQuotePersonInfoDetail::getEmployeeCategory));

        personInfoDetailMap.forEach((employeeCategory, personList) -> {

            CalcRecommendPlanPriceRspDto calcRecommendPlanPriceRspDto = new CalcRecommendPlanPriceRspDto();
            SingaporeRecommendPriceListReqDto singaporeRecommendPriceListReqDto = new SingaporeRecommendPriceListReqDto();
            singaporeRecommendPriceListReqDto.setEmployeeType(employeeCategory);

            // 根据birth计算平均年龄
            CalcAgeRangeEnum calcAgeRangeEnum = CalcAgeRangeEnum.getByAge(getAvgAge(personList));

            singaporeRecommendPriceListReqDto.setAgeMin(calcAgeRangeEnum.getMin());
            singaporeRecommendPriceListReqDto.setAgeMax(calcAgeRangeEnum.getMax());
            singaporeRecommendPriceListReqDto.setEmployeeNum(personList.size());
            singaporeRecommendPriceListReqDto.setSpouseNum(0);
            singaporeRecommendPriceListReqDto.setChildNum(0);


            List<SingaporeRecommendPlanPriceRspDto> singaporePriceList = getSingaporePriceList(singaporeRecommendPriceListReqDto);

            calcRecommendPlanPriceRspDto.setEmployeeCategory(employeeCategory);
            calcRecommendPlanPriceRspDto.setPriceList(singaporePriceList);

            calcRecommendPlanPriceRspDtoList.add(calcRecommendPlanPriceRspDto);
        });

        return calcRecommendPlanPriceRspDtoList;
    }

    @Override
    public List<SingaporeRecommendPlanItemRspDto> getSingaporeRecommendPlanMoreNew(List<SingaporeRecommendPlanReqDto> singaporeRecommendPlanReqDtoList) {
        List<SingaporeRecommendPlanItemRspDto> singaporeRecommendPlanItemRspDtoList = new ArrayList<>();
        // singaporeRecommendPlanReqDtoList 根据 employee_category 排序
        singaporeRecommendPlanReqDtoList.sort(Comparator.comparing(SingaporeRecommendPlanReqDto::getEmployeeType));
        for (SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto : singaporeRecommendPlanReqDtoList) {
            List<SingaporeRecommendPlanItemRspDto> singaporeRecommendPlanMore = getSingaporeRecommendPlanMore(singaporeRecommendPlanReqDto);
            for (SingaporeRecommendPlanItemRspDto singaporeRecommendPlanItemRspDto : singaporeRecommendPlanMore) {
                setCompanyId(singaporeRecommendPlanReqDtoList, singaporeRecommendPlanItemRspDto.getPriceMode(), singaporeRecommendPlanItemRspDto.getCompanyId());
                singaporeRecommendPlanItemRspDto.setEmployeeType(singaporeRecommendPlanReqDto.getEmployeeType());
            }
            singaporeRecommendPlanItemRspDtoList.addAll(singaporeRecommendPlanMore);
        }
        return singaporeRecommendPlanItemRspDtoList;
    }


    @Autowired
    private DataMapper<IgPlan> igPlanDataMapper;
    @Autowired
    private DataMapper<IgPlanConfig> igPlanConfigDataMapper;

    @Override
    public List<SingaporeStandardPlanMainCoverageDto> getChangeCoverages(SingaporeGetChangeCoveragesReqDto singaporeGetChangeCoveragesReqDto) {

        // 返回新主险范围对象
        List<SingaporeStandardPlanMainCoverageDto> singaporeStandardPlanMainCoverageDtos = new ArrayList<>();

        // 原始主险id
        Long originalMainPlanConfigId = singaporeGetChangeCoveragesReqDto.getPlanConfigId();
        // 查询原始主险方案
        IgPlanConfig originalMainPlanConfig = igPlanConfigDataMapper.entity(IgPlanConfig.class).selectOne(originalMainPlanConfigId, true);

        // 原始主险
        List<String> configTag = originalMainPlanConfig.getConfigTag();
        StandardCoverageEnum coverageEnum = StandardCoverageEnum.getTitleByTags(configTag);

        // 原始主险所在方案id
        Long originalMainPlanId = originalMainPlanConfig.getPlanId();

        // 查询当前主险所在ig_plan下的其他相同【主险ig_plan_config】
        DataCondition<IgPlanConfig> mainPlanCondition = new DataCondition<>();
        mainPlanCondition.eq("status", 1);
        mainPlanCondition.eq("plan_id", originalMainPlanId);
        List<IgPlanConfig> igPlanConfigList = igPlanConfigDataMapper.entity(IgPlanConfig.class)
                .select(mainPlanCondition, true);
        // igPlanConfigList 按 created_at asc 排序
        igPlanConfigList.sort(Comparator.comparing(IgPlanConfig::getCreatedAt));

        // 筛选出符合主险类型的ig_plan_config
        List<IgPlanConfig> igPlanConfigListFilter = igPlanConfigList.stream().filter(x -> {
            List<String> planTag = x.getConfigTag();
            return coverageEnum != null && planTag.contains(coverageEnum.getCode())
                    && x.getRelation().equals(IgPlanConfigEnums.Relation.ONESELF.getCode())
                    && (!configTag.contains(StandardEmployeeTypeEnum.FOREIGN_WORKERS.getTag()) || x.getConfigTag().contains(StandardEmployeeTypeEnum.FOREIGN_WORKERS.getTag()));
        }).collect(Collectors.toList());

        // 所有配置的责任列表
        List<Long> planConfigList = igPlanConfigList.stream()
                .map(IgPlanConfig::getId).collect(Collectors.toList());
        Map<Long, List<ProductClientUIDTO>> planConfigDutyDetailBatchData = getProductUIByConfigs(planConfigList);

        for (IgPlanConfig igPlanConfig : igPlanConfigListFilter) {
            // 主险
            SingaporeStandardPlanMainCoverageDto singaporeStandardPlanMainCoverageDto = new SingaporeStandardPlanMainCoverageDto();

            singaporeStandardPlanMainCoverageDto.setPlanId(igPlanConfig.getPlanId());
            singaporeStandardPlanMainCoverageDto.setPlanConfigId(igPlanConfig.getId());
            singaporeStandardPlanMainCoverageDto.setCode(coverageEnum.getValue());
            singaporeStandardPlanMainCoverageDto.setType(coverageEnum.getCode());
            singaporeStandardPlanMainCoverageDto.setTitle(String.format("%s %s", coverageEnum.getTitleText(), igPlanConfig.getName()));
            singaporeStandardPlanMainCoverageDto.setConfigName(igPlanConfig.getName());
            singaporeStandardPlanMainCoverageDto.setBenefit(igPlanConfig.getBenefit());
            singaporeStandardPlanMainCoverageDto.setCoverageTitle(coverageEnum.getTitleText());
            singaporeStandardPlanMainCoverageDto.setAnnualPolicyLimit(igPlanConfig.getAnnualPolicyLimit());
            singaporeStandardPlanMainCoverageDto.setCoverageCode(coverageEnum.getCode());
            singaporeStandardPlanMainCoverageDto.setCoverageCoreType(coverageEnum.getInsuranceTypeDetail().getCode().toUpperCase());
            singaporeStandardPlanMainCoverageDto.setDutyList(planConfigDutyDetailBatchData.get(igPlanConfig.getId()));

            singaporeStandardPlanMainCoverageDtos.add(singaporeStandardPlanMainCoverageDto);
        }


        // 遍历主险添加附加险
        for (SingaporeStandardPlanMainCoverageDto singaporeStandardPlanMainCoverageDto : singaporeStandardPlanMainCoverageDtos) {
            // 重新找出主险数据
            IgPlanConfig masterConfig = igPlanConfigList.stream().filter(x -> x.getId().equals(singaporeStandardPlanMainCoverageDto.getPlanConfigId())).findFirst().get();

            // 找出主险都配置了哪些附加险险种
            List<String> relationCoverageTagsCode;
            if (masterConfig.getConfigTag() != null) {
                List<Long> optionalConfigs = masterConfig.getOptionalConfigs();
                List<IgPlanConfig> optionalConfigsData = igPlanConfigList.stream().filter(y -> optionalConfigs.contains(y.getId())).collect(Collectors.toList());
                // 收集optionalConfigsData下所有   StandardCoverageEnum.getTitleByTags(configTag)
                relationCoverageTagsCode = optionalConfigsData.stream().map(x -> StandardCoverageEnum.getTitleByTags(x.getConfigTag()).getCode()).collect(Collectors.toList());
            } else {
                relationCoverageTagsCode = new ArrayList<>();
            }

            // 筛选出符合主险类型的ig_plan_config
            List<IgPlanConfig> thisFjxPlanConfig = igPlanConfigList.stream().filter(x -> {
                // 附加险的险种
                StandardCoverageEnum thisCoverageEnum = StandardCoverageEnum.getTitleByTags(x.getConfigTag());
                // 筛选
                return thisCoverageEnum != null && !thisCoverageEnum.getIsMaster()// 附加险
                        && x.getRelation().equals(IgPlanConfigEnums.Relation.ONESELF.getCode())// 本人方案
                        && (!configTag.contains(StandardEmployeeTypeEnum.FOREIGN_WORKERS.getTag()) || x.getConfigTag().contains(StandardEmployeeTypeEnum.FOREIGN_WORKERS.getTag())) // 区分外劳
                        && (
                        CollectionUtils.isEmpty(masterConfig.getOptionalConfigs()) // 没有关联查询所有附加险
                                || !relationCoverageTagsCode.contains(thisCoverageEnum.getCode()) // 没有关联险种 该险种都返回
                                || masterConfig.getOptionalConfigs().contains(x.getId()) // 如果有关联关系 该险种那只填充关联的附加险
                        );
            }).collect(Collectors.toList());

            // thisFjxPlanConfig 按 StandardCoverageEnum.getTitleByTags(x.getConfigTag()) 分组
            Map<StandardCoverageEnum, List<IgPlanConfig>> thisFjxPlanConfigGroup = thisFjxPlanConfig.stream().collect(Collectors.groupingBy(x -> StandardCoverageEnum.getTitleByTags(x.getConfigTag())));
            List<SingaporeStandardPlanOptionalCoverageGroupDto> optionalRiderGroupList = new ArrayList<>();

            for (Map.Entry<StandardCoverageEnum, List<IgPlanConfig>> entry : thisFjxPlanConfigGroup.entrySet()) {
                SingaporeStandardPlanOptionalCoverageGroupDto singaporeStandardPlanOptionalCoverageGroupDto = new SingaporeStandardPlanOptionalCoverageGroupDto();
                singaporeStandardPlanOptionalCoverageGroupDto.setCode(entry.getKey().getCode());
                singaporeStandardPlanOptionalCoverageGroupDto.setTitle(entry.getKey().getCode());
                List<SingaporeStandardPlanOptionalCoverageItemDto> optionalRiderList = new ArrayList<>();
                boolean firstData = true;
                for (IgPlanConfig x : entry.getValue()) {
                    SingaporeStandardPlanOptionalCoverageItemDto singaporeStandardPlanOptionalCoverageItemDto = new SingaporeStandardPlanOptionalCoverageItemDto();
                    singaporeStandardPlanOptionalCoverageItemDto.setPlanId(x.getPlanId());
                    singaporeStandardPlanOptionalCoverageItemDto.setPlanConfigId(x.getId());
                    singaporeStandardPlanOptionalCoverageItemDto.setCode(entry.getKey().getCode());
                    singaporeStandardPlanOptionalCoverageItemDto.setType(entry.getKey().getCode());
                    singaporeStandardPlanOptionalCoverageItemDto.setTitle(String.format("%s %s", x.getName(), x.getBenefit()));
                    singaporeStandardPlanOptionalCoverageItemDto.setConfigName(x.getName());
                    singaporeStandardPlanOptionalCoverageItemDto.setBenefit(x.getBenefit());
                    singaporeStandardPlanOptionalCoverageItemDto.setCoverageTitle(entry.getKey().getTitleText());
                    singaporeStandardPlanOptionalCoverageItemDto.setCoverageCode(entry.getKey().getCode());
                    singaporeStandardPlanOptionalCoverageItemDto.setCoverageCoreType(entry.getKey().getInsuranceTypeDetail().getCode().toUpperCase());
                    // IsDefault 默认勾选
                    // 循环的第一条数据设置默认勾选
                    if (firstData) {
                        singaporeStandardPlanOptionalCoverageItemDto.setIsDefault(
                                !CollectionUtils.isEmpty(singaporeGetChangeCoveragesReqDto.getCodeList())
                                        && singaporeGetChangeCoveragesReqDto.getCodeList().contains(entry.getKey().getCode()));
                    } else {
                        singaporeStandardPlanOptionalCoverageItemDto.setIsDefault(false);
                    }
                    firstData = false;

                    singaporeStandardPlanOptionalCoverageItemDto.setDutyList(planConfigDutyDetailBatchData.get(x.getId()));
                    optionalRiderList.add(singaporeStandardPlanOptionalCoverageItemDto);
                }
                singaporeStandardPlanOptionalCoverageGroupDto.setOptionalRiderList(optionalRiderList);
                optionalRiderGroupList.add(singaporeStandardPlanOptionalCoverageGroupDto);
            }
            singaporeStandardPlanMainCoverageDto.setOptionalRiderGroupList(optionalRiderGroupList);

        }

        log.warn("所有责任组装结束：{}", JacksonUtils.writeAsString(singaporeStandardPlanMainCoverageDtos));
        return singaporeStandardPlanMainCoverageDtos;
    }

    private void setCompanyId(List<SingaporeRecommendPlanReqDto> singaporeRecommendPlanReqDtoList, int i, Long companyId) {
        for (SingaporeRecommendPlanReqDto singaporeRecommendPlanReqDto : singaporeRecommendPlanReqDtoList) {
            if (i == 1) {
                singaporeRecommendPlanReqDto.setCompanyId1(companyId);
            }
            if (i == 2) {
                singaporeRecommendPlanReqDto.setCompanyId2(companyId);
            }
            if (i == 3) {
                singaporeRecommendPlanReqDto.setCompanyId3(companyId);
            }
        }
    }

    private Integer getAvgAge(List<IgSgQuotePersonInfoDetail> personInfoDetailList) {

        List<String> birthList = personInfoDetailList.stream().map(IgSgQuotePersonInfoDetail::getBirthText).collect(Collectors.toList());
        // 当前日期
        LocalDate currentDate = LocalDate.now();

        // 解析日期并计算年龄
        List<Integer> ages = new ArrayList<>();
        for (String dateString : birthList) {
            // 解析日期字符串为 LocalDate
            LocalDate birthDate = LocalDate.parse(dateString.substring(0, 10), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            // 计算年龄
            int age = Period.between(birthDate, currentDate).getYears();
            ages.add(age);
        }

        // 计算平均年龄
        double averageAge = ages.stream().mapToInt(Integer::intValue).average().orElse(0.0);

        return BigDecimal.valueOf(averageAge).setScale(2, RoundingMode.HALF_UP).intValue();
    }

    private List<IgSgQuotePersonInfoDetail> getPersonInfoDetailList(String pipeLineNumber) {
        QIgSgQuotePersonInfoDetail qIgSgQuotePersonInfoDetail = QIgSgQuotePersonInfoDetail.ig_sg_quote_person_info_detail;
        QIgSgQuotePersonInfo qIgSgQuotePersonInfo = QIgSgQuotePersonInfo.ig_sg_quote_person_info;
        return bqlQueryFactory.select(Expressions.stringPath("ig_sg_quote_person_info_detail.*"))
                .from(qIgSgQuotePersonInfoDetail)
                .join(qIgSgQuotePersonInfo).on(qIgSgQuotePersonInfo.id.eq(qIgSgQuotePersonInfoDetail.sg_quote_person_info_id))
                .where(qIgSgQuotePersonInfo.number.eq(pipeLineNumber))
                .findList(true, IgSgQuotePersonInfoDetail.class);
    }

    /**
     * 填充责任列表
     */
    private void fillUIDutyList(List<StandardPlanInfoRspDto> standardPlanInfoList) {
        if (CollectionUtil.isNotEmpty(standardPlanInfoList)) {
            // 调用核心ui接口获取责任列表
            List<Long> planConfigList = standardPlanInfoList.stream().map(StandardPlanInfoRspDto::getPlanConfigId).collect(Collectors.toList());
            ResponseVO<Map<Long, List<ProductClientUIDTO>>> planConfigDutyDetailBatch = productUIClient.getPlanConfigDutyDetailBatch(planConfigList, "1");
            Map<Long, List<ProductClientUIDTO>> dutyDetailListMapping = CommonUtil.getResponseData(planConfigDutyDetailBatch);
            for (StandardPlanInfoRspDto standardPlanInfoRspDto : standardPlanInfoList) {
                standardPlanInfoRspDto.setDutyList(dutyDetailListMapping.getOrDefault(standardPlanInfoRspDto.getPlanConfigId(), new ArrayList<>()));
            }
        }
    }

    /**
     * 构造标品方案id列表
     */
    private List<Long> constructStandardPlanIdListByCompany(String standardType, List<Long> standardPlanIdList, String companyId) {
        // 如果传了保司id
        if (StrUtil.isNotBlank(companyId)) {
            // 查询当前渠道下指定保司的已上架的产品列表
            Page<StandardProductInfoRspDto> pageResult = standardPlanMapper.getPlanListForProduct(
                    standardType,
                    1,
                    10000,
                    CustomContext.getChannelId(),
                    null,
                    companyId,
                    StandardShelfStatusEnum.ON_SHELF.getValue()
            );
            // 如果查询到有已上架的产品
            List<StandardProductInfoRspDto> standardProductInfoList = pageResult.getData();
            if (CollectionUtil.isNotEmpty(standardProductInfoList)) {
                List<Long> companyStandardPlanIdList = standardProductInfoList.stream().map(StandardProductInfoRspDto::getStandardPlanId).collect(Collectors.toList());
                if (CollectionUtil.isNotEmpty(standardPlanIdList)) {
                    // 如果保司id和标品方案id都传了，则取交集
                    standardPlanIdList = companyStandardPlanIdList.stream()
                            .filter(standardPlanIdList::contains)
                            .collect(Collectors.toList());
                } else {
                    // 如果只传了保司id，则取根据保司id查询到的标品方案
                    standardPlanIdList = companyStandardPlanIdList;
                }

            }
        }
        return standardPlanIdList;
    }


    /***
     *  获取需要根据配置缓存配置信息
     */
    private List<Long> getFeiLvSgpConfigs() {
        QIgStandardPlanFeilvSgp qIgStandardPlanFeilvSgp = QIgStandardPlanFeilvSgp.ig_standard_plan_feilv_sgp;
        List<IgStandardPlanFeilvSgp> igStandardPlanFeilvSgps = bqlQueryFactory.select(Expressions.stringPath("ig_standard_plan_feilv_sgp.*"))
                                                           .from(qIgStandardPlanFeilvSgp)
                                                           .where(qIgStandardPlanFeilvSgp.business_type.eq(SingaporeFeeTypeEnum.SEX_AGE.getValue()))
                                                           .findList(true, IgStandardPlanFeilvSgp.class);
        if (CollectionUtil.isNotEmpty(igStandardPlanFeilvSgps)){
            return igStandardPlanFeilvSgps.stream().map(IgStandardPlanFeilvSgp::getPlanConfigId).distinct().collect(Collectors.toList());
        }
        return  new ArrayList<>() ;

    }


}
