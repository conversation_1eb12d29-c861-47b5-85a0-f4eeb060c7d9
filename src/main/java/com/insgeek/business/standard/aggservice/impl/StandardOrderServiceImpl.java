package com.insgeek.business.standard.aggservice.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;
import com.fasterxml.jackson.core.type.TypeReference;
import com.insgeek.boot.commons.credential.CredentialUtil;
import com.insgeek.boot.commons.datetime.DateTimeUtil;
import com.insgeek.boot.commons.exception.InsgeekException;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.boot.commons.util.InsgeekPhoneNumberUtil;
import com.insgeek.boot.web.context.IdentityContext;
import com.insgeek.boot.web.pojo.dto.CurrencyAmount;
import com.insgeek.boot.web.util.CurrencyAmountUtil;
import com.insgeek.boot.web.util.MessageUtil;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.contract.mapper.GroupDataDataMapper;
import com.insgeek.business.contract.mapper.OssFileDataMapper;
import com.insgeek.business.contract.sign.dto.pojo.PUser;
import com.insgeek.business.quote.common.dao.constant.QpFileDetailsConst;
import com.insgeek.business.quote.common.exception.QuoteException;
import com.insgeek.business.quote.common.utils.CommonDataUtil;
import com.insgeek.business.quote.frontend.exception.FrontendErrorMsgAndCode;
import com.insgeek.business.quote.util.CommonUtil;
import com.insgeek.business.quote.util.FileUtil;
import com.insgeek.business.quote.util.ZipUtil;
import com.insgeek.business.standard.aggservice.StandardOrderService;
import com.insgeek.business.standard.config.*;
import com.insgeek.business.standard.dto.vo.dto.*;
import com.insgeek.business.standard.dto.vo.req.*;
import com.insgeek.business.standard.dto.vo.rsp.*;
import com.insgeek.business.standard.enums.*;
import com.insgeek.business.standard.excel.listener.StandardRenewalDtoListener;
import com.insgeek.business.standard.excel.strategy.dto.IncreaseUploadSGData;
import com.insgeek.business.standard.excel.write.handler.DropdownSheetWriteHandler;
import com.insgeek.business.standard.interceptor.CustomContext;
import com.insgeek.business.standard.mapper.*;
import com.insgeek.business.standard.mq.producer.StandardOrderStatusChangeProducer;
import com.insgeek.business.standard.service.*;
import com.insgeek.business.standard.util.StringUtil;
import com.insgeek.components.orm.model.core.vo.Page;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.components.orm.model.querydsl.BQLQueryFactory;
import com.insgeek.insurance.meta.utils.context.IdentityUtil;
import com.insgeek.protocol.business.group.client.OrganizationClient;
import com.insgeek.protocol.business.policy.client.DockingClient;
import com.insgeek.protocol.common.client.DateClient;
import com.insgeek.protocol.common.client.FileClient;
import com.insgeek.protocol.common.dto.BusinessInformationDto;
import com.insgeek.protocol.common.dto.UploadResultDto;
import com.insgeek.protocol.data.client.dto.*;
import com.insgeek.protocol.data.client.entity.IgStandardSnapshot;
import com.insgeek.protocol.data.client.entity.QpFileDetails;
import com.insgeek.protocol.insurance.client.PersonClient;
import com.insgeek.protocol.insurance.client.PlanConfigClient;
import com.insgeek.protocol.insurance.client.ProductUIClient;
import com.insgeek.protocol.insurance.client.SaasPersonClient;
import com.insgeek.protocol.insurance.dto.config.RecommendStandardConfig;
import com.insgeek.protocol.insurance.dto.config.RecommendStandardPlan;
import com.insgeek.protocol.insurance.dto.person.PersonCheckVO;
import com.insgeek.protocol.insurance.dto.person.PersonalDTO;
import com.insgeek.protocol.insurance.dto.person.standard.StandardPersonDTO;
import com.insgeek.protocol.insurance.dto.product.request.ProductClientUIDTO;
import com.insgeek.protocol.insurance.enums.*;
import com.insgeek.protocol.insurance.enums.sg.EmployeeCategorySG;
import com.insgeek.protocol.insurance.enums.sg.RelationSG;
import com.insgeek.protocol.platform.common.client.BQLClient;
import com.insgeek.protocol.platform.common.client.GroupClient;
import com.insgeek.protocol.platform.common.dto.entity.*;
import com.insgeek.protocol.platform.fileservice.client.HtmlHandlerClient;
import com.insgeek.protocol.platform.metadata.client.DictClient;
import com.insgeek.protocol.platform.metadata.dto.DictDto;
import com.insgeek.protocol.platform.user.client.UsersClient;
import feign.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 标准订单聚合服务实现
 *
 * <AUTHOR>
 * @version 1.0
 */
@Slf4j
@Service
public class StandardOrderServiceImpl implements StandardOrderService {

    @Autowired
    StandardOrderMapper standardOrderMapper;

    @Autowired
    StandardPlanMapper standardPlanMapper;

    @Autowired
    private StandardQpFileDetailsMapper standardQpFileDetailsMapper;

    @Autowired
    private FileClient fileClient;

    @Autowired
    private PersonServiceImpl personService;

    @Autowired
    private StandardGroupConfig standardGroupConfig;

    @Autowired
    private StandardCommonConfig standardCommonConfig;

    @Autowired
    private SingaporeStandardInsureFileConfig singaporeStandardInsureFileConfig;

    @Autowired
    private DictClient dictClient;

    @Autowired
    private StandardPlanService standardPlanService;

    @Autowired
    private StandardInsureService standardInsureService;

    @Autowired
    private StandardTenantInfoMapper standardTenantInfoMapper;

    @Autowired
    DockingClient dockingClient;

    @Resource
    private DataMapper<QpFileDetails> qpFileDetailsMapper;

    @Autowired
    private FilesService filesService;

    @Autowired
    private PlanConfigClient planConfigClient;

    @Autowired
    private HtmlHandlerClient htmlHandlerClient;

    @Autowired
    private StandardMailService standardMailService;

    @Autowired
    private StandardMailConfig standardMailConfig;

    @Autowired
    private DataMapper<IgStandardSnapshot> standardOrderDataMapper;

    @Autowired
    private DataMapper<IgStandardPlan> standardPlanDataMapper;

    @Autowired
    private DataMapper<IgPlan> planDataMapper;

    @Autowired
    private DataMapper<PTenant> tenantDataMapper;

    @Autowired
    private DataMapper<PUser> userDataMapper;

    @Autowired
    private ProductUIClient productUIClient;

    @Autowired
    private BQLQueryFactory bqlQueryFactory;

    @Autowired
    private OrganizationClient organizationClient;
    @Autowired
    private StandardOrderDetailMapper standardOrderDetailMapper;

    @Autowired
    private SingaporeStandardCoverageIndexConfig standardCoverageIndexConfig;

    @Autowired
    private StandardInsureSgpService standardInsureSgpService;

    @Autowired
    private OssFileDataMapper ossFileDataMapper;

    @Autowired
    private GroupDataDataMapper standardGroupDataMapper;

    @Autowired
    private UsersClient usersClient;

    @Autowired
    private SingaporeStandardGroupConfig singaporeStandardGroupConfig;

    @Autowired
    private StandardOrderLogMapper standardOrderLogMapper;

    @Autowired
    private PersonClient personClient;

    @Resource
    private DateClient dateClient;

    @Autowired
    private StandardOrderStatusChangeProducer standardOrderStatusChangeProducer;
    @Autowired
    private StandardCompanyMapper standardCompanyMapper;

    @Resource
    private GroupClient groupClient;

    @Resource
    private BQLClient bqlClient;

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private SaasPersonClient saasPersonClient;

    @Override
    public Long saveOrder(StandardOrderReqDto standardOrderReqDto) {
        return saveOrder(standardOrderReqDto, StandardOrderStatusEnum.TO_BE_PERFECTED.getDictKey());
    }

    @Override
    public Page<StandardOrderRspDto> getOrderList(Integer pageNum, Integer pageSize, String status, String groupName) {
        return standardOrderMapper.getOrderList(pageNum, pageSize, status, groupName);
    }

    @Override
    public List<StandardOrderCountRspDto> getOrderCount() {
        Page<StandardOrderRspDto> orderList = standardOrderMapper.getOrderList(1, 10000, null, null);
        List<StandardOrderCountRspDto> list = new ArrayList<>();
        StandardOrderCountRspDto standardOrderCountRspDtoTotal = new StandardOrderCountRspDto();
        standardOrderCountRspDtoTotal.setTitle("全部")
                .setStatus(null)
                .setCount(orderList.getPage().getTotal());
        list.add(standardOrderCountRspDtoTotal);

        // 循环遍历并打印
        for (StandardOrderStatusEnum status : StandardOrderStatusEnum.sortedValues()) {
            StandardOrderCountRspDto standardOrderCountRspDto = new StandardOrderCountRspDto();
            standardOrderCountRspDto.setStatus(status.getDictKey())
                    .setTitle(status.getDictValue())
                    .setCount(orderList.getData().stream().filter(item -> item.getStatus().equals(status.getDictKey())).count());
            list.add(standardOrderCountRspDto);
        }
        return list;
    }

    private Long saveOrder(StandardOrderReqDto standardOrderReqDto, String status) {

        IgStandardOrder igStandardOrder = new IgStandardOrder();

        // 更新信息
        Long orderId = standardOrderReqDto.getOrderId();
        if (null != orderId) {
            IgStandardOrder igStandardOrderOld = standardOrderMapper.entity(IgStandardOrder.class).selectOne(orderId, true);
            // 更新支付信息
            if (igStandardOrderOld.getStatus().equals(StandardOrderStatusEnum.TO_BE_PAID.getDictKey())) {
                igStandardOrder.setId(orderId);
                igStandardOrder.setPayerBankName(standardOrderReqDto.getPaymentInfo().getPayerInfo().getPayerBankName());
                igStandardOrder.setPayerTransNo(standardOrderReqDto.getPaymentInfo().getPayerInfo().getPayerTransNo());
                igStandardOrder.setInvoiceEmail(standardOrderReqDto.getPaymentInfo().getInvoiceInfo().getReceiveMail());
                igStandardOrder.setInvoiceTaxNo(standardOrderReqDto.getPayeeBankName());
                igStandardOrder.setInvoiceType(standardOrderReqDto.getPaymentInfo().getInvoiceInfo().getInvoiceType());
                igStandardOrder.setInvoiceBankName(standardOrderReqDto.getPaymentInfo().getInvoiceInfo().getBankName());
                if (null != standardOrderReqDto.getAttorneyFileId()) {
                    igStandardOrder.setAttorneyFileId(standardOrderReqDto.getAttorneyFileId());
                    setStandardOrderAttorney(orderId, standardOrderReqDto.getAttorneyFileId());
                }
                igStandardOrder.setStatus(StandardOrderStatusEnum.TO_BE_CONFIRMED_PAYMENT.getDictKey());
                standardOrderLogMapper.record(igStandardOrder.getId(), StandardOrderStatusEnum.TO_BE_CONFIRMED_PAYMENT.getDictKey());
                standardOrderMapper.updateOne(igStandardOrder, true);
                // 发送完成支付邮件
                sendPaymentMailForWelfare(igStandardOrder.getId());
                // 发mq消息
                standardOrderStatusChangeProducer.constructAndSendMessage(igStandardOrder.getId());
                return igStandardOrder.getId();
            }

            // 如果非待完善状态，直接返回
            List<String> statusList = Arrays.asList(StandardOrderStatusEnum.TO_BE_PERFECTED.getDictKey(), StandardOrderStatusEnum.PRE_ORDER.getDictKey());
            if (!statusList.contains(igStandardOrderOld.getStatus())) {
                return igStandardOrderOld.getId();
            }
        }

        StandardPlanDto planInfo = standardPlanMapper.getPlanInfo(standardOrderReqDto.getStandardPlanId());

        // 更新流水线数据
        if (!CollectionUtils.isEmpty(standardOrderReqDto.getPersonList())) {
            String pipeLineNum = getPersonCheckVO(standardOrderReqDto.getPersonList(), planInfo);
            igStandardOrder.setPipeLineNum(pipeLineNum);
            igStandardOrder.setPersonNum(standardOrderReqDto.getPersonList().size());
        }

        // 根据参数内容重新计算价格
        StandardOrderTotalAmountRspDto standardOrderTotalAmountRspDto = getTotalAmount(standardOrderReqDto);

        // 基本信息
        igStandardOrder.setPlanId(planInfo.getPlanId());
        igStandardOrder.setStandardPlanId(standardOrderReqDto.getStandardPlanId());
        igStandardOrder.setBrokerId(IdentityContext.getUserId());
        igStandardOrder.setStartTime(standardOrderReqDto.getStartTime());

        // 最终付款时间
        ZonedDateTime finalPayTime = DateTimeUtil.startOfDay(DateTimeUtil.offsetDay(standardOrderReqDto.getStartTime(), standardCommonConfig.getFinalPaymentOffsetDays()));
        igStandardOrder.setFinalPayTime(finalPayTime);

        igStandardOrder.setEndTime(DateTimeUtil.offsetYear(DateTimeUtil.endOfDay(DateTimeUtil.offsetDay(standardOrderReqDto.getStartTime(), -1)), 1));
        if (null == standardOrderTotalAmountRspDto.getPrice()) {
            igStandardOrder.setPrice(standardOrderReqDto.getPrice());
        } else {
            igStandardOrder.setPrice(standardOrderTotalAmountRspDto.getPrice());
            igStandardOrder.setComputedRatio(standardOrderTotalAmountRspDto.getPrice().getAmount().divide(planInfo.getPrice().getAmount(), 10, RoundingMode.HALF_UP));
        }
        igStandardOrder.setTotalAmount(standardOrderTotalAmountRspDto.getTotalAmount());
        igStandardOrder.setStatus(status);

        // 企业信息
        igStandardOrder.setGroupName(standardOrderReqDto.getGroupName());
        igStandardOrder.setGroupProvince(standardOrderReqDto.getGroupInfo().getProvince());
        igStandardOrder.setGroupCity(standardOrderReqDto.getGroupInfo().getCity());
        igStandardOrder.setGroupAddress(standardOrderReqDto.getGroupInfo().getAddress());
        igStandardOrder.setGroupOrgCode(standardOrderReqDto.getGroupInfo().getOrgCode());
        igStandardOrder.setGroupSocialCode(standardOrderReqDto.getGroupInfo().getSocialCode());
        igStandardOrder.setGroupCounty(standardOrderReqDto.getGroupInfo().getCounty());

        // 渠道
        igStandardOrder.setChannelId(CustomContext.getChannelId());

        // 报价信息
        igStandardOrder.setFemaleRatio(standardOrderReqDto.getFemaleRatio());
        igStandardOrder.setCity(standardOrderReqDto.getCity());
        if (standardOrderReqDto.getPersonList().isEmpty()) {
            igStandardOrder.setPersonNum(standardOrderReqDto.getPersonNum());
        }
        igStandardOrder.setMaxAge(standardOrderReqDto.getMaxAge());
        igStandardOrder.setMinAge(standardOrderReqDto.getMinAge());
        igStandardOrder.setJobCategory(standardOrderReqDto.getJobCategory());

        // 联系人
        igStandardOrder.setContactPerson(standardOrderReqDto.getContactPerson());
        igStandardOrder.setContactMobile(standardOrderReqDto.getContactMobile());
        igStandardOrder.setContactEmail(standardOrderReqDto.getContactEmail());

        // 发票抬头
        igStandardOrder.setInvoiceTitle(standardOrderReqDto.getGroupName());
        igStandardOrder.setInvoiceTaxNo(standardOrderReqDto.getGroupInfo().getSocialCode());
        // 付款人信息
        igStandardOrder.setPayerName(standardOrderReqDto.getGroupName());

        // 收款方信息
        IgStandardTenantInfo channelInfo = standardTenantInfoMapper.getChannelInfo(CustomContext.getChannelId());
        igStandardOrder.setPayeeName(channelInfo.getBankAccount());
        igStandardOrder.setPayeeBankName(channelInfo.getBankName());
        igStandardOrder.setPayeeBankNo(channelInfo.getBankAccountNo());

        // 所属人
        igStandardOrder.setOwnerId(IdentityContext.getUserId());

        // 支付信息
        igStandardOrder.setPayStatus("0");
        if (null == orderId) {
            // 分享流水号
            igStandardOrder.setChannelSerialNo(standardOrderReqDto.getChannelSerialNo());
            IgStandardOrder igStandardOrder1 = standardOrderMapper.insertOne(igStandardOrder);
            standardOrderLogMapper.record(igStandardOrder.getId(), status);
            return igStandardOrder1.getId();
        } else {
            igStandardOrder.setId(orderId);
            int i = standardOrderMapper.updateOne(igStandardOrder, true);
            if (StandardOrderStatusEnum.TO_BE_PAID.getDictKey().equals(status)) {
                standardOrderLogMapper.record(igStandardOrder.getId(), status);
            }
            log.info("保存标品订单：更新结果「{}」", i);
            return igStandardOrder.getId();
        }
    }

    private String getPersonCheckVO(List<PersonalDTO> personList, StandardPlanDto planInfo) {
        personList.forEach(personalDTO -> {
            personalDTO.setPlanConfigId(planInfo.getPlanConfigId());
            personalDTO.setPlanName(planInfo.getPlanName());
            personalDTO.setGroupId(planInfo.getGroupId());
            personalDTO.setRelation(Relation.SELF);
            personalDTO.setEndTime(planInfo.getEndTime());
            personalDTO.setStartTime(planInfo.getStartTime());
            personalDTO.setMedicareType(MedicareType.CITY_WORKERS);
        });
        PersonCheckVO personCheckVO = personService.postPersonListToCore(personList, standardGroupConfig.getId());

        return personCheckVO.getPipelineNum();
    }

    /**
     * 发送完成支付邮件（员福）
     */
    private void sendPaymentMailForWelfare(Long orderId) {

        IgStandardOrder standardOrder = standardOrderMapper.entity(IgStandardOrder.class).selectOne(orderId, true);

        if (BooleanUtil.isFalse(standardOrder.getInsureTaskCompleted())) {
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_237"));
        }

        StandardMailConfig.MailInfo finishPay = standardMailConfig.getFinishPay();

        StandardMailDto standardMailDto = new StandardMailDto();
        standardMailDto.setCode(finishPay.getCode());
        standardMailDto.setTitle("【渠道标品】客户完成支付提醒“" + DateTimeUtil.format(DateTimeUtil.now(true), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "”");

        standardMailDto.setTo(finishPay.getTo());
        standardMailDto.setCc(finishPay.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(finishPay.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        IgBill billOrderId = standardOrderMapper.getBillOrderId(orderId);

        HashMap<String, String> variables = new HashMap<>();

        PTenant pTenant = tenantDataMapper.entity(PTenant.class).selectOne(standardOrder.getChannelId(), true);
        PUser pUser = userDataMapper.entity(PUser.class).selectOne(standardOrder.getBrokerId(), true);
        List<IgPlan> planList = standardOrderMapper.getPlanListByOrderId(orderId);

        variables.put("groupName", standardOrder.getGroupName());
        variables.put("channelName", pTenant.getName());
        variables.put("brokerName", pUser.getName());
        variables.put("planName", getPlanNameListStr(planList));
        variables.put("bankName", standardOrder.getPayerBankName());
        variables.put("transNo", standardOrder.getPayerTransNo());
        variables.put("billId", billOrderId.getId().toString());
        variables.put("totalAmount", standardOrder.getTotalAmount().getAmount().toString());
        standardMailDto.setVariables(variables);

        standardMailService.sendMail(standardMailDto);
    }

    /**
     * 发送完成支付邮件（雇主）
     */
    private void sendPaymentMailForEmployer(Long orderId) {

        IgStandardOrder standardOrder = standardOrderMapper.entity(IgStandardOrder.class).selectOne(orderId, true);

        if (BooleanUtil.isFalse(standardOrder.getInsureTaskCompleted())) {
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_237"));
        }

        StandardMailConfig.MailInfo finishPayEmployer = standardMailConfig.getFinishPayEmployer();

        StandardMailDto standardMailDto = new StandardMailDto();
        standardMailDto.setCode(finishPayEmployer.getCode());
        standardMailDto.setTitle("【渠道标品】客户完成支付提醒“" + DateTimeUtil.format(DateTimeUtil.now(true), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "”");

        standardMailDto.setTo(finishPayEmployer.getTo());
        standardMailDto.setCc(finishPayEmployer.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(finishPayEmployer.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        IgBill billOrderId = standardOrderMapper.getBillOrderId(orderId);

        HashMap<String, String> variables = new HashMap<>();

        PTenant pTenant = tenantDataMapper.entity(PTenant.class).selectOne(standardOrder.getChannelId(), true);
        PUser pUser = userDataMapper.entity(PUser.class).selectOne(standardOrder.getBrokerId(), true);
        List<IgPlan> planList = standardOrderMapper.getPlanListByOrderId(orderId);

        variables.put("groupName", standardOrder.getGroupName());
        variables.put("channelName", pTenant.getName());
        variables.put("brokerName", pUser.getName());
        variables.put("planName", getPlanNameListStr(planList));
        variables.put("bankName", standardOrder.getPayerBankName());
        variables.put("transNo", standardOrder.getPayerTransNo());
        variables.put("billId", billOrderId.getId().toString());
        variables.put("totalAmount", standardOrder.getTotalAmount().getAmount().toString());
        standardMailDto.setVariables(variables);

        standardMailService.sendMail(standardMailDto);
    }

    @Override
    public StandardFlagRspDto deleteOrder(StandardOrderReqDto standardOrderReqDto) {
        int i = standardOrderMapper.deleteOrder(standardOrderReqDto.getOrderId());
        StandardFlagRspDto standardFlagRspDto = new StandardFlagRspDto();
        standardFlagRspDto.setFlag(true);
        return standardFlagRspDto;
    }

    /**
     * 获取条款
     *
     * @param standardPlanInfoList
     * @param type
     * @return
     */
    private StandardOrderTermRspDto getTerm(List<IgStandardPlanInfo> standardPlanInfoList, String type) {

        // 分组
        Map<Long, Map<String, IgStandardPlanInfo>> resultMap = standardPlanInfoList.stream()
                .collect(Collectors.groupingBy(
                        IgStandardPlanInfo::getChannelId,
                        Collectors.toMap(IgStandardPlanInfo::getType, info -> info, (existingValue, newValue) -> existingValue)
                ));

        // 条款列表
        IgStandardPlanInfo igStandardPlanInfo;
        if (resultMap.containsKey(CustomContext.getChannelId()) && resultMap.get(CustomContext.getChannelId()).containsKey(type)) {
            igStandardPlanInfo = resultMap.get(CustomContext.getChannelId()).getOrDefault(type, null);
        } else {
            igStandardPlanInfo = resultMap.get(0L).getOrDefault(type, null);
        }

        if (null != igStandardPlanInfo) {
            StandardOrderTermRspDto standardOrderTermRspDto = new StandardOrderTermRspDto();
            standardOrderTermRspDto.setTitle(igStandardPlanInfo.getTitle())
                    .setContent(igStandardPlanInfo.getContent())
                    .setOrder(Integer.parseInt(igStandardPlanInfo.getOrderIndex()));
            return standardOrderTermRspDto;
        }
        return null;
    }

    @Override
    public StandardOrderTermsRspDto getTerms(Long standardPlanId) {

        List<IgStandardPlanInfo> standardPlanInfo = standardPlanMapper.getStandardPlanInfo(standardPlanId);

        StandardOrderTermsRspDto standardOrderTermsRspDto = new StandardOrderTermsRspDto();

        List<StandardOrderTermRspDto> standardOrderTermRspDtoList = new ArrayList<>();

        StandardOrderTermRspDto term1 = getTerm(standardPlanInfo, StandardPlanDetailTypeEnum.TYPE9.getValue());
        if (null != term1) {
            standardOrderTermRspDtoList.add(term1);
        }
        StandardOrderTermRspDto term2 = getTerm(standardPlanInfo, StandardPlanDetailTypeEnum.TYPE5.getValue());
        if (null != term2) {
            standardOrderTermRspDtoList.add(term2);
        }
        StandardOrderTermRspDto term3 = getTerm(standardPlanInfo, StandardPlanDetailTypeEnum.TYPE3.getValue());
        if (null != term3) {
            standardOrderTermRspDtoList.add(term3);
        }
        StandardOrderTermRspDto term10 = getTerm(standardPlanInfo, StandardPlanDetailTypeEnum.TYPE10.getValue());
        if (null != term10) {
            standardOrderTermRspDtoList.add(term10);
        }
        standardOrderTermsRspDto.setTerms(standardOrderTermRspDtoList.stream()
                .sorted(Comparator.comparing(StandardOrderTermRspDto::getOrder)).collect(Collectors.toList())
        );
        return standardOrderTermsRspDto;
    }


    @Override
    public com.insgeek.protocol.platform.fileservice.dto.UploadResultDto createBill(Long orderId) {

        // 查询订单信息
        IgStandardOrder standardOrder = standardOrderMapper.entity(IgStandardOrder.class).selectOne(orderId, Boolean.TRUE);
        StandardOrderDto orderDetail = null;
        if (StandardTypeEnum.isWelfareRecommend(standardOrder.getStandardType())) {
            orderDetail = standardOrderMapper.getOrderDetail(orderId);
        } else {
            orderDetail = standardOrderMapper.getOrderDetailMultipleConfigs(orderId);
        }

        com.insgeek.protocol.platform.fileservice.dto.UploadResultDto uploadResultDto;

        if (null == orderDetail.getPayNoticeFileId()) {

            if (BooleanUtil.isFalse(orderDetail.getInsureTaskCompleted())) {
                throw new QuoteException(-1, "账单生成中，请稍后再试");
            }

            // 生成pdf文件
            String billHtml = "";
            if (StandardTypeEnum.isWelfareRecommend(standardOrder.getStandardType())) {
                billHtml = filesService.generateBillHtml(orderDetail);
            } else {
                billHtml = filesService.generateBillHtmlMultipleConfigs(orderDetail);
            }
            if (StringUtils.isBlank(billHtml)) {
                throw new QuoteException(-1, "模板渲染失败");
            }
            // 生成方案确认书PDF
            List<com.insgeek.protocol.platform.fileservice.dto.UploadResultDto> data = htmlHandlerClient.convertHtmlPdfByWk(new MultipartFile[]{getMultipartFile("付款通知书.html", billHtml)}).getData();
            uploadResultDto = data.get(0);

            IgStandardOrder igStandardOrder = new IgStandardOrder();
            igStandardOrder.setId(orderId);
            igStandardOrder.setPayNoticeFileId(Long.valueOf(uploadResultDto.getKey()));
            int i = standardOrderMapper.entity(IgStandardOrder.class).updateOne(igStandardOrder, true);
            log.info("生成付款通知书：更新结果「{}」", i);
        } else {
            Long payNoticeFileId = orderDetail.getPayNoticeFileId();

            ResponseVO<List<UploadResultDto>> listResponseVO = fileClient.listFile(Collections.singletonList(payNoticeFileId.toString()));
            UploadResultDto uploadResultDtoGet = listResponseVO.getData().get(0);
            uploadResultDto = JacksonUtils.covertObject(uploadResultDtoGet, com.insgeek.protocol.platform.fileservice.dto.UploadResultDto.class);
        }

        return uploadResultDto;
    }

    @Override
    public StandardFlagRspDto sendBill(Long orderId, String email) {
        StandardFlagRspDto standardFlagRspDto = new StandardFlagRspDto();
        standardFlagRspDto.setFlag(true);
        IgStandardOrder standardOrder = standardOrderMapper.entity(IgStandardOrder.class).selectOne(orderId, true);
        if (BooleanUtil.isFalse(standardOrder.getInsureTaskCompleted())) {
            throw new QuoteException(-1, "账单生成中，请稍后再试");
        }
        List<IgStandardPlan> standardPlanList = standardOrderMapper.getStandardPlanListByOrderId(orderId);
        // 叠甲：【发邮件部分】目前员福跟雇主的邮件只有收件人、方案名称不一致，但是产品说后期可能会有调整，最好拆分开，所以基本一样的代码写了两份
        if (StandardTypeEnum.isEmployerPlan(standardOrder.getStandardType())) {
            doSendBillEmailWithEmployer(email, standardPlanList, standardOrder);
        } else {
            doSendBillEmailWithWelfare(email, standardPlanList, standardOrder);
        }

        return standardFlagRspDto;
    }

    private void doSendBillEmailWithWelfare(String email, List<IgStandardPlan> standardPlanList, IgStandardOrder standardOrder) {
        StandardMailConfig.MailInfo sendBill = standardMailConfig.getSendBill();

        StandardMailDto standardMailDto = new StandardMailDto();
        standardMailDto.setCode(sendBill.getCode());
        standardMailDto.setTitle("【首期账单】待支付账单提醒");
        standardMailDto.setTo(email);
        standardMailDto.setCc(sendBill.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(sendBill.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        com.insgeek.protocol.platform.fileservice.dto.UploadResultDto billFile = createBill(standardOrder.getId());
        standardMailDto.setOssKeyList(Collections.singletonList(billFile.getKey()));

        HashMap<String, String> variables = new HashMap<>();
        variables.put("planName", getStandardPlanNameListStr(standardPlanList));
        variables.put("startTime", formatDateTime(standardOrder.getStartTime()));
        variables.put("endTime", formatDateTime(standardOrder.getEndTime()));
        variables.put("personNum", standardOrder.getPersonNum().toString());
        variables.put("totalAmount", standardOrder.getTotalAmount().getAmount().toString());
        String payTime = null == standardOrder.getFinalPayTime() ? "" : DateTimeUtil.format(standardOrder.getEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        variables.put("payTime", payTime);
        standardMailDto.setVariables(variables);

        standardMailService.sendMail(standardMailDto);
    }

    /**
     * 发送雇主账单邮件
     */
    private void doSendBillEmailWithEmployer(String email, List<IgStandardPlan> standardPlanList, IgStandardOrder standardOrder) {
        StandardMailConfig.MailInfo sendBillEmployer = standardMailConfig.getSendBillEmployer();

        StandardMailDto standardMailDto = new StandardMailDto();
        standardMailDto.setCode(sendBillEmployer.getCode());
        standardMailDto.setTitle("【首期账单】待支付账单提醒");
        standardMailDto.setTo(email);
        standardMailDto.setCc(sendBillEmployer.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(sendBillEmployer.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        com.insgeek.protocol.platform.fileservice.dto.UploadResultDto billFile = createBill(standardOrder.getId());
        standardMailDto.setOssKeyList(Collections.singletonList(billFile.getKey()));

        HashMap<String, String> variables = new HashMap<>();
        variables.put("planName", getStandardPlanNameListStr(standardPlanList));
        variables.put("startTime", formatDateTime(standardOrder.getStartTime()));
        variables.put("endTime", formatDateTime(standardOrder.getEndTime()));
        variables.put("personNum", String.valueOf(standardOrder.getPersonNum()));
        variables.put("totalAmount", standardOrder.getTotalAmount().getAmount().toString());
        String payTime = null == standardOrder.getFinalPayTime() ? "" : DateTimeUtil.format(standardOrder.getEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        variables.put("payTime", payTime);
        standardMailDto.setVariables(variables);

        standardMailService.sendMail(standardMailDto);
    }

    /**
     * 获取多个标品方案名称拼接后的字符串
     */
    private String getStandardPlanNameListStr(List<IgStandardPlan> standardPlanList) {
        return standardPlanList.stream().map(IgStandardPlan::getTitle).collect(Collectors.joining("，"));
    }



    @Override
    public Long submitOrder(StandardOrderReqDto standardOrderReqDto) {
        Long orderId = saveOrder(standardOrderReqDto, StandardOrderStatusEnum.TO_BE_PAID.getDictKey());

        // 生成方案
        standardInsureService.insure(orderId, CustomContext.getChannelId());

        return orderId;
    }

    @Override
    public Long submitSingaporeOrder(SingaporeStandardOrderReqDto singaporeStandardOrderReqDto) {

        Long orderId = singaporeStandardOrderReqDto.getOrderId();
        IgStandardOrder standardOrder = standardOrderMapper.getSingaporeOrder(orderId);
        if (null == standardOrder) {
            throw new QuoteException(MessageUtil.get("b_b_quote_sgp_015"));
        }

        // 更新订单与订单明细的保费
        updatePremium(singaporeStandardOrderReqDto, orderId);

        // 预插入投保文件
        standardInsureSgpService.generatePreInsureFile(standardOrder);

        // 生成方案
        standardInsureSgpService.insure(singaporeStandardOrderReqDto.getOrderId(), CustomContext.getChannelId());

        return singaporeStandardOrderReqDto.getOrderId();
    }

    /**
     * 更新订单与订单明细的保费
     */
    private void updatePremium(SingaporeStandardOrderReqDto singaporeStandardOrderReqDto, Long orderId) {
        // 保费计算
        StandardSGReqDto standardSGReqDto = new StandardSGReqDto(singaporeStandardOrderReqDto.getSelectedPlanList(), singaporeStandardOrderReqDto.getIncreaseUploadSgData());
        StandardPreCalculateSGRspDto standardPreCalculateSGRspDto = personService.calculateProjectedPremiums(standardSGReqDto);
        // 预估保费返回人员信息
        List<IncreaseUploadSGData> increaseUploadSGData = standardPreCalculateSGRspDto.getIncreaseUploadSGData();
        // 获取每个类型下所有人员的保费的总和
        Map<String, BigDecimal> annualPremiumMapping = increaseUploadSGData.stream()
                .collect(Collectors.groupingBy(
                        // 根据 employmentCategory 分组，以类型对应的code分组
                        data -> EmployeeCategorySG.getCodeByDesc(data.getEmploymentCategory()),
                        // 获取每组中每个对象的 annualPremiums.getAmount() 的和
                        Collectors.mapping(
                                data -> data.getAnnualPreminums().getAmount(),
                                Collectors.reducing(BigDecimal.ZERO, BigDecimal::add)
                        )
                ));

        // increaseUploadSGData 根据证件号去重取人员总数
        int personNum = (int) increaseUploadSGData.stream().map(IncreaseUploadSGData::getIdentityNumber).distinct().count();

        // 要更新的detail
        List<IgStandardOrderDetail> orderDetailList = standardOrderDetailMapper.getByDetailListOrderId(orderId);
        List<IgStandardOrderDetail> updateOrderDetailList = new ArrayList<>();
        BigDecimal totalAmount = BigDecimal.ZERO;
        for (IgStandardOrderDetail igStandardOrderDetail : orderDetailList) {
            IgStandardOrderDetail standardOrderDetail = new IgStandardOrderDetail();
            standardOrderDetail.setId(igStandardOrderDetail.getId());
            if (annualPremiumMapping.containsKey(igStandardOrderDetail.getEmployeeType())) {
                BigDecimal currentDetailAmount = annualPremiumMapping.get(igStandardOrderDetail.getEmployeeType());
                currentDetailAmount = currentDetailAmount.divide(BigDecimal.valueOf(1L), 2, RoundingMode.HALF_UP);
                standardOrderDetail.setAmount(CurrencyAmount.valueOf(currentDetailAmount, IdentityContext.getBusinessCurrency()));
                // 累加总保额
                totalAmount = totalAmount.add(currentDetailAmount);
            }
            long employeeNum = increaseUploadSGData.stream()
                    .filter(x -> igStandardOrderDetail.getEmployeeType().equals(EmployeeCategorySG.getCodeByDesc(x.getEmploymentCategory())))
                    .filter(x -> x.getMemberType().equals(RelationSG.SELF.getValue()))
                    .map(IncreaseUploadSGData::getIdentityNumber)
                    .distinct().count();
            long spouseNum = increaseUploadSGData.stream()
                    .filter(x -> igStandardOrderDetail.getEmployeeType().equals(EmployeeCategorySG.getCodeByDesc(x.getEmploymentCategory())))
                    .filter(x -> x.getMemberType().equals(RelationSG.SPOUSE.getValue()))
                    .map(IncreaseUploadSGData::getIdentityNumber)
                    .distinct().count();
            long childNum = increaseUploadSGData.stream()
                    .filter(x -> igStandardOrderDetail.getEmployeeType().equals(EmployeeCategorySG.getCodeByDesc(x.getEmploymentCategory())))
                    .filter(x -> x.getMemberType().equals(RelationSG.CHILD.getValue()))
                    .map(IncreaseUploadSGData::getIdentityNumber)
                    .distinct().count();
            standardOrderDetail.setEmployeeNum(Math.toIntExact(employeeNum));
            standardOrderDetail.setSpouseNum(Math.toIntExact(spouseNum));
            standardOrderDetail.setChildNum(Math.toIntExact(childNum));
            updateOrderDetailList.add(standardOrderDetail);
        }
        // 保费更新
        if (CollectionUtils.isEmpty(updateOrderDetailList)) {
            throw new QuoteException(MessageUtil.get("b_b_quote_sgp_014"));
        }
        standardOrderDetailMapper.updateAll(updateOrderDetailList, true);

        IgStandardOrder igStandardOrder = new IgStandardOrder();
        igStandardOrder.setId(singaporeStandardOrderReqDto.getOrderId());
        igStandardOrder.setPipeLineNum(singaporeStandardOrderReqDto.getNumber());
        igStandardOrder.setSubmitTime(DateTimeUtil.now(true));
        igStandardOrder.setPersonNum(personNum);
        igStandardOrder.setStatus(SingaporeStandardOrderStatusEnum.ORDER_COMPLETED.getDictKey());
        totalAmount = totalAmount.divide(BigDecimal.valueOf(1L), 2, RoundingMode.HALF_UP);
        igStandardOrder.setTotalAmount(CurrencyAmount.valueOf(totalAmount, IdentityContext.getBusinessCurrency()));
        int i = standardOrderMapper.entity(IgStandardOrder.class).updateOne(igStandardOrder, true);
        log.warn("[提交]更新订单状态：更新结果「{}」", i);
    }

    @Override
    public Long submitOrderPayment(SingaporeOrderInfoSubmitReqDto singaporeOrderInfoSubmitReqDto) {
        // 更新状态为 SingaporeStandardOrderStatusEnum.WAIT_INSURING
        // IgStandardOrder.status
        IgStandardOrder standardOrder = standardOrderMapper.entity(IgStandardOrder.class).selectOne(singaporeOrderInfoSubmitReqDto.getOrderId(), true);
        if (standardOrder == null) {
            throw new QuoteException(MessageUtil.get("b_b_quote_sgp_015"));
        }
        standardOrder.setPayTime(DateTimeUtil.now(true));
        standardOrder.setStatus(SingaporeStandardOrderStatusEnum.TO_UPLOAD_AUTHORIZATION.getDictKey());
        standardOrderMapper.entity(IgStandardOrder.class).updateOne(standardOrder, true);
        // 发送邮件
        sendSgpPaymentMail(standardOrder);
        return singaporeOrderInfoSubmitReqDto.getOrderId();
    }

    /**
     * 国外版发送支付邮件
     */
    private void sendSgpPaymentMail(IgStandardOrder sgpOrderInfo) {
        // 获取邮件配置
        StandardMailConfig.MailInfo sgpPayNotice = standardMailConfig.getSgpPayNotice();
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("dd/MM/yyyy HH:mm:ss");
        // 构建邮件主题与接收人
        StandardMailDto standardMailDto = new StandardMailDto();
        standardMailDto.setCode(sgpPayNotice.getCode());
        standardMailDto.setTitle("Notification of Order Payment “" + DateTimeUtil.format(DateTimeUtil.now(true), dateTimeFormatter) + "”");
        standardMailDto.setCc(sgpPayNotice.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(sgpPayNotice.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));
        standardMailDto.setTo(sgpPayNotice.getTo());
        // 构建模版参数
        HashMap<String, String> variables = new HashMap<>();
        PUser pUser = userDataMapper.entity(PUser.class).selectOne(sgpOrderInfo.getBrokerId(), true);
        variables.put("order_id", sgpOrderInfo.getId().toString());
        variables.put("group_name", sgpOrderInfo.getGroupName());
        variables.put("sale_mobile", StrUtil.isNotBlank(pUser.getPhone()) ? pUser.getPhone() : "");
        variables.put("sales_mail", StrUtil.isNotBlank(pUser.getEmail()) ? pUser.getEmail() : "");
        variables.put("total_amount", "S$ " + sgpOrderInfo.getTotalAmount().getAmount().stripTrailingZeros().toPlainString());
        variables.put("payment_time", DateTimeUtil.format(sgpOrderInfo.getPayTime(), dateTimeFormatter));
        standardMailDto.setVariables(variables);
        // 发送邮件
        standardMailService.sendSgpMail(standardMailDto);
    }

    @Override
    public Map<String, UploadResultDto> getOssFileInfoListByOrderId(Long orderId) {
        return standardOrderMapper.getOssFileInfoListByOrderId(orderId);
    }

    @Override
    public Long generateOrder(StandardOrderReqDto standardOrderReqDto) {

        IgStandardOrder insertStandardOrder;

        IgStandardOrder igStandardOrder = new IgStandardOrder();
        igStandardOrder.setBrokerId(IdentityContext.getUserId());
        igStandardOrder.setOwnerId(IdentityContext.getUserId());
        igStandardOrder.setChannelId(CustomContext.getChannelId());
        igStandardOrder.setStatus(StandardOrderStatusEnum.PRE_ORDER.getDictKey());
        igStandardOrder.setStandardType(standardOrderReqDto.getStandardType());
        igStandardOrder.setChannelSerialNo(standardOrderReqDto.getChannelSerialNo());
        // 雇主
        if (StandardTypeEnum.isEmployerPlan(standardOrderReqDto.getStandardType())) {
            // 预生成订单时保险期间默认为1个月
            igStandardOrder.setInsurancePeriod(1);
            // 渠道付款流程
            IgStandardTenantInfo channelInfo = standardTenantInfoMapper.getChannelInfo(CustomContext.getChannelId());
            if (channelInfo != null) {
                igStandardOrder.setStandardPaymentProcess(channelInfo.getStandardPaymentProcess());
            }
            insertStandardOrder = standardOrderMapper.insertOne(igStandardOrder);
            // 生成订单明细
            IgStandardOrderDetail standardOrderDetail = new IgStandardOrderDetail();
            standardOrderDetail.setStandardPlanId(standardOrderReqDto.getStandardPlanId());
            List<StandardPlanInfoRspDto> standardPlanInfoList = standardPlanMapper.getPlanListForInsuredInfo(Collections.singletonList(standardOrderReqDto.getStandardPlanId()), null);
            if (CollectionUtil.isNotEmpty(standardPlanInfoList)) {
                StandardPlanInfoRspDto standardPlanInfo = standardPlanInfoList.get(0);
                // 序号
                standardOrderDetail.setSequence("0");
                // 订单id
                standardOrderDetail.setStandardOrderId(insertStandardOrder.getId());
                // 标品方案id
                standardOrderDetail.setStandardPlanId(standardPlanInfo.getStandardPlanId());
                // 投保人数
                standardOrderDetail.setPersonNum(standardPlanInfo.getPersonNum());
                // 保费
                standardOrderDetail.setPremium(standardPlanInfo.getPrice());
                // 核心的模版方案id
                standardOrderDetail.setPlanId(standardPlanInfo.getPlanId());
                // 核心的模版方案配置id
                standardOrderDetail.setPlanConfigId(standardPlanInfo.getPlanConfigId());
                // 职业类别，预生成订单明细时取支持的职业类型列表里最小的那个
                List<String> jobCategoryList = standardPlanInfo.getJobCategoryList();
                if (CollectionUtil.isNotEmpty(jobCategoryList)) {
                    String minJobCategory = Collections.min(jobCategoryList);
                    standardOrderDetail.setOccupationType(minJobCategory);
                }
            }
            standardOrderDetailMapper.insertOne(standardOrderDetail);
        } else {
            // 医疗
            StandardPlanDto planInfo = standardPlanMapper.getPlanInfo(standardOrderReqDto.getStandardPlanId());
            igStandardOrder.setPlanId(planInfo.getPlanId());
            igStandardOrder.setStandardPlanId(standardOrderReqDto.getStandardPlanId());
            igStandardOrder.setFemaleRatio(standardOrderReqDto.getFemaleRatio());
            igStandardOrder.setCity(standardOrderReqDto.getCity());
            igStandardOrder.setPersonNum(standardOrderReqDto.getPersonNum());
            igStandardOrder.setMaxAge(standardOrderReqDto.getMaxAge());
            igStandardOrder.setMinAge(standardOrderReqDto.getMinAge());
            igStandardOrder.setJobCategory(standardOrderReqDto.getJobCategory());
            insertStandardOrder = standardOrderMapper.insertOne(igStandardOrder);
        }
        return insertStandardOrder.getId();
    }

    @Override
    public void closeNonPaymentOrder(String param) {
        // 关闭未支付订单
        closeNonPayOrder(param);
        // 超时未支付退保
        List<IgStandardOrder> closeInsuredToBePaidOrders = closeInsuredToBePaidOrder(param);
        // 发送一天后关闭的未支付订单邮件
        sendInsuredToBePaidOrderPreviewMail(param);
    }

    // 查询超过支付截止日的订单
    private void closeNonPayOrder(String param) {

        ZonedDateTime dateTime = DateTimeUtil.now(true);
        if (StringUtils.isNotBlank(param)) {
            dateTime = DateTimeUtil.dateOf(param);
        }

        List<IgStandardOrder> toCloseOrderList = standardOrderMapper.getToCloseOrderList(dateTime);

        if (CollectionUtils.isEmpty(toCloseOrderList)) {
            log.warn("[关闭]无待关闭订单");
            return;
        }
        List<IgStandardOrder> updateOrderList = new ArrayList<>();
        toCloseOrderList.forEach(igStandardOrder -> {
            IgStandardOrder standardOrderUpdate = new IgStandardOrder();
            standardOrderUpdate.setId(igStandardOrder.getId());
            standardOrderUpdate.setStatus(StandardOrderStatusEnum.TIMEOUT_CLOSED.getDictKey());
            updateOrderList.add(standardOrderUpdate);
        });
        // 发送待支付订单状态变更消息
        if (!CollectionUtils.isEmpty(updateOrderList)) {
            standardOrderMapper.entity(IgStandardOrder.class).updateAll(updateOrderList, true);
            for (IgStandardOrder standardOrder : updateOrderList) {
                if (StandardTypeEnum.isWelfareRecommend(standardOrder.getStandardType())) {
                    standardOrderStatusChangeProducer.constructAndSendMessage(standardOrder.getId());
                }
            }
            standardOrderLogMapper.recordAll(updateOrderList);
        }
        // 调用团险接口 删除方案信息
        List<Long> planIdList = toCloseOrderList.stream().map(IgStandardOrder::getGeneratePlanId)
                .flatMap(Collection::stream).collect(Collectors.toList());
        ResponseVO responseVO = personClient.closeOrder(planIdList);
        log.warn("planIdList:{}-return-:{}", JacksonUtils.writeAsString(planIdList), JacksonUtils.writeAsString(responseVO));
    }

    private List<IgStandardOrder> closeInsuredToBePaidOrder(String param) {

        ZonedDateTime dateTime = DateTimeUtil.now(true);
        if (StringUtils.isNotBlank(param)) {
            dateTime = DateTimeUtil.dateOf(param);
        }

        List<IgStandardOrder> toCloseOrderList = standardOrderMapper.getInsuredToBePaidOrderOrderList(dateTime);

        if (CollectionUtils.isEmpty(toCloseOrderList)) {
            log.warn("[关闭]无待关闭订单");
            return new ArrayList<>();
        }
        List<IgStandardOrder> updateOrderList = new ArrayList<>();
        toCloseOrderList.forEach(igStandardOrder -> {
            IgStandardOrder standardOrderUpdate = new IgStandardOrder();
            standardOrderUpdate.setId(igStandardOrder.getId());
            standardOrderUpdate.setStatus(StandardOrderStatusEnum.CANCELLED_DUE_TO_TIMEOUT.getDictKey());
            updateOrderList.add(standardOrderUpdate);
        });

        // 变更订单状态
        if (!CollectionUtils.isEmpty(updateOrderList)) {
            standardOrderMapper.entity(IgStandardOrder.class).updateAll(updateOrderList, true);
            standardOrderLogMapper.recordAll(updateOrderList);
        }

        // 超时未支付退保邮件
        sendInsuredToBePaidOrderMail(param, toCloseOrderList);

        // 调用团险接口 删除方案信息
        List<Long> planIdList = toCloseOrderList.stream().map(IgStandardOrder::getGeneratePlanId)
                .flatMap(Collection::stream).collect(Collectors.toList());
        ResponseVO responseVO = personClient.closeOrder(planIdList);
        log.warn("planIdList:{}-return-:{}", JacksonUtils.writeAsString(planIdList), JacksonUtils.writeAsString(responseVO));

        return toCloseOrderList;
    }

    private void sendInsuredToBePaidOrderPreviewMail(String param) {

        Map<String, String> ccMap = new HashMap<>();
        ccMap.put("<EMAIL>","李阳");
        //ccMap.put("<EMAIL>","曹晓东");
        ccMap.put("<EMAIL>","张田欣");

        ZonedDateTime dateTime = DateTimeUtil.now(true);
        if (StringUtils.isNotBlank(param)) {
            dateTime = DateTimeUtil.dateOf(param);
        }
        // dateTime加一天
        // dateTime = dateTime.plusDays(1);
        String startDateStr = DateTimeUtil.format(dateTime, DateTimeUtil.YYYY_MM_DD);

        // 工作日推算，返回结果只有日期部分
        ResponseVO<String> reckonResultResponseVO = dateClient.findDateAfterNum(startDateStr, 1);
        String reckonDateStr = CommonUtil.getResponseData(reckonResultResponseVO);
        // 将推算出的工作日日期（仅包含年月日）与方案开始时间的时间部分（时分秒）合并，生成最终付款时间
        ZonedDateTime finalPayTime = ZonedDateTime.of(
                LocalDate.parse(reckonDateStr),
                dateTime.toLocalTime(),
                dateTime.getZone()
        );

        List<IgStandardOrder> toCloseOrderList = standardOrderMapper.getInsuredToBePaidOrderOrderList(finalPayTime);

        if (CollectionUtils.isEmpty(toCloseOrderList)) {
            log.warn("[关闭]无1天后待关闭订单");
            return;
        }

        // 发送邮件提醒
        for (IgStandardOrder standardOrder : toCloseOrderList) {
            IdentityUtil.setRobotAuth();
            log.info("sendNonPayToBeCloseMail开始, standardOrderId: {}", standardOrder.getId());
            IgBill billOrderId = standardOrderMapper.getBillOrderId(standardOrder.getId());

            // 发送产生该订单的账号的邮箱(经纪人邮件)
            StandardMailDto standardMailDtoBroker = new StandardMailDto();
            standardMailDtoBroker.setCode(206130L);
            standardMailDtoBroker.setTitle("雇主标品订单1天后自动关闭提醒");
            PUser broker = userDataMapper.entity(PUser.class).selectOne(standardOrder.getBrokerId(), true);
            if (StringUtils.isBlank(broker.getEmail())) {
                log.warn("[发送邮件]无邮箱不发送:{}", broker.getName());
                continue;
            }
            standardMailDtoBroker.setTo(broker.getEmail());
            standardMailDtoBroker.setCc(ccMap);
            HashMap<String, String> variablesBroker = new HashMap<>();
            variablesBroker.put("close_time", formatDateTime(standardOrder.getFinalPayTime()));
            variablesBroker.put("order_id", String.valueOf(standardOrder.getId()));
            variablesBroker.put("group_name", standardOrder.getGroupName());
            variablesBroker.put("total_amount", billOrderId == null ? "" : billOrderId.getAmount().getAmount().setScale(2, RoundingMode.HALF_UP).toString());
            standardMailDtoBroker.setVariables(variablesBroker);
            standardMailService.sendMail(standardMailDtoBroker);

            // 发送渠道负责人邮件
            List<String> channelResponsiblePersonEmail = getChannelResponsiblePersonEmail(standardOrder.getChannelId());
            PTenant pTenant = tenantDataMapper.entity(PTenant.class).selectOne(standardOrder.getChannelId(), true);

            if (!CollectionUtils.isEmpty(channelResponsiblePersonEmail)) {
                StandardMailDto standardMailDtoChannelResponsible = new StandardMailDto();
                standardMailDtoChannelResponsible.setCode(206131L);
                standardMailDtoChannelResponsible.setTitle("【内部】雇主标品订单1天后自动关闭提醒");
                standardMailDtoChannelResponsible.setTo(String.join(",", channelResponsiblePersonEmail));
                standardMailDtoChannelResponsible.setCc(ccMap);
                HashMap<String, String> variablesChannelResponsible = new HashMap<>();
                variablesChannelResponsible.put("close_time", formatDateTime(standardOrder.getFinalPayTime()));
                variablesChannelResponsible.put("order_id", String.valueOf(standardOrder.getId()));
                variablesChannelResponsible.put("channel_name", pTenant.getName());
                variablesChannelResponsible.put("group_name", standardOrder.getGroupName());
                variablesChannelResponsible.put("total_amount", billOrderId == null ? "" : billOrderId.getAmount().getAmount().setScale(2, RoundingMode.HALF_UP).toString());
                variablesChannelResponsible.put("bill_id", billOrderId == null ? "" : billOrderId.getId().toString());
                standardMailDtoChannelResponsible.setVariables(variablesChannelResponsible);
                standardMailService.sendMail(standardMailDtoChannelResponsible);
            }
        }
    }

    private void sendInsuredToBePaidOrderMail(String param, List<IgStandardOrder> toCloseOrderList) {

        Map<String, String> ccMap = new HashMap<>();
        ccMap.put("<EMAIL>","李阳");
        //ccMap.put("<EMAIL>","曹晓东");
        ccMap.put("<EMAIL>","张田欣");

        ZonedDateTime dateTime = DateTimeUtil.now(true);
        if (StringUtils.isNotBlank(param)) {
            dateTime = DateTimeUtil.dateOf(param);
        }
        // dateTime加一天
        dateTime = dateTime.plusDays(1);

        if (CollectionUtils.isEmpty(toCloseOrderList)) {
            log.warn("[关闭]无1天后待关闭订单");
            return;
        }

        // 发送邮件提醒
        for (IgStandardOrder standardOrder : toCloseOrderList) {
            IdentityUtil.setRobotAuth();
            log.info("sendNonPayToBeCloseMail开始, standardOrderId: {}", standardOrder.getId());

            // 联系人
            StandardMailDto standardMailDtoBroker = new StandardMailDto();
            standardMailDtoBroker.setCode(206128L);
            standardMailDtoBroker.setTitle("雇主标品订单超时未支付关闭提醒");
            standardMailDtoBroker.setTo(standardOrder.getContactEmail());
            standardMailDtoBroker.setCc(ccMap);
            HashMap<String, String> variablesBroker = new HashMap<>();
            variablesBroker.put("order_id", String.valueOf(standardOrder.getId()));
            variablesBroker.put("group_name", standardOrder.getGroupName());
            variablesBroker.put("total_amount", standardOrder.getTotalAmount().getAmount().setScale(2, RoundingMode.HALF_UP).toString());
            standardMailDtoBroker.setVariables(variablesBroker);
            standardMailService.sendMail(standardMailDtoBroker);

            // 发送渠道负责人邮件
            List<String> channelResponsiblePersonEmail = getChannelResponsiblePersonEmail(standardOrder.getChannelId());
            if (!CollectionUtils.isEmpty(channelResponsiblePersonEmail)) {
                // 保单信息
                StandardOrderPolicyDto orderPolicyInfo = standardOrderMapper.getOrderPolicyInfo(standardOrder.getId());

                StandardMailDto standardMailDtoChannelResponsible = new StandardMailDto();
                standardMailDtoChannelResponsible.setCode(206129L);
                standardMailDtoChannelResponsible.setTitle("【内部】雇主标品订单超时未支付关闭提醒");
                standardMailDtoChannelResponsible.setTo(String.join(",", channelResponsiblePersonEmail));
                standardMailDtoChannelResponsible.setCc(ccMap);
                HashMap<String, String> variablesChannelResponsible = new HashMap<>();
                variablesChannelResponsible.put("order_id", String.valueOf(standardOrder.getId()));
                variablesChannelResponsible.put("company_name", orderPolicyInfo == null ? "" : orderPolicyInfo.getCompanyName());
                variablesChannelResponsible.put("policy_num", orderPolicyInfo == null ? "" : orderPolicyInfo.getPolicyNum());
                variablesChannelResponsible.put("group_name", standardOrder.getGroupName());
                variablesChannelResponsible.put("total_amount", standardOrder.getTotalAmount().getAmount().setScale(2, RoundingMode.HALF_UP).toString());
                standardMailDtoChannelResponsible.setVariables(variablesChannelResponsible);
                standardMailService.sendMail(standardMailDtoChannelResponsible);
            }
        }
    }

    /**
     * 构造并发送消息
     *
     * @param standardOrderId 标品订单id
     */
    public void sendNonPayToBeCloseMail(Long standardOrderId) {

    }

    // 获取渠道负责人邮箱
    private List<String> getChannelResponsiblePersonEmail(Long channelId) {
        String bql = "select user_id as user_id,user_name as user_name, created_at as created_at, maill as mail  from qp_tenant_user where delete_flg = 0 and user_type = 1 and pt_tenant_id = " + channelId;
        ResponseVO responseVO = bqlClient.selectData(bql);
        List<Map<String, Object>> result = JacksonUtils.readValue(JacksonUtils.writeAsString(responseVO.getData()), new TypeReference<List<Map<String, Object>>>() {
        });
        return result.stream().filter(map -> map.get("mail") != null).map(map -> (String) map.get("mail")).collect(Collectors.toList());
    }


    /**
     * 获取企业信息
     *
     * @param groupId 企业id
     */
    private IgGroup getGroupInfo(Long groupId) {
        ResponseVO<IgGroup> groupResponseVO = groupClient.getDataById(groupId);
        CommonDataUtil.checkAfterGetPlatformData(groupResponseVO, true, groupResponseVO.getMessage());
        IgGroup igGroup = groupResponseVO.getData();
        if (igGroup == null) {
            throw new QuoteException(-1, "未查询到企业，企业id:{}", groupId);
        }
        return igGroup;
    }

    /**
     * 不检查保存
     *
     * @param standardOrderReqDto 请求对象
     * @return id
     */
    @Override
    public Long saveOrderWithoutCheck(StandardOrderReqDto standardOrderReqDto) {
        if (standardOrderReqDto.getOrderId() == null) {
            throw new InsgeekException("orderId is not exist");
        }

        StandardPlanDto planInfo = standardPlanMapper.getPlanInfo(standardOrderReqDto.getStandardPlanId());

        IgStandardOrder igStandardOrder = new IgStandardOrder();
        igStandardOrder.setId(standardOrderReqDto.getOrderId());
        if (null != standardOrderReqDto.getStartTime()) {
            igStandardOrder.setStartTime(standardOrderReqDto.getStartTime());
            igStandardOrder.setEndTime(DateTimeUtil.offsetYear(DateTimeUtil.endOfDay(DateTimeUtil.offsetDay(standardOrderReqDto.getStartTime(), -1)), 1));
        }

        if (!CollectionUtils.isEmpty(standardOrderReqDto.getPersonList())) {
            String personCheckVO = getPersonCheckVO(standardOrderReqDto.getPersonList(), planInfo);
            igStandardOrder.setPipeLineNum(personCheckVO);
        }

        if (null != standardOrderReqDto.getPersonNum()) {
            igStandardOrder.setPersonNum(standardOrderReqDto.getPersonNum());
        }
        if (StringUtils.isNotBlank(standardOrderReqDto.getContactPerson())) {
            igStandardOrder.setContactPerson(standardOrderReqDto.getContactPerson());
        }
        if (StringUtils.isNotBlank(standardOrderReqDto.getContactEmail())) {
            igStandardOrder.setContactEmail(standardOrderReqDto.getContactEmail());
        }
        if (StringUtils.isNotBlank(standardOrderReqDto.getContactMobile())) {
            igStandardOrder.setContactMobile(standardOrderReqDto.getContactMobile());
        }
        if (StringUtils.isNotBlank(standardOrderReqDto.getJobCategory())) {
            igStandardOrder.setJobCategory(standardOrderReqDto.getJobCategory());
        }
        if (standardOrderReqDto.getGroupName() != null) {
            igStandardOrder.setGroupName(standardOrderReqDto.getGroupName());
        }
        if (standardOrderReqDto.getGroupInfo() != null && standardOrderReqDto.getGroupInfo().getProvince() != null) {
            igStandardOrder.setGroupProvince(standardOrderReqDto.getGroupInfo().getProvince());
        }
        if (standardOrderReqDto.getGroupInfo() != null && standardOrderReqDto.getGroupInfo().getCity() != null) {
            igStandardOrder.setGroupCity(standardOrderReqDto.getGroupInfo().getCity());
        }
        if (standardOrderReqDto.getGroupInfo() != null && standardOrderReqDto.getGroupInfo().getAddress() != null) {
            igStandardOrder.setGroupAddress(standardOrderReqDto.getGroupInfo().getAddress());
        }
        if (standardOrderReqDto.getGroupInfo() != null && standardOrderReqDto.getGroupInfo().getOrgCode() != null) {
            igStandardOrder.setGroupOrgCode(standardOrderReqDto.getGroupInfo().getOrgCode());
        }
        if (standardOrderReqDto.getGroupInfo() != null && standardOrderReqDto.getGroupInfo().getSocialCode() != null) {
            igStandardOrder.setGroupSocialCode(standardOrderReqDto.getGroupInfo().getSocialCode());
        }
        if (standardOrderReqDto.getGroupInfo() != null && standardOrderReqDto.getGroupInfo().getCounty() != null) {
            igStandardOrder.setGroupCounty(standardOrderReqDto.getGroupInfo().getCounty());
        }

        int i = standardOrderMapper.updateOne(igStandardOrder, true);
        log.info("[不检查]保存标品订单：更新结果「{}」", i);

        return standardOrderReqDto.getOrderId();
    }

    @Override
    public UploadResultDto getPolicyOssUrl(Long orderId) {
        IdentityUtil.setRobotAuth();
        StandardOrderPolicyDto orderPolicyInfo = standardOrderMapper.getOrderPolicyInfo(orderId);
        Response feignResponse = dockingClient.downloadElectronicFile(orderPolicyInfo.getPolicyId(), null, orderPolicyInfo.getInsOl());
        try (InputStream inputStream = feignResponse.body().asInputStream();) {
            return ossFileDataMapper.uploadFile(inputStream, "policy.zip");
        } catch (IOException e) {
            log.error("文件下载失败:{}", ExceptionUtils.getStackTrace(e));
            throw new QuoteException(-1, "电子保单正在获取中，请稍后再试");
        }
    }

    @Override
    public StandardOrderFileRspDto getOrderFiles(Long orderId) {
        StandardOrderFileRspDto standardOrderFileRspDto = new StandardOrderFileRspDto();

        IgCompany igCompany = standardOrderMapper.getCompanyByOrderId(orderId);
        String fileUrl = filesService.getFileUrl(igCompany.getCompanyLogo());
        standardOrderFileRspDto.setCompanyLogo(fileUrl);
        standardOrderFileRspDto.setCompanyTitle(igCompany.getCompanyName());

        List<StandardOrderFileDto> fileList = standardOrderMapper.getCompanyFilesByOrderId(orderId);

        standardOrderFileRspDto.setFileList(fileList);
        return standardOrderFileRspDto;
    }

    @Override
    public boolean orderShowFiles(Long orderId) {
        IgCompany companyByOrderId = standardOrderMapper.getCompanyByOrderId(orderId);
        if (null == companyByOrderId) {
            return false;
        }
        return singaporeStandardInsureFileConfig.getShowFilesCompanyList().contains(companyByOrderId.getId());
    }

    @Override
    public void downOrderFileSingle(Long fileId, HttpServletResponse response) {
        // 获取文件详细信息
        QpFileDetails qpFileDetails = standardQpFileDetailsMapper.selectOne(fileId, true);
        if (StringUtils.isBlank(qpFileDetails.getOssKey())) {
            throw new InsgeekException(-1, MessageUtil.get("b_b_quote_sgp_017"));
        }
        // 根据OssKey获取OSS中的文件
        UploadResultDto uploadResultDto = ossFileDataMapper.findByOssKey(qpFileDetails.getOssKey());
        if (uploadResultDto == null) {
            throw new InsgeekException(-1, MessageUtil.get("b_b_quote_sgp_017"));
        }
        // 获取OSS中的文件
        File file = getFileFromOss(uploadResultDto);
        // 下载
        doDownloadOrderFileSingle(fileId, file, uploadResultDto.getFileOriginalName(), qpFileDetails.getFileName(), response);
    }

    /**
     * 下载文件
     *
     * @param fileId           文件ID
     * @param file             文件
     * @param fileOriginalName 文件原始名称
     * @param downloadFileName 下载文件名称
     * @param response         响应对象
     */
    private void doDownloadOrderFileSingle(Long fileId,
                                           File file,
                                           String fileOriginalName,
                                           String downloadFileName,
                                           HttpServletResponse response) {
        try (BufferedInputStream bufferedInputStream = new BufferedInputStream(Files.newInputStream(file.toPath()));
             BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(response.getOutputStream())) {
            // 设置响应头，指定下载文件的名称及文件类型
            response.setContentLength(bufferedInputStream.available());
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/" + FileUtil.getFileExtension(fileOriginalName));
            response.addHeader("Content-Disposition", "attachment;filename="
                    + URLEncoder.encode(downloadFileName + "." + FileUtil.getFileExtension(fileOriginalName), "utf-8"));
            // 将文件内容写入输出流
            IOUtils.copy(bufferedInputStream, bufferedOutputStream);
            // 刷新输出流，确保文件内容完全写出
            bufferedOutputStream.flush();
        } catch (IOException e) {
            log.error("File download failed, qpFileDetails.fileId:{}, error info: {}", fileId, ExceptionUtils.getStackTrace(e));
            throw new InsgeekException(Integer.parseInt(FrontendErrorMsgAndCode.DOWNLOAD_ERR.code()), FrontendErrorMsgAndCode.DOWNLOAD_ERR.msg());
        } finally {
            if (file.exists()) {
                file.deleteOnExit();
            }
        }
    }

    /**
     * 获取OSS中的文件
     *
     * @param uploadResultDto Oss文件信息
     */
    private File getFileFromOss(UploadResultDto uploadResultDto) {
        String ossUrl = uploadResultDto.getLargeUrl();
        File file = new File(System.getProperty("java.io.tmpdir"),
                UUID.randomUUID() + "." + FileUtil.getFileExtension(uploadResultDto.getFileOriginalName()));
        try {
            FileUtils.copyURLToFile(new URL(ossUrl), file);
        } catch (IOException e) {
            log.error("文件下载失败:{}", ExceptionUtils.getStackTrace(e));
            throw new InsgeekException(Integer.parseInt(FrontendErrorMsgAndCode.DOWNLOAD_ERR.code()), FrontendErrorMsgAndCode.DOWNLOAD_ERR.msg());
        }
        return file;
    }

    @Override
    public void downOrderFileZip(Long orderId, HttpServletResponse response) {

        IgStandardOrder singaporeOrder = standardOrderMapper.getSingaporeOrder(orderId);

        // 获取文件列表
        List<QpFileDetails> qpFileDetailsList = standardQpFileDetailsMapper
                .getQpFileDetailsListByBusTypeAndBusId(QpFileDetailsConst.BusType.QP_STANDARD_ORDER.getCode(), singaporeOrder.getId());
        // 获取OssKey列表
        List<String> fileOssKeyList = qpFileDetailsList
                .stream()
                .map(QpFileDetails::getOssKey)
                .collect(Collectors.toList());
        // 如果有文件生成中
        if (qpFileDetailsList.stream().anyMatch(x -> StringUtils.isBlank(x.getOssKey()))) {
            throw new InsgeekException(-1, MessageUtil.get("b_b_quote_sgp_018"));
        }

        // 获取Oss中的文件信息DTO
        List<UploadResultDto> uploadResultDtoList = ossFileDataMapper.findByOssKeys(fileOssKeyList);
        if (CollectionUtils.isEmpty(uploadResultDtoList)) {
            throw new InsgeekException(-1, MessageUtil.get("b_b_quote_sgp_018"));
        }

        // 构建临时文件列表
        List<File> tmpFiles = constructTmpFileList(uploadResultDtoList, qpFileDetailsList);

        // 在临时目录生成压缩文件
        String zipFileName = orderId + ".zip";
        File zipFile = new File(System.getProperty("java.io.tmpdir"), zipFileName);
        constructZipFile(tmpFiles, zipFile);

        // 下载
        doDownloadOrderFileZip(orderId, zipFile, tmpFiles, zipFileName, response);
    }

    /**
     * 下载zip文件
     *
     * @param orderId             标品订单ID
     * @param zipFile             zip文件
     * @param tmpFiles            临时文件列表
     * @param downloadZipFileName zip文件名称
     * @param response            响应对象
     */
    private void doDownloadOrderFileZip(Long orderId, File zipFile, List<File> tmpFiles, String downloadZipFileName, HttpServletResponse response) {
        try (BufferedInputStream bufferedInputStream = new BufferedInputStream(Files.newInputStream(zipFile.toPath()));
             BufferedOutputStream bufferedOutputStream = new BufferedOutputStream(response.getOutputStream())) {
            // 设置响应头，指定下载文件的名称及文件类型
            response.setContentLength(bufferedInputStream.available());
            response.setCharacterEncoding("UTF-8");
            response.setContentType("application/zip");
            response.addHeader("Content-Disposition", "attachment;filename="
                    + URLEncoder.encode(downloadZipFileName, "utf-8"));
            // 将文件内容写入输出流
            IOUtils.copy(bufferedInputStream, bufferedOutputStream);
            // 刷新输出流，确保文件内容完全写出
            bufferedOutputStream.flush();
        } catch (Exception e) {
            log.error("File download failed, orderId:{}, error info: {}", orderId, ExceptionUtils.getStackTrace(e));
            throw new InsgeekException(Integer.parseInt(FrontendErrorMsgAndCode.DOWNLOAD_ERR.code()), FrontendErrorMsgAndCode.DOWNLOAD_ERR.msg());
        } finally {
            // 删除压缩文件临时生成的文件
            zipFile.deleteOnExit();
            // 删除临时生成的文件
            for (File tmpFile : tmpFiles) {
                tmpFile.deleteOnExit();
            }
        }
    }

    /**
     * 构建压缩文件
     *
     * @param tmpFiles 临时文件列表
     * @param zipFile  压缩文件
     */
    private void constructZipFile(List<File> tmpFiles, File zipFile) {
        try {
            ZipUtil.zipFiles(tmpFiles, zipFile);
        } catch (IOException e) {
            log.error("Zip压缩失败, error info: {}", ExceptionUtils.getStackTrace(e));
            throw new InsgeekException(Integer.parseInt(FrontendErrorMsgAndCode.DOWNLOAD_ERR.code()), FrontendErrorMsgAndCode.DOWNLOAD_ERR.msg());
        }
    }

    /**
     * 构建临时文件列表
     *
     * @param uploadResultDtoList Oss文件信息列表
     * @param qpFileDetailsList
     */
    private List<File> constructTmpFileList(List<UploadResultDto> uploadResultDtoList, List<QpFileDetails> qpFileDetailsList) {
        Map<String, UploadResultDto> ossKeyMapping = uploadResultDtoList.stream()
                .collect(Collectors.toMap(UploadResultDto::getKey, Function.identity(), (v1, v2) -> v1));
        List<File> tmpFiles = new ArrayList<>();
        for (QpFileDetails qpFileDetails : qpFileDetailsList) {
            // 文件名
            String fileName = qpFileDetails.getFileName();
            // OssKey
            String ossKey = qpFileDetails.getOssKey();
            // Oss文件信息DTO
            UploadResultDto uploadResultDto = ossKeyMapping.get(ossKey);
            // 原始文件名
            String fileOriginalName = uploadResultDto.getFileOriginalName();
            // 文件后缀
            String fileExtension = FileUtil.getFileExtension(fileOriginalName);
            // 临时文件的文件名
            String tmpFileName = fileName + "." + fileExtension;
            File tmpFile = new File(System.getProperty("java.io.tmpdir"), tmpFileName);
            try {
                // 从Oss下载文件
                FileUtils.copyURLToFile(new URL(uploadResultDto.getLargeUrl()), tmpFile);
            } catch (IOException e) {
                log.error("文件下载失败, error info: {}", ExceptionUtils.getStackTrace(e));
                throw new InsgeekException(Integer.parseInt(FrontendErrorMsgAndCode.DOWNLOAD_ERR.code()), FrontendErrorMsgAndCode.DOWNLOAD_ERR.msg());
            }
            tmpFiles.add(tmpFile);
        }
        return tmpFiles;
    }


    public ZonedDateTime getFinalPaymentDate(ZonedDateTime startTime) {
        return DateTimeUtil.startOfDay(DateTimeUtil.offsetDay(startTime, standardCommonConfig.getFinalPaymentOffsetDays()));
    }

    @Override
    public Boolean checkMail(String mail, String groupName) {

        IgGroupData groupData = standardGroupDataMapper.getGroupDataByName(CustomContext.getChannelId(), groupName);

        if (groupData != null && groupData.getContactMail().equals(mail)) {
            return true;
        }

        ResponseVO responseVO = usersClient.checkPassportExistReturnBoolean(mail);

        return Boolean.parseBoolean(responseVO.getData().toString());
    }

    @Override
    public CheckResultDto checkGroupSocialCode(String groupName, String groupSocialCode) {
        // 根据企业名称从核心系统获取企业信息
        IgGroupData groupDataByName = standardGroupDataMapper.getGroupDataByName(CustomContext.getChannelId(), groupName);
        // 如果存在该企业名称
        if (groupDataByName != null) {
            // 判断该企业证件号码与输入的是否一致
            String usci = groupDataByName.getUsci();
            if (!groupSocialCode.equals(usci)) {
                return CheckResultDto.failCheck(MessageUtil.get("b_b_quote_244"), PromptTypeEnum.INLINE);
            }
            return CheckResultDto.passCheck();
        }
        // 如果不存在该企业名称
        // 判断企业证件号码在核心系统中是否已存在
        IgGroupData groupDataByUsci = standardGroupDataMapper.getGroupDataByUsci(CustomContext.getChannelId(), groupSocialCode);
        if (groupDataByUsci != null) {
            return CheckResultDto.failCheck(MessageUtil.get("b_b_quote_230"), PromptTypeEnum.INLINE);
        }
        return CheckResultDto.passCheck();
    }

    @Override
    public CheckResultDto checkPlanPrice(StandardEmployerPriceCalcDto standardEmployerPriceCalcDto) {
        // 校验时的保费只需要计算一个月的即可
        standardEmployerPriceCalcDto.setInsurancePeriod("1");
        // 执行规则流并回写计算后的保费
        standardPlanService.executeRuleAndFillEmployerPlanPrice(Collections.singletonList(standardEmployerPriceCalcDto));
        // 再次投保逻辑
        fillSourcePrice(standardEmployerPriceCalcDto);
        // 校验保费
        return doCheckPlanPrice(standardEmployerPriceCalcDto);
    }

    /**
     * 填充源订单明细的保费
     */
    private void fillSourcePrice(StandardEmployerPriceCalcDto standardEmployerPriceCalcDto) {
        CurrencyAmount sourcePrice = CurrencyAmount.NULL;
        Long cloneSourceOrderDetailId = standardEmployerPriceCalcDto.getCloneSourceOrderDetailId();
        if (Objects.nonNull(cloneSourceOrderDetailId)) {
            IgStandardOrderDetail standardOrderDetail = standardOrderDetailMapper.entity(IgStandardOrderDetail.class).selectOne(cloneSourceOrderDetailId, true);
            if (Objects.isNull(standardOrderDetail)) {
                throw new QuoteException(-1, MessageUtil.get("b_b_quote_251"));
            }
            sourcePrice = standardOrderDetail.getPremium();
        }
        standardEmployerPriceCalcDto.setCloneSourceOrderDetailPrice(sourcePrice);
    }

    /**
     * 校验保费是否合法
     */
    private CheckResultDto doCheckPlanPrice(StandardEmployerPriceCalcDto standardEmployerPriceCalcDto) {
        // 当前填写的保费
        CurrencyAmount currentPrice = standardEmployerPriceCalcDto.getPrice();
        // 计算后的保费
        CurrencyAmount calculatePrice = standardEmployerPriceCalcDto.getCalculatePrice();
        // 源订单明细的保费
        CurrencyAmount cloneSourceDetailPrice = standardEmployerPriceCalcDto.getCloneSourceOrderDetailPrice();
        // 如果计算后的值为空
        if (CurrencyAmountUtil.isNull(calculatePrice)) {
            log.error("保费计算异常，未获取到规则流计算结果");
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_246"));
        }
        // 如果源订单明细的保费不为空并且当前填写的保费小于源订单明细的保费
        if (!CurrencyAmountUtil.isNull(cloneSourceDetailPrice)
                && CurrencyAmountUtil.lt(currentPrice, cloneSourceDetailPrice)) {
            return CheckResultDto.failCheck(MessageUtil.get("b_b_quote_252", calculatePrice.getAmount()), PromptTypeEnum.INLINE);
        }
        // 如果当前填写的保费小于计算后的保费
        if (CurrencyAmountUtil.lt(currentPrice, calculatePrice)) {
            return CheckResultDto.failCheck(MessageUtil.get("b_b_quote_245", calculatePrice.getAmount()), PromptTypeEnum.INLINE);
        }
        // 否则通过
        return CheckResultDto.passCheck();
    }

    @Override
    public UploadResultDto downloadOccupationTypeFile(String companyId, HttpServletResponse response) {
        IgCompany igCompany = standardCompanyMapper.selectOne(Long.valueOf(companyId), true);
        if (igCompany == null) {
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_231"));
        }
        String occupationTypeFileOssKey = igCompany.getOccupationTypeFile();
        ResponseVO<List<UploadResultDto>> fileListResponseVO = fileClient.listFile(Collections.singletonList(occupationTypeFileOssKey));
        List<UploadResultDto> fileList = CommonUtil.getResponseData(fileListResponseVO);
        if (CollectionUtil.isEmpty(fileList)) {
            log.error("下载职业参考表失败！文件数据为空！fileResList：{}", JacksonUtils.writeAsString(fileList));
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_232"));
        }
        return CollectionUtil.getFirst(fileList);
    }

    @Override
    public Long updateInsuredInfo(StandardOrderInfoDto standardOrderInfoDto, String status) {
        // 执行流水线并且绑定流水线号
        executePipelineAndBindNumbers(standardOrderInfoDto);
        // 推算支付截止日期
        IgStandardOrder standardOrder = standardOrderMapper.selectOne(standardOrderInfoDto.getOrderId(), true);
        if (Objects.isNull(standardOrder)) {
            log.error("更新订单投保信息失败！订单数据不存在！orderId：{}", standardOrderInfoDto.getOrderId());
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_238"));
        }
        if (StandardPaymentProcessEnum.FIRST_PAYMENT_THEN_INSURED.getValue().equals(standardOrder.getStandardPaymentProcess())) {
            reckonFinalPayTimeOnSubmitWithEmployer(standardOrderInfoDto);
        }
        // 组装订单数据
        IgStandardOrder assembleStandardOrder = assembleOrderInfo(standardOrderInfoDto, status);
        // 组装订单明细列表
        List<IgStandardOrderDetail> assembleStandardOrderDetailList = assembleOrderDetailInfoList(standardOrderInfoDto);
        // 更新数据
        doUpdateInsuredInfo(assembleStandardOrder, assembleStandardOrderDetailList);
        return standardOrder.getId();
    }

    /**
     * 更新数据
     */
    private void doUpdateInsuredInfo(IgStandardOrder standardOrder, List<IgStandardOrderDetail> standardOrderDetailList) {
        // 更新订单表
        standardOrderMapper.updateOne(standardOrder, true);
        // 先删除订单明细
        List<IgStandardOrderDetail> orderDetailList = standardOrderDetailMapper.getDetailListByOrderId(standardOrder.getId());
        if (CollectionUtil.isNotEmpty(orderDetailList)) {
            List<Long> orderDetailIdList = orderDetailList.stream().map(IgStandardOrderDetail::getId).collect(Collectors.toList());
            standardOrderDetailMapper.deleteByIdList(orderDetailIdList);
        }
        // 新增订单明细
        standardOrderDetailMapper.insertAll(standardOrderDetailList);
    }

    /**
     * 推算支付截止日（提交投保信息时）
     */
    private void reckonFinalPayTimeOnSubmitWithEmployer(StandardOrderInfoDto standardOrderInfoDto) {
        // 方案开始时间
        ZonedDateTime startTime = standardOrderInfoDto.getStartTime();
        // 获取最终付款时间，先付款后投保的流程：支付截止时间为方案生效时间往前推两个工作日
        String startDateStr = DateTimeUtil.format(startTime, DateTimeUtil.YYYY_MM_DD);
        // 工作日推算，返回结果只有日期部分
        ResponseVO<String> reckonResultResponseVO = dateClient.findDateBeforeNum(startDateStr, standardCommonConfig.getEmployerPreFinalPaymentOffsetDays());
        String reckonDateStr = CommonUtil.getResponseData(reckonResultResponseVO);
        // 将推算出的工作日日期（仅包含年月日）与方案开始时间的时间部分（时分秒）合并，生成最终付款时间
        ZonedDateTime finalPayTime = ZonedDateTime.of(
                LocalDate.parse(reckonDateStr),
                startTime.toLocalTime(),
                startTime.getZone()
        );
        standardOrderInfoDto.setFinalPayTime(finalPayTime);
    }

    /**
     * 组装订单明细数据
     */
    private List<IgStandardOrderDetail> assembleOrderDetailInfoList(StandardOrderInfoDto standardOrderInfoDto) {
        List<IgStandardOrderDetail> standardOrderDetailList = new ArrayList<>();
        List<StandardPlanInfoDto> standardPlanInfoList = standardOrderInfoDto.getStandardPlanInfoList();
        List<Long> standardPlanIdList = standardPlanInfoList.stream().map(StandardPlanInfoDto::getStandardPlanId).collect(Collectors.toList());
        List<IgStandardPlan> standardPlanList = standardPlanMapper.entity(IgStandardPlan.class).select(standardPlanIdList, true);
        Map<Long, IgStandardPlan> standardPlanMap = standardPlanList.stream()
                .collect(Collectors.toMap(IgStandardPlan::getId, Function.identity(), (v1, v2) -> v1));
        for (StandardPlanInfoDto standardPlanInfoDto : standardPlanInfoList) {
            IgStandardOrderDetail standardOrderDetail = new IgStandardOrderDetail();
            // 订单id
            standardOrderDetail.setStandardOrderId(standardOrderInfoDto.getOrderId());
            // 序号
            standardOrderDetail.setSequence(standardPlanInfoDto.getSequence());
            // 标品方案id
            standardOrderDetail.setStandardPlanId(standardPlanInfoDto.getStandardPlanId());
            // 投保人数
            Integer personNum = standardPlanInfoDto.getPersonNum();
            if (CollectionUtil.isNotEmpty(standardPlanInfoDto.getPersonList())) {
                personNum = standardPlanInfoDto.getPersonList().size();
            }
            standardOrderDetail.setPersonNum(personNum);
            // 职业类型
            standardOrderDetail.setOccupationType(standardPlanInfoDto.getJobCategory());
            // 保费
            standardOrderDetail.setPremium(standardPlanInfoDto.getPrice());
            // 核心的模版方案id
            IgStandardPlan standardPlan = standardPlanMap.get(standardPlanInfoDto.getStandardPlanId());
            standardOrderDetail.setPlanId(Objects.nonNull(standardPlan) ? standardPlan.getPlanId() : null);
            // 核心的模版方案配置id
            standardOrderDetail.setPlanConfigId(Objects.nonNull(standardPlan) ? standardPlan.getPlanConfigId() : null);
            // 流水线号
            standardOrderDetail.setPipeLineNum(standardPlanInfoDto.getPipeLineNum());
            // 源订单明细id
            standardOrderDetail.setCloneSourceDetailId(standardPlanInfoDto.getCloneSourceOrderDetailId());
            standardOrderDetailList.add(standardOrderDetail);
        }
        return standardOrderDetailList;
    }

    /**
     * 组装订单数据
     */
    private IgStandardOrder assembleOrderInfo(StandardOrderInfoDto standardOrderInfoDto, String status) {
        IgStandardOrder standardOrder = new IgStandardOrder();
        // 订单id
        standardOrder.setId(standardOrderInfoDto.getOrderId());
        // 订单状态
        standardOrder.setStatus(status);
        // 经纪人id
        standardOrder.setBrokerId(IdentityContext.getUserId());
        // 保险期间
        standardOrder.setInsurancePeriod(Integer.valueOf(standardOrderInfoDto.getInsurancePeriod()));
        // 开始时间
        standardOrder.setStartTime(standardOrderInfoDto.getStartTime());
        // 结束时间
        standardOrder.setEndTime(standardOrderInfoDto.getEndTime());
        // 支付截止日期
        standardOrder.setFinalPayTime(standardOrderInfoDto.getFinalPayTime());
        // 投保人数
        Integer totalPersonNum = standardOrderInfoDto.getStandardPlanInfoList().stream().mapToInt(standardPlanInfoDto -> {
            Integer personNum = standardPlanInfoDto.getPersonNum();
            if (CollectionUtil.isNotEmpty(standardPlanInfoDto.getPersonList())) {
                personNum = standardPlanInfoDto.getPersonList().size();
            }
            return personNum;
        }).sum();
        standardOrder.setPersonNum(totalPersonNum);
        // 企业信息
        InsuredGroupInfoDto insuredGroup = standardOrderInfoDto.getInsuredGroup();
        if (insuredGroup != null) {
            // 企业名称
            standardOrder.setGroupName(insuredGroup.getGroupName());
            // 企业所在省份
            standardOrder.setGroupProvince(insuredGroup.getProvince());
            // 企业所在城市
            standardOrder.setGroupCity(insuredGroup.getCity());
            // 企业所在区县
            standardOrder.setGroupCounty(insuredGroup.getCounty());
            // 企业详细地址
            standardOrder.setGroupAddress(insuredGroup.getGroupAddress());
            // 企业社会信用代码
            standardOrder.setGroupSocialCode(insuredGroup.getGroupSocialCode());
            // 发票抬头
            standardOrder.setInvoiceTitle(insuredGroup.getGroupName());
            // 发票税号
            standardOrder.setInvoiceTaxNo(insuredGroup.getGroupSocialCode());
            // 付款人信息
            standardOrder.setPayerName(insuredGroup.getGroupName());
            // 收款方信息
            IgStandardTenantInfo channelInfo = standardTenantInfoMapper.getChannelInfo(CustomContext.getChannelId());
            if (channelInfo != null) {
                // 收款方账户
                standardOrder.setPayeeName(channelInfo.getBankAccount());
                // 收款银行
                standardOrder.setPayeeBankName(channelInfo.getBankName());
                // 收款账号
                standardOrder.setPayeeBankNo(channelInfo.getBankAccountNo());
            }
        }
        // 支付状态
        standardOrder.setPayStatus(StandardPaymentStatusEnum.UN_PAID.getValue());
        // 渠道id
        standardOrder.setChannelId(CustomContext.getChannelId());
        // 渠道分享流水号
        standardOrder.setChannelSerialNo(standardOrderInfoDto.getChannelSerialNo());
        // 所属人
        standardOrder.setOwnerId(IdentityContext.getUserId());
        // 渠道手续费比例，目前固定15%，后期会变
        standardOrder.setChannelFeeRatio(new BigDecimal("0.15"));
        // 联系人信息
        ContactInfoDto contactInfo = standardOrderInfoDto.getContactInfo();
        if (contactInfo != null) {
            // 联系人
            standardOrder.setContactPerson(contactInfo.getContactPerson());
            // 联系人手机号
            standardOrder.setContactMobile(contactInfo.getContactMobile());
            // 联系人邮箱
            standardOrder.setContactEmail(contactInfo.getContactEmail());
        }
        return standardOrder;
    }

    /**
     * 执行流水线并且绑定流水线号
     */
    private void executePipelineAndBindNumbers(StandardOrderInfoDto standardOrderInfoDto) {
        // 流水线处理
        List<StandardPlanInfoDto> standardPlanInfoList = standardOrderInfoDto.getStandardPlanInfoList();
        List<Long> standardPlanIdlist = standardPlanInfoList.stream().map(StandardPlanInfoDto::getStandardPlanId).collect(Collectors.toList());
        Map<Long, StandardPlanDto> standardPlanDtoMapping = standardPlanMapper.getPlanInfoMappingByIdList(standardPlanIdlist);
        for (StandardPlanInfoDto standardPlanInfoDto : standardPlanInfoList) {
            List<PersonalDTO> personList = standardPlanInfoDto.getPersonList();
            if (CollectionUtil.isNotEmpty(personList)) {
                // 走一遍增员获取获取流水线号
                String pipelineNum = getPipelineNumWithEmployer(personList, standardPlanDtoMapping.get(standardPlanInfoDto.getStandardPlanId()));
                standardPlanInfoDto.setPipeLineNum(pipelineNum);
            }
        }
    }

    /**
     * 获取流水线号（雇主标品使用）
     */
    private String getPipelineNumWithEmployer(List<PersonalDTO> personList, StandardPlanDto planInfo) {
        PersonCheckVO personCheckVO = getPersonCheckVOWithEmployer(personList, planInfo);
        return personCheckVO.getPipelineNum();
    }

    /**
     * 调用核心增员接口并获取检查结果
     */
    private PersonCheckVO getPersonCheckVOWithEmployer(List<PersonalDTO> personList, StandardPlanDto planInfo) {
        personList.forEach(personalDTO -> {
            personalDTO.setPlanConfigId(planInfo.getPlanConfigId());
            personalDTO.setPlanName(planInfo.getPlanName());
            personalDTO.setGroupId(planInfo.getGroupId());
            personalDTO.setRelation(Relation.SELF);
            personalDTO.setEndTime(planInfo.getEndTime());
            personalDTO.setStartTime(planInfo.getStartTime());
        });
        return personService.postPersonListToCore(personList, planInfo.getGroupId());
    }

    @Override
    public StandardOrderInfoDto getInsuredInfoDetail(Long orderId) {
        // 获取订单
        IgStandardOrder standardOrder = standardOrderMapper.selectOne(orderId, true);
        if (Objects.isNull(standardOrder)) {
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_238"));
        }
        // 获取订单明细
        List<IgStandardOrderDetail> standardOrderDetailList = standardOrderDetailMapper.getDetailListByOrderId(orderId);
        // 返回组装的订单投保详情
        return assembleStandardOrderInfoDto(standardOrder, standardOrderDetailList);
    }

    /**
     * 组装订单投保详情
     */
    private StandardOrderInfoDto assembleStandardOrderInfoDto(IgStandardOrder standardOrder, List<IgStandardOrderDetail> standardOrderDetailList) {
        StandardOrderInfoDto standardOrderInfoDto = new StandardOrderInfoDto();
        // 订单id
        standardOrderInfoDto.setOrderId(standardOrder.getId());
        // 保障期间
        standardOrderInfoDto.setInsurancePeriod(Objects.nonNull(standardOrder.getInsurancePeriod()) ? String.valueOf(standardOrder.getInsurancePeriod()) : null);
        // 开始时间
        standardOrderInfoDto.setStartTime(standardOrder.getStartTime());
        // 结束时间
        standardOrderInfoDto.setEndTime(standardOrder.getEndTime());
        // 标品类型
        standardOrderInfoDto.setStandardType(standardOrder.getStandardType());
        // 总保费
        standardOrderInfoDto.setTotalAmount(standardOrder.getTotalAmount());
        // 续保方式
        standardOrderInfoDto.setRenewalMethod(standardOrder.getRenewalMethod());
        // 源订单id
        standardOrderInfoDto.setCloneSourceOrderId(standardOrder.getCloneSourceOrderId());
        // 标品方案列表
        List<StandardPlanInfoDto> standardPlanInfoList = constructStandardPlanInfoList(standardOrderDetailList, standardOrder.getStandardType());
        standardOrderInfoDto.setStandardPlanInfoList(standardPlanInfoList);
        // 投保企业信息
        standardOrderInfoDto.setInsuredGroup(constructInsuredGroup(standardOrder));
        // 联系人信息
        standardOrderInfoDto.setContactInfo(constructContactInfo(standardOrder));
        return standardOrderInfoDto;
    }

    /**
     * 组装联系人信息
     */
    private ContactInfoDto constructContactInfo(IgStandardOrder igStandardOrder) {
        ContactInfoDto contactInfoDto = new ContactInfoDto();
        // 联系人
        contactInfoDto.setContactPerson(igStandardOrder.getContactPerson());
        // 联系人手机号
        contactInfoDto.setContactMobile(igStandardOrder.getContactMobile());
        // 联系人邮箱
        contactInfoDto.setContactEmail(igStandardOrder.getContactEmail());
        return contactInfoDto;
    }

    /**
     * 组装投保企业信息
     */
    private InsuredGroupInfoDto constructInsuredGroup(IgStandardOrder igStandardOrder) {
        InsuredGroupInfoDto insuredGroupInfoDto = new InsuredGroupInfoDto();
        // 企业名称
        insuredGroupInfoDto.setGroupName(igStandardOrder.getGroupName());
        // 企业统一社会信用代码
        insuredGroupInfoDto.setGroupSocialCode(igStandardOrder.getGroupSocialCode());
        // 企业所在省份
        insuredGroupInfoDto.setProvince(igStandardOrder.getGroupProvince());
        // 企业所在城市
        insuredGroupInfoDto.setCity(igStandardOrder.getGroupCity());
        // 企业所在区县
        insuredGroupInfoDto.setCounty(igStandardOrder.getGroupCounty());
        // 企业详细地址
        insuredGroupInfoDto.setGroupAddress(igStandardOrder.getGroupAddress());
        return insuredGroupInfoDto;
    }

    /**
     * 组装投保方案列表
     */
    private List<StandardPlanInfoDto> constructStandardPlanInfoList(List<IgStandardOrderDetail> standardOrderDetailList, String standardType) {
        if (CollectionUtil.isNotEmpty(standardOrderDetailList)) {
            // 获取所有的标品方案
            List<Long> standardPlanIdlist = standardOrderDetailList.stream().map(IgStandardOrderDetail::getStandardPlanId).collect(Collectors.toList());
            Map<Long, StandardPlanDto> planInfoMapping = standardPlanMapper.getPlanInfoMappingByIdList(standardPlanIdlist);
            // 调用核心ui接口获取责任列表
            List<Long> planConfigList = planInfoMapping.values().stream().map(StandardPlanDto::getPlanConfigId).collect(Collectors.toList());
            ResponseVO<Map<Long, List<ProductClientUIDTO>>> planConfigDutyDetailBatch = productUIClient.getPlanConfigDutyDetailBatch(planConfigList, "1");
            Map<Long, List<ProductClientUIDTO>> dutyDetailListMapping = CommonUtil.getResponseData(planConfigDutyDetailBatch);
            // 返回构造的标品方案列表
            return doConstructStandardPlanInfoDtoList(standardOrderDetailList, standardType, planInfoMapping, dutyDetailListMapping);
        }
        return Collections.emptyList();
    }

    /**
     * 构造标品方案列表
     */
    private List<StandardPlanInfoDto> doConstructStandardPlanInfoDtoList(List<IgStandardOrderDetail> standardOrderDetailList,
                                                                         String standardType, Map<Long, StandardPlanDto> planInfoMapping,
                                                                         Map<Long, List<ProductClientUIDTO>> dutyDetailListMapping) {
        List<StandardPlanInfoDto> standardPlanInfoDtoList = new ArrayList<>();
        for (IgStandardOrderDetail standardOrderDetail : standardOrderDetailList) {
            // 标品方案
            StandardPlanDto standardPlanInfo = planInfoMapping.getOrDefault(standardOrderDetail.getStandardPlanId(), new StandardPlanDto());
            // 责任列表
            List<ProductClientUIDTO> dutyList = dutyDetailListMapping.getOrDefault(standardPlanInfo.getPlanConfigId(), new ArrayList<>());
            // 添加标品方案信息
            StandardPlanInfoDto standardPlanInfoDto = constructStandardPlanInfoDto(standardType, standardOrderDetail, standardPlanInfo, dutyList);
            standardPlanInfoDtoList.add(standardPlanInfoDto);
        }
        return standardPlanInfoDtoList;
    }

    /**
     * 构造标品方案信息dto
     */
    private StandardPlanInfoDto constructStandardPlanInfoDto(String standardType, IgStandardOrderDetail standardOrderDetail, StandardPlanDto standardPlanInfo, List<ProductClientUIDTO> dutyList) {
        StandardPlanInfoDto standardPlanInfoDto = new StandardPlanInfoDto();
        // 序号
        standardPlanInfoDto.setSequence(standardOrderDetail.getSequence());
        // 标品方案id
        standardPlanInfoDto.setStandardPlanId(standardOrderDetail.getStandardPlanId());
        // 标品名称
        standardPlanInfoDto.setTitle(standardPlanInfo.getTitle());
        // 上架状态
        standardPlanInfoDto.setShelfStatus(standardPlanInfo.getShelfStatus());
        // 核心的模版方案配置id
        standardPlanInfoDto.setPlanConfigId(standardOrderDetail.getPlanConfigId());
        // 投保人数
        standardPlanInfoDto.setPersonNum(standardOrderDetail.getPersonNum());
        // 职业类型
        standardPlanInfoDto.setJobCategory(standardOrderDetail.getOccupationType());
        // 职业类型列表
        standardPlanInfoDto.setJobCategoryList(standardPlanInfo.getSupportOccupationTypeList());
        // 保费
        standardPlanInfoDto.setPrice(standardOrderDetail.getPremium());
        // 保司id
        standardPlanInfoDto.setCompanyId(Long.valueOf(standardPlanInfo.getCompanyId()));
        //保司名称
        standardPlanInfoDto.setCompanyName(standardPlanInfo.getCompanyName());
        // 标品类型
        standardPlanInfoDto.setStandardType(standardType);
        // 责任列表
        standardPlanInfoDto.setDutyList(dutyList);
        // 流水线号
        standardPlanInfoDto.setPipeLineNum(standardOrderDetail.getPipeLineNum());
        // 订单明细id
        standardPlanInfoDto.setStandardOrderDetailId(standardOrderDetail.getId());
        // 拷贝的源明细id
        standardPlanInfoDto.setCloneSourceOrderDetailId((standardOrderDetail.getCloneSourceDetailId()));
        return standardPlanInfoDto;
    }

    @Override
    public Long submitInsuredInfo(StandardOrderInfoDto standardOrderInfoDto) {
        // 获取订单
        IgStandardOrder standardOrder = standardOrderMapper.entity(IgStandardOrder.class).selectOne(standardOrderInfoDto.getOrderId(), true);
        // 判断订单应该变更的状态
        String nextStatus = determineStatusOnEmployerSubmit(standardOrder.getStandardPaymentProcess());
        // 更新订单投保信息
        Long orderId = updateInsuredInfo(standardOrderInfoDto, nextStatus);
        // 更新数据
        updateDataOnEmployerSubmit(standardOrderInfoDto);
        // 立即投保聚合接口
        standardInsureService.insure(orderId, CustomContext.getChannelId());
        return orderId;
    }

    /**
     * 更新订单总保费
     * 回写明细上的费率
     */
    private void updateDataOnEmployerSubmit(StandardOrderInfoDto standardOrderInfoDto) {
        // 计算总保费
        CurrencyAmount totalAmount = getTotalAmountWithEmployer(standardOrderInfoDto);
        if (Objects.isNull(totalAmount)) {
            log.error("提交订单时计算总保费异常，orderId：{}", standardOrderInfoDto.getOrderId());
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_248"));
        }
        // 获取订单详情
        // 更新订单
        IgStandardOrder updateStandardOrder = new IgStandardOrder();
        updateStandardOrder.setId(standardOrderInfoDto.getOrderId());
        updateStandardOrder.setTotalAmount(totalAmount);
        standardOrderMapper.updateOne(updateStandardOrder, true);
        // 获取方案列表
        List<StandardPlanInfoDto> standardPlanInfoList = standardOrderInfoDto.getStandardPlanInfoList();
        // 获取订单明细
        List<IgStandardOrderDetail> standardOrderDetailList = standardOrderDetailMapper.getDetailListByOrderId(standardOrderInfoDto.getOrderId());
        Map<String, IgStandardOrderDetail> detailMapping = standardOrderDetailList.stream().collect(
                Collectors.toMap(
                        x -> x.getSequence() + "-" + x.getStandardPlanId(),
                        Function.identity(),
                        (a, b) -> a)
        );
        // 根据规则流计算结果回写订单明细上的费率
        List<IgStandardOrderDetail> updateStandardOrderDetailList = standardPlanInfoList.stream()
                .map(standardPlanInfoDto -> {
                    IgStandardOrderDetail detail = new IgStandardOrderDetail();
                    detail.setId(detailMapping.get(standardPlanInfoDto.getSequence() + "-" + standardPlanInfoDto.getStandardPlanId()).getId());
                    detail.setComputedPrice(standardPlanInfoDto.getComputedPrice());
                    return detail;
                })
                .collect(Collectors.toList());
        // 更新订单明细
        standardOrderDetailMapper.updateAll(updateStandardOrderDetailList, true);
    }

    /**
     * 判断订单提交时应该变更的状态（雇主标品）
     */
    private String determineStatusOnEmployerSubmit(String standardPaymentProcess) {
        // 判断变更后的订单状态
        String nextStatus;
        if (StandardPaymentProcessEnum.isFirstPaymentThenInsured(standardPaymentProcess)) {
            // 先付费后投保：待完善 -> 待支付
            nextStatus = StandardOrderStatusEnum.TO_BE_PAID.getDictKey();
        } else {
            // 先投保后付费：待完善 -> 投保中，需在立即投保聚合接口执行完成后触发自动投保
            nextStatus = StandardOrderStatusEnum.INSURING.getDictKey();
        }
        return nextStatus;
    }

    @Override
    public StandardOrderEmployerCheckRspDto submitInsuredInfoPreCheck(StandardOrderInfoDto standardOrderInfoDto) {

        List<StandardPlanInfoDto> standardPlanInfoList = standardOrderInfoDto.getStandardPlanInfoList();
        List<Long> standardPlanIdlist = standardPlanInfoList.stream().map(StandardPlanInfoDto::getStandardPlanId).collect(Collectors.toList());
        Map<Long, StandardPlanDto> standardPlanDtoMapping = standardPlanMapper.getPlanInfoMappingByIdList(standardPlanIdlist);

        List<StandardOrderEmployerCheckRspDto.EmployerCheckResultDto> employerCheckResultList = new ArrayList<>();
        for (StandardPlanInfoDto standardPlanInfoDto : standardPlanInfoList) {
            List<PersonalDTO> personList = standardPlanInfoDto.getPersonList();
            // 调用核心增员接口并获取检查结果
            PersonCheckVO personCheckVO = getPersonCheckVOWithEmployer(personList, standardPlanDtoMapping.get(standardPlanInfoDto.getStandardPlanId()));
            // 构造检查结果
            StandardOrderEmployerCheckRspDto.EmployerCheckResultDto employerCheckResult = constructEmployerCheckResult(standardPlanInfoDto, personCheckVO);
            employerCheckResultList.add(employerCheckResult);
        }
        // 构造返回结果
        StandardOrderEmployerCheckRspDto standardOrderEmployerCheckRspDto = new StandardOrderEmployerCheckRspDto();
        standardOrderEmployerCheckRspDto.setEmployerCheckResultList(employerCheckResultList);
        return standardOrderEmployerCheckRspDto;
    }

    /**
     * 构造检查结果
     */
    private StandardOrderEmployerCheckRspDto.EmployerCheckResultDto constructEmployerCheckResult(StandardPlanInfoDto standardPlanInfoDto, PersonCheckVO personCheckVO) {
        StandardOrderEmployerCheckRspDto.EmployerCheckResultDto employerCheckResult = new StandardOrderEmployerCheckRspDto.EmployerCheckResultDto();
        employerCheckResult.setSequence(standardPlanInfoDto.getSequence());
        employerCheckResult.setStandardPlanId(standardPlanInfoDto.getStandardPlanId());
        employerCheckResult.setPipeLineNum(personCheckVO.getPipelineNum());
        employerCheckResult.setHasError(CollectionUtil.isNotEmpty(personCheckVO.getCommonErrorList())
                || CollectionUtil.isNotEmpty(personCheckVO.getPersonErrorList()));
        return employerCheckResult;
    }

    @Override
    public CheckResultDto checkPersonNum(Long standardPlanId, Integer personNum) {
        List<StandardPlanRiskInfoDto> riskInfoList = standardPlanMapper.getPlanPersonRiskInfo(standardPlanId);

        // 如果风控参数为空，直接抛异常（说明没配风控）
        if (CollectionUtil.isEmpty(riskInfoList)) {
            log.error("风控参数为空，请检查标品方案风控参数配置，standardPlanId: {}", standardPlanId);
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_239"));
        }

        Map<String, StandardPlanRiskInfoDto> riskInfoMap = riskInfoList.stream()
                .collect(Collectors.toMap(StandardPlanRiskInfoDto::getBusinessType, Function.identity(), (a, b) -> a));

        // 校验是否包含“最小人数”和“最大人数”配置
        boolean hasMinCheck = riskInfoMap.containsKey(RiskBusinessTypeEnum.MIN_PERSON_CHECK.getValue());
        boolean hasMaxCheck = riskInfoMap.containsKey(RiskBusinessTypeEnum.MAX_PERSON_CHECK.getValue());

        if (!hasMinCheck || !hasMaxCheck) {
            log.error("风控参数配置不全，请检查标品方案风控参数配置，standardPlanId: {}", standardPlanId);
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_239"));
        }

        //  最小承保人数校验
        StandardPlanRiskInfoDto minRisk = riskInfoMap.get(RiskBusinessTypeEnum.MIN_PERSON_CHECK.getValue());
        Integer minPerson = minRisk.getRealMinPerson();
        if (minPerson == null) {
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_240"));
        }
        if (personNum < minPerson) {
            return CheckResultDto.failCheck(
                    MessageUtil.get("b_b_quote_242", minPerson),
                    PromptTypeEnum.INLINE
            );
        }

        // 最大承保人数校验
        StandardPlanRiskInfoDto maxRisk = riskInfoMap.get(RiskBusinessTypeEnum.MAX_PERSON_CHECK.getValue());
        Integer maxPerson = maxRisk.getRealMaxPerson();
        if (maxPerson == null) {
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_241"));
        }
        if (personNum > maxPerson) {
            return CheckResultDto.failCheck(
                    MessageUtil.get("b_b_quote_243", maxPerson),
                    PromptTypeEnum.INLINE
            );
        }

        return CheckResultDto.passCheck();
    }

    @Override
    public Long finishPay(StandardOrderPayInfoReqDto standardOrderPayInfoReqDto) {
        Long orderId = standardOrderPayInfoReqDto.getOrderId();
        // 拦截校验
        IgStandardOrder standardOrder = standardOrderMapper.selectOne(orderId, true);
        if (BooleanUtil.isFalse(standardOrder.getInsureTaskCompleted())) {
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_237"));
        }
        IgStandardOrder updateStandardOrder = new IgStandardOrder();
        updateStandardOrder.setId(standardOrderPayInfoReqDto.getOrderId());
        // 付款流水号
        updateStandardOrder.setPayerTransNo(standardOrderPayInfoReqDto.getPayerInfo().getPayerTransNo());
        // 付款银行
        updateStandardOrder.setPayerBankName(standardOrderPayInfoReqDto.getPayerInfo().getPayerBankName());
        // 发票邮箱
        updateStandardOrder.setInvoiceEmail(standardOrderPayInfoReqDto.getInvoiceInfo().getReceiveMail());
        // 发票类型
        updateStandardOrder.setInvoiceType(standardOrderPayInfoReqDto.getInvoiceInfo().getInvoiceType());
        // 发票开户行
        updateStandardOrder.setInvoiceBankName(standardOrderPayInfoReqDto.getInvoiceInfo().getBankName());
        // 订单状态
        // 先付款后投保：待支付 -> 支付结果确认中
        // 先投保后付款：投保成功待支付 -> 支付结果确认中
        String nextStatus = StandardOrderStatusEnum.TO_BE_CONFIRMED_PAYMENT.getDictKey();
        updateStandardOrder.setStatus(nextStatus);
        // 授权委托书
        Long attorneyFileId = standardOrderPayInfoReqDto.getAttorneyFileId();
        if (Objects.nonNull(attorneyFileId)) {
            updateStandardOrder.setAttorneyFileId(attorneyFileId);
            // 更新授权委托书文件，并发送【授权委托书审核提醒】邮件
            setStandardOrderAttorney(orderId, attorneyFileId);
        }
        // 更新订单
        standardOrderMapper.updateOne(updateStandardOrder, true);
        // 发送【客户完成支付提醒】邮件
        sendPaymentMailForEmployer(orderId);
        return orderId;
    }


    @Override
    public UploadResultDto attorneySingaporeDownload(Long orderId) {
        // IgStandardOrder.attorney_template_file_id oss_key

        IgStandardOrder standardOrder = standardOrderMapper.getSingaporeOrder(orderId);
        if (null == standardOrder) {
            throw new QuoteException(-1, "order is not exist");
        }

        Long attorneyTemplateFileId = standardOrder.getAttorneyTemplateFileId();
        if (Objects.isNull(attorneyTemplateFileId)) {
            throw new QuoteException(-1, "attorneyTemplateFileId is not exist");
        }

        ResponseVO<List<UploadResultDto>> fileResList = fileClient.listFile(Arrays.asList(attorneyTemplateFileId.toString()));
        if (Boolean.FALSE.equals(fileResList.ok())) {
            log.warn("下载授权委托书失败！返回数据：{}", JacksonUtils.writeAsString(fileResList));
            throw new QuoteException(-1, "get file fail");
        }

        List<UploadResultDto> data = fileResList.getData();
        if (CollectionUtils.isEmpty(data)) {
            log.warn("下载授权委托书失败！文件数据为空！fileResList：{}", JacksonUtils.writeAsString(fileResList));
            throw new QuoteException(-1, "get file fail");
        }

        return data.get(0);
    }

    @Override
    public void attorneySingaporeUpload(Long orderId, List<Long> fileList) {
        // 上传OSS
//                        ResponseVO<List<UploadResultDto>> responseVO = ossFileClient.uploadFiles(
//                multipartFileList.toArray(new MultipartFile[multipartFileList.size()]), "quote", "standard");
//        更新 IgStandardOrder.attorney_file_id oss_key
//        IgStandardOrder.status : SingaporeStandardOrderStatusEnum.WAIT_PAY_CONFIRM

        IgStandardOrder standardOrder = standardOrderMapper.getSingaporeOrder(orderId);
        if (null == standardOrder) {
            throw new QuoteException(-1, "order is not exist");
        }
        // 更新文件信息
        standardOrder.setAttorneyFileList(fileList);
        // 更新订单状态
        standardOrder.setStatus(SingaporeStandardOrderStatusEnum.WAIT_PAY_CONFIRM.getDictKey());
        standardOrderMapper.entity(IgStandardOrder.class).updateOne(standardOrder, true);
    }

    @Override
    public List<UploadResultDto> getOrderAttorneyList(Long orderId) {
        IgStandardOrder igStandardOrder = standardOrderMapper.getSingaporeOrder(orderId);
        if (null != igStandardOrder && null != igStandardOrder.getAttorneyFileList()) {
            Map<String, UploadResultDto> fileListByOssKeyList = filesService.getFileListByOssKeyList(igStandardOrder.getAttorneyFileList().stream().map(x -> x.toString()).collect(Collectors.toList()));

            return fileListByOssKeyList.values().stream().collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    @Override
    public StandardOrderShareRspDto getStandardShare(StandardOrderShareReqDto standardOrderShareReqDto) {
        StandardOrderShareRspDto standardOrderShareRspDto = new StandardOrderShareRspDto();
        ResponseVO<List<UploadResultDto>> listResponseVO = fileClient.listFile(Collections.singletonList("2280996898285674811"));
        UploadResultDto uploadResultDto = listResponseVO.getData().get(0);
        standardOrderShareRspDto.setQrCode(uploadResultDto.getLargeUrl());
        standardOrderShareRspDto.setUrl("/test/ksl/test");
        return standardOrderShareRspDto;
    }

    @Override
    public StandardOrderTotalAmountRspDto getTotalAmount(StandardOrderReqDto standardOrderReqDto) {
        StandardOrderTotalAmountRspDto standardOrderTotalAmountRspDto = new StandardOrderTotalAmountRspDto();
        List<PersonalDTO> personList = standardOrderReqDto.getPersonList();

        if (null == standardOrderReqDto.getMaxAge() || null == standardOrderReqDto.getMinAge()) {
            return standardOrderTotalAmountRspDto;
        }

        Integer defaultAgeMax = standardOrderReqDto.getMaxAge();
        int defaultAgeMin = standardOrderReqDto.getMinAge();

        // 返回不可计算结果
        if (!checkAge(defaultAgeMin, defaultAgeMax)) {
            return standardOrderTotalAmountRspDto;
        }
        Integer defaultAvgAge = (defaultAgeMax + defaultAgeMin) / 2;
        String defaultJobCategory = standardOrderReqDto.getJobCategory();
        Integer defaultFemaleRatio = standardOrderReqDto.getFemaleRatio();
        String defaultCity = standardOrderReqDto.getCity();
        Integer personNum = standardOrderReqDto.getPersonNum();
        List<StandardPriceCalcDto> standardPriceCalcDtoList = new ArrayList<>();
        StandardPlanDto planInfo = standardPlanMapper.getPlanInfo(standardOrderReqDto.getStandardPlanId());
        StandardPriceCalcDto standardPriceCalcDto = new StandardPriceCalcDto();
        if (CollUtil.isEmpty(personList)) {
            standardPriceCalcDto.setCity(defaultCity)
                    .setMinAge(defaultAvgAge)
                    .setFemaleRatio(defaultFemaleRatio)
                    .setPersonNum(personNum);
        } else {
            // 如果信息不全。返回
            boolean anyMatch = personList.stream().anyMatch(p -> StringUtils.isAnyBlank(p.getBirth(), p.getMedicareProv()) || Objects.isNull(p.getSex()));
            if (anyMatch)
                return standardOrderTotalAmountRspDto;
            standardPriceCalcDto.setPersonNum(personList.size());
            OptionalInt ageSum = personList.stream()
                    .map(PersonalDTO::getBirth)
                    .mapToInt(DateUtil::ageOfNow)
                    .reduce(Integer::sum);

            // 如果有有效人数，计算平均年龄
            if (!personList.isEmpty()) {
                int averageAge = ageSum.getAsInt() / personList.size();
                standardPriceCalcDto.setMinAge(averageAge);
            }

            // 计算女性占比
            long femaleCount = personList.stream()
                    .filter(p -> p.getSex() == Sex.FEMALE)
                    .count();

            int femaleRatio = BigDecimal.valueOf(femaleCount)
                    .divide(BigDecimal.valueOf(personList.size()), 2, RoundingMode.HALF_UP)
                    .multiply(BigDecimal.valueOf(100))
                    .intValue();

            standardPriceCalcDto.setFemaleRatio(femaleRatio);

            // 地区占比  11-北京  31-上海    -其他
            Map<String, Long> regionCount = personList.stream()
                    .collect(Collectors.groupingBy(PersonalDTO::getMedicareProv, Collectors.counting()));

            Optional<Map.Entry<String, Long>> maxRegion = regionCount.entrySet().stream()
                    .max(Map.Entry.comparingByValue());

            ResponseVO<List<DictDto>> provinceResponse = dictClient.listDepDict("province", "");
            List<DictDto> provinceList = provinceResponse.getData();
            if (maxRegion.isPresent()) {
                String mostCommonRegion = maxRegion.get().getKey();
                Optional<DictDto> dictDto = provinceList.stream().filter(x -> x.getDictValue().equals(mostCommonRegion)).findFirst();
                dictDto.ifPresent(dto -> standardPriceCalcDto.setCity(dto.getApiKey()));
            }

        }
        standardPriceCalcDto.setPrice(planInfo.getPrice())
                .setPlanConfigId(planInfo.getPlanConfigId())
                .setChannel(CustomContext.getChannelId().toString())
                .setPrice(planInfo.getPrice())
                .setType(planInfo.getType())
                .setJobCategory(defaultJobCategory);
        standardPriceCalcDtoList.add(standardPriceCalcDto);
        standardPlanService.getPlanPrice(standardPriceCalcDtoList);
        StandardPriceCalcDto calcDto = standardPriceCalcDtoList.get(0);

        // 不可计算的价格
        if (null == calcDto.getCalculatePrice()) {
            return standardOrderTotalAmountRspDto;
        }

        // 计算总价
        CurrencyAmount calculatePrice = calcDto.getCalculatePrice();
        BigDecimal calcPersonNum = BigDecimal.valueOf(!personList.isEmpty() ? personList.size() : personNum);
        CurrencyAmount totalAmount = CurrencyAmountUtil.multi(calculatePrice, calcPersonNum);
        standardOrderTotalAmountRspDto.setPrice(calculatePrice);
        standardOrderTotalAmountRspDto.setRatio(calcDto.getComputedRatio());
        return standardOrderTotalAmountRspDto.setTotalAmount(totalAmount);
    }

    private boolean checkAge(Integer defaultAgeMin, Integer defaultAgeMax) {
        int maxAge = 65;
        int minAge = 18;

        return defaultAgeMin != null && defaultAgeMax != null
                && defaultAgeMin <= defaultAgeMax
                && defaultAgeMin >= minAge
                && defaultAgeMax <= maxAge;
    }

    @Override
    public StandardOrderDetailRspDto getOrderDetail(Long orderId) {

        StandardOrderDto orderDetail = standardOrderMapper.getOrderDetailInfo(orderId);

        StandardOrderDetailRspDto standardOrderDetailRspDto = getStandardOrderDetailRspDto(orderDetail);

        // 付款信息
        StandardPayInfoDto paymentInfo = new StandardPayInfoDto();

        // 收款方信息
        StandardPayPayeeInfoDto standardPayPayeeInfoDto = new StandardPayPayeeInfoDto();
        standardPayPayeeInfoDto.setPayeeName(orderDetail.getPayeeName());
        standardPayPayeeInfoDto.setPayeeBankName(orderDetail.getPayeeBankName());
        standardPayPayeeInfoDto.setPayeeBankNo(orderDetail.getPayeeBankNo());
        paymentInfo.setPayeeInfo(standardPayPayeeInfoDto);

        // 付款方信息
        StandardPayPayerInfoDto standardPayPayerInfoDto = new StandardPayPayerInfoDto();
        standardPayPayerInfoDto.setPayerName(orderDetail.getPayerName());
        standardPayPayerInfoDto.setPayerBankName(orderDetail.getPayerBankName());
        standardPayPayerInfoDto.setPayerBankNo(orderDetail.getPayerBankNo());
        standardPayPayerInfoDto.setPayerTransNo(orderDetail.getPayerTransNo());
        paymentInfo.setPayerInfo(standardPayPayerInfoDto);

        // 发票信息
        StandardPayInvoiceInfoDto invoiceInfo = new StandardPayInvoiceInfoDto();
        invoiceInfo.setApplyTime(orderDetail.getInvoiceApplyTime());
        invoiceInfo.setTitle(orderDetail.getInvoiceTitle());
        invoiceInfo.setTaxNo(orderDetail.getInvoiceTaxNo());
        invoiceInfo.setBankName(orderDetail.getInvoiceBankName());
        invoiceInfo.setBankAccount(orderDetail.getPayerBankNo());
        invoiceInfo.setInvoiceType(orderDetail.getInvoiceType());
        invoiceInfo.setReceiveMail(orderDetail.getInvoiceEmail());
        paymentInfo.setInvoiceInfo(invoiceInfo);

        paymentInfo.setFinalPayTime(orderDetail.getFinalPayTime());

        paymentInfo.setInvoiceAppliedFlag(null != orderDetail.getInvoiceApplyTime());

        standardOrderDetailRspDto.setPaymentInfo(paymentInfo);

        standardOrderDetailRspDto.setAttorneyFileId(orderDetail.getAttorneyFileId());
        standardOrderDetailRspDto.setBillFileId(orderDetail.getPayNoticeFileId());

        // 授权委托书
        if (null != orderDetail.getAttorneyFileId()) {
            ResponseVO<List<UploadResultDto>> listResponseVO = fileClient.listFile(Collections.singletonList(orderDetail.getAttorneyFileId().toString()));
            if (!CollectionUtils.isEmpty(listResponseVO.getData())) {
                UploadResultDto uploadResultDto = listResponseVO.getData().get(0);
                standardOrderDetailRspDto.setAttorneyFileUrl(uploadResultDto.getLargeUrl());
            }
        }

        // 账单
        if (null != orderDetail.getPayNoticeFileId()) {
            ResponseVO<List<UploadResultDto>> listResponseVO = fileClient.listFile(Collections.singletonList(orderDetail.getPayNoticeFileId().toString()));
            if (!CollectionUtils.isEmpty(listResponseVO.getData())) {
                UploadResultDto uploadResultDto = listResponseVO.getData().get(0);
                standardOrderDetailRspDto.setBillFileUrl(uploadResultDto.getLargeUrl());
            }
        }

        // 保单号
        StandardOrderPolicyDto orderPolicyInfo = standardOrderMapper.getOrderPolicyInfo(orderId);
        standardOrderDetailRspDto.setPolicyNo(null == orderPolicyInfo ? "-" : orderPolicyInfo.getPolicyNum());

        // 标品类型，兼容处理，存在标品类型字段为空的历史数据
        standardOrderDetailRspDto.setStandardType(StrUtil.isNotBlank(orderDetail.getStandardType()) ? orderDetail.getStandardType() : StandardTypeEnum.WELFARE_RECOMMEND.getValue());

        // 支付流程
        standardOrderDetailRspDto.setStandardPaymentProcess(orderDetail.getStandardPaymentProcess());
        log.info("订单信息：{}", orderDetail);

        return standardOrderDetailRspDto;
    }

    private StandardOrderDetailRspDto getStandardOrderDetailRspDto(StandardOrderDto standardOrderDto) {
        BusinessInformationDto businessInformationDto = new BusinessInformationDto();
        businessInformationDto.setName(standardOrderDto.getGroupName());
        businessInformationDto.setOrgCode(standardOrderDto.getGroupOrgCode());
        businessInformationDto.setSocialCode(standardOrderDto.getGroupSocialCode());
        businessInformationDto.setAddress(standardOrderDto.getGroupAddress());
        businessInformationDto.setProvince(standardOrderDto.getGroupProvince());
        businessInformationDto.setCity(standardOrderDto.getGroupCity());
        businessInformationDto.setCounty(standardOrderDto.getGroupCounty());

        StandardOrderDetailRspDto standardOrderDetailRspDto = new StandardOrderDetailRspDto();
        standardOrderDetailRspDto.setStandardPlanId(Objects.nonNull(standardOrderDto.getStandardPlanId()) ? standardOrderDto.getStandardPlanId().toString() : null);
        standardOrderDetailRspDto.setStandardPlanName(standardOrderDto.getStandardPlanName());
        if (StandardTypeEnum.isEmployerPlan(standardOrderDto.getStandardType())) {
            standardOrderDetailRspDto.setInsurancePeriod(Objects.nonNull(standardOrderDto.getInsurancePeriod()) ? String.valueOf(standardOrderDto.getInsurancePeriod()) : null);
        } else {
            standardOrderDetailRspDto.setInsurancePeriod("12");
        }
        standardOrderDetailRspDto.setStartTime(standardOrderDto.getStartTime());
        standardOrderDetailRspDto.setEndTime(standardOrderDto.getEndTime());
        standardOrderDetailRspDto.setPersonNum(standardOrderDto.getPersonNum());
        standardOrderDetailRspDto.setGroupName(standardOrderDto.getGroupName());
        standardOrderDetailRspDto.setGroupInfo(businessInformationDto);
        standardOrderDetailRspDto.setContactPerson(standardOrderDto.getContactPerson());
        standardOrderDetailRspDto.setContactEmail(standardOrderDto.getContactEmail());
        standardOrderDetailRspDto.setContactMobile(standardOrderDto.getContactMobile());
        standardOrderDetailRspDto.setPipeLineNum(standardOrderDto.getPipeLineNum());
        standardOrderDetailRspDto.setTotalAmount(standardOrderDto.getTotalAmount());
        standardOrderDetailRspDto.setPolicyWorkDay(1);
        standardOrderDetailRspDto.setAttorneyInfo(null);
        standardOrderDetailRspDto.setStatus(standardOrderDto.getStatus());
        standardOrderDetailRspDto.setAgeMin(standardOrderDto.getMinAge());
        standardOrderDetailRspDto.setAgeMax(standardOrderDto.getMaxAge());
        standardOrderDetailRspDto.setCity(standardOrderDto.getCity());
        standardOrderDetailRspDto.setFemaleRatio(standardOrderDto.getFemaleRatio());
        standardOrderDetailRspDto.setJobCategory(standardOrderDto.getJobCategory());
        standardOrderDetailRspDto.setPrice(standardOrderDto.getPrice());
        return standardOrderDetailRspDto;
    }

    @Override
    public StandardOrderCheckRspDto checkOrder(StandardOrderReqDto standardOrderCheckReqDto) {

        // ...  其他检查
        StandardOrderCheckRspDto standardOrderCheckRspDto = new StandardOrderCheckRspDto();

        StandardPlanDto planInfo = standardPlanMapper.getPlanInfo(standardOrderCheckReqDto.getStandardPlanId());

        standardOrderCheckReqDto.getPersonList().forEach(personalDTO -> {
            personalDTO.setPlanConfigId(planInfo.getPlanConfigId());
            personalDTO.setPlanName(planInfo.getPlanName());
            personalDTO.setGroupId(planInfo.getGroupId());
            personalDTO.setRelation(Relation.SELF);
            personalDTO.setEndTime(planInfo.getEndTime());
            personalDTO.setStartTime(planInfo.getStartTime());
            personalDTO.setMedicareType(MedicareType.CITY_WORKERS);
        });
        PersonCheckVO personCheckVO = personService.postPersonListToCore(standardOrderCheckReqDto.getPersonList(), standardGroupConfig.getId());

        standardOrderCheckRspDto.setPipeLineNum(personCheckVO.getPipelineNum());
        if (CollectionUtils.isEmpty(personCheckVO.getCommonErrorList()) &&
                CollectionUtils.isEmpty(personCheckVO.getPersonErrorList())) {
            standardOrderCheckRspDto.setHasError(false);
        } else {
            standardOrderCheckRspDto.setHasError(true);
        }

        return standardOrderCheckRspDto;
    }

    @Override
    public StandardOrderSaveCheckRspDto checkOrderSave(StandardOrderReqDto standardOrderReqDto) {
        Long improvedOrderCount = standardOrderMapper.getImprovedOrderCount(standardOrderReqDto.getGroupName(),
                IdentityContext.getUserId(),
                standardOrderReqDto.getStartTime(),
                standardOrderReqDto.getStandardPlanId());

        StandardOrderSaveCheckRspDto standardOrderSaveCheckRspDto = new StandardOrderSaveCheckRspDto();
        standardOrderSaveCheckRspDto.setOrderCount(improvedOrderCount.intValue());
        return standardOrderSaveCheckRspDto;
    }

    /**
     * 更新委托书
     *
     * @param orderId
     * @param attorneyId
     */
    private void setStandardOrderAttorney(Long orderId, Long attorneyId) {

        // 更新文件
        QpFileDetails qpFileDetails = new QpFileDetails();
        qpFileDetails.setBusId(orderId);
        qpFileDetails.setBusType(QpFileDetailsConst.BusType.QP_STANDARD_ORDER.getCode());
        qpFileDetails.setBusClassify(QpFileDetailsConst.BusClassify.QP_STANDARD_ORDER_21.getCode());
        qpFileDetails.setOssKey(attorneyId.toString());
        qpFileDetailsMapper.entity(QpFileDetails.class).insertOne(qpFileDetails);

        // 更新委托书文件ID
        IgStandardOrder igStandardOrder = new IgStandardOrder();
        igStandardOrder.setId(orderId);
        igStandardOrder.setAttorneyFileId(attorneyId);
        standardOrderMapper.entity(IgStandardOrder.class).updateOne(igStandardOrder, true);

        // 获取发送邮件信息
        IgStandardOrder standardOrder = standardOrderMapper.entity(IgStandardOrder.class).selectOne(orderId, true);

        // 方案正在生成中
        if (BooleanUtil.isFalse(standardOrder.getInsureTaskCompleted())) {
            throw new QuoteException(-1, "方案确认中，请稍后提交");
        }

        // 渠道信息
        PTenant pTenant = tenantDataMapper.entity(PTenant.class).selectOne(standardOrder.getChannelId(), true);
        // 销售信息
        PUser pUser = userDataMapper.entity(PUser.class).selectOne(standardOrder.getBrokerId(), true);
        // 方案信息
        List<IgPlan> planList = standardOrderMapper.getPlanListByOrderId(orderId);

        // 邮件发送
        if (StandardTypeEnum.isEmployerPlan(standardOrder.getStandardType())) {
            sendAttorneyNoticeMailForEmployer(attorneyId, pTenant, planList, standardOrder, pUser);
        } else {
            sendAttorneyNoticeMailForWelfare(attorneyId, pTenant, planList, standardOrder, pUser);
        }
    }

    /**
     * 发送授权委托书审核提醒邮件（员福）
     */
    private void sendAttorneyNoticeMailForWelfare(Long attorneyId, PTenant pTenant, List<IgPlan> planList, IgStandardOrder standardOrder, PUser pUser) {
        // 发送邮件
        StandardMailConfig.MailInfo uploadAttorney = standardMailConfig.getUploadAttorney();

        StandardMailDto standardMailDto = new StandardMailDto();
        standardMailDto.setCode(uploadAttorney.getCode());
        standardMailDto.setTitle("【渠道标品】授权委托书审核提醒");

        standardMailDto.setTo(uploadAttorney.getTo());
        standardMailDto.setCc(uploadAttorney.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(uploadAttorney.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        standardMailDto.setOssKeyList(Collections.singletonList(attorneyId.toString()));
        HashMap<String, String> variables = new HashMap<>();
        variables.put("channelName", pTenant.getName());
        variables.put("planName", getPlanNameListStr(planList));
        variables.put("groupName", standardOrder.getGroupName());
        variables.put("brokerName", pUser.getName());
        variables.put("orderId", standardOrder.getId().toString());
        standardMailDto.setVariables(variables);

        standardMailService.sendMail(standardMailDto);
    }

    /**
     * 发送授权委托书审核提醒邮件（雇主）
     */
    private void sendAttorneyNoticeMailForEmployer(Long attorneyId, PTenant pTenant, List<IgPlan> planList, IgStandardOrder standardOrder, PUser pUser) {
        // 发送邮件
        StandardMailConfig.MailInfo uploadAttorneyEmployer = standardMailConfig.getUploadAttorneyEmployer();

        StandardMailDto standardMailDto = new StandardMailDto();
        standardMailDto.setCode(uploadAttorneyEmployer.getCode());
        standardMailDto.setTitle("【渠道标品】授权委托书审核提醒");

        standardMailDto.setTo(uploadAttorneyEmployer.getTo());
        standardMailDto.setCc(uploadAttorneyEmployer.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(uploadAttorneyEmployer.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        standardMailDto.setOssKeyList(Collections.singletonList(attorneyId.toString()));
        HashMap<String, String> variables = new HashMap<>();
        variables.put("channelName", pTenant.getName());
        variables.put("planName", getPlanNameListStr(planList));
        variables.put("groupName", standardOrder.getGroupName());
        variables.put("brokerName", pUser.getName());
        variables.put("orderId", standardOrder.getId().toString());
        standardMailDto.setVariables(variables);

        standardMailService.sendMail(standardMailDto);
    }

    /**
     * 提交委托书
     *
     * @param standardOrderReqDto 委托书对象
     * @return 结果
     */
    @Override
    public StandardFlagRspDto submitAttorney(StandardOrderReqDto standardOrderReqDto) {
        StandardFlagRspDto standardFlagRspDto = new StandardFlagRspDto();
        standardFlagRspDto.setFlag(true);

        Long orderId = standardOrderReqDto.getOrderId();
        Long ossKey = standardOrderReqDto.getOssKey();

        // 更新文件，发送邮件
        setStandardOrderAttorney(orderId, ossKey);

        IgStandardOrder standardOrder = standardOrderMapper.selectOne(orderId, true);
        String standardType = standardOrder.getStandardType();
        String standardPaymentProcess = standardOrder.getStandardPaymentProcess();

        // 更新订单状态并检查是否需要自动投保
        boolean needAutoPolicy = updateOrderStatusAndCheckAutoPolicy(orderId, standardType, standardPaymentProcess);

        // 自动投保
        if (needAutoPolicy) {
            autoPolicyStandardOrder(orderId);
            // 员福标品状态变更时需要发送mq
            if (StandardTypeEnum.isWelfareRecommend(standardType)) {
                standardOrderStatusChangeProducer.constructAndSendMessage(orderId);
            }
        }

        return standardFlagRspDto;
    }

    /**
     * 更新订单状态
     *
     * @param orderId                订单id
     * @param standardType           标品类型
     * @param standardPaymentProcess 标品付款流程
     * @return 是否需要自动投保
     */
    private boolean updateOrderStatusAndCheckAutoPolicy(Long orderId, String standardType, String standardPaymentProcess) {
        // 是否需要自动投保
        boolean needAutoPolicy = false;

        IgStandardOrder updateStandardOrder = new IgStandardOrder();
        updateStandardOrder.setId(orderId);

        if (StandardTypeEnum.isEmployerPlan(standardType)) {
            String nextStatus;
            if (StandardPaymentProcessEnum.isFirstPaymentThenInsured(standardPaymentProcess)) {
                // 先付费后投保：待上传授权委托书 -> 投保中（需触发自动投保）
                nextStatus = StandardOrderStatusEnum.INSURING.getDictKey();
                needAutoPolicy = true;
            } else {
                // 先投保后付费：待上传授权委托书 -> 订单完成
                nextStatus = StandardOrderStatusEnum.ORDER_COMPLETED.getDictKey();
            }
            updateStandardOrder.setStatus(nextStatus);
        } else {
            updateStandardOrder.setStatus(StandardOrderStatusEnum.INSURING.getDictKey());
            standardOrderLogMapper.record(updateStandardOrder.getId(), StandardOrderStatusEnum.INSURING.getDictKey());
            needAutoPolicy = true;
        }
        standardOrderMapper.entity(IgStandardOrder.class).updateOne(updateStandardOrder, true);
        return needAutoPolicy;
    }

    /**
     * 根据标品订单ID自动投保
     *
     * @param orderId            标品订单ID
     */
    @Override
    public void autoPolicyStandardOrder(Long orderId) {
        try {
            // 获取方案id列表
            List<Long> generatePlanIdList = standardOrderMapper.getGeneratePlanIdListByOrderId(orderId);
            // 自动投保
            if (CollectionUtil.isNotEmpty(generatePlanIdList)) {
                IdentityUtil.setRobotAuth();
                dockingClient.standardInsure(generatePlanIdList);
            } else {
                log.error("[标品]自动投保没有找到对应的订单：{}", orderId);
            }
        } catch (Exception e) {
            log.error("[标品]自动投保错误:", e);
        }
    }

    @Override
    public StandardFlagRspDto insuredFailedCallback(StandardOrderFinishReqDto standardOrderFinishReqDto) {
        StandardFlagRspDto standardFlagRspDto = new StandardFlagRspDto();
        standardFlagRspDto.setFlag(true);
        // 加缓存，防止重复执行
        String cacheKey = "standard_order_insured_failed_callback:" + standardOrderFinishReqDto.getOnlineInsuredId();
        if (Objects.nonNull(redisTemplate.opsForValue().get(cacheKey))) {
            return standardFlagRspDto;
        }
        List<Long> planIdList = standardOrderFinishReqDto.getPlanIdList();
        // 根据方案id列表获取标品订单
        IgStandardOrder standardOrder = standardOrderMapper.getOrderByPlanIdList(planIdList);
        if (Objects.isNull(standardOrder)) {
            log.error("【标品】投保失败回调没有找到对应的订单：{}", planIdList);
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_238"));
        }
        // 查询邮件发送使用的参数
        StandardOrderInsuredInfoDto standardOrderInsuredInfoDto = standardOrderMapper.getOrderInsuredInfo(standardOrder.getId());
        standardOrderInsuredInfoDto.setOnlineInsuredId(standardOrderFinishReqDto.getOnlineInsuredId());
        // 叠甲：【发邮件部分】目前员福跟雇主的邮件只有收件人、方案名称不一致，但是产品说后期可能会有调整，最好拆分开，所以基本一样的代码写了两份
        if (StandardTypeEnum.isEmployerPlan(standardOrder.getStandardType())) {
            // 处理雇主逻辑，发送投保失败邮件
            sendEmployerInsuredFiledEmail(standardOrderInsuredInfoDto);
        } else {
            // 处理员福逻辑，发送投保失败邮件
            sendWelfareInsuredFiledEmail(standardOrderInsuredInfoDto);
        }
        // 发送完写缓存，30秒内不重新发送邮件
        redisTemplate.opsForValue().set(cacheKey, standardOrderFinishReqDto.getOnlineInsuredId(), 30L, TimeUnit.SECONDS);
        return standardFlagRspDto;
    }

    /**
     * 雇主标品发送投保失败邮件
     */
    private void sendEmployerInsuredFiledEmail(StandardOrderInsuredInfoDto standardOrderInsuredInfoDto) {
        StandardMailConfig.MailInfo insuredFailedEmployerMainInfo = standardMailConfig.getInsuredFailedEmployer();

        StandardMailDto standardMailDto = new StandardMailDto();
        standardMailDto.setCode(insuredFailedEmployerMainInfo.getCode());
        standardMailDto.setTitle("【渠道标品】投保失败提醒" + DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN));
        standardMailDto.setTo(insuredFailedEmployerMainInfo.getTo());
        standardMailDto.setCc(insuredFailedEmployerMainInfo.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(insuredFailedEmployerMainInfo.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));
        standardMailDto.setType(2);
        standardMailDto.setSmtpFlag(1);

        HashMap<String, String> variables = new HashMap<>();
        variables.put("channel_name", standardOrderInsuredInfoDto.getChannelName());
        variables.put("owner_name", standardOrderInsuredInfoDto.getOwnerName());
        variables.put("insured_company", standardOrderInsuredInfoDto.getGroupName());
        variables.put("online_insured_id", String.valueOf(standardOrderInsuredInfoDto.getOnlineInsuredId()));
        variables.put("plan_name", getPlanNameListStr(standardOrderInsuredInfoDto.getPlanList()));
        standardMailDto.setVariables(variables);

        standardMailService.sendMail(standardMailDto);
    }

    /**
     * 员福标品发送投保失败邮件
     */
    public void sendWelfareInsuredFiledEmail(StandardOrderInsuredInfoDto standardOrderInsuredInfoDto) {
        StandardMailConfig.MailInfo insuredFailedMainInfo = standardMailConfig.getInsuredFailed();

        StandardMailDto standardMailDto = new StandardMailDto();
        standardMailDto.setCode(insuredFailedMainInfo.getCode());
        standardMailDto.setTitle("【渠道标品】投保失败提醒" + DateUtil.format(DateUtil.date(), DatePattern.PURE_DATETIME_PATTERN));
        standardMailDto.setTo(insuredFailedMainInfo.getTo());
        standardMailDto.setCc(insuredFailedMainInfo.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(insuredFailedMainInfo.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));
        standardMailDto.setType(2);
        standardMailDto.setSmtpFlag(1);

        HashMap<String, String> variables = new HashMap<>();
        variables.put("channel_name", standardOrderInsuredInfoDto.getChannelName());
        variables.put("owner_name", standardOrderInsuredInfoDto.getOwnerName());
        variables.put("insured_company", standardOrderInsuredInfoDto.getGroupName());
        variables.put("online_insured_id", String.valueOf(standardOrderInsuredInfoDto.getOnlineInsuredId()));
        variables.put("plan_name", getPlanNameListStr(standardOrderInsuredInfoDto.getPlanList()));
        standardMailDto.setVariables(variables);

        standardMailService.sendMail(standardMailDto);
    }

    @Override
    public StandardFlagRspDto finishOrder(StandardOrderFinishReqDto standardOrderFinishReqDto) {
        List<Long> planIdList = standardOrderFinishReqDto.getPlanIdList();
        // 根据方案id列表获取标品订单
        IgStandardOrder standardOrder = standardOrderMapper.getOrderByPlanIdList(planIdList);
        if (Objects.isNull(standardOrder)) {
            log.error("【标品】投保成功回调没有找到对应的订单：{}", planIdList);
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_238"));
        }
        // 叠甲：【发邮件部分】目前员福跟雇主的邮件只有收件人、方案名称不一致，但是产品说后期可能会有调整，最好拆分开，所以基本一样的代码写了两份
        if (StandardTypeEnum.isEmployerPlan(standardOrder.getStandardType())) {
            // 处理雇主逻辑
            handleEmployerOnInsuredSuccess(planIdList, standardOrder);
        } else {
            // 处理员福的逻辑
            handleWelfareOnOrderFinish(planIdList.get(0), standardOrder);
        }
        StandardFlagRspDto standardFlagRspDto = new StandardFlagRspDto();
        standardFlagRspDto.setFlag(true);
        return standardFlagRspDto;
    }

    /**
     * 处理雇主的逻辑
     */
    private void handleEmployerOnInsuredSuccess(List<Long> planIdList, IgStandardOrder standardOrder) {
        // 更新订单
        updateOrderOnInsuredSuccess(standardOrder);
        // 发送邮件
        sendEmailOnInsuredSuccess(planIdList, standardOrder);
    }

    /**
     * 雇主投保成功时发送邮件
     */
    private void sendEmailOnInsuredSuccess(List<Long> planIdList, IgStandardOrder standardOrder) {
        // 获取方案列表
        List<IgPlan> planList = planDataMapper.entity(IgPlan.class).select(DataCondition.<IgPlan>of().in("id", planIdList), true);
        // 发送经纪人邮件
        sendBrokerEmailWithEmployer(standardOrder, planList);
        // 发送企业邮件
        if (StandardNewGroupFlagEnum.NEW_GROUP.getValue().equals(standardOrder.getIsNewGroup())) {
            // 新企业发送邮件
            sendNewGroupEmailWithEmployer(standardOrder, planList);
        } else {
            // 旧企业发送邮件
            sendOldGroupEmailWithEmployer(standardOrder, planList);
        }
    }

    /**
     * 雇主旧企业发送邮件
     */
    private void sendOldGroupEmailWithEmployer(IgStandardOrder standardOrder, List<IgPlan> planList) {
        StandardMailConfig.MailInfo finishOrder = standardMailConfig.getFinishOrderEmployer();

        StandardMailDto standardMailDto = new StandardMailDto();
        standardMailDto.setCode(finishOrder.getCode());
        standardMailDto.setTitle("【智能团险平台】投保成功提醒【" + DateTimeUtil.format(DateTimeUtil.now(true), DateTimeFormatter.ofPattern("yyyyMMdd")) + "】");
        standardMailDto.setTo(standardOrder.getContactEmail());
        standardMailDto.setCc(finishOrder.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(finishOrder.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        HashMap<String, String> variables = new HashMap<>();
        variables.put("contractName", standardOrder.getContactPerson());
        variables.put("planName", getPlanNameListStr(planList));
        variables.put("startTime", formatDateTime(standardOrder.getStartTime()));
        variables.put("endTime", formatDateTime(standardOrder.getEndTime()));
        variables.put("personNum", String.valueOf(standardOrder.getPersonNum()));
        variables.put("url", getMailUrl(standardOrder));
        standardMailDto.setVariables(variables);

        standardMailService.sendMail(standardMailDto);
    }

    /**
     * 雇主投保成功时发送新企业邮件
     */
    private void sendNewGroupEmailWithEmployer(IgStandardOrder standardOrder, List<IgPlan> planList) {
        StandardMailConfig.MailInfo finishOrderFirst = standardMailConfig.getFinishOrderEmployerFirst();

        StandardMailDto standardMailDto = new StandardMailDto();
        standardMailDto.setCode(finishOrderFirst.getCode());
        standardMailDto.setTitle("【智能团险平台】投保成功提醒【" + DateTimeUtil.format(DateTimeUtil.now(true), DateTimeFormatter.ofPattern("yyyyMMdd")) + "】");
        standardMailDto.setTo(standardOrder.getContactEmail());
        standardMailDto.setCc(finishOrderFirst.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(finishOrderFirst.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        HashMap<String, String> variables = new HashMap<>();
        variables.put("contractName", standardOrder.getContactPerson());
        variables.put("planName", getPlanNameListStr(planList));
        variables.put("startTime", formatDateTime(standardOrder.getStartTime()));
        variables.put("endTime", formatDateTime(standardOrder.getEndTime()));
        variables.put("personNum", String.valueOf(standardOrder.getPersonNum()));
        variables.put("url", getMailUrl(standardOrder));
        standardMailDto.setVariables(variables);

        IgPlan igPlan = planDataMapper.entity(IgPlan.class).selectOne(planList.get(0).getId(), true);
        organizationClient.sendGroupMail(standardMailDto, igPlan.getGroupId());
    }

    /**
     * 雇主投保成功时发送经纪人邮件
     */
    private void sendBrokerEmailWithEmployer(IgStandardOrder standardOrder, List<IgPlan> planList) {
        // 经纪人邮件
        StandardMailConfig.MailInfo finishOrderBroker = standardMailConfig.getFinishOrderEmployerBroker();

        StandardMailDto standardMailDtoBroker = new StandardMailDto();
        standardMailDtoBroker.setCode(finishOrderBroker.getCode());
        standardMailDtoBroker.setTitle("【智能团险平台】投保成功提醒【" + DateTimeUtil.format(DateTimeUtil.now(true), DateTimeFormatter.ofPattern("yyyyMMdd")) + "】 - 【" + standardOrder.getGroupName() + "】");
        PUser pUser = userDataMapper.entity(PUser.class).selectOne(standardOrder.getBrokerId(), true);
        String email = pUser.getEmail();
        if (StringUtils.isBlank(email)) {
            email = standardMailConfig.getDefaultReceiver().stream().map(StandardMailConfig.MailDTO::getAddr).collect(Collectors.joining(","));
        }
        standardMailDtoBroker.setTo(email);
        standardMailDtoBroker.setCc(finishOrderBroker.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDtoBroker.setBcc(finishOrderBroker.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        HashMap<String, String> variablesBroker = new HashMap<>();
        variablesBroker.put("planName", getPlanNameListStr(planList));
        variablesBroker.put("groupName", standardOrder.getGroupName());
        variablesBroker.put("startTime", formatDateTime(standardOrder.getStartTime()));
        variablesBroker.put("endTime", formatDateTime(standardOrder.getEndTime()));
        variablesBroker.put("personNum", String.valueOf(standardOrder.getPersonNum()));
        standardMailDtoBroker.setVariables(variablesBroker);

        standardMailService.sendMail(standardMailDtoBroker);
    }

    /**
     * 格式化时间
     */
    private String formatDateTime(ZonedDateTime zonedDateTime) {
        return DateTimeUtil.format(zonedDateTime, DateTimeUtil.DEFAULT_FORMATTER);
    }

    /**
     * 获取多个方案名称拼接后的字符串
     */
    private String getPlanNameListStr(List<IgPlan> planList) {
        return planList.stream().map(IgPlan::getPlanName).collect(Collectors.joining("，"));
    }

    /**
     * 雇主投保成功时更新订单
     */
    private void updateOrderOnInsuredSuccess(IgStandardOrder standardOrder) {
        IgStandardOrder updateStandardOrder = new IgStandardOrder();
        updateStandardOrder.setId(standardOrder.getId());
        ZonedDateTime successInsuredTime = DateTimeUtil.now(true);
        updateStandardOrder.setSuccessInsuredTime(successInsuredTime);
        String nextStatus;
        if (StandardPaymentProcessEnum.isFirstPaymentThenInsured(standardOrder.getStandardPaymentProcess())) {
            // 先付费后投保：投保中 -> 订单完成
            nextStatus = StandardOrderStatusEnum.ORDER_COMPLETED.getDictKey();
        } else {
            // 先投保后付费：投保中 -> 投保成功待支付
            nextStatus = StandardOrderStatusEnum.INSURED_TO_BE_PAID.getDictKey();
            // 推算支付截止日期
            ZonedDateTime finalPayTime = reckonFinalPayTimeOnInsuredSuccessWithEmployer(successInsuredTime);
            updateStandardOrder.setFinalPayTime(finalPayTime);
        }
        updateStandardOrder.setStatus(nextStatus);
        standardOrderMapper.entity(IgStandardOrder.class).updateOne(updateStandardOrder, true);
    }

    /**
     * 推算支付截止日期（投保成功后）
     */
    private ZonedDateTime reckonFinalPayTimeOnInsuredSuccessWithEmployer(ZonedDateTime successInsuredTime) {
        // 获取最终付款时间，先投保后付款的流程：支付截止时间为投保成功时间往后推三个工作日
        String startDateStr = DateTimeUtil.format(successInsuredTime, DateTimeUtil.YYYY_MM_DD);
        // 工作日推算，返回结果只有日期部分
        ResponseVO<String> reckonResultResponseVO = dateClient.findDateAfterNum(startDateStr, standardCommonConfig.getEmployerAfterFinalPaymentOffsetDays());
        String reckonDateStr = CommonUtil.getResponseData(reckonResultResponseVO);
        // 举例：投保成功时间是2025-05-22 14:23:59，则自动关闭时间为2025-05-25 00:00:00
        // 将推算出的工作日日期（仅包含年月日）与每日开始时间合并，生成最终付款时间
        return ZonedDateTime.of(
                LocalDate.parse(reckonDateStr),
                LocalTime.MIDNIGHT,
                successInsuredTime.getZone()
        );
    }

    /**
     * 处理员福的逻辑
     */
    private void handleWelfareOnOrderFinish(Long planId, IgStandardOrder standardOrder) {
        // 更新订单
        updateOrderOnOrderFinish(standardOrder);
        // 发送邮件
        sendEmailOnOrderFinish(planId, standardOrder);
    }

    /**
     * 员福订单完成时发送邮件
     */
    private void sendEmailOnOrderFinish(Long planId, IgStandardOrder standardOrder) {
        // 获取标品信息
        StandardPlanDto planInfo = standardPlanMapper.getPlanInfo(standardOrder.getStandardPlanId());
        // 给经纪人发送邮件
        sendBrokerEmailWithWelfare(standardOrder, planInfo);
        // 发送企业邮件
        if (StandardNewGroupFlagEnum.NEW_GROUP.getValue().equals(standardOrder.getIsNewGroup())) {
            // 新企业发送邮件
            sendNewGroupEmailWithWelfare(planId, standardOrder, planInfo);
        } else {
            // 旧企业发送邮件
            sendOldGroupEmailWithWelfare(standardOrder, planInfo);
        }
    }

    /**
     * 员福投保成功时给旧企业发送邮件
     */
    private void sendOldGroupEmailWithWelfare(IgStandardOrder standardOrder, StandardPlanDto planInfo) {
        StandardMailConfig.MailInfo finishOrder = standardMailConfig.getFinishOrder();

        StandardMailDto standardMailDto = new StandardMailDto();
        standardMailDto.setCode(finishOrder.getCode());
        standardMailDto.setTitle("【智能团险平台】投保成功提醒【" + DateTimeUtil.format(DateTimeUtil.now(true), DateTimeFormatter.ofPattern("yyyyMMdd")) + "】 - 【" + planInfo.getPlanName() + "】");
        standardMailDto.setTo(standardOrder.getContactEmail());
        standardMailDto.setCc(finishOrder.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(finishOrder.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        HashMap<String, String> variables = new HashMap<>();
        variables.put("contractName", standardOrder.getContactPerson());
        variables.put("planName", planInfo.getPlanName());
        variables.put("startTime", formatDateTime(standardOrder.getStartTime()));
        variables.put("endTime", formatDateTime(standardOrder.getEndTime()));
        variables.put("personNum", standardOrder.getPersonNum().toString());
        variables.put("url", getMailUrl(standardOrder));
        standardMailDto.setVariables(variables);

        standardMailService.sendMail(standardMailDto);
    }

    /**
     * 员福投保成功时给新企业发送邮件
     */
    private void sendNewGroupEmailWithWelfare(Long planId, IgStandardOrder standardOrder, StandardPlanDto planInfo) {
        StandardMailConfig.MailInfo finishOrderFirst = standardMailConfig.getFinishOrderFirst();

        StandardMailDto standardMailDto = new StandardMailDto();

        standardMailDto.setCode(finishOrderFirst.getCode());
        standardMailDto.setTitle("【智能团险平台】投保成功提醒【" + DateTimeUtil.format(DateTimeUtil.now(true), DateTimeFormatter.ofPattern("yyyyMMdd")) + "】 - 【" + planInfo.getPlanName() + "】");
        standardMailDto.setTo(standardOrder.getContactEmail());
        standardMailDto.setCc(finishOrderFirst.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(finishOrderFirst.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        HashMap<String, String> variables = new HashMap<>();
        variables.put("contractName", standardOrder.getContactPerson());
        variables.put("planName", planInfo.getPlanName());
        variables.put("startTime", formatDateTime(standardOrder.getStartTime()));
        variables.put("endTime", formatDateTime(standardOrder.getEndTime()));
        variables.put("personNum", standardOrder.getPersonNum().toString());
        variables.put("url", getMailUrl(standardOrder));
        standardMailDto.setVariables(variables);

        IgPlan igPlan = planDataMapper.entity(IgPlan.class).selectOne(planId, true);
        organizationClient.sendGroupMail(standardMailDto, igPlan.getGroupId());
    }

    /**
     * 员福标品发送经纪人邮件
     */
    private void sendBrokerEmailWithWelfare(IgStandardOrder standardOrder, StandardPlanDto planInfo) {
        // 经纪人邮件
        StandardMailConfig.MailInfo finishOrderBroker = standardMailConfig.getFinishOrderBroker();

        StandardMailDto standardMailDtoBroker = new StandardMailDto();
        standardMailDtoBroker.setCode(finishOrderBroker.getCode());
        standardMailDtoBroker.setTitle("【智能团险平台】投保成功提醒【" + DateTimeUtil.format(DateTimeUtil.now(true), DateTimeFormatter.ofPattern("yyyyMMdd")) + "】 - 【" + standardOrder.getGroupName() + "】");
        PUser pUser = userDataMapper.entity(PUser.class).selectOne(standardOrder.getBrokerId(), true);
        String email = pUser.getEmail();
        if (StringUtils.isBlank(email)) {
            email = standardMailConfig.getDefaultReceiver().stream().map(StandardMailConfig.MailDTO::getAddr).collect(Collectors.joining(","));
        }
        standardMailDtoBroker.setTo(email);
        standardMailDtoBroker.setCc(finishOrderBroker.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDtoBroker.setBcc(finishOrderBroker.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        HashMap<String, String> variablesBroker = new HashMap<>();
        variablesBroker.put("planName", planInfo.getPlanName());
        variablesBroker.put("groupName", standardOrder.getGroupName());
        variablesBroker.put("startTime", formatDateTime(standardOrder.getStartTime()));
        variablesBroker.put("endTime", formatDateTime(standardOrder.getEndTime()));
        variablesBroker.put("personNum", standardOrder.getPersonNum().toString());
        standardMailDtoBroker.setVariables(variablesBroker);

        standardMailService.sendMail(standardMailDtoBroker);
    }

    /**
     * 员福订单完成更新订单数据
     */
    private void updateOrderOnOrderFinish(IgStandardOrder standardOrder) {
        IgStandardOrder updateStandardOrder = new IgStandardOrder();
        updateStandardOrder.setId(standardOrder.getId());
        updateStandardOrder.setStatus(StandardOrderStatusEnum.ORDER_COMPLETED.getDictKey());
        updateStandardOrder.setSuccessInsuredTime(DateTimeUtil.now(true));
        standardOrderMapper.entity(IgStandardOrder.class).updateOne(updateStandardOrder, true);
        log.info("员福订单完成更新状态，订单id：{}", standardOrder.getId());
        standardOrderLogMapper.record(standardOrder.getId(), StandardOrderStatusEnum.ORDER_COMPLETED.getDictKey());
        standardOrderStatusChangeProducer.constructAndSendMessage(standardOrder.getId());
    }

    /**
     * 获取邮件url
     */
    private String getMailUrl(IgStandardOrder igStandardOrder) {
        String url;
        Long channelId = igStandardOrder.getChannelId();
        Long defaultChannelId = standardMailConfig.getDefaultChannelId();
        String insgeekHrUrl = standardMailConfig.getInsgeekHrUrl();
        if (Objects.equals(defaultChannelId, channelId)) {
            url = insgeekHrUrl;
        } else {
            url = standardMailConfig.getUrl() + "?channel=" + channelId;
        }
        return url;
    }

    @Override
    public CurrencyAmount getTotalAmountWithEmployer(StandardOrderInfoDto standardOrderInfoDto) {
        try {
            List<StandardPlanInfoDto> standardPlanInfoList = standardOrderInfoDto.getStandardPlanInfoList();
            if (CollectionUtil.isEmpty(standardPlanInfoList)) {
                return null;
            }
            // 判断是否存在非法数据
            boolean hasInvalidData = standardPlanInfoList.stream().anyMatch(plan ->
                    StrUtil.isBlank(plan.getJobCategory()) ||
                            Objects.isNull(plan.getPersonNum()) ||
                            CurrencyAmountUtil.isNull(plan.getPrice()) ||
                            Objects.isNull(plan.getStandardPlanId()) ||
                            Objects.isNull(plan.getPlanConfigId())
            ) || Objects.isNull(standardOrderInfoDto.getInsurancePeriod());
            if (hasInvalidData) {
                return null;
            }
            // 计算保费前的预检查
            calcPreCheck(standardOrderInfoDto);
            // 计算方案保费
            return calcPlanTotalPrice(standardOrderInfoDto.getStandardPlanInfoList());
        } catch (Exception e) {
            log.info("计算保费异常", e);
            // 与前端的约定，返回null后前端会展示“-”
            return null;
        }
    }

    /**
     * 计算保费前的预检查
     */
    private void calcPreCheck(StandardOrderInfoDto standardOrderInfoDto) {
        List<StandardPlanInfoDto> standardPlanInfoList = standardOrderInfoDto.getStandardPlanInfoList();
        // 组装规则流执行参数
        List<StandardEmployerPriceCalcDto> standardEmployerPriceCalcDtoList = new ArrayList<>();
        for (StandardPlanInfoDto standardPlanInfoDto : standardPlanInfoList) {
            // 设置保险期间
            standardPlanInfoDto.setInsurancePeriod(standardOrderInfoDto.getInsurancePeriod());
            // 组装规则流执行参数
            StandardEmployerPriceCalcDto standardEmployerPriceCalcDto = constructPriceCalcWithEmployer(standardPlanInfoDto);
            standardEmployerPriceCalcDtoList.add(standardEmployerPriceCalcDto);
        }
        // 执行规则流并回写计算后的价格
        standardPlanService.executeRuleAndFillEmployerPlanPrice(standardEmployerPriceCalcDtoList);
        // 提取计算结果
        Map<String, StandardEmployerPriceCalcDto> calcResultMapping = standardEmployerPriceCalcDtoList.stream()
                .collect(Collectors.toMap(
                        x -> x.getSequence() + "-" + x.getStandardPlanId(),
                        Function.identity(),
                        (o1, o2) -> o1)
                );
        // 校验是否合法
        for (StandardPlanInfoDto standardPlanInfoDto : standardPlanInfoList) {
            // 获取该方案计算后的价格
            String key = standardPlanInfoDto.getSequence() + "-" + standardPlanInfoDto.getStandardPlanId();
            StandardEmployerPriceCalcDto standardEmployerPriceCalcDto = calcResultMapping.get(key);
            // 校验当前填写的值是否合法
            CheckResultDto checkResultDto = doCheckPlanPrice(standardEmployerPriceCalcDto);
            if (checkResultDto.getIsPassCheck() != null && !checkResultDto.getIsPassCheck()) {
                throw new QuoteException(-1, MessageUtil.get("b_b_quote_247"));
            }
        }
    }

    /**
     * 计算已上传人员清单的方案保费
     */
    private CurrencyAmount calcPlanTotalPrice(List<StandardPlanInfoDto> includePersonPlanList) {
        // 总价格
        CurrencyAmount includePersonPlanTotalPrice = CurrencyAmount.zero(IdentityContext.getBusinessCurrency());
        for (StandardPlanInfoDto standardPlanInfoDto : includePersonPlanList) {
            // 按照填写的保费计算
            BigDecimal price = standardPlanInfoDto.getPrice().getAmount();
            BigDecimal insurancePeriod = new BigDecimal(standardPlanInfoDto.getInsurancePeriod());
            // 回写计算后的价格
            CurrencyAmount computedPrice = CurrencyAmount.valueOf(
                    price.multiply(insurancePeriod),
                    IdentityContext.getBusinessCurrency()
            );
            standardPlanInfoDto.setComputedPrice(computedPrice);
            // 实际的投保人数
            List<PersonalDTO> personList = standardPlanInfoDto.getPersonList();
            int realPersonNum = CollectionUtil.isNotEmpty(personList) ? personList.size() : standardPlanInfoDto.getPersonNum();
            // 计算当前方案的总价格
            CurrencyAmount planTotalPrice = CurrencyAmount.valueOf(
                    price.multiply(insurancePeriod).multiply(BigDecimal.valueOf(realPersonNum)),
                    IdentityContext.getBusinessCurrency()
            );
            // 累加求和
            includePersonPlanTotalPrice = CurrencyAmountUtil.add(
                    includePersonPlanTotalPrice,
                    planTotalPrice
            );
        }
        return includePersonPlanTotalPrice;
    }

    /**
     * 构造保费计算参数（雇主标品）
     */
    private StandardEmployerPriceCalcDto constructPriceCalcWithEmployer(StandardPlanInfoDto standardPlanInfoDto) {
        StandardEmployerPriceCalcDto standardEmployerPriceCalcDto = new StandardEmployerPriceCalcDto();
        standardEmployerPriceCalcDto.setSequence(standardPlanInfoDto.getSequence());
        standardEmployerPriceCalcDto.setStandardPlanId(standardPlanInfoDto.getStandardPlanId());
        standardEmployerPriceCalcDto.setPlanConfigId(standardPlanInfoDto.getPlanConfigId());
        standardEmployerPriceCalcDto.setPersonNum(standardPlanInfoDto.getPersonNum());
        standardEmployerPriceCalcDto.setJobCategory(standardPlanInfoDto.getJobCategory());
        standardEmployerPriceCalcDto.setCompanyId(standardPlanInfoDto.getCompanyId());
        standardEmployerPriceCalcDto.setPrice(standardPlanInfoDto.getPrice());
        standardEmployerPriceCalcDto.setInsurancePeriod(standardPlanInfoDto.getInsurancePeriod());
        return standardEmployerPriceCalcDto;
    }

    @Override
    public void sendEmployerOrderDealReminderEmail(TimeRangeDTO timeRangeDTO) {
        // 获取参数
        ZonedDateTime startTime = timeRangeDTO.getStartTime();
        ZonedDateTime endTime = timeRangeDTO.getEndTime();
        // 获取明细信息
        List<StandardDealReminderDetailInfoDto> standardDealReminderDetailInfoDtoList = getDealReminderDetailInfoList(startTime, endTime);
        // 获取汇总信息
        List<StandardDealReminderStatisticsInfoDto> dealReminderStatisticsInfoList = getDealReminderStatisticsInfoList(startTime, endTime, standardDealReminderDetailInfoDtoList);
        String startTimeStr = DateTimeUtil.format(startTime, DateTimeFormatter.ofPattern("yyyyMMdd"));
        String endTimeStr = DateTimeUtil.format(endTime, DateTimeFormatter.ofPattern("yyyyMMdd"));
        // 是否有成交数据
        boolean hasDeal = false;
        String ossKey = null;
        if (CollectionUtil.isNotEmpty(standardDealReminderDetailInfoDtoList)) {
            hasDeal = true;
            // 生成excel
            ossKey = generateExcelAndUploadOss(startTimeStr, endTimeStr, standardDealReminderDetailInfoDtoList, dealReminderStatisticsInfoList);
        }
        // 发送邮件
        doSendEmployerOrderDealReminderEmail(startTimeStr, endTimeStr, hasDeal, ossKey);
    }

    /**
     * 发送雇主标品成交提醒邮件
     */
    private void doSendEmployerOrderDealReminderEmail(String startTime, String endTime, boolean hasDeal, String ossKey) {
        StandardMailConfig.MailInfo orderDealReminderEmployer = standardMailConfig.getOrderDealReminderEmployer();

        StandardMailDto standardMailDto = new StandardMailDto();

        standardMailDto.setCode(orderDealReminderEmployer.getCode());
        standardMailDto.setTitle("雇主标品成交数据统计" + startTime + "-" + endTime + ".xlsx");
        standardMailDto.setTo(orderDealReminderEmployer.getTo());
        standardMailDto.setCc(orderDealReminderEmployer.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(orderDealReminderEmployer.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        if (StrUtil.isNotBlank(ossKey)) {
            standardMailDto.setOssKeyList(Collections.singletonList(ossKey));
        }

        HashMap<String, String> variables = new HashMap<>();
        String message = "【" + startTime + "-" + endTime + "】" + (hasDeal ? "雇主标品成交数据详见附件。" : "本周期雇主标品无成交数据。");
        variables.put("message", message);
        standardMailDto.setVariables(variables);

        standardMailService.sendMail(standardMailDto);
    }

    /**
     * 生成excel文件并上传到Oss
     */
    private String generateExcelAndUploadOss(String startTime,
                                             String endTime,
                                             List<StandardDealReminderDetailInfoDto> standardDealReminderDetailInfoDtoList,
                                             List<StandardDealReminderStatisticsInfoDto> dealReminderStatisticsInfoList) {
        // 生成excel文件
        MultipartFile multipartFile = generateExcel(startTime, endTime, dealReminderStatisticsInfoList, standardDealReminderDetailInfoDtoList);
        List<UploadResultDto> uploadResultDtoList = upLoadFileToOss(multipartFile);
        // 返回OssKey
        return uploadResultDtoList.get(0).getKey();
    }

    private List<UploadResultDto> upLoadFileToOss(MultipartFile multipartFile) {
        // 将生成的文件上传到OSS
        ResponseVO<List<UploadResultDto>> response = fileClient.uploadFiles(
                new MultipartFile[]{multipartFile},
                "quote",
                "standard"
        );
        log.info("文件上传完成，返回结果：{}", response);
        List<UploadResultDto> uploadResultDtoList = CommonUtil.getResponseData(response);
        return uploadResultDtoList;
    }

    /**
     * 生成excel文件
     */
    private MultipartFile generateExcel(String startTime,
                                        String endTime,
                                        List<StandardDealReminderStatisticsInfoDto> dealReminderStatisticsInfoList,
                                        List<StandardDealReminderDetailInfoDto> standardDealReminderDetailInfoDtoList) {
        // 构造临时文件名称
        String tempFileName = cn.hutool.core.lang.UUID.randomUUID().toString(true) + ".xlsx";

        // 获取系统临时目录
        String tempDir = System.getProperty("java.io.tmpdir");
        File excelFile = new File(tempDir, tempFileName);
        Path excelFilePath = excelFile.toPath();

        try {
            // 创建 ExcelWriter 写入 Excel 文件
            ExcelWriter excelWriter = EasyExcel.write(excelFile)
                    // 设置列宽自动适应
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    // 设置样式
                    .registerWriteHandler(createCenterStyleStrategy())
                    .build();

            WriteSheet sheet1 = EasyExcel.writerSheet(0,"统计数据")
                    .head(StandardDealReminderStatisticsInfoDto.class)
                    .build();
            excelWriter.write(dealReminderStatisticsInfoList, sheet1);

            WriteSheet sheet2 = EasyExcel.writerSheet(1, "明细数据")
                    .head(StandardDealReminderDetailInfoDto.class)
                    .build();
            excelWriter.write(standardDealReminderDetailInfoDtoList, sheet2);

            excelWriter.finish();

            // 构造文件名称
            String fileName = "雇主标品成交数据统计" + startTime + "-" + endTime + ".xlsx";

            // 将 File 转为 MultipartFile
            return fileToMultipartFile(excelFile, fileName);
        } catch (Exception e) {
            log.error("生成或上传 Excel 文件失败", e);
            throw new QuoteException(-1, "生成Excel文件失败");
        } finally {
            deleteTmpFile(excelFilePath);
        }
    }

    private HorizontalCellStyleStrategy createCenterStyleStrategy() {
        // 创建头部样式
        WriteCellStyle headWriteCellStyle = new WriteCellStyle();
        headWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        headWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 创建字体样式
        WriteFont headWriteFont = new WriteFont();
        // 设置加粗
        headWriteFont.setBold(true);
        headWriteCellStyle.setWriteFont(headWriteFont);

        // 创建内容样式
        WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
        contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
        contentWriteCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 返回样式策略
        return new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
    }


    /**
     * 将 File 转换为 MultipartFile
     */
    private MultipartFile fileToMultipartFile(File file, String fileName) throws IOException {
        FileInputStream inputStream = new FileInputStream(file);
        return new MockMultipartFile(
                "file",
                fileName,
                MediaType.APPLICATION_OCTET_STREAM_VALUE,
                inputStream
        );
    }


    /**
     * 将订单信息汇总
     */
    private List<StandardDealReminderStatisticsInfoDto> getDealReminderStatisticsInfoList(ZonedDateTime startTime,
                                                                                          ZonedDateTime endTime,
                                                                                          List<StandardDealReminderDetailInfoDto> standardDealReminderDetailInfoDtoList) {
        // 将统计的订单信息汇总
        List<StandardDealReminderStatisticsInfoDto> dealReminderStatisticsInfoList = new ArrayList<>();
        Map<String, List<StandardDealReminderDetailInfoDto>> detailInfoMap = standardDealReminderDetailInfoDtoList.stream()
                .collect(Collectors.groupingBy(dealReminderDetailInfoDto -> dealReminderDetailInfoDto.getChannelId() + "-" + dealReminderDetailInfoDto.getCompanyId()));
        for (Map.Entry<String, List<StandardDealReminderDetailInfoDto>> detailInfo : detailInfoMap.entrySet()) {
            List<StandardDealReminderDetailInfoDto> detailInfoDtoList = detailInfo.getValue();
            StandardDealReminderStatisticsInfoDto reminderStatisticsInfoDto = new StandardDealReminderStatisticsInfoDto();
            reminderStatisticsInfoDto.setStartTime(startTime);
            reminderStatisticsInfoDto.setEndTime(endTime);
            reminderStatisticsInfoDto.setChannelId(detailInfoDtoList.get(0).getChannelId());
            reminderStatisticsInfoDto.setChannelName(detailInfoDtoList.get(0).getChannelName());
            reminderStatisticsInfoDto.setCompanyId(detailInfoDtoList.get(0).getCompanyId());
            reminderStatisticsInfoDto.setCompanyName(detailInfoDtoList.get(0).getCompanyName());
            BigDecimal totalAmount = detailInfoDtoList.stream()
                    .map(StandardDealReminderDetailInfoDto::getTotalAmount)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            reminderStatisticsInfoDto.setTotalAmount(totalAmount);
            dealReminderStatisticsInfoList.add(reminderStatisticsInfoDto);
        }
        return dealReminderStatisticsInfoList;
    }

    /**
     * 获取要统计的订单
     */
    private List<StandardDealReminderDetailInfoDto> getDealReminderDetailInfoList(ZonedDateTime startTime, ZonedDateTime endTime) {
        // 获取要统计的订单信息
        List<StandardDealReminderDetailInfoDto> standardDealReminderDetailInfoDtoList = standardOrderMapper.getOrderListWithDealReminder(startTime, endTime);
        List<Long> orderIdList = standardDealReminderDetailInfoDtoList.stream().map(dealReminderDetailInfoDto -> Long.valueOf(dealReminderDetailInfoDto.getOrderId())).collect(Collectors.toList());
        // 获取订单明细信息
        List<StandardOrderDetailInfoDto> standardDetailInfoList = standardOrderDetailMapper.getStandardDetailInfo(orderIdList);
        Map<Long, List<StandardOrderDetailInfoDto>> orderDetailMapping = standardDetailInfoList.stream().collect(Collectors.groupingBy(StandardOrderDetailInfoDto::getOrderId));
        // 组合明细和订单信息
        for (StandardDealReminderDetailInfoDto dealReminderDetailInfoDto : standardDealReminderDetailInfoDtoList) {
            if (orderDetailMapping.containsKey(Long.valueOf(dealReminderDetailInfoDto.getOrderId()))) {
                List<StandardOrderDetailInfoDto> orderDetailInfoDtoList = orderDetailMapping.get(Long.valueOf(dealReminderDetailInfoDto.getOrderId()));
                String standardTitles = orderDetailInfoDtoList.stream().map(StandardOrderDetailInfoDto::getStandardTitle).collect(Collectors.joining("，"));
                String planNames = orderDetailInfoDtoList.stream().map(StandardOrderDetailInfoDto::getPlanName).collect(Collectors.joining("，"));
                dealReminderDetailInfoDto.setStandardTitles(standardTitles);
                dealReminderDetailInfoDto.setPlanNames(planNames);
                dealReminderDetailInfoDto.setCompanyId(orderDetailInfoDtoList.get(0).getCompanyId());
                dealReminderDetailInfoDto.setCompanyName(orderDetailInfoDtoList.get(0).getCompanyName());
                dealReminderDetailInfoDto.setStatusDesc(StandardOrderStatusEnum.getDictValueFromDictKey(dealReminderDetailInfoDto.getStatus()));
            }
        }
        return standardDealReminderDetailInfoDtoList;
    }

    @Override
    public List<StandardMyOrdersConvertRspDto> myOrderSConvert(StandardMyOrdersConvertReqDto standardMyOrdersConvertReqDto) {
        List<Long> orderIdList = standardMyOrdersConvertReqDto.getOrderIdList();
        // 根据id查询所有订单
        List<IgStandardOrder> standardOrderList = standardOrderMapper.entity(IgStandardOrder.class).select(orderIdList, true);
        // 雇主订单
        List<IgStandardOrder> employerOrderList = standardOrderList.stream()
                .filter(standardOrder -> StandardTypeEnum.isEmployerPlan(standardOrder.getStandardType())).collect(Collectors.toList());
        // 处理雇主订单
        List<StandardMyOrdersConvertRspDto> employerOrderConvertRspDtoList = handleEmployerOrderConvert(employerOrderList);
        // 员福订单
        List<IgStandardOrder> welfareOrderList = standardOrderList.stream()
                .filter(standardOrder -> StandardTypeEnum.isWelfareRecommend(standardOrder.getStandardType())).collect(Collectors.toList());
        // 处理员福订单
        List<StandardMyOrdersConvertRspDto> welfareOrderConvertRspDtoList = handleWelfareConvert(welfareOrderList);
        // 合并集合
        List<StandardMyOrdersConvertRspDto> standardMyOrdersConvertRspDtoList = new ArrayList<>();
        standardMyOrdersConvertRspDtoList.addAll(employerOrderConvertRspDtoList);
        standardMyOrdersConvertRspDtoList.addAll(welfareOrderConvertRspDtoList);
        return standardMyOrdersConvertRspDtoList;
    }

    /**
     * 处理员福订单
     */
    private List<StandardMyOrdersConvertRspDto> handleWelfareConvert(List<IgStandardOrder> welfareOrderList) {
        if (CollectionUtil.isEmpty(welfareOrderList)) {
            return new ArrayList<>();
        }
        List<Long> welfareOrderIdList = welfareOrderList.stream().map(IgStandardOrder::getId).collect(Collectors.toList());
        return standardOrderMapper.getWelfareConvertDtoList(welfareOrderIdList);
    }

    /**
     * 处理雇主订单
     */
    private List<StandardMyOrdersConvertRspDto> handleEmployerOrderConvert(List<IgStandardOrder> employerOrderList) {
        if (CollectionUtil.isEmpty(employerOrderList)) {
            return new ArrayList<>();
        }
        List<Long> employerOrderIdList = employerOrderList.stream().map(IgStandardOrder::getId).collect(Collectors.toList());
        return standardOrderDetailMapper.getEmployerConvertDtoList(employerOrderIdList);
    }

    @Override
    public void sendEmployerInsuranceRenewalMail(TimeRangeDTO timeRangeDTO) {
        // 获取参数
        ZonedDateTime startTime = timeRangeDTO.getStartTime();
        ZonedDateTime endTime = timeRangeDTO.getEndTime();
        // 获取在保订单列表
        List<StandardEmployerInsuranceRenewalDto> inInsuranceOrderList = getEmployerInsuranceRenewalList(startTime, endTime);
        String ossKey = "";
        // 是否有在保订单
        boolean hasInInsurance = false;
        // 生成excel并上传到Oss
        if (CollectionUtil.isNotEmpty(inInsuranceOrderList)) {
            hasInInsurance = true;
            ossKey = generateExcelAndUploadOss(inInsuranceOrderList);
        }
        // 发送邮件
        doSendEmployerInsuranceRenewalMail(hasInInsurance, ossKey);
    }

    /**
     * 获取在保订单列表
     */
    private List<StandardEmployerInsuranceRenewalDto> getEmployerInsuranceRenewalList(ZonedDateTime startTime, ZonedDateTime endTime) {
        // 获取在保订单
        List<StandardEmployerInsuranceRenewalDto> inInsuranceOrderList = standardOrderMapper.getInInsuranceOrderList(startTime, endTime);
        // 如果未查询到在保订单，则直接返回
        if (CollectionUtil.isEmpty(inInsuranceOrderList)) {
            return new ArrayList<>();
        }
        // 获取订单明细信息
        List<Long> orderIdList = inInsuranceOrderList.stream().map(order -> Long.valueOf(order.getOrderId())).collect(Collectors.toList());
        List<StandardOrderDetailInfoDto> standardDetailInfoList = standardOrderDetailMapper.getStandardDetailInfo(orderIdList);
        Map<Long, List<StandardOrderDetailInfoDto>> orderDetailMapping = standardDetailInfoList.stream().collect(Collectors.groupingBy(StandardOrderDetailInfoDto::getOrderId));
        // 获取保单信息
        List<Long> planIdList = standardDetailInfoList.stream().map(StandardOrderDetailInfoDto::getPlanId).collect(Collectors.toList());
        List<StandardOrderPolicyDto> policyInfoList = standardPlanMapper.getPolicyInfoByPlanIdList(planIdList);
        Map<Long, StandardOrderPolicyDto> policyMapping = policyInfoList.stream().collect(Collectors.toMap(StandardOrderPolicyDto::getPlanId, Function.identity(), (oldValue, newValue) -> newValue));
        // 获取账单信息
        Map<Long, CurrencyAmount> billAmountAndPlanIdMapping = standardPlanMapper.getBillAmountByPlanIdList(planIdList);
        // 回填其它信息
        for (StandardEmployerInsuranceRenewalDto inInsuranceOrder : inInsuranceOrderList) {
            if (orderDetailMapping.containsKey(Long.valueOf(inInsuranceOrder.getOrderId()))) {
                List<StandardOrderDetailInfoDto> orderDetailInfoDtoList = orderDetailMapping.get(Long.valueOf(inInsuranceOrder.getOrderId()));
                // 标品名称
                String standardTitles = orderDetailInfoDtoList.stream().map(StandardOrderDetailInfoDto::getStandardTitle).collect(Collectors.joining("，"));
                inInsuranceOrder.setStandardTitles(standardTitles);
                // 核心系统的方案名称
                String planNames = orderDetailInfoDtoList.stream().map(StandardOrderDetailInfoDto::getPlanName).collect(Collectors.joining("，"));
                inInsuranceOrder.setPlanNames(planNames);
                // 保单号
                Long planId = orderDetailInfoDtoList.get(0).getPlanId();
                if (policyMapping.containsKey(planId)) {
                    StandardOrderPolicyDto policyInfo = policyMapping.get(planId);
                    inInsuranceOrder.setPolicyNumber(policyInfo.getPolicyNum());
                }
                // 经过保费
                CurrencyAmount amount = billAmountAndPlanIdMapping.get(planId);
                inInsuranceOrder.setTotalAmount(CurrencyAmountUtil.isNull(amount) ? null : amount.getAmount());
            }
        }
        return inInsuranceOrderList;
    }

    /**
     * 发送渠道雇主续保业务提醒邮件
     */
    private void doSendEmployerInsuranceRenewalMail(boolean hasInInsurance, String ossKey) {
        StandardMailConfig.MailInfo orderDealReminderEmployer = standardMailConfig.getOrderInsuranceRenewalEmployer();
        String todayStr = DateTimeUtil.format(DateTimeUtil.now(true), DateTimeFormatter.ofPattern("yyyyMMdd"));

        StandardMailDto standardMailDto = new StandardMailDto();

        standardMailDto.setCode(orderDealReminderEmployer.getCode());
        standardMailDto.setTitle("渠道雇主续保业务提醒" + todayStr + ".xlsx");
        standardMailDto.setTo(orderDealReminderEmployer.getTo());
        standardMailDto.setCc(orderDealReminderEmployer.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(orderDealReminderEmployer.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        if (StrUtil.isNotBlank(ossKey)) {
            standardMailDto.setOssKeyList(Collections.singletonList(ossKey));
        }

        HashMap<String, String> variables = new HashMap<>();
        String message = hasInInsurance ? "20至26个自然日后即将到期的企业名单详见附件。" : "未来20-26个自然日无保障到期的渠道业务。";
        variables.put("message", message);
        standardMailDto.setVariables(variables);

        standardMailService.sendMail(standardMailDto);
    }

    /**
     * 生成excel文件并上传到Oss
     */
    private String generateExcelAndUploadOss(List<StandardEmployerInsuranceRenewalDto> inInsuranceOrderList) {
        // 生成excel文件
        MultipartFile multipartFile = generateExcel(inInsuranceOrderList);
        // 将生成的文件上传到OSS
        List<UploadResultDto> uploadResultDtoList = upLoadFileToOss(multipartFile);
        // 返回OssKey
        return uploadResultDtoList.get(0).getKey();
    }

    /**
     * 生成excel文件
     */
    private MultipartFile generateExcel(List<StandardEmployerInsuranceRenewalDto> inInsuranceOrderList) {
        // 构造临时文件名称
        String tempFileName = cn.hutool.core.lang.UUID.randomUUID().toString(true) + ".xlsx";

        // 获取系统临时目录
        String tempDir = System.getProperty("java.io.tmpdir");
        File excelFile = new File(tempDir, tempFileName);
        Path excelFilePath = excelFile.toPath();

        try {
            // 创建 ExcelWriter 写入 Excel 文件
            ExcelWriter excelWriter = EasyExcel.write(excelFile)
                    // 设置列宽自动适应
                    .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())
                    // 设置样式
                    .registerWriteHandler(createCenterStyleStrategy())
                    .build();

            WriteSheet sheet1 = EasyExcel.writerSheet(0,"未来过期方案")
                    .head(StandardEmployerInsuranceRenewalDto.class)
                    .registerWriteHandler(new DropdownSheetWriteHandler(StandardEmployerInsuranceRenewalDto.class, inInsuranceOrderList.size()))
                    .build();

            excelWriter.write(inInsuranceOrderList, sheet1);

            excelWriter.finish();

            // 构造文件名称
            String todayStr = DateTimeUtil.format(DateTimeUtil.now(true), DateTimeFormatter.ofPattern("yyyyMMdd"));
            String fileName = "渠道雇主续保业务提醒" + todayStr + ".xlsx";

            // 将 File 转为 MultipartFile
            return fileToMultipartFile(excelFile, fileName);
        } catch (Exception e) {
            log.error("生成或上传 Excel 文件失败", e);
            throw new QuoteException(-1, "生成Excel文件失败");
        } finally {
            deleteTmpFile(excelFilePath);
        }
    }

    /**
     * 删除临时文件
     */
    private void deleteTmpFile(Path excelFilePath) {
        // 删除临时文件，使用 java.nio.file.Files#delete 提供更丰富的错误信息
        try {
            Files.delete(excelFilePath);
            log.info("临时文件删除成功：{}", excelFilePath.toAbsolutePath());
        } catch (IOException e) {
            // 捕获具体异常以便排查，如文件正在被占用、权限问题等
            log.warn("临时文件删除失败：{}，原因：{}", excelFilePath.toAbsolutePath(), e.getMessage(), e);
        }
    }

    @Override
    public Long copyOrder(Long copySourceOrderId) {
        // 查询订单
        IgStandardOrder standardOrder = standardOrderMapper.entity(IgStandardOrder.class).selectOne(copySourceOrderId, true);
        if (Objects.isNull(standardOrder) || !IdentityContext.getUserId().equals(standardOrder.getBrokerId())) {
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_238"));
        }
        if (!StandardTypeEnum.isEmployerPlan(standardOrder.getStandardType())) {
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_249"));
        }
        // 查询订单详情
        List<IgStandardOrderDetail> standardOrderDetailList = standardOrderDetailMapper.getDetailListByOrderId(copySourceOrderId);
        // 复制订单并插入数据库
        return doCopyOrderAndInsert(standardOrder, standardOrderDetailList);
    }

    /**
     * 复制订单并插入数据库
     */
    private Long doCopyOrderAndInsert(IgStandardOrder sourceOrder, List<IgStandardOrderDetail> sourceOrderDetailList) {
        // 复制订单基本信息
        IgStandardOrder copyOrder = doCopyOrder(sourceOrder);
        IgStandardOrder insertedOrder = standardOrderMapper.entity(IgStandardOrder.class).insertOne(copyOrder);
        // 复制订单明细
        List<IgStandardOrderDetail> copyOrderDetailList = doCopyOrderDetailList(sourceOrderDetailList, insertedOrder);
        standardOrderDetailMapper.entity(IgStandardOrderDetail.class).insertAll(copyOrderDetailList);
        // 返回保存后的新订单ID
        return insertedOrder.getId();
    }

    /**
     * 复制订单明细
     */
    private List<IgStandardOrderDetail> doCopyOrderDetailList(List<IgStandardOrderDetail> sourceOrderDetailList, IgStandardOrder insertedOrder) {
        List<IgStandardOrderDetail> copyOrderDetailList = new ArrayList<>();
        for (IgStandardOrderDetail sourceOrderDetail : sourceOrderDetailList) {
            IgStandardOrderDetail copyOrderDetail = new IgStandardOrderDetail();
            // 标品订单id
            copyOrderDetail.setStandardOrderId(insertedOrder.getId());
            // 标品方案id
            copyOrderDetail.setStandardPlanId(sourceOrderDetail.getStandardPlanId());
            // 职业类型
            copyOrderDetail.setOccupationType(sourceOrderDetail.getOccupationType());
            // 投保人数
            copyOrderDetail.setPersonNum(sourceOrderDetail.getPersonNum());
            // 核心的模版方案配置id
            copyOrderDetail.setPlanConfigId(sourceOrderDetail.getPlanConfigId());
            // 序号
            copyOrderDetail.setSequence(sourceOrderDetail.getSequence());
            // 流水线号
            copyOrderDetail.setPipeLineNum(sourceOrderDetail.getPipeLineNum());
            // 保费
            copyOrderDetail.setPremium(sourceOrderDetail.getPremium());
            // 核心的模版方案id
            copyOrderDetail.setPlanId(sourceOrderDetail.getPlanId());
            // 拷贝的源明细id
            copyOrderDetail.setCloneSourceDetailId(sourceOrderDetail.getId());
            copyOrderDetailList.add(copyOrderDetail);
        }
        return copyOrderDetailList;
    }

    /**
     * 复制订单基本信息
     */
    private IgStandardOrder doCopyOrder(IgStandardOrder sourceOrder) {
        IgStandardOrder copyOrder = new IgStandardOrder();
        // 渠道id
        copyOrder.setChannelId(CustomContext.getChannelId());
        // 标品类型
        copyOrder.setStandardType(sourceOrder.getStandardType());
        // 渠道付款流程
        IgStandardTenantInfo channelInfo = standardTenantInfoMapper.getChannelInfo(CustomContext.getChannelId());
        if (channelInfo != null) {
            copyOrder.setStandardPaymentProcess(channelInfo.getStandardPaymentProcess());
        }
        // 订单状态
        copyOrder.setStatus(StandardOrderStatusEnum.PRE_ORDER.getDictKey());
        // 经纪人id
        copyOrder.setBrokerId(IdentityContext.getUserId());
        // 保险期间
        copyOrder.setInsurancePeriod(sourceOrder.getInsurancePeriod());
        // 开始时间，取原标品订单结束日期的次日零点
        ZonedDateTime startTime = sourceOrder.getEndTime().plusDays(1).truncatedTo(ChronoUnit.DAYS);
        copyOrder.setStartTime(startTime);
        // 结束时间，根据开始日期向后推送X个月，X根据标品订单中的保障期间取值
        copyOrder.setEndTime(startTime.plusMonths(sourceOrder.getInsurancePeriod()).with(LocalTime.MAX).truncatedTo(ChronoUnit.SECONDS));
        // 投保人数
        copyOrder.setPersonNum(sourceOrder.getPersonNum());
        // 企业名称
        copyOrder.setGroupName(sourceOrder.getGroupName());
        // 企业所在省份
        copyOrder.setGroupProvince(sourceOrder.getGroupProvince());
        // 企业所在城市
        copyOrder.setGroupCity(sourceOrder.getGroupCity());
        // 企业所在区县
        copyOrder.setGroupCounty(sourceOrder.getGroupCounty());
        // 企业详细地址
        copyOrder.setGroupAddress(sourceOrder.getGroupAddress());
        // 企业社会信用代码
        copyOrder.setGroupSocialCode(sourceOrder.getGroupSocialCode());
        // 发票抬头
        copyOrder.setInvoiceTitle(sourceOrder.getGroupName());
        // 发票税号
        copyOrder.setInvoiceTaxNo(sourceOrder.getGroupSocialCode());
        // 付款人信息
        copyOrder.setPayerName(sourceOrder.getGroupName());
        // 收款方账户
        copyOrder.setPayeeName(sourceOrder.getPayeeName());
        // 收款银行
        copyOrder.setPayeeBankName(sourceOrder.getPayeeBankName());
        // 收款账号
        copyOrder.setPayeeBankNo(sourceOrder.getPayeeBankNo());
        // 支付状态
        copyOrder.setPayStatus(StandardPaymentStatusEnum.UN_PAID.getValue());
        // 渠道id
        copyOrder.setChannelId(CustomContext.getChannelId());
        // 渠道分享流水号
        copyOrder.setChannelSerialNo(sourceOrder.getChannelSerialNo());
        // 所属人
        copyOrder.setOwnerId(IdentityContext.getUserId());
        // 渠道手续费比例，目前固定15%，后期会变
        copyOrder.setChannelFeeRatio(new BigDecimal("0.15"));
        // 联系人
        copyOrder.setContactPerson(sourceOrder.getContactPerson());
        // 联系人手机号
        copyOrder.setContactMobile(sourceOrder.getContactMobile());
        // 联系人邮箱
        copyOrder.setContactEmail(sourceOrder.getContactEmail());
        // 拷贝的源订单id
        copyOrder.setCloneSourceOrderId(sourceOrder.getId());
        return copyOrder;
    }

    @Override
    public String getInsuredProcessNotice(Long orderId) {
        // 获取订单
        IgStandardOrder standardOrder = standardOrderMapper.entity(IgStandardOrder.class).selectOne(orderId, true);
        if (Objects.isNull(standardOrder)) {
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_238"));
        }
        Long standardPlanId;
        if (StandardTypeEnum.isEmployerPlan(standardOrder.getStandardType())) {
            // 获取订单详情
            List<IgStandardOrderDetail> standardOrderDetailList = standardOrderDetailMapper.getDetailListByOrderId(orderId);
            if (CollectionUtil.isEmpty(standardOrderDetailList)) {
                throw new QuoteException(-1, "获取投保须知异常");
            }
            standardPlanId =  standardOrderDetailList.get(0).getStandardPlanId();
        } else {
            standardPlanId = standardOrder.getStandardPlanId();
        }
        // 获取投保须知
        return standardPlanService.getInsuredProcessNotice(standardPlanId);
    }

    @Override
    public void uploadInsuranceRenewalExcel(MultipartFile file) {
        try {
            // 创建监听器
            StandardRenewalDtoListener listener = new StandardRenewalDtoListener(standardOrderMapper);
            // 读取excel文件内容
            EasyExcel.read(file.getInputStream(), StandardEmployerInsuranceRenewalDto.class, listener)
                    .sheet()
                    .doRead();
            // 获取不存在的订单id
            List<Long> notExistsOrderIdList = listener.getNotExistsOrderIdList();
            if (CollectionUtil.isNotEmpty(notExistsOrderIdList)) {
                String notExistsOrderIdsStr = notExistsOrderIdList.stream()
                        .map(String::valueOf)
                        .collect(Collectors.joining("，"));
                throw new QuoteException(-1, "上传的文件中部分订单ID不存在，请检查后重新上传，不存在的订单ID如下：" + notExistsOrderIdsStr);
            }
            // 获取读取的数据
            List<StandardEmployerInsuranceRenewalDto> insuranceRenewalList = listener.getDataList();
            // 更新订单续保方式
            List<IgStandardOrder> updateOrderList = new ArrayList<>();
            for (StandardEmployerInsuranceRenewalDto insuranceRenewal : insuranceRenewalList) {
                String renewalMethod = StandardRenewalMethodEnum.getValue(insuranceRenewal.getRenewalMethod());
                // 如果没填续保方式，则设置为无续保方式
                if (StrUtil.isBlank(renewalMethod)) {
                    renewalMethod = StandardRenewalMethodEnum.NONE.getValue();
                }
                IgStandardOrder updateOrder = new IgStandardOrder();
                updateOrder.setId(Long.valueOf(insuranceRenewal.getOrderId()));
                updateOrder.setRenewalMethod(renewalMethod);
                updateOrderList.add(updateOrder);
            }
            if (CollectionUtil.isNotEmpty(updateOrderList)) {
                standardOrderMapper.updateAll(updateOrderList, true);
            }
        } catch (IOException e) {
            log.error("上传续保Excel文件失败", e);
            throw new QuoteException(-1, "上传续保Excel文件失败");
        }
    }

    @Override
    public CheckResultDto checkOrderSimilarity(StandardOrderInfoDto standardOrderInfoDto) {
        // 为空说明不是再次投保
        if (Objects.isNull(standardOrderInfoDto.getCloneSourceOrderId())) {
            return CheckResultDto.passCheck();
        }

        String errorMsg = "由于";
        boolean isPassCheck = true;

        // 查询源订单
        IgStandardOrder sourceOrder = standardOrderMapper.entity(IgStandardOrder.class)
                .selectOne(standardOrderInfoDto.getCloneSourceOrderId(), true);
        if (Objects.isNull(sourceOrder)) {
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_253"));
        }

        // 校验订单人数是否异常
        if (isPersonCountTooLow(standardOrderInfoDto, sourceOrder)) {
            isPassCheck = false;
            errorMsg += "期初投保人数小于原方案50%；";
        }

        // 校验平均年龄是否过大
        if (isAverageAgeTooHigh(standardOrderInfoDto, sourceOrder)) {
            isPassCheck = false;
            errorMsg += "平均年龄超过原方案3岁以上；";
        }

        // 校验标品明细是否一致
        if (!isStandardDetailMatched(
                standardOrderDetailMapper.getDetailListByOrderId(sourceOrder.getId()),
                standardOrderInfoDto.getStandardPlanInfoList()
        )) {
            isPassCheck = false;
            errorMsg += "标品不一致；";
        }

        if (!isPassCheck) {
            errorMsg += "需要进行人工核保。";
            return CheckResultDto.failCheck(errorMsg, PromptTypeEnum.DIALOG);
        }

        return CheckResultDto.passCheck();
    }

    /**
     * 判断当前订单人数是否低于原订单人数的50%
     */
    private boolean isPersonCountTooLow(StandardOrderInfoDto currentOrder, IgStandardOrder sourceOrder) {
        Integer sourcePersonNum = sourceOrder.getPersonNum();

        int currentPersonNum = currentOrder.getStandardPlanInfoList().stream().mapToInt(plan -> {
            Integer personNum = plan.getPersonNum();
            if (CollectionUtil.isNotEmpty(plan.getPersonList())) {
                personNum = plan.getPersonList().size();
            }
            return personNum;
        }).sum();

        BigDecimal currentNum = BigDecimal.valueOf(currentPersonNum);
        BigDecimal sourceNum = BigDecimal.valueOf(sourcePersonNum);
        BigDecimal threshold = sourceNum.multiply(new BigDecimal("0.5")).setScale(2, RoundingMode.HALF_UP);

        return currentNum.compareTo(threshold) < 0;
    }

    /**
     * 判断当前订单平均年龄是否高于原订单平均年龄的 3 岁以上
     */
    private boolean isAverageAgeTooHigh(StandardOrderInfoDto currentOrder, IgStandardOrder sourceOrder) {
        List<IgStandardOrderDetail> sourceOrderDetailList =
                standardOrderDetailMapper.getDetailListByOrderId(sourceOrder.getId());

        BigDecimal sourceAvgAge = calculateAverageAgeByPipeline(
                extractPipeLineNumsFromDetails(sourceOrderDetailList), sourceOrder.getStartTime());

        BigDecimal currentAvgAge = calculateAverageAgeByPipeline(
                extractPipeLineNumsFromPlans(currentOrder.getStandardPlanInfoList()), currentOrder.getStartTime());

        return currentAvgAge.compareTo(sourceAvgAge.add(new BigDecimal("3"))) > 0;
    }

    /**
     * 根据 pipeline 列表和开始时间计算平均年龄
     */
    private BigDecimal calculateAverageAgeByPipeline(List<String> pipeLineNumList, ZonedDateTime startTime) {
        List<StandardPersonDTO> allPersons = getPersonListByPipelines(pipeLineNumList);

        long totalAge = 0;

        for (StandardPersonDTO person : allPersons) {
            CertType certTypeEnum = Arrays.stream(CertType.values())
                    .filter(v -> v.name().equals(person.getCertType()))
                    .findFirst()
                    .orElse(null);
            ZonedDateTime birthday = CredentialUtil.getBirthdayByCredential(
                    certTypeEnum != null ? certTypeEnum.getValue() : null,
                    person.getCertCode()
            );
            if (Objects.isNull(birthday)) {
                throw new QuoteException(-1, MessageUtil.get("b_b_quote_254"));
            }
            long age = DateUtil.betweenYear(
                    DateUtil.date(birthday.toInstant().toEpochMilli()),
                    DateUtil.date(startTime.toInstant().toEpochMilli()),
                    false
            );
            totalAge += age;
        }

        if (allPersons.isEmpty()) {
            return BigDecimal.ZERO;
        }

        BigDecimal avg = new BigDecimal(totalAge)
                .divide(new BigDecimal(allPersons.size()), 2, RoundingMode.HALF_UP);

        return avg.setScale(0, RoundingMode.CEILING);
    }

    /**
     * 提取 pipeline 列表（用于当前订单）
     */
    private List<String> extractPipeLineNumsFromPlans(List<StandardPlanInfoDto> plans) {
        return plans.stream()
                .map(StandardPlanInfoDto::getPipeLineNum)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 提取 pipeline 列表（用于源订单）
     */
    private List<String> extractPipeLineNumsFromDetails(List<IgStandardOrderDetail> details) {
        return details.stream()
                .map(IgStandardOrderDetail::getPipeLineNum)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * 根据 pipeline 列表调用接口获取所有人员信息
     */
    private List<StandardPersonDTO> getPersonListByPipelines(List<String> pipeLineNumList) {
        List<StandardPersonDTO> allPersons = new ArrayList<>();

        for (String pipeLineNum : pipeLineNumList) {
            ResponseVO<List<StandardPersonDTO>> response = saasPersonClient.basicPersonList(
                    standardGroupConfig.getId(), pipeLineNum, 1, 5000
            );

            CommonDataUtil.checkAfterGetPlatformData(response, true, response.getMessage());

            List<StandardPersonDTO> persons = response.getData();
            if (persons != null) {
                allPersons.addAll(persons);
            }
        }

        return allPersons;
    }


    /**
     * 判断两个订单的标品明细（字段组成的组合 + 数量）是否一致
     */
    private boolean isStandardDetailMatched(List<IgStandardOrderDetail> sourceOrderDetailList,
                                            List<StandardPlanInfoDto> standardPlanInfoList) {
        if (sourceOrderDetailList.size() != standardPlanInfoList.size()) {
            return false;
        }

        // 统计两个列表中每种组合的数量
        Map<String, Integer> map1 = getDetailCountMapByKeyString(sourceOrderDetailList);
        Map<String, Integer> map2 = getPlanCountMapByKeyString(standardPlanInfoList);

        // 判断两个 map 是否完全相等
        return map1.equals(map2);
    }

    /**
     * 拼接三个字段作为字符串 key，并统计每个组合的数量（源订单）
     */
    private Map<String, Integer> getDetailCountMapByKeyString(List<IgStandardOrderDetail> detailList) {
        Map<String, Integer> countMap = new HashMap<>();

        for (IgStandardOrderDetail detail : detailList) {
            // 使用下划线拼接字段作为唯一 key
            String key = detail.getStandardPlanId() + "_" + detail.getOccupationType();

            // 统计 key 出现次数
            countMap.put(key, countMap.getOrDefault(key, 0) + 1);
        }

        return countMap;
    }

    /**
     * 拼接三个字段作为字符串 key，并统计每个组合的数量（当前订单）
     */
    private Map<String, Integer> getPlanCountMapByKeyString(List<StandardPlanInfoDto> standardPlanInfoList) {
        Map<String, Integer> countMap = new HashMap<>();

        for (StandardPlanInfoDto standardPlanInfoDto : standardPlanInfoList) {
            // 使用下划线拼接字段作为唯一 key
            String key = standardPlanInfoDto.getStandardPlanId() + "_" + standardPlanInfoDto.getJobCategory();

            // 统计 key 出现次数
            countMap.put(key, countMap.getOrDefault(key, 0) + 1);
        }

        return countMap;
    }

    @Override
    public StandardFlagRspDto notifyBillPayment(List<Long> billIdList) {
        StandardFlagRspDto standardFlagRspDto = new StandardFlagRspDto();
        standardFlagRspDto.setFlag(true);

        // 根据账单id列表查询标品订单列表
        List<IgStandardOrder> igStandardOrderList = standardOrderMapper.getOrderListByBillIdList(billIdList);

        for (IgStandardOrder standardOrder : igStandardOrderList) {
            Long orderId = standardOrder.getId();
            String standardType = standardOrder.getStandardType();
            String standardPaymentProcess = standardOrder.getStandardPaymentProcess();
            Long attorneyFileId = standardOrder.getAttorneyFileId();

            // 雇主标品
            if (StandardTypeEnum.isEmployerPlan(standardType)) {
                handleEmployerPlanOnBillNotify(orderId, standardPaymentProcess, attorneyFileId);
            } else {
                // 员福标品
                handleWelfareRecommendOnBillNotify(orderId, attorneyFileId);
            }
        }

        return standardFlagRspDto;
    }

    /**
     * 处理员福的逻辑
     */
    private void handleWelfareRecommendOnBillNotify(Long orderId, Long attorneyFileId) {
        IgStandardOrder updateStandardOrder = new IgStandardOrder();
        updateStandardOrder.setId(orderId);
        // 如果上传了委托书，直接投保
        if (Objects.nonNull(attorneyFileId)) {
            updateStandardOrder.setStatus(StandardOrderStatusEnum.INSURING.getDictKey());
            standardOrderMapper.entity(IgStandardOrder.class).updateOne(updateStandardOrder, true);
            standardOrderLogMapper.record(updateStandardOrder.getId(), StandardOrderStatusEnum.INSURING.getDictKey());
            autoPolicyStandardOrder(orderId);
            standardOrderStatusChangeProducer.constructAndSendMessage(updateStandardOrder.getId());
        } else {
            // 没上传委托书，则更新为待上传授权委托书状态
            updateStandardOrder.setStatus(StandardOrderStatusEnum.TO_UPLOAD_AUTHORIZATION.getDictKey());
            standardOrderMapper.entity(IgStandardOrder.class).updateOne(updateStandardOrder, true);
            standardOrderLogMapper.record(updateStandardOrder.getId(), StandardOrderStatusEnum.TO_UPLOAD_AUTHORIZATION.getDictKey());
            standardOrderStatusChangeProducer.constructAndSendMessage(updateStandardOrder.getId());
        }
        log.info("员福标品订单更新状态，订单id：{}", orderId);
    }

    /**
     * 处理雇主的逻辑
     */
    private void handleEmployerPlanOnBillNotify(Long orderId, String standardPaymentProcess, Long attorneyFileId) {
        String nextStatus;
        // 是否需要自动投保
        boolean needAutoPolicy = false;
        // 是否上传授权委托书
        boolean hasAttorneyFile = Objects.nonNull(attorneyFileId);

        if (StandardPaymentProcessEnum.isFirstPaymentThenInsured(standardPaymentProcess)) {
            // 先付费后投保
            if (hasAttorneyFile) {
                // 已上传授权委托书：支付结果确认中 -> 投保中(需触发自动投保)
                nextStatus = StandardOrderStatusEnum.INSURING.getDictKey();
                needAutoPolicy = true;
            } else {
                // 未上传授权委托书：支付结果确认中 -> 待上传授权委托书
                nextStatus = StandardOrderStatusEnum.TO_UPLOAD_AUTHORIZATION.getDictKey();
            }
        } else {
            // 先投保后付费
            // 已上传授权委托书：支付结果确认中 -> 订单完成
            // 未上传授权委托书：支付结果确认中 -> 待上传授权委托书
            nextStatus = hasAttorneyFile
                    ? StandardOrderStatusEnum.ORDER_COMPLETED.getDictKey()
                    : StandardOrderStatusEnum.TO_UPLOAD_AUTHORIZATION.getDictKey();
        }
        IgStandardOrder updateStandardOrder = new IgStandardOrder();
        updateStandardOrder.setId(orderId);
        updateStandardOrder.setStatus(nextStatus);
        standardOrderMapper.entity(IgStandardOrder.class).updateOne(updateStandardOrder, true);
        // 数据更新完后触发自动投保
        if (needAutoPolicy) {
            autoPolicyStandardOrder(orderId);
        }
        log.info("雇主标品订单更新状态，订单id：{}", orderId);
    }

    @Override
    public String attorneyDownload(Long orderId) {
        // 生成pdf文件
        String planHtml = filesService.generateAttorneyHtml(orderId);
        // 生成方案确认书PDF
        List<com.insgeek.protocol.platform.fileservice.dto.UploadResultDto> data = htmlHandlerClient.convertHtmlPdfByWk(new MultipartFile[]{getMultipartFile("法人授权委托书.html", planHtml)}).getData();
        return data.get(0).getLargeUrl();
    }

    private MultipartFile getMultipartFile(String filename, String text) {
        ByteArrayResource byteArrayResource = new ByteArrayResource(text.getBytes()) {
            @Override
            public String getFilename() {
                return filename; // 指定文件名
            }
        };
        try {
            // 创建MultipartFile对象
            return new MockMultipartFile(
                    "file",           // 表单中文件的名称
                    filename,    // 文件名
                    "text/plain",     // 文件类型
                    byteArrayResource.getInputStream() // 获取输入流
            );
        } catch (Exception e) {
            log.error("生成文件失败", e);
        }
        return null;
    }


    @Override
    public StandardFlagRspDto applyInvoice(StandardOrderReqDto standardOrderCheckReqDto) {
        // 获取订单信息
        IgStandardOrder standardOrder = standardOrderMapper.entity(IgStandardOrder.class).selectOne(standardOrderCheckReqDto.getOrderId(), true);
        if (Objects.isNull(standardOrder)) {
            throw new QuoteException(20, MessageUtil.get("b_b_quote_238"));
        }
        // 更新发票申请时间
        standardOrder.setInvoiceApplyTime(ZonedDateTime.now());
        standardOrderMapper.entity(IgStandardOrder.class).updateOne(standardOrder, true);

        // 获取订单保单信息
        StandardOrderPolicyDto orderPolicyInfo = standardOrderMapper.getOrderPolicyInfo(standardOrder.getId());
        
        // 发送邮件
        if (StandardTypeEnum.isEmployerPlan(standardOrder.getStandardType())) {
            // 雇主
            sendFirstBillApplyInvoiceEmailWithEmployer(standardOrder, orderPolicyInfo);
        } else {
            // 员福
            sendFirstBillApplyInvoiceEmailWithWelfare(standardOrder, orderPolicyInfo);
        }

        StandardFlagRspDto standardFlagRspDto = new StandardFlagRspDto();
        standardFlagRspDto.setFlag(true);

        return standardFlagRspDto;
    }

    /**
     * 发送首期账单发票申请邮件（雇主）
     */
    private void sendFirstBillApplyInvoiceEmailWithEmployer(IgStandardOrder standardOrder, StandardOrderPolicyDto orderPolicyInfo) {
        StandardMailConfig.MailInfo applyInvoiceEmployer = standardMailConfig.getApplyInvoiceEmployer();

        StandardMailDto standardMailDto = new StandardMailDto();
        standardMailDto.setCode(applyInvoiceEmployer.getCode());
        String format = DateTimeUtil.format(standardOrder.getInvoiceApplyTime(), DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        standardMailDto.setTitle("【渠道标品】首期账单发票申请【" + format + "】");
        standardMailDto.setTo(applyInvoiceEmployer.getTo());
        standardMailDto.setCc(applyInvoiceEmployer.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(applyInvoiceEmployer.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        HashMap<String, String> variables = new HashMap<>();
        variables.put("invoiceTitle", standardOrder.getInvoiceTitle());
        variables.put("invoiceSocCode", standardOrder.getGroupSocialCode());
        variables.put("invoiceBankName", null == standardOrder.getInvoiceBankName() ? "" : standardOrder.getInvoiceBankName());
        variables.put("invoiceType", null == standardOrder.getInvoiceType() ? "" : StandardInvoiceTypeEnum.getByValue(standardOrder.getInvoiceType()));
        variables.put("invoiceAmount", standardOrder.getTotalAmount().getAmount().toString());
        variables.put("policyNum", null == orderPolicyInfo ? "" : orderPolicyInfo.getPolicyNum());
        variables.put("companyName", null == orderPolicyInfo ? "" : orderPolicyInfo.getCompanyName());
        variables.put("groupName", standardOrder.getGroupName());
        variables.put("invoiceEmail", null == standardOrder.getInvoiceEmail() ? "" : standardOrder.getInvoiceEmail());
        variables.put("invoiceContactName", standardOrder.getContactPerson());
        variables.put("address", standardOrder.getGroupAddress());
        standardMailDto.setVariables(variables);

        standardMailService.sendMail(standardMailDto);
    }

    /**
     * 发送首期账单发票申请邮件（员福）
     */
    private void sendFirstBillApplyInvoiceEmailWithWelfare(IgStandardOrder igStandardOrder, StandardOrderPolicyDto orderPolicyInfo) {
        StandardMailConfig.MailInfo applyInvoice = standardMailConfig.getApplyInvoice();

        StandardMailDto standardMailDto = new StandardMailDto();
        standardMailDto.setCode(applyInvoice.getCode());
        String format = DateTimeUtil.format(igStandardOrder.getInvoiceApplyTime(), DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        standardMailDto.setTitle("【渠道标品】首期账单发票申请【" + format + "】");
        standardMailDto.setTo(applyInvoice.getTo());
        standardMailDto.setCc(applyInvoice.getCc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getAddr, StandardMailConfig.MailDTO::getName)));
        standardMailDto.setBcc(applyInvoice.getBcc().stream().collect(Collectors.toMap(StandardMailConfig.MailDTO::getName, StandardMailConfig.MailDTO::getAddr)));

        HashMap<String, String> variables = new HashMap<>();
        variables.put("invoiceTitle", igStandardOrder.getInvoiceTitle());
        variables.put("invoiceSocCode", igStandardOrder.getGroupSocialCode());
        variables.put("invoiceBankName", null == igStandardOrder.getInvoiceBankName() ? "" : igStandardOrder.getInvoiceBankName());
        variables.put("invoiceType", null == igStandardOrder.getInvoiceType() ? "" : StandardInvoiceTypeEnum.getByValue(igStandardOrder.getInvoiceType()));
        variables.put("invoiceAmount", igStandardOrder.getTotalAmount().getAmount().toString());
        variables.put("policyNum", null == orderPolicyInfo ? "" : orderPolicyInfo.getPolicyNum());
        variables.put("companyName", null == orderPolicyInfo ? "" : orderPolicyInfo.getCompanyName());
        variables.put("groupName", igStandardOrder.getGroupName());
        variables.put("invoiceEmail", null == igStandardOrder.getInvoiceEmail() ? "" : igStandardOrder.getInvoiceEmail());
        variables.put("invoiceContactName", igStandardOrder.getContactPerson());
        variables.put("address", igStandardOrder.getGroupAddress());
        standardMailDto.setVariables(variables);

        standardMailService.sendMail(standardMailDto);
    }


    @Override
    public Long createdOrderSnapshot(IgStandardSnapshot standardSnapshot) {
        // 目前没逻辑，直接插入
        IgStandardSnapshot igStandardSnapshot = standardOrderDataMapper.entity(IgStandardSnapshot.class).insertOne(standardSnapshot);

        return igStandardSnapshot.getId();
    }

    @Override
    public OrderSnapshotRspDto getOrderSnapshot(Long snapShotId) {
        IgStandardSnapshot igStandardSnapshot = standardOrderDataMapper.entity(IgStandardSnapshot.class)
                .selectOne(snapShotId, true);
        if (ObjectUtils.isEmpty(igStandardSnapshot)) {
            return null;
        } else {
            IgStandardPlan igStandardPlan = standardPlanDataMapper.entity(IgStandardPlan.class)
                    .selectOne(igStandardSnapshot.getStandardPlanId(), true);

            // 查询标品的宣传信息
            List<IgStandardPlanInfo> standardInformationList = bqlQueryFactory.from(QIgStandardPlanInfo.ig_standard_plan_info)
                    .select(
                            QIgStandardPlanInfo.ig_standard_plan_info.id.as("id"),
                            QIgStandardPlanInfo.ig_standard_plan_info.type.as("type"),
                            QIgStandardPlanInfo.ig_standard_plan_info.title.as("title"),
                            QIgStandardPlanInfo.ig_standard_plan_info.content.as("content")
                    )
                    .where(QIgStandardPlanInfo.ig_standard_plan_info.standard_plan_id.eq(igStandardSnapshot.getStandardPlanId()).and(QIgStandardPlanInfo.ig_standard_plan_info.type.eq(StandardPlanDetailTypeEnum.TYPE3.getValue())))
                    .findList(true, IgStandardPlanInfo.class);

            ResponseVO<Map<Long, List<ProductClientUIDTO>>> planConfigDutyDetailBatch = productUIClient.getPlanConfigDutyDetailBatch(Collections.singletonList(igStandardPlan.getPlanConfigId()), "1");
            // 数据对象转换
            OrderSnapshotRspDto orderSnapshotRspDtos = JacksonUtils.covertObject(igStandardSnapshot, OrderSnapshotRspDto.class);
            // 获取配置的责任详情
            List<ProductClientUIDTO> productClientUIDTOS = planConfigDutyDetailBatch.getData().get(igStandardPlan.getPlanConfigId());
            orderSnapshotRspDtos.setDutyList(productClientUIDTOS);
            orderSnapshotRspDtos.setContent(standardInformationList.size() > 0 ? standardInformationList.get(0).getContent() : "");
            orderSnapshotRspDtos.setTitle(igStandardPlan.getTitle());
            List<IgStandardPlanInfo> standardPlanInfo = standardPlanMapper.getStandardPlanInfo(igStandardSnapshot.getStandardPlanId());
            if (CollectionUtil.isNotEmpty(standardPlanInfo)) {
                orderSnapshotRspDtos.setStandardOrderTermRspDto(getTerm(standardPlanInfo, StandardPlanDetailTypeEnum.TYPE5.getValue()));
            }
            return orderSnapshotRspDtos;
        }
    }

    @Override
    public void downloadPolicy(HttpServletResponse response, Long orderId) {
        // 设置2014，规避权限
        IdentityUtil.setRobotAuth();
        StandardOrderPolicyDto orderPolicyInfo = standardOrderMapper.getOrderPolicyInfo(orderId);
        response.setHeader("Access-Control-Expose-Headers", "Content-disposition");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + orderId + ".zip");
        response.setContentType("application/zip");
        Response feignResponse = dockingClient.downloadElectronicFile(orderPolicyInfo.getPolicyId(), null, orderPolicyInfo.getInsOl());
        try (InputStream inputStream = feignResponse.body().asInputStream();
             OutputStream outputStream = response.getOutputStream()) {
            IoUtil.copy(inputStream, outputStream);
        } catch (IOException e) {
            log.error("文件下载失败:{}", ExceptionUtils.getStackTrace(e));
            throw new QuoteException(-1, "电子保单正在获取中，请稍后再试");
        }

    }

    @Override
    public SingaporeOrderDetailRspDto getSgpCartDetail() {

        // 购物车信息
        List<IgStandardOrderDetail> igStandardOrderDetailList = standardOrderDetailMapper.getOrderDetailListByUid(IdentityContext.getUserId());
        SingaporeOrderDetailRspDto singaporeOrderDetailRspDto = new SingaporeOrderDetailRspDto();
        singaporeOrderDetailRspDto.setCartId(0L);
        singaporeOrderDetailRspDto.setTotalAmount(CurrencyAmount.valueOf(0L, IdentityContext.getBusinessCurrency()));
        singaporeOrderDetailRspDto.setPlanList(new ArrayList<>());

        // 如果为空，返回默认信息
        if (CollectionUtils.isEmpty(igStandardOrderDetailList)) {
            return singaporeOrderDetailRspDto;
        }
        // 根据员工类型升序排序
        igStandardOrderDetailList.sort(Comparator.comparing(IgStandardOrderDetail::getEmployeeType));

        // 取第一个，设置购物车id 和 总保额
        IgStandardOrderDetail igStandardOrderDetail1 = igStandardOrderDetailList.get(0);
        singaporeOrderDetailRspDto.setCartId(igStandardOrderDetail1.getStandardOrderId());
        singaporeOrderDetailRspDto.setTotalAmount(igStandardOrderDetailList.stream()
                .map(IgStandardOrderDetail::getAmount)
                .reduce(CurrencyAmountUtil::add)
                .orElseGet(() -> CurrencyAmount.zero(IdentityContext.getBusinessCurrency()))
        );

        List<SingaporeCartDetailPlanRspDto> planList = this.getPlanList(igStandardOrderDetailList);

        singaporeOrderDetailRspDto.setPlanList(planList);

        return singaporeOrderDetailRspDto;
    }

    private List<SingaporeCartDetailPlanRspDto> getPlanList(List<IgStandardOrderDetail> igStandardOrderDetailList) {

        // 所有标品信息
        List<SingaporeStandardPlanDto> singaporeStandardPlanDtoList = standardPlanService.getStandardPlanInfo(new ArrayList<>());

        // 设置所有的方案
        List<SingaporeCartDetailPlanRspDto> singaporeCartDetailPlanRspDtoList = new ArrayList<>();
        for (IgStandardOrderDetail igStandardOrderDetail : igStandardOrderDetailList) {
            SingaporeCartDetailPlanRspDto singaporeCartDetailPlanRspDto = new SingaporeCartDetailPlanRspDto();

            List<Long> planConfigIdList = igStandardOrderDetail.getPlanConfigIdList();
            List<Long> planIdList = igStandardOrderDetail.getPlanIdList();

            List<SingaporeStandardPlanDto> singaporeStandardPlanDtoListFilter = singaporeStandardPlanDtoList.stream()
                    .filter(x -> planIdList.contains(x.getPlanId()))
                    .collect(Collectors.toList());

            // 获取主险对应的方案信息
            SingaporeStandardPlanDto singaporeStandardPlanDto = this.getMainPlan(planConfigIdList, singaporeStandardPlanDtoListFilter);

            if (null == singaporeStandardPlanDto) {
                continue;
            }

            singaporeCartDetailPlanRspDto.setId(igStandardOrderDetail.getId());
            singaporeCartDetailPlanRspDto.setEmployeeType(igStandardOrderDetail.getEmployeeType());
            singaporeCartDetailPlanRspDto.setEmployeeTitle(StandardEmployeeTypeEnum.getByValue(igStandardOrderDetail.getEmployeeType()));
            singaporeCartDetailPlanRspDto.setPlanTitle(singaporeStandardPlanDto.getPlanTitle());
            singaporeCartDetailPlanRspDto.setCompanyLogo(singaporeStandardPlanDto.getCompanyLogo());
            singaporeCartDetailPlanRspDto.setTotalAmount(igStandardOrderDetail.getAmount());
            int personNum = igStandardOrderDetail.getChildNum() + igStandardOrderDetail.getSpouseNum() + igStandardOrderDetail.getEmployeeNum();
            singaporeCartDetailPlanRspDto.setPersonNum(personNum);
            singaporeCartDetailPlanRspDto.setCoverages(this.getCoverages(igStandardOrderDetail, singaporeStandardPlanDtoListFilter));

            singaporeCartDetailPlanRspDtoList.add(singaporeCartDetailPlanRspDto);
        }

        return singaporeCartDetailPlanRspDtoList;
    }

    @Override
    public Long submitOrderCart(SingaporeCartSubmitReqDto singaporeCartSubmitReqDto) {

        if (CollectionUtils.isEmpty(singaporeCartSubmitReqDto.getOrderDetailIdList())) {
            throw new QuoteException(-1, "order detail id is null");
        }

        // 已有的
        List<IgStandardOrderDetail> igStandardOrderDetailList = standardOrderDetailMapper.getByIdList(singaporeCartSubmitReqDto.getOrderDetailIdList());

        // 第一个用于查询
        IgStandardOrderDetail igStandardOrderDetail = igStandardOrderDetailList.get(0);

        // 更新连带的配置信息
        this.updateRelations(igStandardOrderDetailList);

        // 删除没有的
        List<IgStandardOrderDetail> igStandardOrderDetailListAll = standardOrderDetailMapper.getByDetailListOrderId(igStandardOrderDetail.getStandardOrderId());
        List<IgStandardOrderDetail> igStandardOrderDetailListDelete = igStandardOrderDetailListAll.stream()
                .filter(x -> !igStandardOrderDetailList.stream().map(IgStandardOrderDetail::getId).collect(Collectors.toList())
                        .contains(x.getId())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(igStandardOrderDetailListDelete)) {
            igStandardOrderDetailListDelete.forEach(igStandardOrderDetailDelete -> {
                standardOrderDetailMapper.entity(IgStandardOrderDetail.class).deleteOne(igStandardOrderDetailDelete.getId(), true);
            });
        }

        // 订单更新
        IgStandardOrder igStandardOrder = standardOrderMapper.entity(IgStandardOrder.class).selectOne(igStandardOrderDetail.getStandardOrderId(), true);
        if (CollectionUtils.isEmpty(igStandardOrderDetailList)) {
            throw new QuoteException(-1, "order detail is null");
        }

        // 更新账单
        IgStandardOrder igStandardOrderUpdate = new IgStandardOrder();

        igStandardOrderUpdate.setId(igStandardOrder.getId());
        igStandardOrderUpdate.setStatus(SingaporeStandardOrderStatusEnum.TO_BE_PERFECTED.getDictKey());
        igStandardOrderUpdate.setTotalAmount(igStandardOrderDetailList.stream().map(IgStandardOrderDetail::getAmount).reduce(CurrencyAmountUtil::add).orElseGet(() -> CurrencyAmount.zero(IdentityContext.getBusinessCurrency())));
        igStandardOrderUpdate.setPersonNum(igStandardOrderDetailList.stream().mapToInt(x -> x.getChildNum() + x.getEmployeeNum() + x.getSpouseNum()).sum());
        int i = standardOrderMapper.entity(IgStandardOrder.class).updateOne(igStandardOrderUpdate, true);
        log.warn("更新订单状态：更新结果「{}」", i);

        return igStandardOrder.getId();
    }

    /**
     * 增加连带的配置ID
     *
     * @param igStandardOrderDetailList 订单明细列表
     */
    private void updateRelations(List<IgStandardOrderDetail> igStandardOrderDetailList) {
        // 查询所有标品
        ResponseVO<List<RecommendStandardPlan>> responseVO = planConfigClient.getStandardConfig(singaporeStandardGroupConfig.getIdList());
        if (CollectionUtil.isEmpty(responseVO.getData())) {
            throw new QuoteException(-1, "standard config is empty");
        }
        List<RecommendStandardPlan> recommendStandardPlans = responseVO.getData();

        List<Long> planIdList = igStandardOrderDetailList.stream().map(IgStandardOrderDetail::getPlanIdList).flatMap(Collection::stream).collect(Collectors.toList());
        Map<Long, RecommendStandardConfig> recommendStandardConfigMap = recommendStandardPlans.stream()
                .filter(x -> null != x.getRecommendStandardConfigList()
                        && !CollectionUtils.isEmpty(x.getRecommendStandardConfigList())
                        && planIdList.contains(x.getPlanId())
                )
                .map(RecommendStandardPlan::getRecommendStandardConfigList)
                .flatMap(Collection::stream)
                .filter(x -> null != x.getConfigTag())
                .collect(Collectors.toMap(RecommendStandardConfig::getId, x -> x, (x, y) -> x));

        igStandardOrderDetailList.forEach(igStandardOrderDetail -> {
            List<Long> planConfigIdList = igStandardOrderDetail.getPlanConfigIdList();

            List<Long> planConfigIdListAdd = new ArrayList<>();
            for (Long planConfigId : planConfigIdList) {
                if (!recommendStandardConfigMap.containsKey(planConfigId)) {
                    log.error("plan_config_id is no exist{}", planConfigId);
                    continue;
                }

                RecommendStandardConfig recommendStandardConfig = recommendStandardConfigMap.get(planConfigId);

                if (!recommendStandardConfig.getRelation().equals(IgPlanConfigEnums.Relation.ONESELF.getCode())) {
                    continue;
                }
                StandardCoverageEnum standardCoverageEnum = StandardCoverageEnum.getTitleByTags(recommendStandardConfig.getConfigTag());

                if (null == standardCoverageEnum) {
                    log.error("plan_config_id tags is empty {}", JacksonUtils.writeAsString(recommendStandardConfig));
                    continue;
                }
                if (igStandardOrderDetail.getSpouseNum() > 0) {
                    Optional<RecommendStandardConfig> first = recommendStandardConfigMap.values().stream()
                            .filter(x -> x.getPlanId().equals(recommendStandardConfig.getPlanId())
                                    && standardCoverageEnum.equals(StandardCoverageEnum.getTitleByTags(recommendStandardConfig.getConfigTag()))
                                    && x.getConfigTag().contains(standardCoverageEnum.getCode())
                                    && x.getName().contains(recommendStandardConfig.getName())
                                    && x.getRelation().equals(IgPlanConfigEnums.Relation.SPOUSE.getCode())
                            )
                            .findFirst();
                    if (first.isPresent()) {
                        planConfigIdListAdd.add(first.get().getId());
                    } else {
                        log.error("plan_config_id related spouse config no exist{}", JacksonUtils.writeAsString(recommendStandardConfig));
                    }
                }
                if (igStandardOrderDetail.getChildNum() > 0) {
                    Optional<RecommendStandardConfig> first = recommendStandardConfigMap.values().stream()
                            .filter(x -> x.getPlanId().equals(recommendStandardConfig.getPlanId())
                                    && standardCoverageEnum.equals(StandardCoverageEnum.getTitleByTags(recommendStandardConfig.getConfigTag()))
                                    && x.getConfigTag().contains(standardCoverageEnum.getCode())
                                    && x.getName().contains(recommendStandardConfig.getName())
                                    && x.getRelation().equals(IgPlanConfigEnums.Relation.CHILDREN.getCode())
                            )
                            .findFirst();
                    if (first.isPresent()) {
                        planConfigIdListAdd.add(first.get().getId());
                    } else {
                        log.error("plan_config_id related child config no exist{}", JacksonUtils.writeAsString(recommendStandardConfig));
                    }
                }
            }

            if (!planConfigIdListAdd.stream().distinct().collect(Collectors.toList()).equals(igStandardOrderDetail.getPlanConfigIdList())) {
                IgStandardOrderDetail igStandardOrderDetailUpdate = new IgStandardOrderDetail();
                planConfigIdListAdd.addAll(igStandardOrderDetail.getPlanConfigIdList());
                igStandardOrderDetailUpdate.setId(igStandardOrderDetail.getId());
                igStandardOrderDetailUpdate.setPlanConfigIdList(planConfigIdListAdd);
                int i = standardOrderDetailMapper.entity(IgStandardOrderDetail.class).updateOne(igStandardOrderDetailUpdate, true);
                log.warn("update IgStandardOrderDetail {} result「{}」", JacksonUtils.writeAsString(igStandardOrderDetailUpdate), i);
            }
        });
    }

    @Override
    public Long submitOrderInfo(SingaporeOrderInfoSubmitReqDto singaporeOrderInfoSubmitReqDto) {
        // 参数校验
        checkParamsForSgpOrderSubmit(singaporeOrderInfoSubmitReqDto);
        // 查询订单
        Long orderId = singaporeOrderInfoSubmitReqDto.getOrderId();
        IgStandardOrder igStandardOrder = standardOrderMapper.entity(IgStandardOrder.class).selectOne(orderId, true);
        if (null == igStandardOrder) {
            throw new QuoteException(-1, "order is not exist");
        }
        // 构造标品订单
        IgStandardOrder updateSgpIgStandardOrder = constructUpdateSgpIgStandardOrder(singaporeOrderInfoSubmitReqDto);
        standardOrderMapper.entity(IgStandardOrder.class).updateOne(updateSgpIgStandardOrder, true);
        return orderId;
    }

    /**
     * 构造标品订单
     */
    private IgStandardOrder constructUpdateSgpIgStandardOrder(SingaporeOrderInfoSubmitReqDto singaporeOrderInfoSubmitReqDto) {
        IgStandardOrder igStandardOrderUpdate = new IgStandardOrder();
        igStandardOrderUpdate.setId(singaporeOrderInfoSubmitReqDto.getOrderId());
        igStandardOrderUpdate.setStartTime(singaporeOrderInfoSubmitReqDto.getStartTime());
        igStandardOrderUpdate.setEndTime(singaporeOrderInfoSubmitReqDto.getEndTime());
        igStandardOrderUpdate.setGroupIncorporatedIn(singaporeOrderInfoSubmitReqDto.getGroupIncorporatedIn());
        igStandardOrderUpdate.setGroupRegistrationNo(singaporeOrderInfoSubmitReqDto.getGroupRegistrationNo());
        igStandardOrderUpdate.setGroupName(singaporeOrderInfoSubmitReqDto.getGroupName());
        igStandardOrderUpdate.setGroupRegistrationSsic(singaporeOrderInfoSubmitReqDto.getGroupRegistrationSsic());
        igStandardOrderUpdate.setGroupRegistrationGst(singaporeOrderInfoSubmitReqDto.getGroupRegistrationGst());
        igStandardOrderUpdate.setGroupPostCode(singaporeOrderInfoSubmitReqDto.getGroupPostCode());
        igStandardOrderUpdate.setGroupUnitNo(singaporeOrderInfoSubmitReqDto.getGroupUnitNo());
        igStandardOrderUpdate.setGroupAddress(singaporeOrderInfoSubmitReqDto.getGroupAddress());
        igStandardOrderUpdate.setContactTitle(singaporeOrderInfoSubmitReqDto.getContactTitle());
        igStandardOrderUpdate.setContactPerson(singaporeOrderInfoSubmitReqDto.getContactPerson());
        igStandardOrderUpdate.setContactPhone(singaporeOrderInfoSubmitReqDto.getContactPhone());
        igStandardOrderUpdate.setGroupOrgCode(singaporeOrderInfoSubmitReqDto.getGroupRegistrationSsic());
        igStandardOrderUpdate.setGroupSocialCode(singaporeOrderInfoSubmitReqDto.getGroupRegistrationSsic());
        igStandardOrderUpdate.setContactEmail(singaporeOrderInfoSubmitReqDto.getContactEmail());
        igStandardOrderUpdate.setContactMobile(singaporeOrderInfoSubmitReqDto.getContactMobile());
        igStandardOrderUpdate.setStatus(SingaporeStandardOrderStatusEnum.TO_BE_PERFECTED.getDictKey());
        igStandardOrderUpdate.setGroupBusinessNature(singaporeOrderInfoSubmitReqDto.getGroupBusinessNature());
        igStandardOrderUpdate.setContactDesignation(singaporeOrderInfoSubmitReqDto.getContactDesignation());
        return igStandardOrderUpdate;
    }

    /**
     * 参数校验
     */
    private void checkParamsForSgpOrderSubmit(SingaporeOrderInfoSubmitReqDto singaporeOrderInfoSubmitReqDto) {
        if (StringUtil.checkString(singaporeOrderInfoSubmitReqDto.getGroupRegistrationNo())) {
            throw new QuoteException(-1, "Invalid UEN/CO. Registration No. Please check and try again.");
        }
        if (StringUtil.checkString(singaporeOrderInfoSubmitReqDto.getGroupName())) {
            throw new QuoteException(-1, "Invalid Company Name. Please check and try again.");
        }
        if (singaporeOrderInfoSubmitReqDto.getGroupName().contains("/")) {
            throw new QuoteException(-1, "Invalid Company Name. Please check and try again.");
        }
        if (StringUtil.checkString(singaporeOrderInfoSubmitReqDto.getGroupPostCode())) {
            throw new QuoteException(-1, "Invalid Postal Code. Please check and try again.");
        }
        if (StringUtil.checkString(singaporeOrderInfoSubmitReqDto.getGroupAddress())) {
            throw new QuoteException(-1, "Invalid Address. Please check and try again.");
        }
        if (StringUtil.checkString(singaporeOrderInfoSubmitReqDto.getGroupUnitNo())) {
            throw new QuoteException(-1, "Invalid Unit No.. Please check and try again.");
        }
        if (StringUtil.checkString(singaporeOrderInfoSubmitReqDto.getContactDesignation())) {
            throw new QuoteException(-1, "Invalid Designation. Please check and try again.");
        }
        if (null != singaporeOrderInfoSubmitReqDto.getContactPhone() && StringUtil.checkString(singaporeOrderInfoSubmitReqDto.getContactPhone())) {
            throw new QuoteException(-1, "Invalid Telephone. Please check and try again.");
        }
        if (StringUtil.checkString(singaporeOrderInfoSubmitReqDto.getContactPerson())) {
            throw new QuoteException(-1, "Invalid Name. Please check and try again.");
        }
        if (StringUtil.checkString(singaporeOrderInfoSubmitReqDto.getContactMobile())) {
            throw new QuoteException(-1, "Invalid Mobile. Please check and try again.");
        }
        boolean phoneNumberValid = InsgeekPhoneNumberUtil.validatePhoneNumberPre(singaporeOrderInfoSubmitReqDto.getContactMobile());
        if (!phoneNumberValid) {
            throw new QuoteException(-1, "Invalid mobile number. Please check and try again.");
        }
        boolean emailValid = Validator.isEmail(singaporeOrderInfoSubmitReqDto.getContactEmail());
        if (!emailValid) {
            throw new QuoteException(-1, "Invalid email address. Please check and try again.");
        }
    }

    @Override
    public SingaporeOrderDetailRspDto getSgpOrderDetail(Long orderId) {
        IgStandardOrder igStandardOrder = standardOrderMapper.getSingaporeOrder(orderId);

        List<IgStandardOrderDetail> igStandardOrderDetailList = standardOrderDetailMapper.getByDetailListOrderId(igStandardOrder.getId());

        SingaporeOrderDetailRspDto singaporeOrderDetailRspDto = new SingaporeOrderDetailRspDto();
        singaporeOrderDetailRspDto.setOrderId(igStandardOrder.getId());
        singaporeOrderDetailRspDto.setStartTime(igStandardOrder.getStartTime());
        singaporeOrderDetailRspDto.setEndTime(igStandardOrder.getEndTime());
        singaporeOrderDetailRspDto.setGroupIncorporatedIn(igStandardOrder.getGroupIncorporatedIn());
        singaporeOrderDetailRspDto.setGroupRegistrationNo(igStandardOrder.getGroupRegistrationNo());
        singaporeOrderDetailRspDto.setGroupName(igStandardOrder.getGroupName());
        singaporeOrderDetailRspDto.setGroupRegistrationSsic(igStandardOrder.getGroupRegistrationSsic());
        singaporeOrderDetailRspDto.setGroupRegistrationGst(igStandardOrder.getGroupRegistrationGst());
        singaporeOrderDetailRspDto.setGroupPostCode(igStandardOrder.getGroupPostCode());
        singaporeOrderDetailRspDto.setGroupUnitNo(igStandardOrder.getGroupUnitNo());
        singaporeOrderDetailRspDto.setGroupAddress(igStandardOrder.getGroupAddress());
        singaporeOrderDetailRspDto.setContactTitle(igStandardOrder.getContactTitle());
        singaporeOrderDetailRspDto.setContactPerson(igStandardOrder.getContactPerson());
        singaporeOrderDetailRspDto.setContactPhone(igStandardOrder.getContactPhone());
        singaporeOrderDetailRspDto.setContactMobile(igStandardOrder.getContactMobile());
        singaporeOrderDetailRspDto.setContactEmail(igStandardOrder.getContactEmail());
        singaporeOrderDetailRspDto.setTotalAmount(igStandardOrder.getTotalAmount());
        singaporeOrderDetailRspDto.setSubmitTime(igStandardOrder.getSubmitTime());
        singaporeOrderDetailRspDto.setContactDesignation(igStandardOrder.getContactDesignation());
        singaporeOrderDetailRspDto.setGroupBusinessNature(igStandardOrder.getGroupBusinessNature());
//        String fileUrl = filesService.getFileUrl(igStandardOrder.getAttorneyFileId().toString());
//        singaporeOrderDetailRspDto.setAttorneyFileUrl(fileUrl);
        singaporeOrderDetailRspDto.setStatus(igStandardOrder.getStatus());
        singaporeOrderDetailRspDto.setPlanList(this.getPlanList(igStandardOrderDetailList));

        singaporeOrderDetailRspDto.setPlanConfigList(standardPlanMapper.getPlanConfigListByOrderId(orderId));

        return singaporeOrderDetailRspDto;
    }

    private List<SingaporeStandardPlanMainCoverageDto> getCoverages(IgStandardOrderDetail igStandardOrderDetail, List<SingaporeStandardPlanDto> singaporeStandardPlanDtoList) {

        List<SingaporeStandardPlanMainCoverageDto> singaporeStandardPlanMainCoverageDtos = new ArrayList<>();

        Map<Long, RecommendStandardConfig> recommendStandardConfigMap = singaporeStandardPlanDtoList.stream()
                .map(SingaporeStandardPlanDto::getRecommendStandardConfigList).flatMap(Collection::stream)
                .filter(x -> IgPlanConfigEnums.Relation.ONESELF.getCode().equals(x.getRelation()) && igStandardOrderDetail.getPlanConfigIdList().contains(x.getId()))
                .collect(Collectors.toMap(RecommendStandardConfig::getId, Function.identity()));

        ResponseVO<Map<Long, List<ProductClientUIDTO>>> planConfigDutyDetailBatch = productUIClient.getPlanConfigDutyDetailBatch(recommendStandardConfigMap.values().stream().map(RecommendStandardConfig::getId).collect(Collectors.toList()), "1");
        Map<Long, List<ProductClientUIDTO>> planConfigDutyDetailBatchData = planConfigDutyDetailBatch.getData();

        Map<Long, RecommendStandardConfig> recommendStandardConfigMapMain = new HashMap<>();
        Map<Long, RecommendStandardConfig> recommendStandardConfigMapExtend = new HashMap<>();
        igStandardOrderDetail.getPlanConfigIdList().forEach(planConfigId -> {
            RecommendStandardConfig recommendStandardConfig = recommendStandardConfigMap.get(planConfigId);
            if (recommendStandardConfig != null) {
                if ("1".equals(recommendStandardConfig.getIsMainConfig())) {
                    recommendStandardConfigMapExtend.put(planConfigId, recommendStandardConfig);
                } else {
                    recommendStandardConfigMapMain.put(planConfigId, recommendStandardConfig);
                }
            }
        });

        Map<String, List<Long>> coverageConfigIdMap = new HashMap<>();
        recommendStandardConfigMapMain.values().forEach(recommendStandardConfig -> {
            if (CollectionUtil.isNotEmpty(recommendStandardConfig.getOptionalConfigs())) {
                recommendStandardConfig.getOptionalConfigs().forEach(optionalConfig -> {
                    if (recommendStandardConfigMap.containsKey(optionalConfig)) {
                        RecommendStandardConfig relateConfig = recommendStandardConfigMap.get(optionalConfig);
                        String code = StandardCoverageEnum.getTitleByTags(relateConfig.getConfigTag()).getCode();
                        if (!coverageConfigIdMap.containsKey(code)) {
                            coverageConfigIdMap.put(code, new ArrayList<>());
                        }
                        coverageConfigIdMap.get(code).add(optionalConfig);
                    }
                });
            }
        });


        // 所有主险
        recommendStandardConfigMapMain.values().forEach(recommendStandardConfig -> {
            StandardCoverageEnum standardCoverageEnum = StandardCoverageEnum.getTitleByTags(recommendStandardConfig.getConfigTag());
            SingaporeStandardPlanMainCoverageDto singaporeStandardPlanMainCoverageDto = new SingaporeStandardPlanMainCoverageDto();
            singaporeStandardPlanMainCoverageDto.setPlanId(recommendStandardConfig.getPlanId());
            singaporeStandardPlanMainCoverageDto.setPlanConfigId(recommendStandardConfig.getId());
            singaporeStandardPlanMainCoverageDto.setCoverageTitle(Objects.requireNonNull(standardCoverageEnum).getTitleText());
            singaporeStandardPlanMainCoverageDto.setCoverageCode(standardCoverageEnum.getCode());
            singaporeStandardPlanMainCoverageDto.setCoverageCoreType(standardCoverageEnum.getInsuranceTypeDetail().getCode().toUpperCase());
            singaporeStandardPlanMainCoverageDto.setConfigName(recommendStandardConfig.getName());
            singaporeStandardPlanMainCoverageDto.setBenefit(recommendStandardConfig.getBenefit());
            singaporeStandardPlanMainCoverageDto.setDutyList(planConfigDutyDetailBatchData.get(recommendStandardConfig.getId()));

            List<SingaporeStandardPlanOptionalCoverageItemDto> singaporeStandardPlanOptionalCoverageItemDtoList = new ArrayList<>();
            recommendStandardConfigMapExtend.values().stream().filter(x -> x.getPlanId().equals(recommendStandardConfig.getPlanId())).forEach(x -> {
                StandardCoverageEnum standardCoverageEnumExtend = StandardCoverageEnum.getTitleByTags(x.getConfigTag());
                SingaporeStandardPlanOptionalCoverageItemDto singaporeStandardPlanOptionalCoverageItemDto = new SingaporeStandardPlanOptionalCoverageItemDto();

                // 是否关联配置
                if (null == standardCoverageEnumExtend) {
                    log.info("附加险的方案类型为空");
                    return;
                }
                String code = standardCoverageEnumExtend.getCode();
                if (coverageConfigIdMap.containsKey(code) && !coverageConfigIdMap.get(code).contains(x.getId())) {
                    return;
                }

                singaporeStandardPlanOptionalCoverageItemDto.setPlanId(x.getPlanId());
                singaporeStandardPlanOptionalCoverageItemDto.setPlanConfigId(x.getId());
                singaporeStandardPlanOptionalCoverageItemDto.setCoverageTitle(Objects.requireNonNull(standardCoverageEnumExtend).getTitleText());
                singaporeStandardPlanOptionalCoverageItemDto.setCoverageCode(standardCoverageEnumExtend.getCode());
                singaporeStandardPlanOptionalCoverageItemDto.setCoverageCoreType(standardCoverageEnumExtend.getInsuranceTypeDetail().getCode().toUpperCase());
                singaporeStandardPlanOptionalCoverageItemDto.setConfigName(x.getName());
                singaporeStandardPlanOptionalCoverageItemDto.setBenefit(x.getBenefit());
                singaporeStandardPlanOptionalCoverageItemDto.setAnnualPolicyLimit(x.getAnnualPolicyLimit());
                singaporeStandardPlanOptionalCoverageItemDto.setDutyList(planConfigDutyDetailBatchData.get(x.getId()));
                singaporeStandardPlanOptionalCoverageItemDtoList.add(singaporeStandardPlanOptionalCoverageItemDto);
            });
            singaporeStandardPlanMainCoverageDto.setOptionalRiderList(singaporeStandardPlanOptionalCoverageItemDtoList);

            singaporeStandardPlanMainCoverageDto.setAnnualPolicyLimit(recommendStandardConfig.getAnnualPolicyLimit());
            singaporeStandardPlanMainCoverageDtos.add(singaporeStandardPlanMainCoverageDto);
        });

        return singaporeStandardPlanMainCoverageDtos;
    }

    private SingaporeStandardPlanDto getMainPlan(List<Long> planConfigIdList, List<SingaporeStandardPlanDto> singaporeStandardPlanDtoList) {
        Map<Long, SingaporeStandardPlanDto> singaporeStandardPlanDtoMap = singaporeStandardPlanDtoList.stream()
                .collect(Collectors.toMap(SingaporeStandardPlanDto::getPlanId, Function.identity()));

        log.warn("获取主主方案参数：{}-{}", planConfigIdList, singaporeStandardPlanDtoList);
        Map<Long, RecommendStandardConfig> recommendStandardConfigMap = singaporeStandardPlanDtoList.stream()
                .map(SingaporeStandardPlanDto::getRecommendStandardConfigList).flatMap(Collection::stream)
                .collect(Collectors.toList())
                .stream().filter(x -> "0".equals(x.getIsMainConfig()))
                .collect(Collectors.toMap(RecommendStandardConfig::getId, Function.identity()));

        Map<String, Integer> coverageMap = new HashMap<>();
        standardCoverageIndexConfig.getMain().forEach((x, y) -> {
            coverageMap.put(y, x);
        });

        Map<Long, Integer> planConfigMap = new HashMap<>();
        for (Long planConfigId : planConfigIdList) {
            if (recommendStandardConfigMap.containsKey(planConfigId)) {
                RecommendStandardConfig recommendStandardConfig = recommendStandardConfigMap.get(planConfigId);
                StandardCoverageEnum standardCoverageEnum = StandardCoverageEnum.getTitleByTags(recommendStandardConfig.getConfigTag());
                if (null != standardCoverageEnum) {
                    Integer i = coverageMap.get(standardCoverageEnum.getCode());
                    planConfigMap.put(planConfigId, i);
                }
            }
        }

        Optional<Map.Entry<Long, Integer>> min = planConfigMap.entrySet().stream()
                .min(Map.Entry.comparingByValue());
        RecommendStandardConfig recommendStandardConfig = new RecommendStandardConfig();
        if (min.isPresent()) {
            recommendStandardConfig = recommendStandardConfigMap.get(min.get().getKey());
        }

        return singaporeStandardPlanDtoMap.get(recommendStandardConfig.getPlanId());
    }
}
