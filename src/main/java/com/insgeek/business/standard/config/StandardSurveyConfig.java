package com.insgeek.business.standard.config;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Getter
@Setter
@ToString
@Component
@RefreshScope
@ConfigurationProperties(prefix = "standard.survey")
public class StandardSurveyConfig {

    /**
     * 问卷调查弹窗
     */
    private Boolean surveyPopupEnabled = Boolean.FALSE;

    /**
     * 用户访谈弹窗
     */
    private Boolean interviewPopupEnabled = Boolean.FALSE;

}
