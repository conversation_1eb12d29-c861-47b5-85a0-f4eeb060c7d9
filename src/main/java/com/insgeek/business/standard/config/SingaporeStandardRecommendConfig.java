package com.insgeek.business.standard.config;

import com.insgeek.business.standard.dto.vo.dto.SingaporeStandardPlanMainCoverageDto;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Component
@RefreshScope
@ConfigurationProperties(prefix = "standard.sgp.recommend")
public class SingaporeStandardRecommendConfig {

    private Integer cacheTimeMinutes = 30;

    private List<PriceRangePriceWeight> priceRangePriceWeightList;

    /**
     * 不推荐保司配置
     */
    private List<Long> notRecommendCompanyIdList = new ArrayList<>();

    /**
     * 优先推荐配置
     */
    private Map<Long, Map<String, Long>> channelCompanyFirst = new HashMap<>();

    /**
     * benchmark 年龄配置
     */
    private List<benchmarkAge> benchmarkAgeGroups = new ArrayList<>();
    /**
     * 配置的责任信息
     */
    private Map<String, List<SingaporeStandardPlanMainCoverageDto>> benchmarkCoverages = new HashMap<>();

    /**
     * 人数特殊配置 - 少于多少人只推荐
     */
    private Integer lessThanRecommendPersonNum = 1;
    /**
     * 只推荐的保司
     */
    private List<Long> lessThanRecommendCompanyIdList = new ArrayList<>();

    /**
     * 人数特殊配置 - 少于多少人不推荐
     */
    private Integer lessThanNotRecommendPersonNum = 4;
    /**
     * 只推荐的保司 - 少于多少人不推荐
     */
    private List<Long> lessThanNotRecommendCompanyIdList = new ArrayList<>();

    @Data
    @Accessors(chain = true)
    public static class PriceRangePriceWeight {
        private BigDecimal priceMin;
        private BigDecimal priceMax;
        List<CodeWeight> codeWeightList;
    }

    @Data
    @Accessors(chain = true)
    public static class benchmarkAge {
        private String ageRange;
        private String employee;
        private String midManagement;
        private String management;
        private String foreignWorker;
    }

    @Data
    @Accessors(chain = true)
    public static class CodeWeight {
        private String type;
        private String code;
        private Integer weight;
    }

    public Map<String, Integer> getCodeWeightListByPrice(BigDecimal price) {
        for (PriceRangePriceWeight priceRangePriceWeight : priceRangePriceWeightList) {
            if (price.compareTo(priceRangePriceWeight.priceMin) >= 0 && price.compareTo(priceRangePriceWeight.priceMax) < 0) {
                return priceRangePriceWeight.codeWeightList.stream()
                        .collect(Collectors.toMap(
                                CodeWeight::getType,
                                CodeWeight::getWeight
                        ));
            }
        }
        return null;
    }
}
