package com.insgeek.business.standard.config;

import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Getter
@Setter
@ToString
@Component
@RefreshScope
@ConfigurationProperties(prefix = "standard.channel")
public class StandardChannelConfig {
    private Long defaultId;
    private Boolean gotoGideButtonFlag = false;
    private List<Long> channelIdList = new ArrayList<>();
}
