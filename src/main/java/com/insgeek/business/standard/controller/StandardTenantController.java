package com.insgeek.business.standard.controller;

import com.insgeek.boot.web.auth.annotation.AuthOptional;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.standard.dto.vo.rsp.StandardTenantInfoRspDto;
import com.insgeek.business.standard.service.StandardPlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 标品控制器
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/v1/standard/tenant")
@Slf4j
public class StandardTenantController {

    @Autowired
    private StandardPlanService standardPlanService;

    /**
     * 获取默认渠道id
     */
    @GetMapping("/default")
    @AuthOptional
    public ResponseVO<Long> defaultTenantInfo() {
        return ResponseVO.<Long>builder().data(standardPlanService.getDefaultTenantId()).build();
    }

    /**
     * 获取渠道信息
     */
    @GetMapping("/info")
    @AuthOptional
    public ResponseVO<StandardTenantInfoRspDto> info() {
        return ResponseVO.<StandardTenantInfoRspDto>builder().data(standardPlanService.getTenantInfo()).build();
    }

}
