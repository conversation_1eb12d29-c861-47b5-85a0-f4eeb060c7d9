package com.insgeek.business.standard.controller;

import com.insgeek.boot.commons.datetime.DateTimeUtil;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.backend.aggservice.EnterpriseInfoService;
import com.insgeek.business.quote.backend.dto.quote.QpEnterpriseInfoDto;
import com.insgeek.business.quote.common.dto.BusinessInformationDTO;
import com.insgeek.business.quote.common.dto.QpCustomerDetailDTO;
import com.insgeek.business.quote.common.service.QpCustomerService;
import com.insgeek.business.standard.enums.StandardTypeEnum;
import com.insgeek.business.standard.mapper.StandardPlanMapper;
import com.insgeek.protocol.common.client.BusinessInformationClient;
import com.insgeek.protocol.common.dto.BusinessInformationDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.List;
import java.util.Objects;

/**
 * 企业信息控制器
 *
 * <AUTHOR>
 * @version 1.0
 */
@RestController
@RequestMapping("/v1/standard/customer")
@Slf4j
public class StandardCustomerController {

    @Autowired
    BusinessInformationClient businessInformationClient;

    @Resource
    private QpCustomerService qpCustomerService;

    @Autowired
    private EnterpriseInfoService enterpriseInfoService;

    @Autowired
    private StandardPlanMapper standardPlanMapper;


    /**
     * 查询企业信息
     *
     * @param groupName 企业组名
     * @return 包含查询结果的响应对象
     */
    @GetMapping("/query")
    public ResponseVO query(@RequestParam("group_name") String groupName, @RequestParam("standard_type") String standardType) {
        BusinessInformationDTO businessInformationDTO = new BusinessInformationDTO();

        // 断保企业
        QpEnterpriseInfoDto enterpriseInfo = enterpriseInfoService.getEnterpriseInfo(groupName, null, null);
        if (null != enterpriseInfo && "1".equals(enterpriseInfo.getInsuredEnterpriseFlag())) {
            businessInformationDTO.setReputationFlag(true);
            return ResponseVO.data(businessInformationDTO);
        }

        // 方案结束日期距今天大于90天的（员福 ）
        if (StandardTypeEnum.isWelfareRecommend(standardType)) {
            ZonedDateTime maxPlanEndTime = standardPlanMapper.getMaxPlanEndTime(groupName.trim());
            ZonedDateTime dateTime = DateTimeUtil.offsetDay(DateTimeUtil.endOfDay(DateTimeUtil.now(true)), 90);
            if (null != maxPlanEndTime && maxPlanEndTime.compareTo(dateTime) < 1) {
                businessInformationDTO.setReputationFlag(true);
                return ResponseVO.data(businessInformationDTO);
            }
        }

        // 查询企业详细信息
        ResponseVO<BusinessInformationDto> businessInformationDtoResponse = businessInformationClient.findDetailByName(groupName);
        BusinessInformationDto businessInformationDto = businessInformationDtoResponse.getData();
        if (!businessInformationDtoResponse.ok()) {
            return businessInformationDtoResponse;
        }
        if (Objects.isNull(businessInformationDto)) {
            return businessInformationDtoResponse;
        }

        BeanUtils.copyProperties(businessInformationDto, businessInformationDTO);
        businessInformationDTO.setReputationFlag(false);

        // 获取企业对应的客户详情
        List<QpCustomerDetailDTO> qpCustomerDetailDTOS = qpCustomerService.selectDetailByEnterpriseName(groupName);

        // 如果没有找到对应的客户详情，则默认设置为非失信
        if (!qpCustomerDetailDTOS.isEmpty()) {
            // 根据客户详情中的信誉标志决定最终的企业信誉状态
            boolean isAnyReputationTrue = qpCustomerDetailDTOS.stream()
                    .map(QpCustomerDetailDTO::getReputationFlag)
                    .distinct()
                    // flag包含任何一个true值立即返回
                    .anyMatch(flag -> flag);

            if (Boolean.TRUE.equals(isAnyReputationTrue)) {
                businessInformationDTO.setReputationFlag(true);
                return ResponseVO.data(businessInformationDTO);
            }
        }

        return ResponseVO.data(businessInformationDTO);
    }
}
