package com.insgeek.business.quote.quotation.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.insgeek.business.quote.backend.Enums.QuoteTypeEnum;
import com.insgeek.business.quote.backend.exception.QuoteErrorCode;
import com.insgeek.business.quote.common.dao.constant.QpFileDetailsConst;
import com.insgeek.business.quote.common.dao.constant.QpQuotationConst;
import com.insgeek.business.quote.common.dao.constant.QpQuoteConst;
import com.insgeek.business.quote.common.enums.PayTypeEnum;
import com.insgeek.business.quote.common.enums.dict.PayUnitEnum;
import com.insgeek.business.quote.common.utils.CommonDataUtil;
import com.insgeek.business.quote.dto.MetaDescriptionPDto;
import com.insgeek.business.quote.dto.MetaDescriptionParam;
import com.insgeek.business.quote.dto.MetaDescriptionRDto;
import com.insgeek.business.quote.dto.MetaDescriptionResult;
import com.insgeek.business.quote.enums.EntityKeyEnum;
import com.insgeek.business.quote.feilv.FeilvEmployerQuotationService;
import com.insgeek.business.quote.quotation.dto.version.compare.OuterJoinVersionContext;
import com.insgeek.business.quote.quotation.service.QuotationService;
import com.insgeek.business.quote.util.CommonUtil;
import com.insgeek.business.quote.util.DataProcessing;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.components.orm.model.querydsl.BQLQueryFactory;
import com.insgeek.protocol.data.client.constants.Constant;
import com.insgeek.protocol.data.client.entity.*;
import com.insgeek.protocol.platform.common.dto.entity.IgCompany;
import com.insgeek.protocol.platform.metadata.dto.ItemDto;
import com.querydsl.core.types.dsl.Expressions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.stream.Collectors;



/**
 * {@link com.insgeek.business.quote.common.service.impl.QpQuoteServiceImpl}
 *
 * <AUTHOR>
 */
@Service("quotationServiceImpl")
@Slf4j
public class QuotationServiceImpl extends AbstractQuotationService implements QuotationService {

    @Autowired
    private DataMapper<QpQuotation> quotationDataMapper;

    @Autowired
    private DataMapper<QpQuotationConfig> quotationConfigDataMapper;

    @Autowired
    BQLQueryFactory bqlQueryFactory;

    @Autowired
    private SpecialServiceImpl specialService;

    @Autowired
    private DataMapper<QpQuotationInfo> quotationInfoDataMapper;

    @Autowired
    private FeilvEmployerQuotationService feilvEmployerQuotationService;

    @Resource
    DataMapper<IgCompany> companyDataMapper;

    //////////////////报价重构/////////////////////
    @Override
    public List<MetaDescriptionResult> query(MetaDescriptionParam param) {
        check(param);
        QpQuotation data = CommonUtil.convertToObject(QpQuotation.class, param, false);
        List<QpQuotation> dataList = quotationDataMapper.entity(QpQuotation.class).select(data, true);
        List<MetaDescriptionResult> results = CommonUtil.convertToListObject(Lists.newArrayList(dataList), defaultMap());
        quoteFileService.joinFiles(dataList.stream().map(QpQuotation::getId).collect(Collectors.toList()), results, Lists.newArrayList(QpFileDetailsConst.BusType.QP_QUOTATION.getCode()), null);
        return results;
    }

    @Override
    public MetaDescriptionResult queryContrastsByIds(List<Long> ids) {
        List<QpQuotation> qpQuotationList = quotationDataMapper.entity(QpQuotation.class).select(Lists.newArrayList(ids), true);
        if (CollectionUtil.isEmpty(qpQuotationList)) {
            return MetaDescriptionResult.getInstance();
        }
        MetaDescriptionResult result = CommonUtil.multiVersionCompare(ids, qpQuotationList, defaultMap());
        quoteFileService.joinValsFiles(qpQuotationList.stream().map(QpQuotation::getId).collect(Collectors.toList()), result, Lists.newArrayList(QpFileDetailsConst.BusType.QP_QUOTATION.getCode()), null);
        return result;
    }


    private List<MetaDescriptionResult> compareOuterJoin(MetaDescriptionParam param, List<QpQuotation> currentDataList) {
        Integer compare = param.getCompare();
        if (compare.equals(0)) {
            // 不比较
            return CommonUtil.convertToListObject(currentDataList, defaultMap());
        }
        OuterJoinVersionContext<QpQuotation> outerJoin = previousVersionCompareService.findQpQuotationPreviousVersionOuterJoin(currentDataList);
        return CommonUtil.compareOuterJoin(currentDataList, compare, outerJoin, defaultMap());
    }

    /**
     * 0-本人
     * 1-子女
     * 2-配偶
     * 3-父母
     * <p>
     * 排序按照 本人 子女 父母 配偶 的顺序排序
     *
     * @param qpQuotation
     * @return
     */
    public String getRelationSorted(QpQuotation qpQuotation) {
        String relation = qpQuotation.getRelation();
        if (relation == null) {
            return "0";
        }
        if (relation.equals("0")) {
            return "0";
        }
        if (relation.equals("1")) {
            return "1";
        }
        if (relation.equals("3")) {
            return "2";
        }
        if (relation.equals("2")) {
            return "3";
        }

        return relation;
    }

    public ZonedDateTime getCreatedAtSorted(QpQuotation qpQuotation) {
        ZonedDateTime firstCreatedAt = qpQuotation.getFirstCreatedAt();
        if (firstCreatedAt == null) {
            return qpQuotation.getCreatedAt();
        }
        return firstCreatedAt;
    }


    public String getClientTypeSorted(QpQuotation qpQuotation) {
        String clientType = qpQuotation.getClientType();
        if (clientType == null) {
            return "1";
        }
        return clientType;
    }

    @Override
    public List<MetaDescriptionResult> queryContrast(MetaDescriptionParam param) {
        check(param);
        QpQuotation quote = CommonUtil.convertToObject(QpQuotation.class, param, false);
        assert quote != null;
        List<QpQuotation> currentDataList = quotationDataMapper.entity(QpQuotation.class).select(quote, true);

        if (CollectionUtil.isEmpty(currentDataList)) {
            return Lists.newArrayList();
        }
        // client_type 企业付费方案在个人付费方案之前
        // 1-企业增员  就是 企业付费方案
        // 2-个人推荐  就是 个人付费方案
        currentDataList.sort(Comparator.comparing(this::getClientTypeSorted)
                .thenComparing(this::getRelationSorted)
                .thenComparing(this::getCreatedAtSorted));

        List<MetaDescriptionResult> results = compareOuterJoin(param, currentDataList);

        if (quote.getId() != null) {
            results = CommonUtil.filterWithCurrentData(currentDataList, results);
        }
        // 方案主设置赔付率
        QQpQuotationPredictResult qQpQuotationPredictResult = QQpQuotationPredictResult.qp_quotation_predict_result;
        QQpQuotationPredict qQpQuotationPredict = QQpQuotationPredict.qp_quotation_predict;
        QQpQuotation qpQuotation = QQpQuotation.qp_quotation;
        List<QpQuotationPredictResult> qpQuotationPredictResultList = new ArrayList<>();
        // 判断是查询所有方案组 还是 查询单个方案组
        if(param.getData().get("id")==null ||  (param.getData().get("id").getVal() != null && Long.valueOf(param.getData().get("id").getVal().toString())==0)){
            // 询报价id
            Long quotationInfoId = Long.valueOf(param.getData().get("quotation_info_id").getVal().toString());
            List<QpQuotationPredict> qpQuotationPredictList = bqlQueryFactory.select(Expressions.stringPath("qp_quotation_predict.*")).from(qQpQuotationPredict).where(qQpQuotationPredict.quotation_info_id.eq(quotationInfoId)).findList(true,QpQuotationPredict.class);
            QpQuotationPredict quotationPredict = new QpQuotationPredict();
            if (CollectionUtil.isNotEmpty(qpQuotationPredictList)){
                Optional<QpQuotationPredict> optionalQpQuotationPredict = qpQuotationPredictList.stream().max(Comparator.comparing(QpQuotationPredict::getCreatedAt));
                if (optionalQpQuotationPredict.isPresent()){
                    quotationPredict = optionalQpQuotationPredict.get();
                }
                // 查询 qp_quotation id
                List<QpQuotation> qpQuotationList = bqlQueryFactory.select(Expressions.stringPath("qp_quotation.*")).from(qpQuotation).where(qpQuotation.quotation_info_id.eq(quotationPredict.getQuotationInfoId())).findList(true, QpQuotation.class);
                if (CollectionUtil.isNotEmpty(qpQuotationList)){
                    List<Long> ids = qpQuotationList.stream().map(QpQuotation::getId).collect(Collectors.toList());
                    qpQuotationPredictResultList = bqlQueryFactory.select(Expressions.stringPath("qp_quotation_predict_result.*")).from(qQpQuotationPredictResult).where(qQpQuotationPredictResult.business_id.in(ids),qQpQuotationPredictResult.task_id.eq(quotationPredict.getTaskId())).findList(true,QpQuotationPredictResult.class);
                }
            }
        }else{
             // id 传值的情况，标识查询的是单个方案组 直接正常set预测率就可以了
            QpQuotation quotation= bqlQueryFactory.select(Expressions.stringPath("qp_quotation.*")).from(qpQuotation).where(qpQuotation.id.eq(Long.valueOf(param.getData().get("id").getVal().toString()))).findOne(true, QpQuotation.class);
            List<QpQuotationPredict> qpQuotationPredictList = bqlQueryFactory.select(Expressions.stringPath("qp_quotation_predict.*")).from(qQpQuotationPredict).where(qQpQuotationPredict.quotation_info_id.eq(quotation.getQuotationInfoId())).findList(true,QpQuotationPredict.class);
            QpQuotationPredict quotationPredict = new QpQuotationPredict();
            if (CollectionUtil.isNotEmpty(qpQuotationPredictList)){
                Optional<QpQuotationPredict> optionalQpQuotationPredict = qpQuotationPredictList.stream().max(Comparator.comparing(QpQuotationPredict::getCreatedAt));
                if (optionalQpQuotationPredict.isPresent()){
                    quotationPredict = optionalQpQuotationPredict.get();
                }
                qpQuotationPredictResultList = bqlQueryFactory.select(Expressions.stringPath("qp_quotation_predict_result.*")).from(qQpQuotationPredictResult).where(qQpQuotationPredictResult.business_id.eq(Long.valueOf(param.getData().get("id").getVal().toString())),qQpQuotationPredictResult.task_id.eq(quotationPredict.getTaskId())).findList(true,QpQuotationPredictResult.class);
            }
        }
        if (CollectionUtil.isNotEmpty(qpQuotationPredictResultList)){
            Map<Long, QpQuotationPredictResult> qpQuotationPredictResultMap =
                    qpQuotationPredictResultList.stream().collect(Collectors.toMap(QpQuotationPredictResult::getBusinessId, x -> x, (x, y) -> x));
            results.forEach(x->{
                if (qpQuotationPredictResultMap.containsKey(x.getId())){
                    QpQuotationPredictResult qpQuotationPredictResult = qpQuotationPredictResultMap.get(x.getId());
                    if (qpQuotationPredictResult!=null){
                        x.setPredictRate(qpQuotationPredictResult.getPayoutRatio());
                    }
                }
            });
        }
        quoteFileService.joinFiles(currentDataList.stream().map(QpQuotation::getId).collect(Collectors.toList()), results, Lists.newArrayList(QpFileDetailsConst.BusType.QP_QUOTATION.getCode()), null);
        return results;
    }

    @Override
    public Long saveAllowNull(MetaDescriptionParam param) {
        check(param);
        checkSave(param);
        //插入
        if (Objects.isNull(param.getId()) || param.getId() == 0L) {
            QpQuotation quote = CommonUtil.convertToObject(QpQuotation.class, param, false);
            Long quotationInfoId = null;
            if (quote != null) {
                quotationInfoId = quote.getQuotationInfoId();
            }
            QpQuotationInfo quotationInfo = bqlQueryFactory.select(Expressions.stringPath("qp_quotation_info.*"))
                    .from(QQpQuotationInfo.qp_quotation_info)
                    .where(QQpQuotationInfo.qp_quotation_info.id.eq(quotationInfoId))
                    .findOne(true, QpQuotationInfo.class);
            assert quote != null;
            quote.setBvId(null);
            quote.setFirstCreatedAt(null);
            quote.setCompanyId(quotationInfo.getInsuranceConsumerId());
            quote = quotationDataMapper.entity(QpQuotation.class).insertOne(quote);
            param.setId(quote.getId());
            saveFiles(param);
        } else {
            Map<String, Object> stringObjectMap = CommonUtil.convertToMap(param, defaultMap());
            quotationDataMapper.entity(QpQuotation.class).batchSuperUpdate(Lists.newArrayList(stringObjectMap), true);
            saveFiles(param);
        }
        // 更新同方案组下所有方案数据
        synchronizeConfig(param);

        log.info("自动计算方案价格 quotation_id:{}", param.getId());
        feilvEmployerQuotationService.getFeilvReturnDtos(param.getId(),null);
        return param.getId();
    }


    public void syncCompany(MetaDescriptionParam param) {
        Map<String, MetaDescriptionPDto> data = param.getData();
        QpQuotationInfo quotationInfo = quotationInfoDataMapper.entity(QpQuotationInfo.class).selectOne(param.getId(), Boolean.TRUE);
        List<QpQuotation> qpQuotations = bqlQueryFactory.select(Expressions.stringPath("qp_quotation.*"))
                .from(QQpQuotation.qp_quotation)
                .where(QQpQuotation.qp_quotation.quotation_info_id.eq(param.getId()))
                .findList(Boolean.TRUE, QpQuotation.class);

        List<QpQuotation> updateQpQUotationList = new ArrayList<>();
        if (quotationInfo != null && CollUtil.isNotEmpty(qpQuotations)) {
            // 如果是雇主并且，是需要同步的
            if (data.containsKey("sync_company") && Objects.equals(quotationInfo.getQuoteType(), QuoteTypeEnum.EMPLOYER.getCode())) {
                MetaDescriptionPDto syncCompany = data.get("sync_company");
                if (syncCompany.getVal().equals(1)) {
                    updateQpQUotationList = updateCompanyId(qpQuotations, quotationInfo.getInsuranceConsumerId(),quotationInfo.getCooperationInfoId());
                }
            }
            // 非雇主直接更新
            if (!Objects.equals(quotationInfo.getQuoteType(), QuoteTypeEnum.EMPLOYER.getCode())) {
                updateQpQUotationList = updateCompanyId(qpQuotations, quotationInfo.getInsuranceConsumerId(),quotationInfo.getCooperationInfoId());
            }
        }
        if (CollUtil.isNotEmpty(updateQpQUotationList)) {
            quotationDataMapper.entity(QpQuotation.class).updateAll(updateQpQUotationList, Boolean.TRUE);
        }
    }

    //询价方案同方案组保持数据一致的分组
    private static final List<String> UP_GROUPS = Lists.newArrayList(Constant.QUOTE_MESSAGE_CODE, Constant.VALUE_CONTRIBUTION_CODE);

    /**
     * 更新同方案组下所有方案数据
     *
     * @param param 入参，更新的数据
     */
    private void synchronizeConfig(MetaDescriptionParam param) {
        List<Map<String, Object>> batchSuperUpdate = Lists.newArrayList();

        //查询本条数据
        QpQuotation quotation = quotationDataMapper.entity(QpQuotation.class).selectOne(param.getId(), true);

        //查询本组数据
        List<QpQuotationConfig> quotationConfigs = quotationConfigDataMapper.entity(QpQuotationConfig.class).select(new QpQuotationConfig().setQuotationId(quotation.getId()), true);

        //需要修改的字段
        List<String> fieldsNeedToBeModified = CommonUtil.convertToRdto(quotation, defaultMap())
                .stream()
                .filter(v -> UP_GROUPS.contains(v.getGroupCode())).map(MetaDescriptionRDto::getFieldCode).collect(Collectors.toList());
        //对象转换map
        Map<String, Object> stringObjectMap = CommonUtil.convertToMap(param, defaultMap());
        //保存可修改数据
        Map<String, Object> upFiledMap = Maps.newHashMap();
        for (Map.Entry<String, Object> entry : stringObjectMap.entrySet()) {
            if (fieldsNeedToBeModified.contains(entry.getKey())) {
                upFiledMap.put(entry.getKey(), entry.getValue());
            }
        }
        if (CollectionUtil.isEmpty(upFiledMap)) {
            return;
        }
        for (QpQuotationConfig config : quotationConfigs) {
            Map<String, Object> clone = ObjectUtil.cloneByStream(upFiledMap);
            clone.put(DataProcessing.PRIMARY_KEY_FIELD, config.getId());
            batchSuperUpdate.add(clone);
        }
        if (CollectionUtil.isNotEmpty(batchSuperUpdate)) {
            quotationConfigDataMapper.entity(QpQuotationConfig.class).batchSuperUpdate(batchSuperUpdate, true);
        }
    }

    /**
     * 校验参数不能为空
     *
     * @param param 入参
     */
    private void check(MetaDescriptionParam param) {
        if (Objects.isNull(param)) {
            CommonDataUtil.businessException(QuoteErrorCode.DATA_IS_EMPTY);
        }
    }

    /**
     * 校验参数不能为空
     *
     * @param param 入参
     */
    @SuppressWarnings("java:S3776")
    private void checkSave(MetaDescriptionParam param) {
        if (param.containsKey(QpQuotationConst.NAME)) {
            if (param.valIsNull(QpQuotationConst.NAME) || StringUtils.isBlank(param.getValByKey(QpQuotationConst.NAME).toString())) {
                CommonDataUtil.businessException(QuoteErrorCode.DATA_IS_EMPTY);
            }
            DataCondition<QpQuotation> condition = new DataCondition<>();
            if (param.containsKey(QpQuotationConst.QUOTATION_INFO_ID)) {
                condition.eq(QpQuotationConst.QUOTATION_INFO_ID, param.getData().get(QpQuotationConst.QUOTATION_INFO_ID).getVal());
            } else {
                if (null == param.getId() || param.getId() == 0) {
                    CommonDataUtil.businessException(QuoteErrorCode.DATA_ID_IS_EMPTY);
                }
                QpQuotation quote = quotationDataMapper.entity(QpQuotation.class).selectOne(param.getId(), true);
                if (null == quote) {
                    CommonDataUtil.businessException(QuoteErrorCode.OPERATION_EMPTY);
                }
                assert quote != null;
                condition.eq(QpQuotationConst.QUOTATION_INFO_ID, quote.getQuotationInfoId());
            }
            List<QpQuotation> quoteList = quotationDataMapper.entity(QpQuotation.class).select(condition, true);
            quoteList = quoteList.stream().filter(v -> !v.getId().equals(param.getId())).collect(Collectors.toList());
            for (QpQuotation quote : quoteList) {
                if (Objects.equals(quote.getName(), param.getData().get(QpQuotationConst.NAME).getVal())) {
                    CommonDataUtil.businessException(QuoteErrorCode.DATA_IS_ERROR, "方案组名称不能重复");
                }
            }
        }
        //  默认企业付费
        if (param.valIsNull(QpQuotationConst.CLIENT_TYPE)) {
            param.addData(QpQuotationConst.CLIENT_TYPE, 1);
        }
        // 如果是期缴的时候 缴费单位指定为月
        if (param.containsKey(QpQuoteConst.PAY_TYPE)) {
            if (!param.valIsNull(QpQuoteConst.PAY_TYPE) && param.getStringValByKey(QpQuoteConst.PAY_TYPE).equals(PayTypeEnum.REGULAR_PAYMENT.getCode())) {
                param.addData(QpQuoteConst.PAY_UNIT, PayUnitEnum.MONTH.getValue());
            }
        }
    }

    /**
     * @return 字段集合
     */
    private Map<String, ItemDto> defaultMap() {
        return metaService.getMapItemDtoByEntityKey(EntityKeyEnum.QP_QUOTATION.getVal());
    }


    public List<QpQuotation> queryByInfoId(List<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Lists.newArrayList();
        }
        DataCondition<QpQuotation> condition = new DataCondition<>();
        condition.in(EntityKeyEnum.QP_QUOTATION.getRelevanceFiled(), ids);
        List<QpQuotation> select = quotationDataMapper.entity(QpQuotation.class).select(condition, true);
        select.sort((o1, o2) -> {
            int i1 = ids.indexOf(o1.getQuotationInfoId());
            i1 = i1 == -1 ? Integer.MAX_VALUE : i1;
            int i2 = ids.indexOf(o2.getQuotationInfoId());
            i2 = i2 == -1 ? Integer.MAX_VALUE : i2;
            return i1 - i2;
        });
        return select;
    }
    public List<QpQuotation> updateCompanyId(List<QpQuotation> list, Long companyId,Long cooperationInfoId) {
        List<QpQuotation> quotations = new ArrayList<>();
        if (ObjectUtil.isNotEmpty(companyId)) {

            // 查询保司名称
            IgCompany igCompany = companyDataMapper.entity(IgCompany.class).selectOne(companyId, true);

            for (QpQuotation item : list) {
                QpQuotation qpQuotation = new QpQuotation();
                qpQuotation.setCompanyId(companyId);
                qpQuotation.setCompanyName(igCompany.getCompanyName());
                qpQuotation.setCooperationInfoId(cooperationInfoId);
                qpQuotation.setId(item.getId());
                quotations.add(qpQuotation);
            }
        }
        return quotations;
    }
}
