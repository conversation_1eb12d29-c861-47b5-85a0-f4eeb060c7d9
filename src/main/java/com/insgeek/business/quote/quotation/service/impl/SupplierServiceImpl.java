package com.insgeek.business.quote.quotation.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import com.alibaba.fastjson.JSON;
import com.insgeek.boot.commons.datetime.DateTimeUtil;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.boot.web.auth.dto.IdentityDto;
import com.insgeek.boot.web.context.IdentityContext;
import com.insgeek.boot.web.pojo.dto.CurrencyAmount;
import com.insgeek.boot.web.util.CurrencyAmountUtil;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.backend.Enums.QuoteTypeEnum;
import com.insgeek.business.quote.common.dao.mapper.QuotationInfoMapper;
import com.insgeek.business.quote.common.enums.dict.RenewFlagEnum;
import com.insgeek.business.quote.common.exception.BusinessException;
import com.insgeek.business.quote.feilv.commission.CommissionFeilvService;
import com.insgeek.business.quote.quotation.dto.*;
import com.insgeek.business.quote.quotation.enums.InsuranceCompanyEnum;
import com.insgeek.business.quote.quotation.enums.InsuranceEmployerCompanyEnum;
import com.insgeek.business.quote.quotation.enums.SupplierTypeEnum;
import com.insgeek.business.quote.quotation.service.QuotationManageAggService;
import com.insgeek.business.quote.quotation.service.QuotationPredictService;
import com.insgeek.business.quote.quotation.service.SupplierService;
import com.insgeek.business.quote.util.CalculateMedianUtils;
import com.insgeek.business.quote.util.CompanyUtil;
import com.insgeek.business.quote.util.RedisLockUtils;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.components.orm.model.querydsl.BQLQueryFactory;
import com.insgeek.protocol.business.policy.entity.*;
import com.insgeek.protocol.data.client.entity.*;
import com.insgeek.protocol.dataapp.bi.client.HungerCoefficientClient;
import com.insgeek.protocol.dataapp.bi.dto.HungerCoefficientDto;
import com.insgeek.protocol.insurance.entity.IgProductType;
import com.insgeek.protocol.insurance.entity.IgSpecial;
import com.insgeek.protocol.insurance.entity.QIgProductType;
import com.insgeek.protocol.insurance.entity.model.QIgSpecial;
import com.insgeek.protocol.platform.data.client.BQLClient;
import com.querydsl.core.types.dsl.Expressions;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorCompletionService;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Date: 2024-11-07  15:44
 * @Author: YuanSiYuan
 */
@Service("supplierServiceImpl")
@Slf4j
public class SupplierServiceImpl implements SupplierService {

    @Autowired
    BQLQueryFactory bqlQueryFactory;

    @Autowired
    DataMapper<IgPreferSupplier> igPreferSupplierDataMapper;

    @Autowired
    DataMapper<QpQuotationInfo> quotationInfoDataMapper;

    @Resource
    QuotationManageAggService quotationManageAggService;

    @Autowired
    QuotationPredictService quotationPredictService;

    @Autowired
    CommissionFeilvService commissionFeilvService;

    @Qualifier(value = "supplierNetCost")
    @Autowired
    private Executor threadPoolTaskExecutor;
    @Resource
    RedisLockUtils redisLockUtils;

    @Autowired
    DataMapper<QpQuotationHistoryCustomer> qpQuotationHistoryCustomerDataMapper;

    @Autowired
    BQLClient bqlClient;

    @Resource
    HungerCoefficientClient hungerCoefficientClient;

    @Resource
    QuotationInfoMapper quotationInfoMapper;
    /**
     * 匹配可承保供应商
     *
     * @param quotationInfoId 报价ID
     * @return
     */
    @Override
    public List<SupplierInfoDto> matchSupplier(Long quotationInfoId, List<String> matchConditions) {
        log.warn("询报价ID:{} 可承保供应商匹配开始", quotationInfoId);
        QQpQuotationInfo qQpQuotationInfo = QQpQuotationInfo.qp_quotation_info;
        QpQuotationInfo qpQuotationInfo = bqlQueryFactory.select(Expressions.stringPath("qp_quotation_info.*"))
                                                         .from(qQpQuotationInfo)
                                                         .where(qQpQuotationInfo.id.eq(quotationInfoId))
                                                         .findOne(QpQuotationInfo.class);
        if (qpQuotationInfo == null) {
            return new ArrayList<>();
        }
        QQpQuotation qpQuotation = QQpQuotation.qp_quotation;
        List<QpQuotation> quotationList = bqlQueryFactory.select(Expressions.stringPath("qp_quotation.*"))
                                                         .from(qpQuotation)
                                                         .where(qpQuotation.quotation_info_id.eq(quotationInfoId))
                                                         .findList(QpQuotation.class);
        if(CollectionUtils.isEmpty(quotationList)) {
            log.warn("询报价ID:{} 可承保供应商匹配结束，无匹配结果",quotationInfoId);
            return new ArrayList<>();
        }
        // 方案配置
        QQpQuotationConfig qpQuotationConfig = QQpQuotationConfig.qp_quotation_config;
        List<QpQuotationConfig> quotationConfigList = bqlQueryFactory.select(Expressions.stringPath("qp_quotation_config.*"))
                                                                     .from(qpQuotationConfig)
                                                                     .where(qpQuotationConfig.quotation_id.in(quotationList.stream()
                                                                                                                           .map(QpQuotation::getId)
                                                                                                                           .collect(Collectors.toList())))
                                                                     .findList(QpQuotationConfig.class);

        //查询责任
        QQpQuotationDuty qQpQuotationDuty = QQpQuotationDuty.qp_quotation_duty;
        QQpQuotationConfig qQpQuotationConfig = QQpQuotationConfig.qp_quotation_config;
        List<QpQuotationDuty> quotationDutyList = bqlQueryFactory.select(Expressions.stringPath("qp_quotation_duty.*"))
                                                                 .from(qQpQuotationInfo).leftJoin(qpQuotation)
                                                                 .on(qQpQuotationInfo.id.eq(qpQuotation.quotation_info_id))
                                                                 .leftJoin(qQpQuotationConfig)
                                                                 .on(qQpQuotationConfig.quotation_id.eq(qpQuotation.id))
                                                                 .leftJoin(qQpQuotationDuty)
                                                                 .on(qQpQuotationConfig.id.eq(qQpQuotationDuty.quotation_config_id))
                                                                 .where(qQpQuotationInfo.id.eq(quotationInfoId))
                                                                 .findList(QpQuotationDuty.class);
        // 查询责任 保额 信息
        List<QuotationDutyDto> quotationDutys = bqlQueryFactory.select(qpQuotation.id.as("quotation_id"), qpQuotationConfig.id.as("quotation_config_id"),qpQuotation.occupation.as("job_category"), Expressions.stringPath("qp_quotation_duty.*"))
                                                               .from(qQpQuotationInfo).leftJoin(qpQuotation)
                                                               .on(qQpQuotationInfo.id.eq(qpQuotation.quotation_info_id))
                                                               .leftJoin(qQpQuotationConfig)
                                                               .on(qQpQuotationConfig.quotation_id.eq(qpQuotation.id))
                                                               .leftJoin(qQpQuotationDuty)
                                                               .on(qQpQuotationConfig.id.eq(qQpQuotationDuty.quotation_config_id))
                                                               .where(qQpQuotationInfo.id.eq(quotationInfoId))
                                                               .findList(QuotationDutyDto.class);
        QIgProductType qIgProductType = QIgProductType.ig_product_type;
        List<IgProductType> igProductTypeList = bqlQueryFactory.select(Expressions.stringPath("ig_product_type.*"))
                                                               .from(qIgProductType).findList(IgProductType.class);
        Map<Long, String> igProductTypeMap = igProductTypeList.stream()
                                                              .collect(Collectors.toMap(IgProductType::getId, IgProductType::getType));

        QIgCompanyUnderwritingArea qIgCompanyUnderwritingArea = QIgCompanyUnderwritingArea.ig_company_underwriting_area;
        List<IgCompanyUnderwritingArea> igCompanyUnderwritingAreas = bqlQueryFactory.select(Expressions.stringPath("ig_company_underwriting_area.*"))
                                                                                    .from(qIgCompanyUnderwritingArea)
                                                                                    .findList(IgCompanyUnderwritingArea.class);
        Map<Long, List<IgCompanyUnderwritingArea>> igCompanyUnderwritingAreaMap = igCompanyUnderwritingAreas.stream()
                                                                                                            .collect(Collectors.groupingBy(IgCompanyUnderwritingArea::getCooperationId));


        List<Long> dutyIds = quotationDutyList.stream().map(QpQuotationDuty::getId).collect(Collectors.toList());
        List<Long> quotationIds = quotationList.stream().map(QpQuotation::getId).collect(Collectors.toList());
        List<Long> quotationConfigIds = quotationConfigList.stream().map(QpQuotationConfig::getId)
                                                           .collect(Collectors.toList());

        // 查询特约
        QIgSpecial qIgSpecial = QIgSpecial.ig_special;
        List<IgSpecial> igSpecialList = bqlQueryFactory.select(Expressions.stringPath("ig_special.*")).from(qIgSpecial)
                                                       .where(qIgSpecial.qp_quotation_id.in(quotationIds)
                                                                                        .or(qIgSpecial.qp_quotation_duty_id.in(dutyIds))
                                                                                        .or(qIgSpecial.qp_quotation_config_id.in(quotationConfigIds)))
                                                       .findList(IgSpecial.class);

        //  查询方案的责任的
        List<Long> specialTemplateIdList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(igSpecialList)) {
            specialTemplateIdList = igSpecialList.stream().map(IgSpecial::getSpecialTemplateId).distinct()
                                                 .collect(Collectors.toList());
        }
        List<String> payTypeList = quotationList.stream().map(QpQuotation::getPayType).distinct().collect(Collectors.toList());
        List<SupplierInfoDto> supplierInfo = getSupplierInfo(qpQuotationInfo);
        // 询报价的职业分类
        Map<Long, List<QuotationDutyDto>> quotationDutyMap = new HashMap<>();
        Map<Long, List<QuotationDutyDto>> quotationConfigDutyMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(quotationDutys)) {
            quotationDutyMap = quotationDutys.stream().collect(Collectors.groupingBy(QuotationDutyDto::getQuotationId));
            quotationConfigDutyMap=quotationDutys.stream().filter(quotationDutyDto -> quotationDutyDto.getQuotationConfigId()!=null).collect(Collectors.groupingBy(QuotationDutyDto::getQuotationConfigId));

        }

        List<SupplierInfoDto> supplierInfoDtos = new ArrayList<>();
        for (SupplierInfoDto supplierInfoDto : supplierInfo) {
            if (matchConditions == null || CollectionUtils.isEmpty(matchConditions)) {
                // 全条件匹配
                Boolean matchSupplier = matchSupplier(supplierInfoDto, qpQuotationInfo, payTypeList, quotationDutyList, quotationDutyMap, specialTemplateIdList, igProductTypeMap, igCompanyUnderwritingAreaMap, quotationConfigDutyMap);
                if (Boolean.FALSE.equals(matchSupplier)){
                    supplierInfoDtos.add(supplierInfoDto);
                }
            }
        }
        //单条件
        if (CollectionUtil.isNotEmpty(matchConditions)) {
            matchSuppleList(matchConditions, qpQuotationInfo, payTypeList, quotationDutyList, quotationDutyMap, specialTemplateIdList, supplierInfoDtos, supplierInfo, igProductTypeMap, igCompanyUnderwritingAreaMap, quotationConfigDutyMap);
        }
        return supplierInfoDtos;
    }

    private List<SupplierInfoDto> matchSuppleList(List<String> matchConditions, QpQuotationInfo qpQuotationInfo, List<String> payTypeList, List<QpQuotationDuty> quotationDutyList, Map<Long, List<QuotationDutyDto>> quotationDutyMap, List<Long> specialTemplateIdList, List<SupplierInfoDto> supplierInfoDtoList, List<SupplierInfoDto> supplierInfoDtos, Map<Long, String> igProductTypeMap, Map<Long, List<IgCompanyUnderwritingArea>> igCompanyUnderwritingAreaMap, Map<Long, List<QuotationDutyDto>> quotationConfigDutyMap) {

        //        return matchConditions.stream()
        //                .reduce(supplierInfoDtos, (list, condition) -> list.stream()
        //                        .filter(supplierInfo -> !matchSingleSupplier(supplierInfo, qpQuotationInfo, payTypeList, quotationDutyList, quotationDutyMap, specialTemplateIdList, condition, igProductTypeMap, igCompanyUnderwritingAreaMap))
        //                        .collect(Collectors.toList()), (list1, list2) -> Stream.concat(list1.stream(), list2.stream()).collect(Collectors.toList()));
        // 单条件匹配
        for (String conditions : matchConditions) {
            for (SupplierInfoDto supplierInfo : supplierInfoDtos) {
                // 单条件匹配
                Boolean matchSingleRes = matchSingleSupplier(supplierInfo, qpQuotationInfo, payTypeList, quotationDutyList, quotationDutyMap, specialTemplateIdList, conditions, igProductTypeMap, igCompanyUnderwritingAreaMap, quotationConfigDutyMap);
                if (!matchSingleRes) {
                    supplierInfoDtoList.add(supplierInfo);
                }
            }
        }
        return supplierInfoDtoList;
    }

    /**
     * 匹配供应商
     *
     * @param supplierInfoDto
     * @param qpQuotationInfo
     * @param payTypeList
     * @param quotationDutyList
     * @param quotationDutyMap
     * @param specialTemplateIdList
     */
    private Boolean matchSupplier(SupplierInfoDto supplierInfoDto, QpQuotationInfo qpQuotationInfo, List<String> payTypeList, List<QpQuotationDuty> quotationDutyList, Map<Long, List<QuotationDutyDto>> quotationDutyMap, List<Long> specialTemplateIdList,Map<Long, String> igProductTypeMap, Map<Long, List<IgCompanyUnderwritingArea>> igCompanyUnderwritingAreaMap, Map<Long, List<QuotationDutyDto>> quotationConfigDutyMap ) {
        log.warn("询报价ID:{}供应商匹配信息:{}",qpQuotationInfo.getId(),JacksonUtils.writeAsString(qpQuotationInfo));
        // 匹配条件
        Boolean  flg= Boolean.FALSE;
        // 责任 需要单独查询
        List<IgProductTerms> igProductTermsList = supplierInfoDto.getResponsibility();
        // 区域匹配
        if (Boolean.FALSE.equals(flg)){
           flg= matchArea(supplierInfoDto, qpQuotationInfo, igCompanyUnderwritingAreaMap);
           log.warn("询报价ID:{} 供应商Id:{} 匹配区域信息 匹配结果:{}",qpQuotationInfo.getId(), supplierInfoDto.getCooperationInfoId(),flg);
        }
        //  业务模式
        if (Boolean.FALSE.equals(flg)){
            flg=  matchBusinessMode(supplierInfoDto, qpQuotationInfo);
            log.warn("询报价ID:{}供应商Id:{} 匹配 业务模式 匹配结果:{}", qpQuotationInfo.getId(),supplierInfoDto.getCooperationInfoId(),flg);
        }
        // 缴费类型
        if (Boolean.FALSE.equals(flg)){
            flg=  matchPayType(qpQuotationInfo,supplierInfoDto, payTypeList);
            log.warn("询报价ID:{} 供应商Id:{} 匹配 缴费类型 匹配结果:{}", qpQuotationInfo.getId(),supplierInfoDto.getCooperationInfoId(),flg);
        }
        //  保障期间
        if (Boolean.FALSE.equals(flg)){
            flg= matchInsurancePeriod(supplierInfoDto, qpQuotationInfo);
            log.warn("询报价ID:{}供应商Id:{} 匹配 保障期间 匹配结果:{}", qpQuotationInfo.getId(),supplierInfoDto.getCooperationInfoId(),flg);
        }
        //  责任
        if (Boolean.FALSE.equals(flg)){
            flg=  matchResponsibility(qpQuotationInfo,supplierInfoDto,igProductTermsList, quotationDutyList, igProductTypeMap);
            log.warn("询报价ID:{} 供应商Id:{} 匹配 责任 匹配结果:{}",qpQuotationInfo.getId(), supplierInfoDto.getCooperationInfoId(),flg);
        }
        // 互斥
        if (Boolean.FALSE.equals(flg)){
            flg=  matchExclusiveDuty(qpQuotationInfo,supplierInfoDto, igProductTypeMap, quotationConfigDutyMap,quotationDutyList);
            log.warn("询报价ID:{} 供应商Id:{} 匹配 互斥 匹配结果:{}",qpQuotationInfo.getId(), supplierInfoDto.getCooperationInfoId(),flg);
        }
        //主副险匹配
        if (Boolean.FALSE.equals(flg)){
            flg=  matchPrimarySecondaryDuty(qpQuotationInfo,supplierInfoDto, igProductTypeMap,quotationConfigDutyMap);
            log.warn("询报价ID:{} 供应商Id:{} 匹配 主副险匹配 匹配结果:{}", qpQuotationInfo.getId(),supplierInfoDto.getCooperationInfoId(),flg);
        }
        // 保额
        if (Boolean.FALSE.equals(flg)){
            flg= matchMaxAmountV2(qpQuotationInfo,supplierInfoDto, quotationDutyMap, igProductTypeMap);
            log.warn("询报价ID:{} 供应商Id:{} 匹配 保额 匹配结果:{}", qpQuotationInfo.getId(),supplierInfoDto.getCooperationInfoId(),flg);
        }
        // 特约
        if (Boolean.FALSE.equals(flg)){
            flg= matchSpecialSupplier(qpQuotationInfo ,supplierInfoDto,specialTemplateIdList);
            log.warn("询报价ID:{}供应商Id:{} 匹配 特约 匹配结果:{}", qpQuotationInfo.getId(),supplierInfoDto.getCooperationInfoId(),flg);
        }
        log.warn("询报价ID:{} 供应商Id:{} 最终匹配结果 {}", qpQuotationInfo.getId(), supplierInfoDto.getCooperationInfoId(),flg);
        return flg;
    }


    /**
     * 匹配供应商
     *
     * @param supplierInfoDto
     * @param qpQuotationInfo
     * @param payTypeList
     * @param quotationDutyList
     * @param quotationDutyMap
     * @param specialTemplateIdList
     */
    private Boolean matchSingleSupplier(SupplierInfoDto supplierInfoDto, QpQuotationInfo qpQuotationInfo, List<String> payTypeList, List<QpQuotationDuty> quotationDutyList, Map<Long, List<QuotationDutyDto>> quotationDutyMap, List<Long> specialTemplateIdList, String condition, Map<Long, String> igProductTypeMap, Map<Long, List<IgCompanyUnderwritingArea>> igCompanyUnderwritingAreaMap, Map<Long, List<QuotationDutyDto>> quotationConfigDutyMap ) {
        // 注册地
        if ("0".equals(condition)) {
            List<IgCompanyUnderwritingArea> igCompanyUnderwritingAreas = igCompanyUnderwritingAreaMap.get(supplierInfoDto.getCooperationInfoId());
            if (CollectionUtil.isEmpty(igCompanyUnderwritingAreas)) {
                return Boolean.TRUE;
            }
            if (CollectionUtil.isNotEmpty(igCompanyUnderwritingAreaMap)) {
                List<IgCompanyUnderwritingArea> igCompanyUnderwritingAreaList = igCompanyUnderwritingAreas.stream()
                                                                                                          .filter(igCompanyUnderwritingArea -> {
                                                                                                              String province = igCompanyUnderwritingArea.getProvince();
                                                                                                              String city = igCompanyUnderwritingArea.getCity();
                                                                                                              String supplierProvince = qpQuotationInfo.getSupplierProvince();
                                                                                                              String supplierCity = qpQuotationInfo.getSupplierCity();
                                                                                                              log.warn("询报价归属地省:{} 市:{}", supplierProvince, supplierCity);
                                                                                                              log.warn("供应商省:{} 市:{}", province, city);
                                                                                                              if (supplierProvince == null) {
                                                                                                                  return Boolean.FALSE;
                                                                                                              } else if (Objects.equals(province, supplierProvince)) {
                                                                                                                  return Boolean.TRUE;
                                                                                                              } else if ("99999".equals(province)) {
                                                                                                                  //港 澳 台
                                                                                                                  List<String> pprovinceList = Arrays.asList("82", "71", "81");
                                                                                                                  if (pprovinceList.contains(supplierProvince)) {
                                                                                                                      return Boolean.FALSE;
                                                                                                                  }
                                                                                                                  return Boolean.TRUE;
                                                                                                              } else if (Objects.equals(city, supplierCity)) {
                                                                                                                  return Boolean.TRUE;
                                                                                                              }
                                                                                                              return Boolean.FALSE;
                                                                                                          })
                                                                                                          .collect(Collectors.toList());

                if (CollectionUtil.isNotEmpty(igCompanyUnderwritingAreaList)) {
                    return Boolean.FALSE;
                } else {
                    return Boolean.TRUE;
                }
            }
        }
        if ("1".equals(condition)) {
            //  业务模式
            if (supplierInfoDto.getBusinessMode() == null) {
                return Boolean.TRUE;
            }
            if (!CollUtil.containsAny(supplierInfoDto.getBusinessMode(), Collections.singletonList(String.valueOf(qpQuotationInfo.getBusinessModel() - 1)))) {
                return Boolean.TRUE;
            }
        }
        if ("2".equals(condition)) {
//            //缴费类型
//            if (supplierInfoDto.getPaymentType() == null || !CollectionUtil.containsAll(supplierInfoDto.getPaymentType(), payTypeList)) {
//                return Boolean.TRUE;
//            }
            if (supplierInfoDto.getPaymentType()==null){
                return Boolean.TRUE;
            }

            List<String> paymentType = new ArrayList<>();
            paymentType= supplierInfoDto.getPaymentType().stream().map(s -> "0".equals(s)?"1":s).distinct().collect(Collectors.toList());
            log.warn("供应商Id:{} 供应商时缴费类型:{}  询报价缴费类型:{}", supplierInfoDto.getCooperationInfoId(), paymentType, payTypeList);
            if (CollectionUtil.containsAll(paymentType, payTypeList)){
                return Boolean.FALSE;
            }
            return Boolean.TRUE;
        }
        if ("3".equals(condition)) {
            //保障期间  保障开始日期 保证结束日期
            return matchInsurancePeriod(supplierInfoDto, qpQuotationInfo);
        }
        // 责任 需要单独查询
        List<IgProductTerms> igProductTermsList = supplierInfoDto.getResponsibility();
        if ("4".equals(condition)) {
            return  matchResponsibility(qpQuotationInfo,supplierInfoDto,igProductTermsList, quotationDutyList, igProductTypeMap);
        }
        //互斥责任类型
        // 供应商的互斥责任包含 报价的互斥责任
        if ("5".equals(condition)) {
                List<String> typeCodes = supplierInfoDto.getIgProductTermsDtos().stream()
                                                  .map(igProductTermsDto -> igProductTypeMap.get(igProductTermsDto.getProductTypeId().get(0)))
                                                  .collect(Collectors.toList());
               List<String> dutyCodeList = quotationDutyList.stream().map(QpQuotationDuty::getBusinessType).distinct()
                                                         .collect(Collectors.toList());
               if (!CollectionUtil.containsAll(typeCodes, dutyCodeList)){
                   return Boolean.TRUE;
               }
                //  报价的方案配置循环
                return  !quotationConfigDutyMap.entrySet().stream().allMatch(entry -> {
                    List<String> quotaDutyIds = entry.getValue().stream().map(QuotationDutyDto::getBusinessType)
                                                     .collect(Collectors.toList());
                    // 询报价的对应的责任
                    List<IgProductTerms> supplierInfoList = igProductTermsList.stream()
                                                                              .filter(v -> quotaDutyIds.contains(igProductTypeMap.get(v.getProductTypeId().get(0))))
                                                                              .collect(Collectors.toList());
                    if (CollectionUtil.isEmpty(supplierInfoList)|| quotaDutyIds.size()!=supplierInfoList.size()){
                        return Boolean.TRUE;
                    }
                    // 询报价对应的供应商的互斥责任
                    List<String> dutyIds = supplierInfoList.stream().map(IgProductTerms::getExcluseDutyType).flatMap(List::stream)
                                                           .map(typeId -> igProductTypeMap.get(typeId))
                                                           .collect(Collectors.toList());
                    // 互斥的责任不供应商的责任列表
                    if (CollectionUtil.isEmpty(dutyIds) || !CollectionUtil.containsAll(typeCodes, quotaDutyIds)){
                        return Boolean.TRUE;
                    }
                    if (CollectionUtil.contains(quotaDutyIds,dutyIds)){
                        return Boolean.TRUE;
                    }
                    if (!CollectionUtil.containsAny(dutyIds,quotaDutyIds)){
                        return Boolean.TRUE;
                    }
                    return Boolean.FALSE;
                });
//            for (Map.Entry<Long, List<QuotationDutyDto>> entry : quotationConfigDutyMap.entrySet()) {
//                List<String> quotaDutyIds = entry.getValue().stream().map(QuotationDutyDto::getBusinessType)
//                                            .collect(Collectors.toList());
//                List<IgProductTerms> supplierInfoList = igProductTermsList.stream()
//                                                                 .filter(v -> quotaDutyIds.contains(igProductTypeMap.get(v.getProductTypeId().get(0))))
//                                                                 .collect(Collectors.toList());
//                if (!CollectionUtil.containsAll(typeCodes, quotaDutyIds)){
//                    return Boolean.TRUE;
//                }
//                if (CollectionUtil.isEmpty(supplierInfoList)) {
//                    return Boolean.TRUE;
//                }
//                List<String> dutyIds = supplierInfoList.stream().map(IgProductTerms::getExcluseDutyType).flatMap(List::stream)
//                                            .map(typeId -> igProductTypeMap.get(typeId))
//                                            .collect(Collectors.toList());
//                // 互斥的责任不供应商的责任列表
//                if (!CollectionUtil.containsAll(dutyIds,quotaDutyIds)){
//                    return Boolean.FALSE;
//                }
//                if (CollectionUtil.containsAll(quotaDutyIds,dutyIds)){
//                    return Boolean.FALSE;
//                }
//                return Boolean.TRUE;
//            }
        }
        if ("6".equals(condition)) {
            // 保额
            return matchMaxAmountV2(qpQuotationInfo,supplierInfoDto, quotationDutyMap, igProductTypeMap);

        }
        if ("7".equals(condition)) {
            List<IgCompanyCooperationSpecial> specialList = supplierInfoDto.getSpecialList();
            if (CollectionUtil.isEmpty(specialList)) {
                return Boolean.TRUE;
            }
            List<Long> specialTemplates = supplierInfoDto.getSpecialList().stream()
                                                         .map(IgCompanyCooperationSpecial::getSpecialTemplateId)
                                                         .collect(Collectors.toList());
            // 特约
            return !CollUtil.containsAll(specialTemplates, specialTemplateIdList);
        }
        if ("8".equals(condition)){
            //供应商的 附加险对应的责任主险
            // 供应商的附加险 包含 报价的附加险 并且主险也包含， 通过
           return   matchPrimarySecondaryDuty(qpQuotationInfo,supplierInfoDto, igProductTypeMap,quotationConfigDutyMap);
        }
        // 1.循环 方案配置的的责任
        // 2. 找到供应商责任对应附加险的主险
        // 3. 判断  主险责任是否在 方案配置中
        //   不存在 跳过 该供应商
        if ("9".equals(condition)){
            List<String> typeCodes = supplierInfoDto.getIgProductTermsDtos().stream()
                                                    .map(igProductTermsDto -> igProductTypeMap.get(igProductTermsDto.getProductTypeId().get(0)))
                                                    .collect(Collectors.toList());
            return quotationConfigDutyMap.entrySet().stream().anyMatch(entry -> {
                    List<String> quotaDutyIds = entry.getValue().stream().map(QuotationDutyDto::getBusinessType)
                                                     .collect(Collectors.toList());
                    // 供应商责任不完全包含询报价责任
                   if (!CollectionUtil.containsAll(typeCodes, quotaDutyIds)) {
                     return Boolean.TRUE;
                   }
                  return quotaDutyIds.stream().anyMatch(dutyType -> {
                      //  供应商 责任 主险
                      List<IgProductTerms> supplierInfoList = igProductTermsList.stream()
                                                                                .filter(v -> dutyType.equals(igProductTypeMap.get(v.getProductTypeId().get(0))))
                                                                                .collect(Collectors.toList());
                      List<String> dutyIds = supplierInfoList.stream().map(IgProductTerms::getMainInsurance)
                                                             .flatMap(List::stream)
                                                             .map(typeId -> igProductTypeMap.get(typeId))
                                                             .collect(Collectors.toList());
                      if (!CollectionUtil.isEmpty(dutyIds)) {
                          if (CollectionUtil.containsAny(dutyIds,quotaDutyIds)){
                              return Boolean.FALSE;
                          }
                          return !CollectionUtil.containsAll(quotaDutyIds, dutyIds);
                      }
                      return Boolean.FALSE;
                  });
            });
        }
        return Boolean.FALSE;
    }


    /**
     * 匹配特约
     */
    public Boolean matchSpecialSupplier( QpQuotationInfo qpQuotationInfo ,SupplierInfoDto supplierInfoDto,List<Long> specialTemplateIdList) {
        List<IgCompanyCooperationSpecial> specialList = supplierInfoDto.getSpecialList();
        if (CollectionUtil.isEmpty(specialList)) {
            return Boolean.TRUE;
        }
        List<Long> specialTemplates = supplierInfoDto.getSpecialList().stream()
                                                     .map(IgCompanyCooperationSpecial::getSpecialTemplateId)
                                                     .collect(Collectors.toList());

        Collection<Long> disjunction = CollectionUtil.subtract(specialTemplateIdList,specialList.stream()
                                                                             .map(IgCompanyCooperationSpecial::getSpecialTemplateId)
                                                                             .collect(Collectors.toList()) );
        log.warn("询报价ID:{} 特约匹配 供应商ID:{}  差集信息:{}",qpQuotationInfo.getId(),supplierInfoDto.getCooperationInfoId(),disjunction);
        log.warn("询报价ID:{} 开始触发 特约匹配 供应商ID:{}, 供应商特约:{} ,询报价特约:{}",qpQuotationInfo.getId(),supplierInfoDto.getCooperationInfoId(), JacksonUtils.writeAsString(specialTemplates), specialList.stream().map(IgCompanyCooperationSpecial::getSpecialTemplateId).collect(Collectors.toList()));
        // 特约
        return !CollUtil.containsAll(specialTemplates, specialTemplateIdList);
    }

    /**
     * 匹配互斥责任
     */
    private Boolean matchExclusiveDuty( QpQuotationInfo qpQuotationInfo ,SupplierInfoDto supplierInfoDto, Map<Long, String> igProductTypeMap, Map<Long, List<QuotationDutyDto>> quotationConfigDutyMap,List<QpQuotationDuty> quotationDutyList) {
        List<String> typeCodes = supplierInfoDto.getIgProductTermsDtos().stream()
                                                .map(igProductTermsDto -> igProductTypeMap.get(igProductTermsDto.getProductTypeId().get(0)))
                                                .collect(Collectors.toList());
        List<String> dutyCodeList = quotationDutyList.stream().map(QpQuotationDuty::getBusinessType).distinct()
                                                     .collect(Collectors.toList());
        if (!CollectionUtil.containsAll(typeCodes, dutyCodeList)){
            return Boolean.TRUE;
        }
        List<IgProductTerms> igProductTermsList = supplierInfoDto.getResponsibility();
        if (CollectionUtil.isEmpty(igProductTermsList)){
            return Boolean.TRUE;
        }
        //  报价的方案配置循环
        return  !quotationConfigDutyMap.entrySet().stream().allMatch(entry -> {
            List<String> quotaDutyIds = entry.getValue().stream().map(QuotationDutyDto::getBusinessType)
                                             .collect(Collectors.toList());
            // 询报价的对应的责任
            List<IgProductTerms> supplierInfoList = igProductTermsList.stream()
                                                                      .filter(v -> quotaDutyIds.contains(igProductTypeMap.get(v.getProductTypeId().get(0))))
                                                                      .collect(Collectors.toList());
            if (CollectionUtil.isEmpty(supplierInfoList)|| quotaDutyIds.size()!=supplierInfoList.size()){
                return Boolean.TRUE;
            }
            // 询报价对应的供应商的互斥责任
            List<String> dutyIds = supplierInfoList.stream().map(IgProductTerms::getExcluseDutyType).flatMap(List::stream)
                                                   .map(typeId -> igProductTypeMap.get(typeId))
                                                   .collect(Collectors.toList());
            log.warn("询报价ID:{} 供应商Id:{} , 供应商的互斥责任:{}, 询报价的责任:{}",qpQuotationInfo.getId(),supplierInfoDto.getCooperationInfoId(), dutyIds, quotaDutyIds);
            // 互斥的责任不供应商的责任列表
            if (CollectionUtil.isEmpty(dutyIds) || !CollectionUtil.containsAll(typeCodes, quotaDutyIds)){
                return Boolean.TRUE;
            }
            if (CollectionUtil.contains(quotaDutyIds,dutyIds)){
                return Boolean.TRUE;
            }
            if (!CollectionUtil.containsAny(dutyIds,quotaDutyIds)){
                return Boolean.TRUE;
            }
            return Boolean.FALSE;
        });
    }

    /**
     * 缴费类型
     */
    private Boolean matchPayType( QpQuotationInfo qpQuotationInfo,SupplierInfoDto supplierInfoDto, List<String> payTypeList) {
        //缴费类型
        if (supplierInfoDto.getPaymentType()==null){
            return Boolean.TRUE;
        }
        List<String> paymentType = new ArrayList<>();
        paymentType= supplierInfoDto.getPaymentType().stream().map(s -> {
                    if ( "0".equals(s)) {
                        return "1";
                    }else {
                        return "0";
                    }
                }
        ).distinct().collect(Collectors.toList());
        log.warn("询报价ID:{} 供应商Id:{} 供应商时缴费类型:{}  询报价缴费类型:{}",qpQuotationInfo.getId(),supplierInfoDto.getCooperationInfoId(), paymentType, payTypeList);
        if (CollectionUtil.containsAll(paymentType, payTypeList)){
            return Boolean.FALSE;
        }
        return Boolean.TRUE;

    }


    /**
     * 归属地
     * @param supplierInfoDto
     * @param qpQuotationInfo
     * @param igCompanyUnderwritingAreaMap
     * @return
     */
    private Boolean matchArea(SupplierInfoDto supplierInfoDto,QpQuotationInfo qpQuotationInfo ,Map<Long, List<IgCompanyUnderwritingArea>> igCompanyUnderwritingAreaMap) {
        List<IgCompanyUnderwritingArea> igCompanyUnderwritingAreas = igCompanyUnderwritingAreaMap.get(supplierInfoDto.getCooperationInfoId());
        if (CollectionUtil.isEmpty(igCompanyUnderwritingAreas)) {
            return Boolean.TRUE;
        }
        if (CollectionUtil.isNotEmpty(igCompanyUnderwritingAreaMap)) {
            List<IgCompanyUnderwritingArea> igCompanyUnderwritingAreaList = igCompanyUnderwritingAreas.stream()
                                                                                                      .filter(igCompanyUnderwritingArea -> {
                                                                                                          String province = igCompanyUnderwritingArea.getProvince();
                                                                                                          String city = igCompanyUnderwritingArea.getCity();
                                                                                                          String supplierProvince = qpQuotationInfo.getSupplierProvince();
                                                                                                          String supplierCity = qpQuotationInfo.getSupplierCity();
                                                                                                          log.warn("询报价归属地省:{} 市:{}", supplierProvince, supplierCity);
                                                                                                          log.warn("供应商省:{} 市:{}", province, city);
                                                                                                          if (supplierProvince == null) {
                                                                                                              return Boolean.FALSE;
                                                                                                          } else if (Objects.equals(province, supplierProvince)) {
                                                                                                              return Boolean.TRUE;
                                                                                                          } else if ("99999".equals(province)) {
                                                                                                              //港 澳 台
                                                                                                              List<String> pprovinceList = Arrays.asList("82", "71", "81");
                                                                                                              if (pprovinceList.contains(supplierProvince)) {
                                                                                                                  return Boolean.FALSE;
                                                                                                              }
                                                                                                              return Boolean.TRUE;
                                                                                                          } else if (Objects.equals(city, supplierCity)) {
                                                                                                              return Boolean.TRUE;
                                                                                                          }
                                                                                                          return Boolean.FALSE;
                                                                                                      })
                                                                                                      .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(igCompanyUnderwritingAreaList)) {
                return Boolean.FALSE;
            } else {
                return Boolean.TRUE;
            }
        }
        return Boolean.TRUE;
    }



    /**
     *  业务模式
     */
    private Boolean matchBusinessMode(SupplierInfoDto supplierInfoDto,QpQuotationInfo qpQuotationInfo) {
        //  业务模式
        if (supplierInfoDto.getBusinessMode() == null) {
            return Boolean.TRUE;
        }
        if (!CollUtil.containsAny(supplierInfoDto.getBusinessMode(), Collections.singletonList(String.valueOf(qpQuotationInfo.getBusinessModel() - 1)))) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    /***
     * 主副险匹配
     * @param supplierInfoDto
     * @param igProductTypeMap
     * @return
     */
    private Boolean matchPrimarySecondaryDuty( QpQuotationInfo qpQuotationInfo,SupplierInfoDto supplierInfoDto,Map<Long, String> igProductTypeMap,Map<Long, List<QuotationDutyDto>> quotationConfigDutyMap) {
        List<String> typeCodes = supplierInfoDto.getIgProductTermsDtos().stream()
                                                .map(igProductTermsDto -> igProductTypeMap.get(igProductTermsDto.getProductTypeId().get(0)))
                                                .collect(Collectors.toList());
        List<IgProductTermsDto> igProductTermsList = supplierInfoDto.getIgProductTermsDtos();
        return quotationConfigDutyMap.entrySet().stream().anyMatch(entry -> {
            List<String> quotaDutyIds = entry.getValue().stream().map(QuotationDutyDto::getBusinessType).collect(Collectors.toList());
            // 供应商责任不完全包含询报价责任
            if (!CollectionUtil.containsAll(typeCodes, quotaDutyIds)) {
                return Boolean.TRUE;
            }
            return quotaDutyIds.stream().anyMatch(dutyType -> {
                //  供应商 责任 主险
                List<IgProductTerms> supplierInfoList = igProductTermsList.stream()
                                                                          .filter(v -> dutyType.equals(igProductTypeMap.get(v.getProductTypeId().get(0))))
                                                                          .collect(Collectors.toList());
                List<String> dutyIds = supplierInfoList.stream().map(IgProductTerms::getMainInsurance)
                                                       .flatMap(List::stream)
                                                       .map(igProductTypeMap::get)
                                                       .collect(Collectors.toList());
                if (!CollectionUtil.isEmpty(dutyIds)) {
                    boolean result = quotaDutyIds.stream().noneMatch(dutyIds::contains);
                    if (result){
                        log.warn("询报价ID:{} 供应商ID:{},询报价的责任:{} 供应商的匹配的主副险责任:{} 主副险匹配结果{}",qpQuotationInfo.getId(),supplierInfoDto.getCooperationInfoId(),dutyType,dutyIds, true);
                    }
                    return result;
                }
                return Boolean.FALSE;
            });
        });
    }




    /**
     * 最高保额匹配
     */
    public Boolean matchMaxAmount(SupplierInfoDto supplierInfoDto, Map<Long, List<QuotationDutyDto>> quotationDutyMap, Map<Long, String> igProductTypeMap) {
        // 计算笛卡尔积的结果
        List<CooperateCategoryDto> cooperateCategoryList = supplierInfoDto.getIgProductTermsDtos()
                                                                          .stream().flatMap(v -> (ObjectUtils.isEmpty(v.getIgProductTermsExtraList()) ? CompanyUtil.defaultProductTermsExtra() : v.getIgProductTermsExtraList())
                        .stream()
                        .flatMap(v1 -> v1.getCooperateCategory().stream().map(v2 -> {
                            CooperateCategoryDto ccd = new CooperateCategoryDto();
                            ccd.setMaxAmount(ObjectUtils.isEmpty(v1.getMaxCoverageLimit()) ? CurrencyAmount.valueOf(Long.MAX_VALUE, IdentityContext.getBusinessCurrency()) : v1.getMaxCoverageLimit());
                            ccd.setCategoryType(v2);
                            ccd.setResponsibilityType(igProductTypeMap.get(v.getProductTypeId().get(0)));
                            return ccd;
                        }).collect(Collectors.toList()).stream())
                        .collect(Collectors.toList())
                        .stream())
                                                                          .collect(Collectors.toList());

        // 按照责任+职业类别分组求最大
        Map<String, List<CooperateCategoryDto>> productTypeMap = cooperateCategoryList.stream()
                                                                                      .collect(
                                                                                              Collectors.toMap(
                                                                                                      v -> v.getResponsibilityType() + "-" + v.getCategoryType(),
                                                                                                      v -> v,
                                                                                                      (v1, v2) -> v1.getMaxAmount().compareTo(v2.getMaxAmount()) < 0 ? v1 : v2
                                                                                              )
                                                                                      ).values().stream().collect(Collectors.groupingBy(CooperateCategoryDto::getCategoryType));

        log.warn("供应商ID:{}，供应商最高保额信息:{}", supplierInfoDto.getCooperationInfoId(), JacksonUtils.writeAsString(productTypeMap));
        // 职业类别
        for (Map.Entry<Long, List<QuotationDutyDto>> entry : quotationDutyMap.entrySet()) {
            List<QuotationDutyDto> value = entry.getValue();
            log.warn("供应商ID:{}，询报价最高保额信息:{}", supplierInfoDto.getCooperationInfoId(), JacksonUtils.writeAsString(value));
            //职业类别
            String jobCategory = value.get(0).getJobCategory();
            if (productTypeMap.containsKey(jobCategory)) {
                // 获取当前报价组的责任类型
                List<String> quotaBusinessTypeList = value.stream().distinct().map(QuotationDutyDto::getBusinessType)
                                                          .collect(Collectors.toList());
                // 保司是否包含报价组责任
                boolean isContains = productTypeMap.get(jobCategory).stream().map(CooperateCategoryDto::getResponsibilityType)
                                                   .collect(Collectors.toSet())
                                                   .containsAll(quotaBusinessTypeList);
                // 供应商的职业类别
                if (isContains) {
                    // 最高保额
                    List<CooperateCategoryDto> igProductTermsDtoList = productTypeMap.get(jobCategory);
                    Map<String, CooperateCategoryDto> collect = igProductTermsDtoList.stream()
                                                                                     .collect(Collectors.toMap(CooperateCategoryDto::getResponsibilityType, v -> v, (v1, v2) -> v1));
                    return value.stream().anyMatch(v -> {
                        CooperateCategoryDto cooperateCategoryDto = collect.get(v.getBusinessType());
                        if (cooperateCategoryDto==null){
                            return Boolean.TRUE;
                        }
                        // 如果没配置限额，相当于不限制限额
                        return cooperateCategoryDto.getMaxAmount().getAmount().compareTo(v.getTotalAmount().getAmount()) < 0;
                    });
                } else {
                    return Boolean.TRUE;
                }
            } else {
                return Boolean.TRUE;
            }
        }
        return Boolean.TRUE;
    }



    /**
     * 最高保额匹配
     */
    public Boolean matchMaxAmountV2( QpQuotationInfo qpQuotationInfo,SupplierInfoDto supplierInfoDto, Map<Long, List<QuotationDutyDto>> quotationDutyMap, Map<Long, String> igProductTypeMap) {
        // 计算笛卡尔积的结果
        List<CooperateCategoryDto> cooperateCategoryList = supplierInfoDto.getIgProductTermsDtos()
                                                                          .stream().flatMap(v -> (ObjectUtils.isEmpty(v.getIgProductTermsExtraList()) ? CompanyUtil.defaultProductTermsExtra() : v.getIgProductTermsExtraList())
                        .stream()
                        .flatMap(v1 -> v1.getCooperateCategory().stream().map(v2 -> {
                            CooperateCategoryDto ccd = new CooperateCategoryDto();
                            ccd.setMaxAmount(ObjectUtils.isEmpty(v1.getMaxCoverageLimit()) ? CurrencyAmount.valueOf(Long.MAX_VALUE, IdentityContext.getBusinessCurrency()) : v1.getMaxCoverageLimit());
                            ccd.setCategoryType(v2);
                            ccd.setResponsibilityType(igProductTypeMap.get(v.getProductTypeId().get(0)));
                            return ccd;
                        }).collect(Collectors.toList()).stream())
                        .collect(Collectors.toList())
                        .stream())
                                                                          .collect(Collectors.toList());

        // 按照责任+职业类别分组求最大
        Map<String, List<CooperateCategoryDto>> productTypeMap = cooperateCategoryList.stream()
                                                                                      .collect(
                                                                                              Collectors.toMap(
                                                                                                      v -> v.getResponsibilityType() + "-" + v.getCategoryType(),
                                                                                                      v -> v,
                                                                                                      (v1, v2) -> v1.getMaxAmount().compareTo(v2.getMaxAmount()) < 0 ? v1 : v2
                                                                                              )
                                                                                      ).values().stream().collect(Collectors.groupingBy(CooperateCategoryDto::getCategoryType));

        log.warn("询报价ID:{} 供应商ID:{}，供应商最高保额信息:{}", qpQuotationInfo.getId(),supplierInfoDto.getCooperationInfoId(), JacksonUtils.writeAsString(productTypeMap));
        // 职业类别
        return  quotationDutyMap.entrySet().stream().anyMatch(entry -> {
                    List<QuotationDutyDto> value = entry.getValue();
                    log.warn("询报价ID:{} 供应商ID:{}，询报价最高保额信息:{}",qpQuotationInfo.getId(), supplierInfoDto.getCooperationInfoId(), JacksonUtils.writeAsString(value));
                    //职业类别
                    String jobCategory = value.get(0).getJobCategory();
                    if (productTypeMap.containsKey(jobCategory)) {
                        // 获取当前报价组的责任类型
                        List<String> quotaBusinessTypeList = value.stream().distinct().map(QuotationDutyDto::getBusinessType)
                                                                  .collect(Collectors.toList());
                        // 保司是否包含报价组责任
                        boolean isContains = productTypeMap.get(jobCategory).stream()
                                                           .map(CooperateCategoryDto::getResponsibilityType)
                                                           .collect(Collectors.toSet()).containsAll(quotaBusinessTypeList);
                        // 供应商的职业类别
                        if (isContains) {
                            // 最高保额
                            List<CooperateCategoryDto> igProductTermsDtoList = productTypeMap.get(jobCategory);
                            Map<String, CooperateCategoryDto> collect = igProductTermsDtoList.stream()
                                                                                             .collect(Collectors.toMap(CooperateCategoryDto::getResponsibilityType, v -> v, (v1, v2) -> v1));
                            return value.stream().anyMatch(v -> {
                                CooperateCategoryDto cooperateCategoryDto = collect.get(v.getBusinessType());
                                if (cooperateCategoryDto != null) {
                                    // 如果没配置限额，相当于不限制限额
                                    if (cooperateCategoryDto.getMaxAmount()!=null && v.getTotalAmount()!=null){
                                        boolean res = cooperateCategoryDto.getMaxAmount().getAmount().compareTo(v.getTotalAmount().getAmount()) < 0;
                                        if (res){
                                            log.warn("询报价ID:{} 供应商ID:{}，供应商的最高保额:{} 询报价的最高保额:{} 最高保额匹配结果:{}", qpQuotationInfo.getId(), supplierInfoDto.getCooperationInfoId(), JacksonUtils.writeAsString(cooperateCategoryDto),JacksonUtils.writeAsString(v),res);
                                        }
                                        return res;
                                    }
                                }
                                return Boolean.FALSE;
                            });
                        }
                    }
                    return Boolean.FALSE;
                }
        );
    }



    /**
     * 责任数据匹配
     */
    public Boolean matchResponsibility( QpQuotationInfo qpQuotationInfo,SupplierInfoDto supplierInfoDto,List<IgProductTerms> responsibility, List<QpQuotationDuty> quotationDutyList, Map<Long, String> igProductTypeMap) {
        if (CollectionUtils.isEmpty(responsibility) || CollectionUtils.isEmpty(quotationDutyList)) {
            return Boolean.FALSE;
        }
        // 责任类型
        List<String> dutyTypeList = responsibility.stream().map(IgProductTerms::getProductTypeId).flatMap(List::stream)
                                                  .map(igProductTypeMap::get)
                                                  .distinct().collect(Collectors.toList());
        List<String> dutyCodeList = quotationDutyList.stream().map(QpQuotationDuty::getBusinessType).distinct()
                                                     .collect(Collectors.toList());
        log.warn("询报价ID:{} 供应商ID:{}，供应商责任类型:{}，报价责任类型:{}", qpQuotationInfo.getId(),  supplierInfoDto.getCooperationInfoId(),dutyTypeList, dutyCodeList);
        return !CollectionUtil.containsAll(dutyTypeList, dutyCodeList);
    }


    /**
     * 保障期间匹配
     * // 4 一年以下的任意时间
     * // 3 1天到1年的任意天数
     * // 2 1个月到1年的任意天数
     * // 1 1年
     */
    public Boolean matchInsurancePeriod(SupplierInfoDto supplierInfoDto, QpQuotationInfo qpQuotationInfo) {
        List<String> insurancePeriodList = supplierInfoDto.getInsurancePeriod();
        //保障期间  保障开始日期 保证结束日期
        ZonedDateTime guaranteeEndDate = qpQuotationInfo.getGuaranteeEndDate();
        ZonedDateTime guaranteeStartDate = qpQuotationInfo.getGuaranteeStartDate();
        long betweenDays = DateTimeUtil.betweenDays(guaranteeStartDate, guaranteeEndDate, Boolean.TRUE);
        if (CollectionUtil.isEmpty(insurancePeriodList)) {
            return Boolean.TRUE;
        }
        log.warn("供应商ID:{}, 供应商保障期间:{},询报价的保障期间:{}", supplierInfoDto.getCooperationInfoId(),insurancePeriodList, betweenDays);
        for (String insurancePeriod : insurancePeriodList) {
            if ("4".equals(insurancePeriod)) {
                if (betweenDays <= 366) {
                    return Boolean.FALSE;
                }
            }
            if ("3".equals(insurancePeriod)) {
                if (betweenDays >= 1 && betweenDays <= 366) {
                    return Boolean.FALSE;
                }
            }
            if ("2".equals(insurancePeriod)) {
                if (betweenDays >= 28 && betweenDays <= 366) {
                    return Boolean.FALSE;
                }
            }
            if ("1".equals(insurancePeriod)) {
                if (betweenDays == 365 || betweenDays == 366 ||betweenDays == 364) {
                    return Boolean.FALSE;
                }
            }
        }
        return Boolean.TRUE;
    }

    /**
     * 查询供应商信息
     */
    public List<SupplierInfoDto> getSupplierInfo(QpQuotationInfo qpQuotationInfo) {
        List<SupplierInfoDto> supplierInfoList = new ArrayList<>();
        QIgCompanyCooperationInfo qIgCompanyCooperationInfo = QIgCompanyCooperationInfo.ig_company_cooperation_info;

        log.warn("询报价ID:{} 供应商匹配规则:{}", qpQuotationInfo.getId(), qpQuotationInfo.getQuoteType() );
        List<IgCompanyCooperationInfo> igCompanyCooperationInfos = bqlQueryFactory.select(Expressions.stringPath("ig_company_cooperation_info.*"))
                                                                                  .from(qIgCompanyCooperationInfo)
                                                                                  .where(qIgCompanyCooperationInfo.quote_status.eq("1"))
                                                                                  .findList(IgCompanyCooperationInfo.class);


        Integer quoteType = qpQuotationInfo.getQuoteType();
        SupplierTypeEnum enumByCode = SupplierTypeEnum.getEnumByCode(String.valueOf(quoteType));
        log.warn("询报价ID:{} 供应商匹配数据:{}",qpQuotationInfo.getId(), JacksonUtils.writeAsString(igCompanyCooperationInfos));
        if (enumByCode!=null && CollectionUtil.isNotEmpty(igCompanyCooperationInfos)){
            if (SupplierTypeEnum.EMPLOYERS_LIABILITY.getQuotationCode().equals(String.valueOf(quoteType))){
                igCompanyCooperationInfos = igCompanyCooperationInfos.stream().filter(v -> v.getProductCategory() !=null && v.getProductCategory().contains(SupplierTypeEnum.EMPLOYERS_LIABILITY.getCode()))
                                                                     .collect(Collectors.toList());
            }
            if (SupplierTypeEnum.PURE_ACCIDENT.getQuotationCode().equals(String.valueOf(quoteType))){
               igCompanyCooperationInfos = igCompanyCooperationInfos.stream().filter(v -> v.getProductCategory() !=null && v.getProductCategory().contains(SupplierTypeEnum.PURE_ACCIDENT.getCode()) || v.getProductCategory().contains(SupplierTypeEnum.GROUP_MEDICAL.getCode()))
                                                                     .collect(Collectors.toList());
            }
            if (SupplierTypeEnum.GROUP_MEDICAL.getQuotationCode().equals(String.valueOf(quoteType))){
               igCompanyCooperationInfos = igCompanyCooperationInfos.stream().filter(v -> v.getProductCategory() !=null && v.getProductCategory().contains(SupplierTypeEnum.GROUP_MEDICAL.getCode()))
                                                                     .collect(Collectors.toList());
            }
        }
        List<Long> cooperationIds=new ArrayList<>();
        if (CollectionUtil.isNotEmpty(igCompanyCooperationInfos)){
            cooperationIds = igCompanyCooperationInfos.stream().map(IgCompanyCooperationInfo::getId)
                                                      .collect(Collectors.toList());
        }
        log.warn("询报价ID:{} 待匹配的供应商信息ID:{}",qpQuotationInfo.getId(), cooperationIds);
        //查询特约
        QIgCompanyCooperationSpecial qIgCompanyCooperationSpecial = QIgCompanyCooperationSpecial.ig_company_cooperation_special;
        List<IgCompanyCooperationSpecial> igCompanyCooperationSpecialList = bqlQueryFactory.select(Expressions.stringPath("ig_company_cooperation_special.*"))
                                                                                           .from(qIgCompanyCooperationSpecial)
                                                                                           .where(qIgCompanyCooperationSpecial.cooperation_info_id.in(cooperationIds))
                                                                                           .findList(IgCompanyCooperationSpecial.class);
        Map<Long, List<IgCompanyCooperationSpecial>> igSpecialMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(igCompanyCooperationSpecialList)) {
            // 特约分组
            igSpecialMap = igCompanyCooperationSpecialList.stream()
                                                          .collect(Collectors.groupingBy(IgCompanyCooperationSpecial::getCooperationInfoId));

        }

        List<IgProductTerms> igProductTermsList = new ArrayList<>();
        QIgProductTerms qIgProductTerms = QIgProductTerms.ig_product_terms;
        if (CollectionUtil.isNotEmpty(cooperationIds)){
            //查询责任
             igProductTermsList = bqlQueryFactory.select( qIgProductTerms.duty_code.as("duty_code"),
                                                                             qIgProductTerms.id.as("id"),
                                                                             qIgProductTerms.cooperation_id.as("cooperation_id"),
                                                                             qIgProductTerms.product_type_id.as("product_type_id"),
                                                                             qIgProductTerms.main_insurance.as("main_insurance"),
                                                                             qIgProductTerms.excluse_duty_type.as("excluse_duty_type")
                                                                     )
                                                                     .from(qIgProductTerms)
                                                                     .where(qIgProductTerms.cooperation_id.in(cooperationIds))
                                                                     .findList(IgProductTerms.class);
        }

        //查询保额限制信息
        QIgProductTermsExtra qIgProductTermsExtra = QIgProductTermsExtra.ig_product_terms_extra;
        List<IgProductTermsExtra> igProductTermsExtraList = bqlQueryFactory.select(qIgProductTermsExtra.id.as("id"), qIgProductTermsExtra.product_terms_id.as("product_terms_id"), qIgProductTermsExtra.maxCoverageLimit.as("max_coverage_limit"), qIgProductTermsExtra.cooperate_category.as("cooperate_category"))
                                                                           .from(qIgProductTerms)
                                                                           .leftJoin(qIgProductTermsExtra)
                                                                           .on(qIgProductTerms.id.eq(qIgProductTermsExtra.product_terms_id))
                                                                           .where(qIgProductTermsExtra.maxCoverageLimit.isNotNull())
                                                                           .findList(IgProductTermsExtra.class);
        Map<Long, List<IgProductTermsExtra>> productTermsExtraMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(igProductTermsExtraList)) {
            productTermsExtraMap = igProductTermsExtraList.stream()
                                                          .collect(Collectors.groupingBy(IgProductTermsExtra::getProductTermsId));
        }

        Map<Long, List<IgProductTerms>> igProductTermsMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(igProductTermsList)) {
            // 责任分组
            igProductTermsMap = igProductTermsList.stream()
                                                  .collect(Collectors.groupingBy(IgProductTerms::getCooperationId));
        }
        if (!CollectionUtils.isEmpty(igCompanyCooperationInfos)) {
            for (IgCompanyCooperationInfo igCompanyCooperationInfo : igCompanyCooperationInfos) {
                SupplierInfoDto supplierInfoDto = new SupplierInfoDto();
                supplierInfoDto.setCooperationInfoId(igCompanyCooperationInfo.getId());
                supplierInfoDto.setProvince(igCompanyCooperationInfo.getProvince());
                supplierInfoDto.setCity(igCompanyCooperationInfo.getCity());
                // 承包模式
                supplierInfoDto.setBusinessMode(igCompanyCooperationInfo.getUnderwriteModel());
                // 保险期间
                supplierInfoDto.setInsurancePeriod(igCompanyCooperationInfo.getInsurancePeriod());
                //责任信息
                supplierInfoDto.setResponsibility(igProductTermsMap.get(igCompanyCooperationInfo.getId()));
                // 特约信息
                supplierInfoDto.setSpecialList(igSpecialMap.get(igCompanyCooperationInfo.getId()));
                // 缴费方式
                supplierInfoDto.setPaymentType(igCompanyCooperationInfo.getPaymentMethod());
                // 保额信息
                List<IgProductTerms> productTermsList = igProductTermsMap.get(igCompanyCooperationInfo.getId());
                List<IgProductTermsDto> igProductTermsDtos = matchSumInsured(productTermsList, productTermsExtraMap);
                supplierInfoDto.setIgProductTermsDtos(igProductTermsDtos);
                supplierInfoList.add(supplierInfoDto);
            }
        }
        return supplierInfoList;
    }

    /**
     * 匹配责任保额信息
     *
     * @param responsibility
     * @param productTermsExtraMap
     * @return
     */
    private List<IgProductTermsDto> matchSumInsured(List<IgProductTerms> responsibility, Map<Long, List<IgProductTermsExtra>> productTermsExtraMap) {
        List<IgProductTermsDto> igProductTermsDtos = new ArrayList<>();
        if (CollectionUtils.isEmpty(responsibility)) {
            return igProductTermsDtos;
        }
        for (IgProductTerms igProductTerms : responsibility) {
            IgProductTermsDto igProductTermsDto = new IgProductTermsDto();
            List<IgProductTermsExtra> igProductTermsExtraList = productTermsExtraMap.get(igProductTerms.getId());
            BeanUtils.copyProperties(igProductTerms, igProductTermsDto);
            igProductTermsDto.setIgProductTermsExtraList(igProductTermsExtraList);
            igProductTermsDtos.add(igProductTermsDto);
        }
        return igProductTermsDtos;
    }


    /**
     * 优选供应商
     *
     * @param quotationId
     */
    @Override
    public List<IgPreferSupplier> preferSupplier(Long quotationId) {
        QIgPreferSupplier qIgPreferSupplier = QIgPreferSupplier.ig_prefer_supplier;
        return  bqlQueryFactory.select(Expressions.stringPath("ig_prefer_supplier.*"))
                                                      .from(qIgPreferSupplier)
                                                      .where(qIgPreferSupplier.quotation_info_id.eq(quotationId))
                                                      .orderBy(qIgPreferSupplier.sort_num.asc())
                                                      .findList(IgPreferSupplier.class);
    }

    /**
     * 保存供应商
     *
     * @param supplierReq
     */
    @Override
    public void saveSupplier(SupplierReq supplierReq) {
        // 更新排序
        List<IgPreferSupplier> preferSupplierList = supplierReq.getPreferSupplierList();
        if (!CollectionUtils.isEmpty(preferSupplierList)) {
            igPreferSupplierDataMapper.entity(IgPreferSupplier.class).updateAll(preferSupplierList);
        }
        if (CollectionUtil.isNotEmpty(supplierReq.getSpecificSupplierIds())){
            updateSpecificSupplier(supplierReq.getQuotationInfoId(),supplierReq.getSpecificSupplierIds(),supplierReq.getType(),supplierReq.getInitData());
        }
    }

    /**
     * 修改协议拥金
     *
     * @param quotation_infoId
     * @param cooperationId
     * @param commissionRate
     */
    @Override
    public void updateCommissionRequire(Long quotation_infoId, Long cooperationId, BigDecimal commissionRate,BigDecimal commissionRequire) {
        DataCondition<IgPreferSupplier> dataCondition = new DataCondition<>();
        dataCondition.eq("quotation_info_id", quotation_infoId);
        dataCondition.eq("cooperation_id", cooperationId);
        List<IgPreferSupplier> preferSupplierList = igPreferSupplierDataMapper.entity(IgPreferSupplier.class)
                                                                              .select(dataCondition);
        if (!CollectionUtils.isEmpty(preferSupplierList)) {
            IgPreferSupplier igPreferSupplier = preferSupplierList.get(0);
            if (preferSupplierList.size() > 1) {
                Optional<IgPreferSupplier> optionalIgPreferSupplier = preferSupplierList.stream()
                                                                     .filter(supplier -> supplier.getCommissionRate() == null)
                                                                     .findFirst();
                if (optionalIgPreferSupplier.isPresent()) {
                    igPreferSupplier = optionalIgPreferSupplier.get();
                }
            }
            if (commissionRate != null ){
                igPreferSupplier.setCommissionRate(commissionRate.multiply(new BigDecimal("100")));
            }
            if (commissionRequire != null){
                igPreferSupplier.setCommissionRequire(commissionRequire);
            }
            log.warn("询报价ID:{} 供应商ID:{} 更新协议佣金:{} 佣金要求:{}",quotation_infoId,cooperationId,commissionRate,commissionRequire);
            igPreferSupplierDataMapper.entity(IgPreferSupplier.class).updateOne(igPreferSupplier);
        }

    }


    /**
     * 特定供应商列表
     * 特定供应商、实际承保供应商的选项 需要按照“合作产品类别”过滤，
     * 报价类型=雇主，可选项是 供应商合作产品类别包含“雇主责任险”的所有供应商
     * 报价类型=团险意外，可选项是 供应商合作产品类别包含“团体医疗”OR“纯意外险”的所有供应商
     * 报价类型=团险医疗，可选项是 供应商合作产品类别包含“团体医疗”的所有供应商
     *
     * @return
     */
    @Override
    public List<SpecificSupplierDto> specificSupplierList(Long quotationInfoId) {
        QQpQuotationInfo qpQuotationInfo  = QQpQuotationInfo.qp_quotation_info;
        QpQuotationInfo quotationInfo = bqlQueryFactory.select(Expressions.stringPath("qp_quotation_info.*"))
                                             .from(qpQuotationInfo).where(qpQuotationInfo.id.eq(quotationInfoId))
                                             .findOne(QpQuotationInfo.class);
        SupplierTypeEnum supplierTypeEnum = SupplierTypeEnum.getEnumByCode(String.valueOf(quotationInfo.getQuoteType()));
        QIgCompanyCooperationInfo qIgCompanyCooperationInfo = QIgCompanyCooperationInfo.ig_company_cooperation_info;
        QIgCompany igCompany = QIgCompany.ig_company;
        List<SpecificSupplierDto> specificSupplierDtos = bqlQueryFactory.select(qIgCompanyCooperationInfo.id.as("id"), igCompany.company_name.as("company_name"),qIgCompanyCooperationInfo.product_category.as("product_category"))
                                                                        .from(qIgCompanyCooperationInfo)
                                                                        .leftJoin(igCompany)
                                                                        .on(qIgCompanyCooperationInfo.company_id.eq(igCompany.id))
                                                                        .findList(SpecificSupplierDto.class);
        specificSupplierDtos = specificSupplierDtos.stream().filter(specificSupplierDto -> CollectionUtil.isNotEmpty(specificSupplierDto.getProductCategory())).collect(Collectors.toList());
        if (supplierTypeEnum!=null){
            if (SupplierTypeEnum.EMPLOYERS_LIABILITY.getQuotationCode().equals(String.valueOf(quotationInfo.getQuoteType()))){
                specificSupplierDtos=specificSupplierDtos.stream().filter(specificSupplierDto -> specificSupplierDto.getProductCategory().contains(supplierTypeEnum.getCode())).collect(Collectors.toList());
            }
            if (SupplierTypeEnum.PURE_ACCIDENT.getQuotationCode().equals(String.valueOf(quotationInfo.getQuoteType()))){
                specificSupplierDtos=specificSupplierDtos.stream().filter(specificSupplierDto -> specificSupplierDto.getProductCategory().contains(SupplierTypeEnum.PURE_ACCIDENT.getCode()) || specificSupplierDto.getProductCategory().contains(SupplierTypeEnum.GROUP_MEDICAL.getCode())).collect(Collectors.toList());
            }
            if (SupplierTypeEnum.GROUP_MEDICAL.getQuotationCode().equals(String.valueOf(quotationInfo.getQuoteType()))){
                specificSupplierDtos=specificSupplierDtos.stream().filter(specificSupplierDto -> specificSupplierDto.getProductCategory().contains(SupplierTypeEnum.GROUP_MEDICAL.getCode())).collect(Collectors.toList());
            }
        }
        if (CollectionUtils.isEmpty(specificSupplierDtos)) {
            return new ArrayList<>();
        }
        return specificSupplierDtos;
    }


    /**
     * 匹配保费规模
     *
     * @param quotationInfoId
     * @return
     */
    @Override
    public BigDecimal matchPremiumScale(Long quotationInfoId) {
        QQpQuotationInfo qQpQuotationInfo = QQpQuotationInfo.qp_quotation_info;
        QpQuotationInfo qpQuotationInfo = bqlQueryFactory.select(Expressions.stringPath("qp_quotation_info.*"))
                                                         .from(qQpQuotationInfo)
                                                         .where(qQpQuotationInfo.id.eq(quotationInfoId))
                                                         .findOne(true, QpQuotationInfo.class);
        if (qpQuotationInfo == null){
            return null;
        }
        Integer number=  qpQuotationInfo.getOpeningInsuredCount();
        Integer renewFlag = qpQuotationInfo.getRenewFlag();
        if (RenewFlagEnum.RENEW.getValue().equals(renewFlag)){
            DataCondition<QpQuotationHistoryCustomer> dataCondition = new DataCondition<>();
            dataCondition.eq("quotation_info_id", quotationInfoId);
            dataCondition.eq("customer_id", qpQuotationInfo.getCustomerId());
            List<QpQuotationHistoryCustomer> qpQuotationHistoryCustomers = qpQuotationHistoryCustomerDataMapper.entity(QpQuotationHistoryCustomer.class)
                                                                                                               .select(dataCondition);
            if (CollectionUtil.isNotEmpty(qpQuotationHistoryCustomers)){
                number=qpQuotationHistoryCustomers.get(0).getUnderInsuranceRelaxBegin();
            }
        }
        BigDecimal sum = BigDecimal.ZERO;

        Integer quoteType = qpQuotationInfo.getQuoteType();
        //查询责任
        QQpQuotationDuty qQpQuotationDuty = QQpQuotationDuty.qp_quotation_duty;
        QQpQuotationConfig qQpQuotationConfig = QQpQuotationConfig.qp_quotation_config;
        QQpQuotation qpQuotation = QQpQuotation.qp_quotation;
        List<QpQuotationAmountDto> quotationList = bqlQueryFactory.select(qpQuotation.id.as("id"), qpQuotation.opening_insured_count.as("number"))
                                                                  .from(qQpQuotationInfo).leftJoin(qpQuotation)
                                                                  .on(qQpQuotationInfo.id.eq(qpQuotation.quotation_info_id))
                                                                  .leftJoin(qQpQuotationConfig)
                                                                  .on(qQpQuotationConfig.quotation_id.eq(qpQuotation.id))
                                                                  .leftJoin(qQpQuotationDuty)
                                                                  .on(qQpQuotationConfig.id.eq(qQpQuotationDuty.quotation_config_id))
                                                                  .where(qQpQuotationInfo.id.eq(quotationInfoId)
                                                                                            .and(qpQuotation.relation.eq("0"))) //本人
                                                                  .groupBy(qpQuotation.id)
                                                                  .findList(QpQuotationAmountDto.class);

        List<QpQuotationAmountDto> qpQuotationDutyIds = bqlQueryFactory.select(qpQuotation.id.as("id"), qQpQuotationDuty.id.as("duty_id"), qQpQuotationDuty.price.amount.as("amount"))
                                                                       .from(qQpQuotationInfo).leftJoin(qpQuotation)
                                                                       .on(qQpQuotationInfo.id.eq(qpQuotation.quotation_info_id))
                                                                       .leftJoin(qQpQuotationConfig)
                                                                       .on(qQpQuotationConfig.quotation_id.eq(qpQuotation.id))
                                                                       .leftJoin(qQpQuotationDuty)
                                                                       .on(qQpQuotationConfig.id.eq(qQpQuotationDuty.quotation_config_id))
                                                                       .where(qQpQuotationInfo.id.eq(quotationInfoId)
                                                                                                 .and(qpQuotation.relation.eq("0"))) //本人
                                                                       .findList(QpQuotationAmountDto.class);
        Map<Long, List<QpQuotationAmountDto>> qpQuotationDutyMap = qpQuotationDutyIds.stream()
                                                                                     .collect(Collectors.groupingBy(QpQuotationAmountDto::getId));
        if (QuoteTypeEnum.EMPLOYER.getCode().equals(quoteType)) {
            // 获取责任
            if (!CollectionUtils.isEmpty(quotationList)) {
                for (QpQuotationAmountDto qpQuotationAmountDto : quotationList) {
                    List<QpQuotationAmountDto> qpQuotationAmountDtos = qpQuotationDutyMap.get(qpQuotationAmountDto.getId());
                    if (!CollectionUtils.isEmpty(qpQuotationAmountDtos)) {
                        Optional<BigDecimal> reduce = qpQuotationAmountDtos.stream()
                                                                           .map(QpQuotationAmountDto::getAmount)
                                                                           .reduce(BigDecimal::add);
                        if (reduce.isPresent()) {
                            BigDecimal multiply = reduce.get()
                                                        .multiply(BigDecimal.valueOf(qpQuotationAmountDto.getNumber()));
                            sum = sum.add(multiply);
                        }
                        if (quotationList.size() == 1) {
                            sum = reduce.get().multiply(BigDecimal.valueOf(number));
                        }
                    }
                }
            }
        }
        // 意外 、医疗
        if (QuoteTypeEnum.GEEK_PLUS.getCode().equals(quoteType) || QuoteTypeEnum.TRADITION.getCode().equals(quoteType)) {
            // 高频责任
            List<String> highFrequencyList = Arrays.asList("1321230", "1321220", "1321130", "1321120", "1322100", "1321600", "1321601", "1321602", "1321603");
            if (CollectionUtil.isNotEmpty(quotationList)) {
                List<Long> quotationIdList = quotationList.stream().map(QpQuotationAmountDto::getId).collect(Collectors.toList());
                //  查询每个方案组的责任信息
                List<QpQuotationAmountDto> qpQuotationList = bqlQueryFactory.select(qpQuotation.id.as("id"), qQpQuotationConfig.id.as("config_id"), qQpQuotationDuty.business_type.as("business_type"), qQpQuotationDuty.price.amount.as("amount"))
                                                                            .from(qpQuotation).leftJoin(qpQuotation)
                                                                            .leftJoin(qQpQuotationConfig)
                                                                            .on(qQpQuotationConfig.quotation_id.eq(qpQuotation.id))
                                                                            .leftJoin(qQpQuotationDuty)
                                                                            .on(qQpQuotationConfig.id.eq(qQpQuotationDuty.quotation_config_id))
                                                                            .where(qpQuotation.id.in(quotationIdList))
                                                                            .findList(QpQuotationAmountDto.class);
                if (CollectionUtil.isNotEmpty(qpQuotationList)) {
                    Map<Long, List<QpQuotationAmountDto>> collect = qpQuotationList.stream()
                                                                                   .collect(Collectors.groupingBy(QpQuotationAmountDto::getId));
                    Map<Long, List<QpQuotationAmountDto>> quotaionMap = qpQuotationList.stream()
                                                                                       .collect(Collectors.groupingBy(QpQuotationAmountDto::getConfigId));
                    int num = 0;
                    for (Map.Entry<Long, List<QpQuotationAmountDto>> entry : collect.entrySet()) {
                        List<QpQuotationAmountDto> dutyList = entry.getValue();
                        List<Long> configIdList = dutyList.stream().map(QpQuotationAmountDto::getConfigId).distinct()
                                                          .collect(Collectors.toList());
                        for (Long configIds : configIdList) {
                            List<QpQuotationAmountDto> qpQuotationAmountList = quotaionMap.get(configIds);
                            // 获取责任
                            List<QpQuotationAmountDto> dutyHighFrequencyList = qpQuotationAmountList.stream()
                                                                                                    .filter(qpQuotationAmountDto -> highFrequencyList.contains(String.valueOf(qpQuotationAmountDto.getBusinessType())))
                                                                                                    .collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(dutyHighFrequencyList)) {
                                num = num + 1;
                                // 包含高频医疗责任的均值
                                BigDecimal reduce = qpQuotationAmountList.stream()
                                                                         .filter(qpQuotationAmountDto -> qpQuotationAmountDto.getAmount() != null)
                                                                         .map(QpQuotationAmountDto::getAmount)
                                                                         .reduce(BigDecimal.ZERO, BigDecimal::add);
                                sum = sum.add(reduce);
                            }
                        }
                    }
                    if (sum.compareTo(BigDecimal.ZERO) == 0) {
                        for (Map.Entry<Long, List<QpQuotationAmountDto>> entry : collect.entrySet()) {
                            List<QpQuotationAmountDto> dutyList = entry.getValue();
                            List<Long> configIdList = dutyList.stream().map(QpQuotationAmountDto::getConfigId)
                                                              .distinct().collect(Collectors.toList());
                            for (Long configIds : configIdList) {
                                List<QpQuotationAmountDto> qpQuotationAmountList = quotaionMap.get(configIds);
                                // 获取责任
                                List<QpQuotationAmountDto> dutyHighFrequencyList = qpQuotationAmountList.stream()
                                                                                                        .filter(qpQuotationAmountDto -> highFrequencyList.contains(String.valueOf(qpQuotationAmountDto.getBusinessType())))
                                                                                                        .collect(Collectors.toList());
                                if (CollectionUtil.isEmpty(dutyHighFrequencyList)) {
                                    num = num + 1;
                                    // 包含高频医疗责任的均值
                                    BigDecimal reduce = qpQuotationAmountList.stream()
                                                                             .map(QpQuotationAmountDto::getAmount)
                                                                             .filter(Objects::nonNull)
                                                                             .reduce(BigDecimal.ZERO, BigDecimal::add);
                                    sum = sum.add(reduce);
                                }
                            }
                        }

                    }
                    if (sum.compareTo(BigDecimal.ZERO) != 0 && number != null) {
                        sum = sum.multiply(BigDecimal.valueOf(number));
                        sum = sum.divide(BigDecimal.valueOf(num), 2, RoundingMode.HALF_UP);
                    }
                }
            }
        }
        return sum;
    }

    /**
     * 计算预测赔付率
     *
     * @param quotationInfoId
     */
    @Override
    public BigDecimal predict(Long quotationInfoId) {
        BigDecimal totalRiskPremium = totalRiskPremium(quotationInfoId);
        if (totalRiskPremium!=null){
            BigDecimal bigDecimal = matchPremiumScale(quotationInfoId);
            if (bigDecimal.compareTo(BigDecimal.ZERO)!=0){
                BigDecimal premiumScale =totalRiskPremium.divide(bigDecimal, 2, RoundingMode.HALF_UP);
                BigDecimal premium = premiumScale.multiply(BigDecimal.valueOf(100));
                QpQuotationInfo  quotationInfo = new QpQuotationInfo();
                quotationInfo.setId(quotationInfoId);
                quotationInfo.setPredictPayoutRatio(premium);
                quotationInfoDataMapper.entity(QpQuotationInfo.class).updateOne(quotationInfo,Boolean.TRUE);
                return premium;
            }
        }
        return BigDecimal.ZERO;
    }


    /**
     *  计算风险总保费
     * @param quotationInfoId
     * @return
     */

    private BigDecimal  totalRiskPremium(Long quotationInfoId) {
        QQpQuotationInfo qQpQuotationInfo = QQpQuotationInfo.qp_quotation_info;
        QpQuotationInfo qpQuotationInfo = bqlQueryFactory.select(Expressions.stringPath("qp_quotation_info.*"))
                                                         .from(qQpQuotationInfo)
                                                         .where(qQpQuotationInfo.id.eq(quotationInfoId))
                                                         .findOne(true, QpQuotationInfo.class);
        if (qpQuotationInfo == null){
            return null;
        }
        Integer number=  qpQuotationInfo.getOpeningInsuredCount();
        Integer renewFlag = qpQuotationInfo.getRenewFlag();
        if (RenewFlagEnum.RENEW.getValue().equals(renewFlag)){
            DataCondition<QpQuotationHistoryCustomer> dataCondition = new DataCondition<>();
            dataCondition.eq("quotation_info_id", quotationInfoId);
            dataCondition.eq("customer_id", qpQuotationInfo.getCustomerId());
            List<QpQuotationHistoryCustomer> qpQuotationHistoryCustomers = qpQuotationHistoryCustomerDataMapper.entity(QpQuotationHistoryCustomer.class)
                                                                                                               .select(dataCondition);
            number=qpQuotationHistoryCustomers.get(0).getUnderInsuranceRelaxBegin();
        }
        BigDecimal sum = BigDecimal.ZERO;
        Integer quoteType = qpQuotationInfo.getQuoteType();
        //查询责任
        QQpQuotationDuty qQpQuotationDuty = QQpQuotationDuty.qp_quotation_duty;
        QQpQuotationConfig qQpQuotationConfig = QQpQuotationConfig.qp_quotation_config;
        QQpQuotation qpQuotation = QQpQuotation.qp_quotation;
        List<QpQuotationAmountDto> quotationList = bqlQueryFactory.select(qpQuotation.id.as("id"), qpQuotation.opening_insured_count.as("number"))
                                                                  .from(qQpQuotationInfo).leftJoin(qpQuotation)
                                                                  .on(qQpQuotationInfo.id.eq(qpQuotation.quotation_info_id))
                                                                  .leftJoin(qQpQuotationConfig)
                                                                  .on(qQpQuotationConfig.quotation_id.eq(qpQuotation.id))
                                                                  .leftJoin(qQpQuotationDuty)
                                                                  .on(qQpQuotationConfig.id.eq(qQpQuotationDuty.quotation_config_id))
                                                                  .where(qQpQuotationInfo.id.eq(quotationInfoId).and(qpQuotation.relation.eq("0"))) //本人
                                                                  .groupBy(qpQuotation.id)
                                                                  .findList(QpQuotationAmountDto.class);

        List<QpQuotationAmountDto> qpQuotationDutyIds = bqlQueryFactory.select(qpQuotation.id.as("id"), qQpQuotationDuty.id.as("duty_id"), qQpQuotationDuty.riskPrice.as("risk_price"))
                                                                       .from(qQpQuotationInfo).leftJoin(qpQuotation)
                                                                       .on(qQpQuotationInfo.id.eq(qpQuotation.quotation_info_id))
                                                                       .leftJoin(qQpQuotationConfig)
                                                                       .on(qQpQuotationConfig.quotation_id.eq(qpQuotation.id))
                                                                       .leftJoin(qQpQuotationDuty)
                                                                       .on(qQpQuotationConfig.id.eq(qQpQuotationDuty.quotation_config_id))
                                                                       .where(qQpQuotationInfo.id.eq(quotationInfoId).and(qpQuotation.relation.eq("0"))) //本人
                                                                       .findList(QpQuotationAmountDto.class);
        Map<Long, List<QpQuotationAmountDto>> qpQuotationDutyMap = qpQuotationDutyIds.stream()
                                                                                     .collect(Collectors.groupingBy(QpQuotationAmountDto::getId));
        if (QuoteTypeEnum.EMPLOYER.getCode().equals(quoteType)) {
            // 获取责任
            if (!CollectionUtils.isEmpty(quotationList)) {
                for (QpQuotationAmountDto qpQuotationAmountDto : quotationList) {
                    List<QpQuotationAmountDto> qpQuotationAmountDtos = qpQuotationDutyMap.get(qpQuotationAmountDto.getId());
                    if (!CollectionUtils.isEmpty(qpQuotationAmountDtos)) {
                        List<CurrencyAmount> reduce = qpQuotationAmountDtos.stream()
                                                                           .map(QpQuotationAmountDto::getRiskPrice)
                                                                           .filter(Objects::nonNull)
                                                                           .collect(Collectors.toList());
                        if (CollectionUtil.isNotEmpty(reduce)) {
                            BigDecimal amount = CurrencyAmountUtil.sum(reduce).getAmount();
                            BigDecimal multiply = amount.multiply(BigDecimal.valueOf(qpQuotationAmountDto.getNumber()));
                            sum = sum.add(multiply);
                        }
                        if (quotationList.size() == 1 && CollectionUtil.isNotEmpty(reduce)) {
                            BigDecimal amount = CurrencyAmountUtil.sum(reduce).getAmount();
                            sum = amount.multiply(BigDecimal.valueOf(number));
                        }
                    }
                }
            }
        }
        // 意外 、医疗
        if (QuoteTypeEnum.GEEK_PLUS.getCode().equals(quoteType) || QuoteTypeEnum.TRADITION.getCode().equals(quoteType)) {
            // 高频责任
            List<String> highFrequencyList = Arrays.asList("1321230", "1321220", "1321130", "1321120", "1322100", "1321600", "1321601", "1321602", "1321603");
            if (CollectionUtil.isNotEmpty(quotationList)) {
                List<Long> quotationIdList = quotationList.stream().map(QpQuotationAmountDto::getId).collect(Collectors.toList());
                //  查询每个方案组的责任信息
                List<QpQuotationAmountDto> qpQuotationList = bqlQueryFactory.select(qpQuotation.id.as("id"), qQpQuotationConfig.id.as("config_id"), qQpQuotationDuty.business_type.as("business_type"), qQpQuotationDuty.riskPrice.as("risk_price"))
                                                                            .from(qpQuotation).leftJoin(qpQuotation)
                                                                            .leftJoin(qQpQuotationConfig)
                                                                            .on(qQpQuotationConfig.quotation_id.eq(qpQuotation.id))
                                                                            .leftJoin(qQpQuotationDuty)
                                                                            .on(qQpQuotationConfig.id.eq(qQpQuotationDuty.quotation_config_id))
                                                                            .where(qpQuotation.id.in(quotationIdList))
                                                                            .findList(QpQuotationAmountDto.class);
                if (CollectionUtil.isNotEmpty(qpQuotationList)) {
                    Map<Long, List<QpQuotationAmountDto>> collect = qpQuotationList.stream()
                                                                                   .collect(Collectors.groupingBy(QpQuotationAmountDto::getId));
                    Map<Long, List<QpQuotationAmountDto>> quotaionMap = qpQuotationList.stream()
                                                                                       .collect(Collectors.groupingBy(QpQuotationAmountDto::getConfigId));
                    int num = 0;
                    for (Map.Entry<Long, List<QpQuotationAmountDto>> entry : collect.entrySet()) {
                        List<QpQuotationAmountDto> dutyList = entry.getValue();
                        List<Long> configIdList = dutyList.stream().map(QpQuotationAmountDto::getConfigId).distinct()
                                                          .collect(Collectors.toList());
                        for (Long configIds : configIdList) {
                            List<QpQuotationAmountDto> qpQuotationAmountList = quotaionMap.get(configIds);
                            // 获取责任
                            List<QpQuotationAmountDto> dutyHighFrequencyList = qpQuotationAmountList.stream()
                                                                                                    .filter(qpQuotationAmountDto -> highFrequencyList.contains(String.valueOf(qpQuotationAmountDto.getBusinessType())))
                                                                                                    .collect(Collectors.toList());
                            if (CollectionUtil.isNotEmpty(dutyHighFrequencyList)) {
                                num = num + 1;
                                // 包含高频医疗责任的均值
                                List<CurrencyAmount> currencyAmountList = qpQuotationAmountList.stream()
                                                                                     .map(QpQuotationAmountDto::getRiskPrice)
                                                                                     .filter(Objects::nonNull)
                                                                                     .collect(Collectors.toList());
                                if (CollectionUtil.isNotEmpty(currencyAmountList)){
                                    CurrencyAmount sumCurrencyAmount = CurrencyAmountUtil.sum(currencyAmountList);
                                    sum = sum.add(sumCurrencyAmount.getAmount());
                                }
                            }
                        }
                    }
                    if (sum.compareTo(BigDecimal.ZERO) == 0) {
                        for (Map.Entry<Long, List<QpQuotationAmountDto>> entry : collect.entrySet()) {
                            List<QpQuotationAmountDto> dutyList = entry.getValue();
                            List<Long> configIdList = dutyList.stream().map(QpQuotationAmountDto::getConfigId)
                                                              .distinct().collect(Collectors.toList());
                            for (Long configIds : configIdList) {
                                List<QpQuotationAmountDto> qpQuotationAmountList = quotaionMap.get(configIds);
                                // 获取责任
                                List<QpQuotationAmountDto> dutyHighFrequencyList = qpQuotationAmountList.stream()
                                                                                                        .filter(qpQuotationAmountDto -> highFrequencyList.contains(String.valueOf(qpQuotationAmountDto.getBusinessType())))
                                                                                                        .collect(Collectors.toList());
                                if (CollectionUtil.isEmpty(dutyHighFrequencyList)) {
                                    num = num + 1;
                                    // 包含高频医疗责任的均值
                                    List<CurrencyAmount> currencyAmountList = qpQuotationAmountList.stream()
                                                                                         .map(QpQuotationAmountDto::getRiskPrice)
                                                                                          .filter(Objects::nonNull)
                                                                                         .collect(Collectors.toList());
                                    if (CollectionUtil.isNotEmpty(currencyAmountList)){
                                        CurrencyAmount sumCurrencyAmount = CurrencyAmountUtil.sum(currencyAmountList);
                                        sum = sum.add(sumCurrencyAmount.getAmount());
                                    }
                                }
                            }
                        }

                    }
                    if (sum.compareTo(BigDecimal.ZERO) != 0 && number != null) {
                        sum = sum.multiply(BigDecimal.valueOf(number));
                        sum = sum.divide(BigDecimal.valueOf(num), 2, RoundingMode.HALF_UP);
                    }
                }
            }
        }
        return sum;
    }

    /**
     * 触发赔付预测
     *
     * @param quotationInfoId
     */
    @Override
    public void triggerPredict(Long quotationInfoId) {
        CreatePredictDto condition = new CreatePredictDto();
        condition.setQuotationInfoId(quotationInfoId);
        quotationPredictService.createTask(condition);
    }

    /**
     * 下载文件
     *
     * @param quoteInfoId
     * @param type
     * @param response
     */
    @Override
    public void exportQuoteExcel(Long quoteInfoId, Long cooperationId, String type, HttpServletResponse response) {
        quotationManageAggService.checkExcel(quoteInfoId);
        // 导出文件
        quotationManageAggService.exportQuoteExcelNewV3(quoteInfoId, cooperationId, type, response);

    }

    /**
     * 更新协议佣金率
     *
     * @param quotationInfoId
     * @param supplierId
     */
    @SneakyThrows
    @Override
    @Async("asyncExecutor")
    public void updateCommissionRequire(Long quotationInfoId, List<Long> supplierId) {
        if (CollectionUtil.isEmpty(supplierId)){
            return;
        }
        //  方案保费
        List<QpQuotationAmountDto> schemePremiums = getSchemePremiums(quotationInfoId);
        ExecutorCompletionService<Map<Long, Map<String, String>>> service = new ExecutorCompletionService<>(threadPoolTaskExecutor);
        for (Long id : supplierId) {
            service.submit(() -> {
                return commissionFeilvService.getFeilvReturnDtos(quotationInfoId, id);
            });
        }
        for (Long id : supplierId) {
                Map<Long, Map<String, String>> feilvReturnDtos = service.take().get();
                log.warn("询报价ID:{}获取汇率表净费信息:{}",quotationInfoId,JacksonUtils.writeAsString(feilvReturnDtos) );
                // 多线程并发跑 ，之后更新结果
                List<FeilvReturnDto> qpQuotationInfoDtos = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(feilvReturnDtos)) {
                    for (Map.Entry<Long, Map<String, String>> entry : feilvReturnDtos.entrySet()) {
                        FeilvReturnDto qpQuotationInfoDto = JSON.parseObject(JSON.toJSONString(entry.getValue()), FeilvReturnDto.class);
                        qpQuotationInfoDtos.add(qpQuotationInfoDto);
                    }
                }
                if (CollectionUtil.isNotEmpty(qpQuotationInfoDtos)) {
                    if (CollectionUtil.isNotEmpty(schemePremiums)) {
                        // 用户 存在一个方案
                        Map<Long, List<FeilvReturnDto>> quotationMap = qpQuotationInfoDtos.stream().collect(Collectors.groupingBy(FeilvReturnDto::getQuotationConfigId));
                        Map<Long, List<QpQuotationAmountDto>> qpQuotationAmountDtoMap = schemePremiums.stream().filter(qpQuotationAmountDto -> qpQuotationAmountDto.getAmount()!=null).collect(Collectors.groupingBy(QpQuotationAmountDto::getId));
                        // 方案佣金率=（方案保费-方案净费）/方案保费
                        log.warn("询报价ID:{} 供应商ID:{} 方案保费信息:{} ",quotationInfoId,id,JacksonUtils.writeAsString(qpQuotationAmountDtoMap));
                        log.warn("询报价ID:{} 供应商ID:{} 方案净费信息:{} ",quotationInfoId,id,JacksonUtils.writeAsString(quotationMap));
                        List<BigDecimal> planCommissionRateList = new ArrayList<>();
                        BigDecimal netCost = BigDecimal.ZERO;
                        for (Map.Entry<Long, List<QpQuotationAmountDto>> entry : qpQuotationAmountDtoMap.entrySet()) {
                            List<QpQuotationAmountDto> qpQuotationAmountDtoList = entry.getValue();
                            Optional<BigDecimal> reduce = qpQuotationAmountDtoList.stream()
                                                                                  .map(QpQuotationAmountDto::getAmount)
                                                                                  .reduce(BigDecimal::add);
                            List<FeilvReturnDto> qpQuotationInfoDtoList = quotationMap.get(entry.getKey());
                            netCost = qpQuotationInfoDtoList.stream().map(FeilvReturnDto::getNetCost)
                                                            .filter(Objects::nonNull)
                                                            .map(s -> {
                                                                try {
                                                                    return new BigDecimal(s);
                                                                } catch (NumberFormatException e) {
                                                                    return BigDecimal.ZERO;
                                                                }
                                                            }).reduce(BigDecimal::add)
                                                            .orElse(BigDecimal.ZERO);
                            if (reduce.isPresent()){
                                BigDecimal subtract = reduce.get().subtract(netCost);
                                BigDecimal planCommissionRate = subtract.divide(reduce.get(), 2, RoundingMode.HALF_UP);
                                planCommissionRateList.add(planCommissionRate);
                            }
                        }
                        log.warn("询报价ID:{} 供应商ID:{} 协议佣金集合:{} ",quotationInfoId,id,planCommissionRateList);
                        BigDecimal bigDecimal = CalculateMedianUtils.calculateMedian(planCommissionRateList);
                        log.warn("询报价ID:{} 供应商ID:{} 协议佣金集合计算结果:{} ",quotationInfoId,id,bigDecimal);
                        Map<Long, List<FeilvReturnDto>> quotationInfoMap = qpQuotationInfoDtos.stream().collect(Collectors.groupingBy(FeilvReturnDto::getQuotationInfoId));
                        for (Map.Entry<Long, List<FeilvReturnDto>> entry : quotationInfoMap.entrySet()) {
                            // 更新佣金率
                            List<FeilvReturnDto> value = entry.getValue();
                            if (CollectionUtil.isNotEmpty(value)){
                                Long supplier = value.get(0).getSupplierId();
                                updateCommissionRequire(entry.getKey(), supplier, bigDecimal,null);
                            }

                        }
                    }
                }
        }
    }


    /**
     * 优选供应商数据排序
     * 优选供应商列表：展示内容为特定供应商+可承保供应商相关信息；
     *  默认排序：优先展示特定供应商，随后展示可承保供应商，可承保供应商按照协议佣金率从高到低排列，如果佣金相同，按照饥渴系数从大到小排列，
     *  饥渴系数（从BI获取） =（当年度期望年化保费 - 当年度已生效年化保费）/当年度期望年化保费：
     * 团体医疗险
     * 所有产品类别合计
     * 低频医疗险
     * 雇主责任险
     *
     * @param quotationInfoId
     */
    @Override
    @Async("asyncExecutor")
    public void updatePreferSupplierSort(Long quotationInfoId) {
        log.warn("询报价ID:{} 供应商数据排序开始", quotationInfoId);
        IdentityDto identity  = new IdentityDto();
        identity.setTenantId(2014L);
        identity.setUserId(1997L);
        IdentityContext.setIdentity(identity);
        QIgPreferSupplier qIgPreferSupplier = QIgPreferSupplier.ig_prefer_supplier;
        QIgCompany qIgCompany =QIgCompany.ig_company;
        QIgCompanyCooperationInfo qIgCompanyCooperationInfo =QIgCompanyCooperationInfo.ig_company_cooperation_info;
        QQpQuotationInfo qQpQuotationInfo = QQpQuotationInfo.qp_quotation_info;
        QpQuotationInfo qpQuotationInfo = bqlQueryFactory.select(Expressions.stringPath("qp_quotation_info.*"))
                                                         .from(qQpQuotationInfo)
                                                         .where(qQpQuotationInfo.id.eq(quotationInfoId))
                                                         .findOne(QpQuotationInfo.class);

        List<IgPreferSupplier> igPreferSuppliers = bqlQueryFactory.select(Expressions.stringPath("ig_prefer_supplier.*"))
                                                                  .from(qIgPreferSupplier)
                                                                  .where(qIgPreferSupplier.quotation_info_id.eq(quotationInfoId))
                                                                  .findList(IgPreferSupplier.class);
        List<PerferSupplierDto> perferSupplierDtos = bqlQueryFactory.select(qIgCompany.id.as("company_id"),qIgCompanyCooperationInfo.id.as("cooperation_info_id"))
                                                                  .from(qIgPreferSupplier)
                                                                  .leftJoin(qIgCompanyCooperationInfo).on(qIgCompanyCooperationInfo.id.eq(qIgPreferSupplier.cooperation_id))
                                                                  .leftJoin(qIgCompany).on(qIgCompany.id.eq(qIgCompanyCooperationInfo.company_id))
                                                                  .where(qIgPreferSupplier.quotation_info_id.eq(quotationInfoId))
                                                                  .findList(PerferSupplierDto.class);
        List<IgPreferSupplier> igPreferSupplierList1 = igPreferSuppliers.stream()
                                                                        .filter(igPreferSupplier -> "1".equals(igPreferSupplier.getType()))
                                                                        .collect(Collectors.toList());

        String quoteTypeString = "";
        if (CollectionUtil.isNotEmpty(igPreferSuppliers)){
            Integer quoteType = qpQuotationInfo.getQuoteType();
            if (quoteType == 1){
                //团险医疗方案
                quoteTypeString="团体医疗险";
            }
            if (quoteType == 3){
                //雇主方案
                quoteTypeString="雇主责任险";
            }
        }
        HungerCoefficientDto hungerCoefficientDto = new HungerCoefficientDto();
        hungerCoefficientDto.setProductName(quoteTypeString);
        Map<Long, Long> cooperIdMap  = new HashMap<>();
        Map<String, Object> resultMap = new HashMap<>();
        if (StringUtils.isNotBlank(quoteTypeString)){
            List<Long> cooperationIds = perferSupplierDtos.stream()
                    .map(PerferSupplierDto::getCompanyId).distinct()
                    .collect(Collectors.toList());
            cooperIdMap = perferSupplierDtos.stream().collect(Collectors.toMap(PerferSupplierDto::getCooperationInfoId, PerferSupplierDto::getCompanyId, (k1,k2)->k2));
            hungerCoefficientDto.setCompanyIdList(cooperationIds);
            resultMap = new HashMap<>();
            if (CollectionUtil.isNotEmpty(cooperationIds)){
                ResponseVO<List<Map<String, Object>>> listResponseVO = hungerCoefficientClient.searchHistory(hungerCoefficientDto);
                List<Map<String, Object>> data = listResponseVO.getData();
                if (CollectionUtil.isNotEmpty(data)){
                    log.warn("询报价ID:{} 查询中台饥渴系数:{}", quotationInfoId, JacksonUtils.writeAsString(listResponseVO.getData()));
                    resultMap = data.stream().filter(map -> map.get("hungry_coefficient")!=null).collect(Collectors.toMap(
                            map -> (String) map.get("company_ins_id"),
                            map -> map.get("hungry_coefficient") ,
                            (k1,k2)->k2
                    ));
                }

            }
        }
        List<IgPreferSupplier> igPreferSupplierUpDate = new ArrayList<>();
        AtomicReference<Integer> sortNum = new AtomicReference<>(0);
        if (CollectionUtil.isNotEmpty(igPreferSuppliers)) {
            // 特定供应商
            igPreferSupplierList1= sortAndCollect(igPreferSupplierList1, cooperIdMap, resultMap);
            igPreferSupplierList1.forEach(igPreferSupplier -> {
                igPreferSupplier.setSortNum(sortNum.getAndSet(sortNum.get() + 1));
            });
            log.warn("询报价ID:{} 特定供应商排序结果{}",quotationInfoId,JacksonUtils.writeAsString(igPreferSupplierList1));
            igPreferSupplierUpDate.addAll(igPreferSupplierList1);
            // 可承保供应商
            List<IgPreferSupplier> igPreferSupplierList0 = igPreferSuppliers.stream()
                                                                           .filter(igPreferSupplier -> "0".equals(igPreferSupplier.getType()))
                                                                           .collect(Collectors.toList());
            igPreferSupplierList0= sortAndCollect(igPreferSupplierList0, cooperIdMap, resultMap);
            igPreferSupplierList0.forEach(igPreferSupplier -> {
                igPreferSupplier.setSortNum(sortNum.getAndSet(sortNum.get() + 1));
            });
            igPreferSupplierUpDate.addAll(igPreferSupplierList0);
        }
        log.warn("询报价ID:{} 供应商数据排序结果:{}", quotationInfoId,JacksonUtils.writeAsString(igPreferSupplierUpDate));
        if (CollectionUtil.isNotEmpty(igPreferSupplierUpDate)){
            igPreferSupplierDataMapper.entity(IgPreferSupplier.class).updateAll(igPreferSupplierUpDate);
        }
        log.warn("询报价ID:{} 供应商数据排序结束", quotationInfoId);
    }

    public List<IgPreferSupplier> sortAndCollect(List<IgPreferSupplier> igPreferSupplierList1, Map<Long, Long> cooperIdMap ,Map<String, Object> finalResultMap) {
        if (igPreferSupplierList1 == null || igPreferSupplierList1.isEmpty()) {
            return Collections.emptyList();
        }
        if (cooperIdMap == null || finalResultMap == null) {
            return Collections.emptyList();
        }
        igPreferSupplierList1.sort((o1, o2) -> {
            // 比较 commissionRequire
            if (o1.getCommissionRequire() != null && o2.getCommissionRequire() != null) {
                int requireComparison = o2.getCommissionRequire().compareTo(o1.getCommissionRequire());
                if (requireComparison != 0) {
                    return requireComparison;
                }
                if (o1.getCommissionRequire().equals(o2.getCommissionRequire())) {
                    // 比较 finalResultValue
                    double resultValue1 = getFinalResultValue(o1, cooperIdMap, finalResultMap);
                    double resultValue2 = getFinalResultValue(o2, cooperIdMap, finalResultMap);
                    return  Double.compare(resultValue2, resultValue1);
                }
            } else {
                // 如果其中一个为 null，则返回 1 或 -1
                return (o2.getCommissionRequire() == null) ? -1 : 1;
            }
            // 比较 finalResultValue
            double resultValue1 = getFinalResultValue(o1, cooperIdMap, finalResultMap);
            double resultValue2 = getFinalResultValue(o2, cooperIdMap, finalResultMap);
            return  Double.compare(resultValue2, resultValue1);
        });
        return igPreferSupplierList1;

    }

    private double getFinalResultValue(IgPreferSupplier igPreferSupplier, Map<Long, Long> cooperIdMap ,Map<String, Object> finalResultMap) {
        Long cooperationId = igPreferSupplier.getCooperationId();
        Long companyId = cooperIdMap.get(cooperationId);
        Object o = finalResultMap.get(String.valueOf(companyId));
        if (companyId != null && o != null) {
           return Double.parseDouble(finalResultMap.get(String.valueOf(companyId)).toString());
        }
        return -999.0;
    }


    /**
     * 查询净费信息
     *
     * @param quotationInfoId
     * @param supplierId
     * @return
     */
    @Override
    public List<FeilvReturnDto> getSupplierNetFee(Long quotationInfoId, Long supplierId) {
        Map<Long, Map<String, String>> feilvReturnDtos = commissionFeilvService.getFeilvReturnDtos(quotationInfoId, supplierId);
        log.warn("询报价ID:{} 供应商ID:{} ,查询净费信息:{}", quotationInfoId,supplierId ,JacksonUtils.writeAsString(feilvReturnDtos));
//        Map<Long, Map<String, String>> feilvReturnDtos = new HashMap<>() ;
        List<FeilvReturnDto> qpQuotationInfoDtos = new ArrayList<>();
        if (MapUtil.isNotEmpty(feilvReturnDtos)) {
            for (Map.Entry<Long, Map<String, String>> entry : feilvReturnDtos.entrySet()) {
                FeilvReturnDto qpQuotationInfoDto = JSON.parseObject(JSON.toJSONString(entry.getValue()), FeilvReturnDto.class);
                qpQuotationInfoDtos.add(qpQuotationInfoDto);
            }
        }
        return qpQuotationInfoDtos;
    }

    /**
     * 方案保费
     *
     * @param quotationInfoId
     * @return
     */
    private List<QpQuotationAmountDto> getSchemePremiums(Long quotationInfoId) {
        //查询责任
        QQpQuotationDuty qQpQuotationDuty = QQpQuotationDuty.qp_quotation_duty;
        QQpQuotationConfig qQpQuotationConfig = QQpQuotationConfig.qp_quotation_config;
        QQpQuotation qpQuotation = QQpQuotation.qp_quotation;
        QQpQuotationInfo qQpQuotationInfo = QQpQuotationInfo.qp_quotation_info;
        List<QpQuotationAmountDto> quotationList = bqlQueryFactory.select(qQpQuotationConfig.id.as("id "), qQpQuotationDuty.price.amount.as("amount"))
                                                                  .from(qQpQuotationInfo).leftJoin(qpQuotation)
                                                                  .on(qQpQuotationInfo.id.eq(qpQuotation.quotation_info_id))
                                                                  .leftJoin(qQpQuotationConfig)
                                                                  .on(qQpQuotationConfig.quotation_id.eq(qpQuotation.id))
                                                                  .leftJoin(qQpQuotationDuty)
                                                                  .on(qQpQuotationConfig.id.eq(qQpQuotationDuty.quotation_config_id))
                                                                  .where(qQpQuotationInfo.id.eq(quotationInfoId)
                                                                                            .and(qpQuotation.relation.eq("0"))) //本人
                                                                  .findList(QpQuotationAmountDto.class);
        return quotationList;
    }


    /**
     * 更新特定供应商
     *
     * @param quotationInfoId
     * @param cooperationIds
     */
    @Override
    public Boolean updateSpecificSupplier(Long quotationInfoId, List<Long> cooperationIds, Integer type,Boolean initData) {
        log.warn("询报价Id :{} 更新特定供应商信息  供应商Id:{} 类型:{}", quotationInfoId, cooperationIds, type);
        Boolean result = redisLockUtils.tryLock("supplier", String.valueOf(quotationInfoId), 30);
        if (Boolean.FALSE.equals(result)) {
            log.warn("询报价ID:{}，供应商ID:{} 请重新选择特定的供应商", quotationInfoId, cooperationIds);
            throw new BusinessException(-1,"您操作的太快了, 请重新选择特定的供应商。");
        }
        if (Boolean.TRUE.equals(result)) {
            try {
                QQpQuotationInfo qpQuotationInfo = QQpQuotationInfo.qp_quotation_info;
                QpQuotationInfo quotationInfo = bqlQueryFactory.select(Expressions.stringPath("qp_quotation_info.*"))
                                                               .from(qpQuotationInfo)
                                                               .where(qpQuotationInfo.id.eq(quotationInfoId))
                                                               .findOne(QpQuotationInfo.class);
                QpQuotationInfo qpQuotationSave = quotationInfo.setSpecificSuppliers(cooperationIds);
                if (Boolean.FALSE.equals(initData)) {
                    quotationInfoDataMapper.entity(QpQuotationInfo.class).updateOne(qpQuotationSave, Boolean.TRUE);
                }
                List<IgPreferSupplier> igPreferSupplierList = new ArrayList<>();
                QIgPreferSupplier qIgPreferSupplier = QIgPreferSupplier.ig_prefer_supplier;
                List<IgPreferSupplier> igPreferSuppliers = bqlQueryFactory.select(Expressions.stringPath("ig_prefer_supplier.*"))
                                                                          .from(qIgPreferSupplier)
                                                                          .where(qIgPreferSupplier.quotation_info_id.eq(quotationInfoId).and(qIgPreferSupplier.type.eq("1")))
                                                                          .findList(IgPreferSupplier.class);
                List<Long> specificSuppliers = new ArrayList<>();
                if (CollectionUtil.isNotEmpty(igPreferSuppliers)) {
                    specificSuppliers = igPreferSuppliers.stream().map(IgPreferSupplier::getCooperationId)
                                                         .collect(Collectors.toList());
                }
                Integer quoteType = quotationInfo.getQuoteType();
                // 差集
                // 需要删除的供应商 数据库存在，但是在特定供应商中不存在
                // A 有 B 没有的元素
                List<Long> addList = new ArrayList<>(CollUtil.subtract(cooperationIds, specificSuppliers));
                List<Long> delList = new ArrayList<>(CollUtil.subtract(specificSuppliers, cooperationIds));
                if (CollUtil.isEmpty(delList) && CollUtil.isEmpty(addList)) {
                    log.warn("询报价ID:{} 无数据更新", quotationInfoId);
                    return Boolean.TRUE;
                }
                if (CollectionUtil.isNotEmpty(delList)) {
                    log.warn("询报价ID:{} 删除数据:{}", quotationInfoId, delList);
                    DataCondition<IgPreferSupplier> dataCondition = new DataCondition<>();
                    dataCondition.eq("quotation_info_id", quotationInfoId);
                    dataCondition.in("cooperation_id", delList);
                    dataCondition.eq("type", type);
                    // 删除之前的优选供应商
                    List<IgPreferSupplier> preferSupplierList = igPreferSupplierDataMapper.entity(IgPreferSupplier.class)
                                                                                          .select(dataCondition);

                    if (!CollectionUtils.isEmpty(preferSupplierList)) {
                        List<Long> ids = preferSupplierList.stream().map(IgPreferSupplier::getId)
                                                           .collect(Collectors.toList());
                        igPreferSupplierDataMapper.entity(IgPreferSupplier.class).deleteAll(ids);
                    }
                }
                //新增数据
                if (CollectionUtil.isNotEmpty(addList)) {
                    log.warn("询报价ID:{} 新增数据:{}", quotationInfoId, addList);
                    QIgCompanyCooperationInfo qIgCompanyCooperationInfo = QIgCompanyCooperationInfo.ig_company_cooperation_info;
                    List<IgCompanyCooperationInfo> igCompanyCooperationInfos = bqlQueryFactory.select(Expressions.stringPath("ig_company_cooperation_info.*"))
                                                                                              .from(qIgCompanyCooperationInfo)
                                                                                              .where(qIgCompanyCooperationInfo.id.in(addList))
                                                                                              .findList(IgCompanyCooperationInfo.class);

                    DataCondition<IgPreferSupplier> dataCondition = new DataCondition<>();
                    dataCondition.eq("quotation_info_id", quotationInfoId);
                    List<IgPreferSupplier> preferSupplierList = igPreferSupplierDataMapper.entity(IgPreferSupplier.class)
                                                                                          .select(dataCondition);


                    List<Long> companyIds = igCompanyCooperationInfos.stream()
                                                                     .map(IgCompanyCooperationInfo::getCompanyId)
                                                                     .collect(Collectors.toList());
                    QIgCompany qIgCompany = QIgCompany.ig_company;
                    List<IgCompany> igCompanyList = bqlQueryFactory.select(Expressions.stringPath("ig_company.*"))
                                                                   .from(qIgCompany).where(qIgCompany.id.in(companyIds))
                                                                   .findList(IgCompany.class);
                    Map<Long, String> igCompanyMap = igCompanyList.stream()
                                                                  .collect(Collectors.toMap(IgCompany::getId, igCompany -> igCompany.getCompanyName()));

                    int maxSortNum = 0;
                    if (CollectionUtil.isNotEmpty(preferSupplierList)) {
                        maxSortNum = preferSupplierList.stream().map(IgPreferSupplier::getSortNum).max(Integer::compare)
                                                       .orElse(0);
                    }
                    if (!CollectionUtils.isEmpty(igCompanyCooperationInfos)) {
                        for (IgCompanyCooperationInfo igCompanyCooperationInfo : igCompanyCooperationInfos) {
                            IgPreferSupplier igPreferSupplier = new IgPreferSupplier();
                            igPreferSupplier.setCooperationId(igCompanyCooperationInfo.getId());
                            igPreferSupplier.setQuotationInfoId(quotationInfoId);
                            igPreferSupplier.setName(igCompanyCooperationInfo.getCooperationName());
                            igPreferSupplier.setCompanyName(igCompanyMap.get(igCompanyCooperationInfo.getCompanyId()));
                            igPreferSupplier.setSortNum(maxSortNum++);
                            // 特定供应商
                            igPreferSupplier.setType(String.valueOf(type));
                            //区分雇主 ，医疗 意外
                            if (QuoteTypeEnum.EMPLOYER.getCode().equals(quoteType)) {
                                InsuranceEmployerCompanyEnum insuranceCompanyEnum = InsuranceEmployerCompanyEnum.findCompanyId(igCompanyCooperationInfo.getCompanyId());
                                if (insuranceCompanyEnum != null) {
                                    BigDecimal bigDecimal = insuranceCompanyEnum.getCommissionRate();
                                    // 佣金要求
                                    igPreferSupplier.setCommissionRequire(bigDecimal);
                                }
                            }
                            if (QuoteTypeEnum.TRADITION.getCode().equals(quoteType) || QuoteTypeEnum.GEEK_PLUS.getCode()
                                                                                                              .equals(quoteType)) {
                                InsuranceCompanyEnum insuranceCompanyEnum = InsuranceCompanyEnum.findCompanyId(igCompanyCooperationInfo.getCompanyId());
                                if (insuranceCompanyEnum != null) {
                                    BigDecimal bigDecimal = InsuranceCompanyEnum.calculateCommissionRate(insuranceCompanyEnum.getName(), insuranceCompanyEnum.getCommissionRate());
                                    // 佣金要求
                                    igPreferSupplier.setCommissionRequire(bigDecimal);
                                }
                            }
                            // 协议佣金率
                            log.warn("询报价ID:{} 插入数据:{}", quotationInfoId, JacksonUtils.writeAsString(igPreferSupplier));
                            igPreferSupplierList.add(igPreferSupplier);
                        }
                        igPreferSupplierDataMapper.entity(IgPreferSupplier.class).insertAll(igPreferSupplierList);
                        log.warn("询报价ID:{} 异步更新协议佣金:{}", quotationInfoId, addList);
                        // 异步更新 协议佣金
                        this.updateCommissionRequire(quotationInfoId, addList);
                    }
                }
                this.updatePreferSupplierSort(quotationInfoId);
                return Boolean.TRUE;
            } catch (Exception e) {
                log.error("询报价ID:{}，供应商ID:{} 更新供应商数据异常:{}", quotationInfoId, cooperationIds, e.getMessage(),e);
            } finally {
                log.info("更新特定供应商");
                redisLockUtils.unlock("supplier", String.valueOf(quotationInfoId));
            }
        }
        return Boolean.FALSE;
    }

    /**
     * 修复后协议佣金
     *
     * @param quotationInfoIds
     */
    @Override
    public void updateCommissionRate(List<Long> quotationInfoIds) {
        log.warn("询报价ID:{}，接口更新佣金要求数据开始 :{}", quotationInfoIds);
        for (Long quotationInfoId :quotationInfoIds) {
            DataCondition<IgPreferSupplier> dataCondition = new DataCondition<>();
            dataCondition.eq("quotation_info_id", quotationInfoId);
            List<IgPreferSupplier> preferSupplierList = igPreferSupplierDataMapper.entity(IgPreferSupplier.class)
                                                                                  .select(dataCondition);
            if (CollectionUtil.isNotEmpty(preferSupplierList)){
                log.warn("询报价ID:{}，接口更新佣金要求数据 :{}", quotationInfoId, JacksonUtils.writeAsString(preferSupplierList));
                List<Long> coperationIds = preferSupplierList.stream().map(IgPreferSupplier::getCooperationId)
                                                             .collect(Collectors.toList());
                log.warn("询报价ID:{}，接口更新供应商ID :{}", quotationInfoId, JacksonUtils.writeAsString(coperationIds));
                this.updateCommissionRequire(quotationInfoId, coperationIds);
            }
        }
    }

    /**
     * 获取供应商信息
     *
     * @return
     */
    @Override
    public List<CompanySupplierInfoDto> getSupplierInfo() {
        QIgCompany qIgCompany = QIgCompany.ig_company;
        QIgCompanyCooperationInfo qIgCompanyCooperationInfo  =QIgCompanyCooperationInfo.ig_company_cooperation_info;
        List<CompanySupplierInfoDto> igCompanyList = bqlQueryFactory.select(qIgCompanyCooperationInfo.id.as("cooperation_info_id"),
                                                                            qIgCompanyCooperationInfo.cooperation_name.as("cooperation_name"),
                                                                            qIgCompanyCooperationInfo.company_id.as("company_id"),
                                                                            qIgCompany.company_name.as("company_name")
                                                                    )
                                                       .from(qIgCompany)
                                                       .join(qIgCompanyCooperationInfo).on(qIgCompany.id.eq(qIgCompanyCooperationInfo.company_id))
                                                       .findList(true,CompanySupplierInfoDto.class);
        return igCompanyList;
    }


    /**
     * 手动出发供应商推荐逻辑
     *
     * @param quotationInfoId
     */
    public void createPreferSupplier(Long quotationInfoId) {
        log.warn("询报价ID:{} 触发推荐供应商逻辑",quotationInfoId);
        BigDecimal bigDecimal = this.matchPremiumScale(quotationInfoId);
        log.warn("询报价ID:{}匹配最高保额:{}", quotationInfoId,bigDecimal);
        BigDecimal predict = this.predict(quotationInfoId);
        log.warn("询报价ID:{}计算赔付率:{}", quotationInfoId,predict);
        QpQuotationInfo qpQuotationInfo = new QpQuotationInfo();
        qpQuotationInfo.setId(quotationInfoId);
        qpQuotationInfo.setPremiumScale(String.valueOf(bigDecimal));
        quotationInfoMapper.updateOne(qpQuotationInfo);
        List<SupplierInfoDto> supplierInfoDtoList = this.matchSupplier(quotationInfoId, null);
        log.warn("询报价ID:{}推荐供应商信息:{}",quotationInfoId,JacksonUtils.writeAsString(supplierInfoDtoList.stream().map(SupplierInfoDto::getCooperationInfoId).collect(Collectors.toList())));
        if (!CollectionUtils.isEmpty(supplierInfoDtoList)) {
            DataCondition<IgPreferSupplier> dataCondition = new DataCondition<>();
            dataCondition.eq("quotation_info_id", quotationInfoId);
            // 删除之前的优选供应商
            List<IgPreferSupplier> preferSupplierList = igPreferSupplierDataMapper.entity(IgPreferSupplier.class)
                                                                                  .select(dataCondition);
            if (!CollectionUtils.isEmpty(preferSupplierList)) {
                List<Long> ids = preferSupplierList.stream().map(IgPreferSupplier::getId).collect(Collectors.toList());
                igPreferSupplierDataMapper.entity(IgPreferSupplier.class).deleteAll(ids);
            }

            List<Long> cooperationInfoIdList = supplierInfoDtoList.stream().map(SupplierInfoDto::getCooperationInfoId)
                                                                  .collect(Collectors.toList());
            SupplierReq supplierRe = new SupplierReq();
            supplierRe.setQuotationInfoId(quotationInfoId);
            supplierRe.setSpecificSupplierIds(cooperationInfoIdList);
            supplierRe.setType(0);
            supplierRe.setInitData(Boolean.TRUE);
            this.saveSupplier(supplierRe);
            log.warn("询报价ID:{} 保存供应商信息:{}", quotationInfoId, JacksonUtils.writeAsString(supplierInfoDtoList.stream()
                                                                                                                     .map(SupplierInfoDto::getCooperationInfoId)
                                                                                                                     .collect(Collectors.toList())));
        }
    }
}


