package com.insgeek.business.quote.quotation.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.HashUtil;
import com.google.common.collect.Lists;
import com.insgeek.boot.commons.collections.StreamKit;
import com.insgeek.boot.commons.exception.InsgeekException;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.boot.commons.json.ObjectUtil;
import com.insgeek.boot.web.context.IdentityContext;
import com.insgeek.boot.web.pojo.dto.CurrencyAmount;
import com.insgeek.boot.web.util.CurrencyAmountUtil;
import com.insgeek.boot.web.util.MessageUtil;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.backend.Enums.QuoteTypeEnum;
import com.insgeek.business.quote.backend.aggservice.impl.CheckListServiceImpl;
import com.insgeek.business.quote.backend.dto.quote.QuoteDutyVo;
import com.insgeek.business.quote.backend.dto.quote.QuoteDutysDto;
import com.insgeek.business.quote.backend.dto.quote.QuoteDutysResult;
import com.insgeek.business.quote.backend.exception.QuoteDutyErrorCode;
import com.insgeek.business.quote.backend.exception.QuoteErrorCode;
import com.insgeek.business.quote.common.dao.condition.DefaultBaseCondition;
import com.insgeek.business.quote.common.dao.condition.interfaces.BaseCondition;
import com.insgeek.business.quote.common.dto.vo.SpecialVo;
import com.insgeek.business.quote.common.enums.PayPeriodPriceEnum;
import com.insgeek.business.quote.common.enums.PayTypeEnum;
import com.insgeek.business.quote.common.enums.dict.Property;
import com.insgeek.business.quote.common.enums.dict.RenewFlagEnum;
import com.insgeek.business.quote.common.exception.BusinessException;
import com.insgeek.business.quote.common.feign.api.QuotationDutyFeginService;
import com.insgeek.business.quote.common.service.DutyCommonService;
import com.insgeek.business.quote.common.service.QpQuoteApproveService;
import com.insgeek.business.quote.common.service.impl.RedisManageServicesImpl;
import com.insgeek.business.quote.common.service.impl.RuleServices;
import com.insgeek.business.quote.common.utils.CommonDataUtil;
import com.insgeek.business.quote.common.utils.ThreadLocalUtil;
import com.insgeek.business.quote.feilv.contribution.ContributionFeilvService;
import com.insgeek.business.quote.frontend.aggservice.impl.VersionAggServiceImpl;
import com.insgeek.business.quote.frontend.dto.manage.QpQuoteDutyDto;
import com.insgeek.business.quote.frontend.dto.manage.QpQuoteDutySplitDto;
import com.insgeek.business.quote.frontend.dto.version.VersionDto;
import com.insgeek.business.quote.frontend.enums.EntityKeyEnum;
import com.insgeek.business.quote.frontend.enums.OperationEnum;
import com.insgeek.business.quote.quotation.dao.constant.QpQuotationDutyConst;
import com.insgeek.business.quote.quotation.dao.constant.QpQuotationDutySplitConst;
import com.insgeek.business.quote.quotation.dao.mapper.QuotationDutyMapper;
import com.insgeek.business.quote.quotation.dao.mapper.QuotationDutySplitMapper;
import com.insgeek.business.quote.quotation.dto.QpQuotationConfigDTO;
import com.insgeek.business.quote.quotation.dto.QuotationDutyGroup;
import com.insgeek.business.quote.quotation.dto.SpecialRecommendParamDTO;
import com.insgeek.business.quote.quotation.service.QuotationConfigService;
import com.insgeek.business.quote.quotation.service.QuotationDutyService;
import com.insgeek.business.quote.quotation.service.SpecialService;
import com.insgeek.business.quote.quotation.service.VersionService;
import com.insgeek.business.quote.sgp.config.SGDutyConfig;
import com.insgeek.business.quote.sgp.service.SingaporeDutyExcelBasicService;
import com.insgeek.business.quote.util.DataProcessing;
import com.insgeek.components.orm.model.impl.bql.BqlMapper;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.insurance.meta.utils.context.IdentityUtil;
import com.insgeek.protocol.data.client.dto.*;
import com.insgeek.protocol.data.client.entity.*;
import com.insgeek.protocol.insurance.client.CopyDutyRuleInstanceClient;
import com.insgeek.protocol.insurance.client.DutyClient;
import com.insgeek.protocol.insurance.client.SpecialRulesClient;
import com.insgeek.protocol.insurance.dto.config.CopyDutyRuleInsuranceDto;
import com.insgeek.protocol.insurance.dto.duty.request.ProductRuleDutyDTO;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontProductDutyInstanceDTO;
import com.insgeek.protocol.insurance.dto.product.response.IgProductRO;
import com.insgeek.protocol.insurance.dto.special.instance.ListParamDTO;
import com.insgeek.protocol.insurance.vo.specialrules.special.vo.SpecialParamDTO;
import com.insgeek.protocol.platform.common.client.PolicyCommissionRulesClient;
import com.insgeek.protocol.platform.common.dto.entity.IgPolicyCommissionRules;
import com.insgeek.protocol.platform.common.dto.entity.IgProduct;
import com.insgeek.protocol.platform.common.dto.entity.IgProductType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * @ClassName: QuoteDutyServiceImpl
 * @Description: 保险责任操作
 * @Author: YYY
 * @Date: 2022/7/28 21:26
 **/
@SuppressWarnings("AliControlFlowStatementWithoutBraces")
@Slf4j
@Service
public class QuotationDutyServiceImpl implements QuotationDutyService {

    @Autowired
    private QuotationDutyMapper quotationDutyMapper;

    @Autowired
    private QuotationDutySplitMapper quotationDutySplitMapper;

    @SuppressWarnings("all")
    @Autowired
    private DataMapper<QpQuotationConfig> quotationConfigDataMapper;

    @Autowired
    private DataMapper<QpQuotation> quotationDataMapper;

    @Autowired
    private DataMapper<QpQuotationInfo> quotationInfoDataMapper;

    @SuppressWarnings("all")
    @Autowired
    private DataMapper<QpQuotation> quoteDataMapper;

    @Autowired
    private CheckListServiceImpl checkListService;

    @Resource
    private VersionAggServiceImpl versionAggService;

    @Resource
    private RedisTemplate redisTemplate;

    @SuppressWarnings("all")
    @Resource
    private DataMapper<IgProduct> productDataMapper;

    @SuppressWarnings("all")
    @Resource
    private DataMapper<IgProductType> productTypeDataMapper;

    @SuppressWarnings("all")
    @Resource
    private DataMapper<QpProduct> qpProductDataMapper;

    @Autowired
    private SpecialRulesClient specialRulesClient;

    @Resource
    private QuotationConfigService quotationConfigServic;
    @Resource
    private RuleServices ruleServices;

    @Resource
    private CopyDutyRuleInstanceClient copyDutyRuleInstanceClient;

    @Resource
    private DataMapper<QpQuotationDuty> qpQuotationDutyDataMapper;

    @Resource
    private DataMapper<QpQuotationDutySplit> qpQuotationDutySplitDataMapper;

    @Autowired
    private DutyClient dutyClient;

    @Autowired
    private SGDutyConfig sgDutyConfig;

    @Resource
    private QuotationDutyFeginService quotationDutyFeginService;

    @Autowired
    private SpecialService specialService;
    @Autowired
    private BqlMapper<QpQuotationInfo> quotationInfoBqlMapper;

    @Autowired
    private QpQuoteApproveService qpQuoteApproveService;

    @Resource
    private PolicyCommissionRulesClient policyCommissionRulesClient;

    @Autowired
    ContributionFeilvService contributionFeilvService;

    @Resource
    private DataMapper<QpQuotationConfig> qpQuoteConfigDataMapper;
    @Resource
    private DataMapper<QpQuotation> qpQuotationDataMapper;
    @Autowired
    private DutyCommonService dutyCommonService;

    @Autowired
    private QuotationDutyService quotationDutyService;

    @Autowired
    private SingaporeDutyExcelBasicService singaporeDutyExcelBasicService;

    @Value("${quote.no-employer-special-user-id}")
    private String noEmployerSpecialUserId;


    @Override
    public List<Long> saveDutys(Long configId, List<QuotationDutyDTO> dutys, SaveDutyDto saveDutyDto) {
        List<Long> result = Lists.newArrayList();
        //处理数据
        handleData(dutys);
        //1.检验保险责任数据
        checkDutys(dutys);

        //2.将责任分拆为增删改查
        List<QpQuotationDuty> dutyList = quotationDutyMapper.selectListByEntity(new QpQuotationDuty().setQuotationConfigId(configId), false);
        QuotationDutyGroup dutyGroup = QuotationDutyGroup.obtainDutyGroup(transformation(dutyList), dutys);

        //3.责任保存、修改、删除
        result.addAll(Objects.requireNonNull(createDuty(configId, dutyGroup.getAddDutys(), saveDutyDto)));
        result.addAll(Objects.requireNonNull(updateDuty(configId, dutyGroup.getModifyDutys())));
        deleteDuty(configId, dutyGroup.getRemoveDutys());

        //99.其他后置处理
        otherHandle(configId, dutyGroup, dutys, saveDutyDto);

        // 价值贡献率计算
        contributionFeilvService.getContributionFeilv(configId);

        return result;
    }

    /**
     * 检验责任 数据是否合法
     *
     * @param dutys 责任
     */
    private void handleData(List<QuotationDutyDTO> dutys) {
        if (CollectionUtils.isEmpty(dutys)) {
            return;
        }
        //责任类型重复及非空(business_type)
        dutys.forEach(item -> {
            if ((Objects.isNull(item.getBusinessType()) || Objects.equals("defaultBusiType", item.getBusinessType())) && !Objects.isNull(item.getType())) {
                item.setBusinessType(item.getType());
            }
            if (StringUtils.isBlank(item.getTraceId())) {
                item.setTraceId(UUID.randomUUID().toString());
            }
        });
    }

    @Override
    public Map<String, List<QuoteDutysDto>> queryQuoteDutys() {
        Map<String, List<QuoteDutysDto>> quoteDutyCache = getQuoteDutyCache();
        return quoteDutyCache;
    }

    @Override
    public QuoteDutysResult getQuoteDuty(String type, Integer property) {
        Map<String, List<QuoteDutysDto>> typeAndDutysDtos = queryQuoteDutys();
        if (!typeAndDutysDtos.containsKey(type)) {
            return null;
        }
        List<QuoteDutysDto> quoteDutysDtos = typeAndDutysDtos.get(type);
        for (QuoteDutysDto quoteDutysDto : quoteDutysDtos) {
            if (Objects.equals(property, quoteDutysDto.getProperty())) {
                return new QuoteDutysResult(type, quoteDutysDto.getProductId(), property, quoteDutysDto.getQpProductId());
            }
        }
        return null;
    }


    @Override
    public List<QuotationDutyDTO> getDutysByDutyIds(List<Long> dutyIds) {
        if (ObjectUtils.isEmpty(dutyIds)) {
            return new ArrayList<>();
        }
        DataCondition<QpQuotationDuty> dataCondition = new DataCondition<>();
        dataCondition.in("id", dutyIds);
        List<QpQuotationDuty> qpQuoteDuties = qpQuotationDutyDataMapper.entity(QpQuotationDuty.class).select(dataCondition, true);
        List<QuotationDutyDTO> quoteDutyDTOS = ObjectUtil.convertList(qpQuoteDuties, QuotationDutyDTO.class);

        DataCondition<QpQuotationDutySplit> dutySplitDataCondition = new DataCondition<>();
        dutySplitDataCondition.in("quotation_duty_id", dutyIds);
        List<QpQuotationDutySplit> dutySplits = qpQuotationDutySplitDataMapper.entity(QpQuotationDutySplit.class).select(dutySplitDataCondition, true);
        Map<Long, List<QpQuotationDutySplit>> longListMap = StreamKit.groupingBy(dutySplits, QpQuotationDutySplit::getQuotationDutyId);
        quoteDutyDTOS.forEach(item -> {
            List<QpQuotationDutySplit> qpQuotationDutySplits = longListMap.get(item.getId());
            item.setDutySplit(qpQuotationDutySplits);
        });
        return quoteDutyDTOS;
    }

    /**
     * @return 获取redis缓存
     */
    private Map<String, List<QuoteDutysDto>> getQuoteDutyCache() {
        Map<String, List<QuoteDutysDto>> result = (Map<String, List<QuoteDutysDto>>) redisTemplate.opsForValue().get(RedisManageServicesImpl.REDIS_QUOTE_PRODUCT);
        if (CollectionUtils.isEmpty(result) || result.size() <= 0) {
            result = setQuoteDutyCache();
        }
        return result;
    }

    /**
     * 从核心表获取报价产品责任
     *
     * @return
     */
    private Map<String, List<QuoteDutysDto>> setQuoteDutyCache() {
        Map<String, List<QuoteDutysDto>> result = new HashMap<>();
        //查询上架的报价产品
        DataCondition<QpProduct> condition_qp = new DataCondition<>();
        condition_qp.in("status", 1);
        List<QpProduct> qpProductList = qpProductDataMapper.entity(QpProduct.class).select(condition_qp, true);
        if (CollectionUtils.isEmpty(qpProductList)) {
            return result;
        }
        List<Long> qpProductIds = qpProductList.stream().map(QpProduct::getProductId).collect(Collectors.toList());

        //查询核心的报价产品
        DataCondition<IgProduct> condition = new DataCondition<>();
        condition.in("platform", 2);
        List<IgProduct> productListAll = productDataMapper.entity(IgProduct.class).select(condition, true);
        if (CollectionUtils.isEmpty(productListAll)) {
            return result;
        }
        List<IgProduct> productList = productListAll.stream().filter(v -> qpProductIds.contains(v.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(productList)) {
            return result;
        }

        //获取核心type类型
        DataCondition<IgProductType> condition_type = new DataCondition<>();
        List<IgProductType> productTypeList = productTypeDataMapper.entity(IgProductType.class).select(condition_type, true);
        Map<Long, IgProductType> typeIdAndProductType = productTypeList.stream().collect(Collectors.toMap(IgProductType::getId, v -> v, (v1, v2) -> v1));
        productList.stream().forEach(product -> {
            IgProductType igProductType = typeIdAndProductType.get(product.getProductTypeId());
            if (!Objects.isNull(igProductType)) {
                product.setType(igProductType.getType());
            }
            QuoteDutysDto dutysDto = new QuoteDutysDto(igProductType.getType(), product.getId(), Integer.valueOf(product.getProperty()), 0L);
            if (!result.containsKey(dutysDto.getType())) {
                result.put(dutysDto.getType(), Lists.newArrayList(dutysDto));
            } else {
                result.get(dutysDto.getType()).add(dutysDto);
            }
        });

        redisTemplate.opsForValue().set(RedisManageServicesImpl.REDIS_QUOTE_PRODUCT, result, 365, TimeUnit.DAYS);
        return result;
    }

    /**
     * 转换为dto
     *
     * @param dutyList 责任
     * @return
     */
    private List<QuotationDutyDTO> transformation(List<QpQuotationDuty> dutyList) {
        List<QuotationDutyDTO> result = Lists.newArrayList();
        if (dutyList.isEmpty()) {
            return result;
        }
        List dutyIds = dutyList.stream().map(QpQuotationDuty::getId).map(String::valueOf).collect(Collectors.toList());
        // 是否存在分段产品
        DefaultBaseCondition<QpQuotationDutySplit> dutySplitCondition = new DefaultBaseCondition<>();
        dutySplitCondition.in(QpQuotationDutySplitConst.F_QDI, dutyIds);

        List<QpQuotationDutySplit> list = quotationDutySplitMapper.selectListByCondition(dutySplitCondition);
        Map<Long, List<QpQuotationDutySplit>> dutySplit = list.stream().collect(Collectors.groupingBy(QpQuotationDutySplit::getQuotationDutyId));
        dutyList.stream().forEach(duty -> {
            QuotationDutyDTO dutyDTO = ObjectUtil.covertObject(duty, QuotationDutyDTO.class);
            if (dutySplit.containsKey(duty.getId())) {
                dutyDTO.setDutySplit(dutySplit.get(duty.getId()));
            }
            result.add(dutyDTO);
        });
        return result;
    }

    /**
     * 检验责任 数据是否合法
     *
     * @param dutys 责任
     */
    private void checkDutys(List<QuotationDutyDTO> dutys) {
        //1.不能为null
        if (CollectionUtils.isEmpty(dutys)) {
            CommonDataUtil.businessException(QuoteErrorCode.QUOTE_DUTY_NULL);
        }
        Map<String, List<QpQuotationDuty>> dutyTypes = dutys.stream().filter(v -> !Objects.isNull(v.getBusinessType())).collect(Collectors.groupingBy(QpQuotationDuty::getBusinessType));
        // 判断哪些产品无需进行校验，只要产品中有一个责任类型为无需校验的责任类型，则不进行后续校验
        List<String> nonVaildRepeatProductList = sgDutyConfig.getNonVaildRepeatProductList();
        List<String> distinctTypes = dutys.stream().map(QuotationDutyDTO::getProductTypeName).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        // 如果distinctTypes为空，则需要根据pid去查询所有的productTypeName
        if (CollectionUtil.isEmpty(distinctTypes)) {
            List<Long> distinctPidList = dutys.stream().map(QpQuotationDuty::getProductId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            distinctTypes = singaporeDutyExcelBasicService.queryProductTypeNameByPidList(distinctPidList);
        }
        if (CollectionUtil.isNotEmpty(distinctTypes) && distinctTypes.stream().anyMatch(nonVaildRepeatProductList::contains)) {
            return;
        }

        if (CollectionUtils.isEmpty(dutyTypes) || dutys.size() != dutyTypes.size()) {
            CommonDataUtil.businessException(QuoteDutyErrorCode.QUOTE_DUTY_REPEAT);
        }
        //3.产品责任id（priductId）重复及非空
        Map<Long, List<QpQuotationDuty>> dutyPriductIs = dutys.stream().filter(v -> !Objects.isNull(v.getProductId())).collect(Collectors.groupingBy(QpQuotationDuty::getProductId));
        if (CollectionUtils.isEmpty(dutyPriductIs) || dutys.size() != dutyPriductIs.size()) {
            CommonDataUtil.businessException(QuoteDutyErrorCode.QUOTE_DUTY_REPEAT);
        }

        // 检查门诊和住院是否同时存在
        checkListService.checkProductContent(dutys.stream().map(QuotationDutyDTO::getProductId).collect(Collectors.toList()));

    }

    /**
     * 处理创建的保险责任
     *
     * @param configId    配置id
     * @param dutys       责任
     * @param saveDutyDto flag
     */
    @Override
    public List<Long> createDuty(Long configId, List<QuotationDutyDTO> dutys, SaveDutyDto saveDutyDto) {
        if (CollectionUtils.isEmpty(dutys)) {
            return Lists.newArrayList();
        }
        List<QpQuotationDuty> qpQuoteDuties = ObjectUtil.convertList(dutys, QpQuotationDuty.class);
        qpQuoteDuties.forEach(item -> {
            item.setQuotationConfigId(configId);
            item.setId(null);// 解决 bvId 插入 0 的问题
            if(item.getCostPrice()==null || item.getCostPrice().getAmount() == null){
                item.setCostPrice(null);
            }
            if(item.getPrice()==null || item.getPrice().getAmount() == null){
                item.setPrice(null);
            }
        });
        List<QpQuotationDutySplit> qpQuoteDutySplits = new ArrayList<>();
        // 创建对象
        log.info("qpQuoteDuties insertList:{}", JacksonUtils.writeAsString(qpQuoteDuties));
        List<String> dutyIds = quotationDutyMapper.insertList(qpQuoteDuties);
        // 创建分段责任信息
        for (int i = 0; i < dutyIds.size(); i++) {
            final Long quoteDutyId = Long.parseLong(dutyIds.get(i));
            qpQuoteDuties.get(i).setId(quoteDutyId);
            dutys.get(i).setId(quoteDutyId);
            // 是否是分段产品
            Integer property = dutys.get(i).getProperty();
            if (property.equals(Property.SPLIT.getValue()) || property.equals(Property.SUBSECTION_ONCE_DEDUCTION.getValue()) || property.equals(Property.PROJECT.getValue())) {
                List<QpQuotationDutySplit> quoteDutySplit = dutys.get(i).getDutySplit();
                qpQuoteDutySplits.addAll(quoteDutySplit.stream().map(v -> v.setQuotationDutyId(quoteDutyId)).collect(Collectors.toList()));
                //project不为空说明是费用项目数据
                if (!CollectionUtils.isEmpty(quoteDutySplit) && quoteDutySplit.get(0).getProject() != null) {
                    updateDutyWithProject(Long.parseLong(dutyIds.get(i)), quoteDutySplit);
                }
            }
        }
        if (!qpQuoteDutySplits.isEmpty() && qpQuoteDutySplits.size() != 0) {
            quotationDutySplitMapper.insertList(qpQuoteDutySplits);
        }
        log.info("default specialFlag={}", JacksonUtils.writeAsString(saveDutyDto));
        if (saveDutyDto.getSaveSpecialFlag()) {
            //部分医疗责任保存默认特约
            saveDefaultSpecialRuleForMedicalDuty(configId, qpQuoteDuties.stream().collect(Collectors.toMap(QpQuotationDuty::getBusinessType, QpQuotationDuty::getId)));
        }

        //产品工厂
        handleProductParam(configId, dutyIds, saveDutyDto);

        return dutyIds.stream().map(Long::parseLong).collect(Collectors.toList());
    }

    /**
     * 处理产品工厂的复制的准备数据
     *
     * @param configId    配置
     * @param dutyIds     责任
     * @param saveDutyDto 参数
     */
    private void handleProductParam(Long configId, List<String> dutyIds, SaveDutyDto saveDutyDto) {
        if (saveDutyDto.getSaveProductFactoryFormInsuranceToQuote()) {
            log.info("产品工厂-》{} saveDutyDto={}", configId, JacksonUtils.writeAsString(saveDutyDto));
            CopyProductFactoryData copyProductFactoryData = saveDutyDto.getCopyProductFactoryData();
            List<ProductQuoteInsuranceDto> productQuoteInsuranceDtos = copyProductFactoryData.getProductQuoteInsuranceDtos();

            BaseCondition<QpQuotationDuty> baseCondition = new DefaultBaseCondition<>();
            baseCondition.in("id", dutyIds);
            List<QpQuotationDuty> dutysNew = quotationDutyMapper.selectListByCondition(baseCondition, false);
            Map<Long, QpQuotationDuty> proIdAndDuty = dutysNew.stream().collect(Collectors.toMap(QpQuotationDuty::getProductId, v -> v));
            for (ProductQuoteInsuranceDto productQuoteInsuranceDto : productQuoteInsuranceDtos) {
                if (proIdAndDuty.containsKey(productQuoteInsuranceDto.getProductId())) {
                    productQuoteInsuranceDto.setQuoteDutyId(proIdAndDuty.get(productQuoteInsuranceDto.getProductId()).getId());
                    List<ProductQuoteInsuranceDetailDto> detailDtos = productQuoteInsuranceDto.getDetailDtos();
                    if (!CollectionUtils.isEmpty(detailDtos)) {
                        List<QpQuotationDutySplit> dutySplitList = quotationDutySplitMapper.selectListByEntity(new QpQuotationDutySplit().setQuotationDutyId(productQuoteInsuranceDto.getQuoteDutyId()), false);
                        if (CollectionUtils.isEmpty(dutySplitList)) {
                            continue;
                        }
                        int size = dutySplitList.size();
                        for (int i = 0; i < detailDtos.size(); i++) {
                            if (i >= size) {
                                break;
                            }
                            QpQuotationDutySplit qpQuoteDutySplit = dutySplitList.get(i);
                            detailDtos.get(i).setQuoteDutyDetailId(qpQuoteDutySplit.getId());
                        }
                    }
                }
            }
        }
    }

    /**
     * 保存特约
     *
     * @param configId             配置id
     * @param productTypeMapDutyId BusinessType dutyid
     */
    private void saveDefaultSpecialRuleForMedicalDuty(Long configId, Map<String, Long> productTypeMapDutyId) {
        Long quoteId = quotationConfigDataMapper.entity(QpQuotationConfig.class).selectOne(configId, true).getQuotationId();
        //查询特约模版
        Map<String, List<SpecialVo>> specialRuleTemplate = ruleServices.getAllSpecialRuleTemplateMap();

        //查询责任和默认特约映射关系
        SpecialParamDTO specialParamDTO = initSpecialParamDTO(quoteId);
        List<Map<String, Object>> params = new ArrayList<>();
        for (Map.Entry<String, Long> typeDutyIdEntry : productTypeMapDutyId.entrySet()) {
            if (specialRuleTemplate.containsKey(typeDutyIdEntry.getKey()) && !CollectionUtils.isEmpty(specialRuleTemplate.get(typeDutyIdEntry.getKey()))) {
                for (SpecialVo specialVo : specialRuleTemplate.get(typeDutyIdEntry.getKey())) {
                    Map<String, Object> param = new HashMap<>();
                    param.put("business_type", specialVo.getKey());
                    param.put("name", specialVo.getSpecialName());
                    param.put("quote_duty_id", typeDutyIdEntry.getValue());
                    param.put("template_id", specialVo.getTemplateId());
                    params.add(param);
                }
            }
        }
        specialParamDTO.setParams(params);
        log.info("保存默认特约入参-saveDefaltSpecialParam{}:", JacksonUtils.writeAsString(specialParamDTO));
        //保存默认特约
        ResponseVO<Map<String, Object>> saveSpecialResponseVO = specialRulesClient.saveSpecial(specialParamDTO);
        if (!saveSpecialResponseVO.ok()) {
            log.error("保存默认特约失败-saveDefaltSpecialResult:{}", saveSpecialResponseVO.getMessage());
        }
    }

    /**
     * 初始化 特约保存参数
     *
     * @param quoteId 报价方案id
     * @return 1
     */
    private SpecialParamDTO initSpecialParamDTO(Long quoteId) {
        SpecialParamDTO specialParamDTO = new SpecialParamDTO();
        specialParamDTO.setDataId(quoteId);
        specialParamDTO.setDataType(1);
        specialParamDTO.setPlatform("3");
        specialParamDTO.setSpecialPart(2);
        specialParamDTO.setOldRuleList(new ArrayList<>());
        return specialParamDTO;
    }


    /**
     * 处理责任下拓展（费用项目）
     *
     * @param dutyId             责任id
     * @param quoteDutySplitList 责任下拓展
     */
    public void updateDutyWithProject(Long dutyId, List<QpQuotationDutySplit> quoteDutySplitList) {
        QpQuotationDuty qpQuoteDuty = new QpQuotationDuty();
        qpQuoteDuty.setId(dutyId);
        CurrencyAmount sumAmount = null;
        CurrencyAmount maxPayment = null;
        CurrencyAmount minDeduction = null;
        CurrencyAmount onceQuota = null;
        for (QpQuotationDutySplit v : quoteDutySplitList) {
            if (!Objects.isNull(v.getAmount())) {
                if (Objects.isNull(sumAmount)) {
                    sumAmount = v.getAmount();
                } else {
                    sumAmount =CurrencyAmountUtil.sum(sumAmount,v.getAmount());
                }
            }

            if (!Objects.isNull(v.getPayment())) {
                if (Objects.isNull(maxPayment) || v.getPayment()> maxPayment.getAmount().doubleValue()) {
                    maxPayment = CurrencyAmount.builder().amount(BigDecimal.valueOf(v.getPayment())).build();
                }
            }

            if (!Objects.isNull(v.getOnceQuota())) {
                if (Objects.isNull(onceQuota)) {
                    onceQuota = v.getOnceQuota();
                } else {
                    onceQuota =CurrencyAmountUtil.sum(onceQuota,v.getOnceQuota());
                }
            }

            if (!Objects.isNull(v.getDeduction()) || !Objects.isNull(v.getOnceDeduction())) {
                CurrencyAmount deduction = v.getDeduction();
                if (Objects.isNull(deduction)) {
                    deduction = CurrencyAmount.builder().build();
                }
                CurrencyAmount onceDeduction = v.getOnceDeduction();
                if (Objects.isNull(onceDeduction)) {
                    onceDeduction = CurrencyAmount.builder().build();
                }
                int beForeMax = Math.max(deduction.getAmount() == null ? 0 : deduction.getAmount().intValue(), onceDeduction.getAmount() == null ? 0 : onceDeduction.getAmount().intValue());
                if (!Objects.isNull(minDeduction)) {
                    beForeMax = Math.max(beForeMax, minDeduction.getAmount() == null ? 0 : minDeduction.getAmount().intValue());
                }
                minDeduction = CurrencyAmount.builder().amount(BigDecimal.valueOf(beForeMax)).currency(sumAmount == null ? null : sumAmount.getCurrency()).build() ;
            }
        }

        qpQuoteDuty.setOnceQuota(onceQuota);
        qpQuoteDuty.setAmount(sumAmount);
        qpQuoteDuty.setPayment(maxPayment == null ? null : maxPayment.getAmount().intValue());
        qpQuoteDuty.setDeduction(minDeduction);
        quotationDutyMapper.updateOne(qpQuoteDuty);
    }

    /**
     * 处理修改的保险责任
     *
     * @param configId 配置id
     * @param dutys    责任
     */
    private List<Long> updateDuty(Long configId, List<QuotationDutyDTO> dutys) {
        if (CollectionUtils.isEmpty(dutys)) {
            return Lists.newArrayList();
        }
        List<QpQuotationDuty> qpQuoteDuties = ObjectUtil.convertList(dutys, QpQuotationDuty.class);
        qpQuoteDuties.forEach(item -> item.setQuotationConfigId(configId));
        log.info("qpQuoteDuties updateList:{}", JacksonUtils.writeAsString(qpQuoteDuties));
        List<QuoteDutyVo> quoteDutyVos = ObjectUtil.convertList(qpQuoteDuties, QuoteDutyVo.class);

        List<Map<String, Object>> personList = DataProcessing.convertToCamelCase(quoteDutyVos);

        log.info("qpQuoteDuties personList:{}", JacksonUtils.writeAsString(personList));
        qpQuotationDutyDataMapper.entity(QpQuotationDuty.class).batchSuperUpdate(personList, true);
        // 更新分段责任信息,先删后增
        List<Long> dutyIds = dutys.stream().map(QuotationDutyDTO::getId).collect(Collectors.toList());
        List<QpQuotationDutySplit> qpQuoteDutySplits = new ArrayList<>();
        List<QpQuotationDutySplit> removeQpQuoteDutySplitList = new ArrayList<>();
        for (int i = 0; i < dutyIds.size(); i++) {
            Long quoteDutyId = dutyIds.get(i);
            List<QpQuotationDutySplit> removeQpQuoteDutySplits = quotationDutySplitMapper.selectListByEntity(new QpQuotationDutySplit().setQuotationDutyId(quoteDutyId));
            removeQpQuoteDutySplitList.addAll(removeQpQuoteDutySplits);
            // 是否是分段产品
            Integer property = dutys.get(i).getProperty();
            if (property.equals(Property.SPLIT.getValue()) || property.equals(Property.SUBSECTION_ONCE_DEDUCTION.getValue()) || property.equals(Property.PROJECT.getValue())) {
                List<QpQuotationDutySplit> quoteDutySplit = dutys.get(i).getDutySplit();
                qpQuoteDutySplits.addAll(quoteDutySplit.stream().map(v -> v.setQuotationDutyId(quoteDutyId)).collect(Collectors.toList()));
                //project不为空说明是费用项目数据
                if (!CollectionUtils.isEmpty(quoteDutySplit) && quoteDutySplit.get(0).getProject() != null) {
                    updateDutyWithProject(dutyIds.get(i), quoteDutySplit);
                }
            }
        }
        if (!removeQpQuoteDutySplitList.isEmpty()) {
            quotationDutySplitMapper.deleteListWithoutPermission(removeQpQuoteDutySplitList);
        }
        if (!qpQuoteDutySplits.isEmpty() && qpQuoteDutySplits.size() != 0) {
            quotationDutySplitMapper.insertList(qpQuoteDutySplits);
        }
        if (!CollectionUtils.isEmpty(qpQuoteDuties)) {
            generateDutyVersionData(qpQuoteDuties, qpQuoteDutySplits, OperationEnum.UPDATE.getValue());
        }
        return qpQuoteDuties.stream().map(QpQuotationDuty::getId).collect(Collectors.toList());
    }


    private void generateDutyVersionData(List<QpQuotationDuty> qpQuoteDuties, List<QpQuotationDutySplit> qpQuoteDutySplits, Integer operationType) {
        log.debug("generateDutyVersionData --------- qpQuoteDuties:{},qpQuoteDutySplits:{},operationType:{}", JacksonUtils.writeAsString(qpQuoteDuties), JacksonUtils.writeAsString(qpQuoteDutySplits), operationType);
        List<QpQuoteDutyDto> dutyDtos = new ArrayList<>();
        Map<Long, List<QpQuotationDutySplit>> spiltMap = qpQuoteDutySplits.stream().collect(Collectors.groupingBy(QpQuotationDutySplit::getQuotationDutyId));
        for (QpQuotationDuty qpQuoteDuty : qpQuoteDuties) {
            QpQuoteDutyDto qpQuoteDutyDto = BeanUtil.toBean(qpQuoteDuty, QpQuoteDutyDto.class);
            List<QpQuotationDutySplit> dutySpilts = spiltMap.get(qpQuoteDuty.getId());
            if (!CollectionUtils.isEmpty(dutySpilts)) {
                List<QpQuoteDutySplitDto> splitDtoList = new ArrayList<>(dutySpilts.size());
                for (QpQuotationDutySplit dutySpilt : dutySpilts) {
                    splitDtoList.add(BeanUtil.toBean(dutySpilt, QpQuoteDutySplitDto.class));
                }
                qpQuoteDutyDto.setDutySplit(splitDtoList);
            }
            dutyDtos.add(qpQuoteDutyDto);
        }
        log.debug("generateDutyVersionData dutyDtos : {}", JacksonUtils.writeAsString(dutyDtos));
        List<VersionDto> versionList = new ArrayList<>(dutyDtos.size());
        for (QpQuoteDutyDto dutyDto : dutyDtos) {
            VersionDto versionDto = new VersionDto();
            versionDto.setObjectId(Long.parseLong(dutyDto.getId()));
            versionDto.setEntityKey(EntityKeyEnum.DUTY.getType());
            versionDto.setEntityHash(String.valueOf(HashUtil.identityHashCode(dutyDto)));
            versionDto.setEntityValue(JacksonUtils.writeAsString(dutyDto));
            versionDto.setOperationType(operationType);
            versionDto.setOperationTime(ZonedDateTime.now());
            versionDto.setBusinessId(Long.parseLong(dutyDto.getId()));
            versionList.add(versionDto);
        }
        log.debug("generateDutyVersionData versionList : {}", JacksonUtils.writeAsString(versionList));
        versionAggService.insertNewDateVersion(versionList);
    }

    /**
     * 处理删除的保险责任
     *
     * @param configId 配置id
     * @param dutys    责任
     */
    private List<Long> deleteDuty(Long configId, List<QuotationDutyDTO> dutys) {
        if (CollectionUtils.isEmpty(dutys)) {
            return Lists.newArrayList();
        }
        List<QpQuotationDuty> qpQuoteDuties = ObjectUtil.convertList(dutys, QpQuotationDuty.class);
        // 创建对象
        log.info("qpQuoteDuties deleteList:{}", JacksonUtils.writeAsString(qpQuoteDuties));
        quotationDutyMapper.deleteListWithoutPermission(qpQuoteDuties);
        return qpQuoteDuties.stream().map(QpQuotationDuty::getId).collect(Collectors.toList());
    }

    /**
     * 保存责任后的其他处理
     *
     * @param configId  配置id
     * @param dutyGroup 责任分组
     * @param dutys     责任集合
     */
    private void otherHandle(Long configId, QuotationDutyGroup dutyGroup, List<QuotationDutyDTO> dutys, SaveDutyDto saveDutyDto) {
        //更新配置金额
        upQuoteConfigPrice(configId, dutys);
        //记录日志
        saveLog(dutyGroup);
        // todo 同步产品工厂
        pushProductData(saveDutyDto);
        // 新增责任默认带出特约 todo 影响到 localhost:8080/quote/v2/core/1710805561282653230/config_duty/batch?is_quote=0
        // 先注释
        recommendSpecial(dutyGroup,configId);
        // 添加默认理赔退费特约
        qpQuoteApproveService.addDefaultSpecial(null, configId);
    }

    private void recommendSpecial(QuotationDutyGroup dutyGroup, Long configId) {
        if (ThreadLocalUtil.isBoolean()) {
            return;
        }
        if (CollectionUtils.isEmpty(dutyGroup.getAddDutys())) {
            return;
        }

        SpecialRecommendParamDTO paramDto = new SpecialRecommendParamDTO();
        paramDto.setScene(3);//2.询价 3.报价
        paramDto.setType(3);//1.方案组 2.方案(配置) 3.责任
        List<Long> dutyIdList = dutyGroup.getAddDutys().stream().map(QuotationDutyDTO::getId).collect(Collectors.toList());
        paramDto.setIdList(dutyIdList);
        paramDto.setSpecialCityCode(null);// 这里不传使用 info 里存的没有默认用北京
        QpQuotationConfig quotationConfig = quotationConfigDataMapper.entity(QpQuotationConfig.class).selectOne(configId, true);
        QpQuotation quotation = quotationDataMapper.entity(QpQuotation.class).selectOne(quotationConfig.getQuotationId(), true);
        paramDto.setInfoId(quotation.getQuotationInfoId());
        paramDto.setQuoteOrQuotationId(quotation.getId());
        // TOB-19850 部分账号提交的雇主报价不带默认特约
        Long[] noEmployerSpecialUserIds = convertStringToLongArray(noEmployerSpecialUserId);
        QpQuotationInfo quotationInfo = quotationInfoDataMapper.entity(QpQuotationInfo.class).selectOne(quotation.getQuotationInfoId(), true);
        if (quotationInfo.getQuoteType().equals(QuoteTypeEnum.EMPLOYER.getCode()) && Arrays.asList(noEmployerSpecialUserIds).contains(quotationInfo.getSalesUid())) {
            return;
        }

        // 获取地区信息
        String sql = "select quotation_config_id," +
                "quotation_config_id.qp_quotation_config.qp_quotation.qp_quotation_info.special_city_code as special_city_code, " +
                "quotation_config_id.qp_quotation_config.qp_quotation.qp_quotation_info.renew_flag as renew_flag " +
                " from qp_quotation_duty where id in (%s)";

        String dutyIdString = dutyIdList.stream().map(String::valueOf).collect(Collectors.joining(","));
        if (Strings.isNotBlank(dutyIdString)) {
            List<QpQuotationInfo> execute = quotationInfoBqlMapper.entity(QpQuotationInfo.class).execute(String.format(sql, dutyIdString), true);
            QpQuotationInfo qpQuoteInfo = execute.stream().findFirst().orElse(new QpQuotationInfo());
            paramDto.setSpecialCityCode(qpQuoteInfo.getSpecialCityCode());
            // 续期创建责任不推荐特约
            if (Objects.equals(qpQuoteInfo.getRenewFlag(), RenewFlagEnum.RENEW.getValue())) {
                return;
            }
        }

        specialService.recommend(paramDto);
    }

    private void pushProductData(SaveDutyDto saveDutyDto) {
        if (Objects.isNull(saveDutyDto) || !saveDutyDto.getSaveProductFactoryFormInsuranceToQuote()) {
            return;
        }
        CopyDutyRuleInsuranceDto copyDutyRuleInsuranceDto = convertBeanToMapForCopyDutyRuleInstance(saveDutyDto);
        log.info("pushProductData-saveDutyDto:{}", JacksonUtils.writeAsString(saveDutyDto));
        log.info("pushProductData-copyDutyRuleInsuranceDto：{}", JacksonUtils.writeAsString(copyDutyRuleInsuranceDto));
        ResponseVO responseVO = copyDutyRuleInstanceClient.batchCopyDutyRuleInstance(copyDutyRuleInsuranceDto);
        log.info("pushProductData-responseVO：{}", JacksonUtils.writeAsString(responseVO));
    }

    /**
     * 记录日志
     *
     * @param dutyGroup 责任 todo
     */
    private void saveLog(QuotationDutyGroup dutyGroup) {

    }

    /**
     * 计算配置总金额
     *
     * @param quoteConfigId 配置id
     * @param dutys         责任
     */
    @Override
    public void upQuoteConfigPrice(Long quoteConfigId, List<QuotationDutyDTO> dutys) {

        if (!CollectionUtils.isEmpty(dutys)) {
            List<CurrencyAmount> dutyPriceList = dutys.stream().map(item -> {
                if (null == item.getPrice()) {
                    return CurrencyAmount.builder().build();
                }
                return item.getPrice();
            }).collect(Collectors.toList());
            CurrencyAmount configPrice = CurrencyAmountUtil.sum(dutyPriceList);
            QpQuotationConfig qpQuotationConfig = quotationConfigDataMapper.entity(QpQuotationConfig.class).selectOne(quoteConfigId, true);
            QpQuotationConfig qpQuotationConfig1 = new QpQuotationConfig();
            //第一次价格赋值基准报价
            //if (qpQuotationConfig.getBasePrice() == null) {
            qpQuotationConfig1.setBasePrice(configPrice);
            //}
            quotationConfigDataMapper.entity(QpQuotationConfig.class).updateOne(qpQuotationConfig1
                            .setId(quoteConfigId).setPrice(configPrice),
                    true);
            //第一次价格还需要计算价值贡献率等 这交互哎
            //if (qpQuotationConfig.getBasePrice() == null) {
            QpQuotationConfigDTO qpQuoteConfigDTO = new QpQuotationConfigDTO();
            qpQuoteConfigDTO.setId(quoteConfigId);
            qpQuoteConfigDTO.setBasePrice(configPrice);

            quotationConfigServic.calculateValuesByPriceAndBasePrice(qpQuoteConfigDTO);
            quotationConfigDataMapper.entity(QpQuotationConfig.class).updateOne(qpQuotationConfig1
                    .setId(quoteConfigId).setValueContributionRate(qpQuoteConfigDTO.getValueContributionRate())
                    .setExcessValueRatio(qpQuoteConfigDTO.getExcessValueRatio())
                    .setExcessValueAllocationRate(qpQuoteConfigDTO.getExcessValueAllocationRate()),
                    true
            );
            //}
            modifyConfigPeriodPriceByConfig(quoteConfigId, null);
        }
    }


    /**
     * 更新配置下的 缴费周期对应费用
     *
     * @param quotationConfigId 配置id
     */
    @Override
    public void modifyConfigPeriodPriceByConfig(Long quotationConfigId, QpQuotation qpQuotation) {
        QpQuotationConfig qpQuotationConfig = quotationConfigDataMapper.entity(QpQuotationConfig.class).selectOne(quotationConfigId, true);
        if (Objects.isNull(qpQuotationConfig)) {
            return;
        }
        if (Objects.isNull(qpQuotation)) {
            qpQuotation = quotationDataMapper.entity(QpQuotation.class).selectOne(qpQuotationConfig.getQuotationId(), true);
        }
        if (Objects.isNull(qpQuotation)) {
            return;
        }

        BigDecimal payPeriodPrice = qpQuotationConfig.getPrice() == null ? null : qpQuotationConfig.getPrice().getAmount();
        //缴费类型-趸缴不处理，期缴在if中
        if (Objects.equals(qpQuotation.getPayType(), PayTypeEnum.REGULAR_PAYMENT.getCode()) && payPeriodPrice != null && payPeriodPrice.doubleValue() > 0) {
            //期缴处理-缴费周期
            PayPeriodPriceEnum payPeriodPriceEnum = PayPeriodPriceEnum.getByCode(qpQuotation.getPayPeriod());
            if (Objects.isNull(payPeriodPriceEnum)) {
                throw new InsgeekException("缴费周期错误：PayPeriod=" + qpQuotation.getPayPeriod() + "，PayType=" + qpQuotation.getPayType());
            }
            payPeriodPrice = payPeriodPrice.divide(new BigDecimal(payPeriodPriceEnum.getDivisor()), 2, RoundingMode.HALF_UP);
        }
        //更新缴费周期
        quotationConfigDataMapper.entity(QpQuotationConfig.class)
                .updateOne(new QpQuotationConfig()
                        .setId(quotationConfigId)
                        .setPayPeriodPrice(CurrencyAmount.builder().amount(payPeriodPrice).currency(IdentityContext.getBusinessCurrency()).build()), true);

    }

    /**
     * 更新配置下的 缴费周期对应费用
     *
     * @param quoteId 方案id
     */
    @Override
    public void modifyConfigPeriodPriceByQuote(Long quoteId) {
//        QpQuotation qpQuote = quoteDataMapper.entity(QpQuotation.class).selectOne(quoteId, true);
//        if (Objects.isNull(qpQuote)) {
//            return;
//        }
//        List<QpQuotationConfig> qpQuoteConfigs = quoteConfigDataMapper.entity(QpQuotationConfig.class).select(new QpQuotationConfig().setQuoteId(quoteId), true);
//        if (CollectionUtils.isEmpty(qpQuoteConfigs)) {
//            return;
//        }
//        for (QpQuotationConfig qpQuoteConfig : qpQuoteConfigs) {
//            modifyConfigPeriodPriceByConfig(qpQuoteConfig.getId(), qpQuote);
//        }
    }

    public CopyDutyRuleInsuranceDto convertBeanToMapForCopyDutyRuleInstance(SaveDutyDto saveDutyDto) {
        CopyDutyRuleInsuranceDto copyDutyRuleInsuranceDto = new CopyDutyRuleInsuranceDto();
        List<Long> dutyIdList = new ArrayList<>();
        Map<Long, Long> dutyAndSplitIdMap = new HashMap<>();
        CopyProductFactoryData copyProductFactoryData = saveDutyDto.getCopyProductFactoryData();
        //config
        dutyAndSplitIdMap.put(copyProductFactoryData.getInsuranceConfigId(), copyProductFactoryData.getQuoteConfigId());
        //insuranceDutyIds
        List<ProductQuoteInsuranceDto> productQuoteInsuranceDtos = copyProductFactoryData.getProductQuoteInsuranceDtos();
        dutyIdList = productQuoteInsuranceDtos.stream().map(ProductQuoteInsuranceDto::getInsuranceDutyId).collect(Collectors.toList());
        //dutyAndSplitMapping
        for (ProductQuoteInsuranceDto productQuoteInsuranceDto : productQuoteInsuranceDtos) {
            dutyAndSplitIdMap.put(productQuoteInsuranceDto.getInsuranceDutyId(), productQuoteInsuranceDto.getQuoteDutyId());
            for (ProductQuoteInsuranceDetailDto productQuoteInsuranceDetailDto : productQuoteInsuranceDto.getDetailDtos()) {
                dutyAndSplitIdMap.put(productQuoteInsuranceDetailDto.getInsuranceDutyDetailId(), productQuoteInsuranceDetailDto.getQuoteDutyDetailId());
            }
        }
        copyDutyRuleInsuranceDto.setDutyIdList(dutyIdList);
        copyDutyRuleInsuranceDto.setDutyAndSplitIdMap(dutyAndSplitIdMap);
        //转换
        copyDutyRuleInsuranceDto.setCopyDutyRuleInstanceType(2);
        return copyDutyRuleInsuranceDto;
    }

    @Override
    public ResponseVO readDutyListByConfigIds(List<Long> configList, Integer renewalCompareFlag) {
        return dutyClient.readDutyListByConfigIdV2(6, configList);
        /*List<FrontProductDutyInstanceDTO> respList = new ArrayList<>();
        //ResponseVO<List<ProductRuleDutyDTO>> responseVO = dutyClient.readDutyListByConfigIdV2(6, configList);
        ResponseVO<List<FrontProductDutyInstanceDTO>> responseVO = dutyClient.batchGetDutyListByConfigForFront(6, configList);
        if (responseVO.ok()) {
            respList = JacksonUtils.readValue(JacksonUtils.writeAsString(responseVO.getData()), new TypeReference<List<FrontProductDutyInstanceDTO>>() {
            });

            if (!Objects.equals(renewalCompareFlag, RenewalCompareFlagEnum.NONCOMPARE.getCode()) && !CollectionUtils.isEmpty(respList)) {
//                respList = quoteRenewService.compareRenewalDutyDetail(respList.get(0).getConfigId(), respList, renewalCompareFlag);
            }
        } else {
            return responseVO;
        }
        return ResponseVO.builder().data(respList).build();*/
    }

    @Override
    public ResponseVO readDutyListByConfigIdsNew(List<Long> configList, Integer renewalCompareFlag) {
        ListParamDTO params = new ListParamDTO();
        params.setScene(3);
        params.setType(2);
        params.setIdList(configList);
        return dutyClient.batchGetDutyListByConfigForFront(3, 0, params);
    }

    @SuppressWarnings("AliControlFlowStatementWithoutBraces")
    @Override
    public ResponseVO saveDutyListAdapter(Long configId, List<FrontProductDutyInstanceDTO> frontProductDutyInstanceDTOList, Integer xAppKey) {
        checkDutysAgentRatio(configId, frontProductDutyInstanceDTOList);
//        ResponseVO<Object> dutyListResp = dutyClient.createDutyV2(2, configId, productRuleDutyDTOList);
        List<QpQuotationDuty> olddutyList = quotationDutyMapper.selectListByEntity(new QpQuotationDuty().setQuotationConfigId(configId), true);
        ResponseVO<Object> dutyListResp = dutyClient.createDutyForFront(6, configId, frontProductDutyInstanceDTOList);
        Map<Long, FrontProductDutyInstanceDTO> pidToProducts = frontProductDutyInstanceDTOList.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));
        Map<Long, CurrencyAmount> oldDutyIdToPrice = olddutyList.stream().filter(x->x.getPrice()!=null).collect(Collectors.toMap(x -> x.getId(), QpQuotationDuty::getPrice));
        List<QpQuotationDuty> dutyList = quotationDutyMapper.selectListByEntity(new QpQuotationDuty().setQuotationConfigId(configId), true);
        //手动保存需要清楚责任上的公式和price_source
        List<QpQuotationDuty> toUpdate = dutyList.stream().filter(x -> {
            boolean a = (x.getPriceSource()!=null && x.getPriceSource()== 1) || StringUtils.isNotBlank(x.getFormula());
            Map<String, Object> dutyField = pidToProducts.get(x.getProductId()).getDutyField();
            Map<String,Object> newPrice = null;
            if(dutyField.get("price")!=null)
                 newPrice = (Map<String, Object>) dutyField.get("price");
            if(!oldDutyIdToPrice.containsKey(x.getId()))
                return false;
            return new BigDecimal(String.valueOf(newPrice.get("amount") == null ? 0 : newPrice.get("amount"))).compareTo(oldDutyIdToPrice.get(x.getId()).getAmount()) != 0 && a;
        }).map(
                    duty -> {
                        duty.setPriceSource(0);
                        //duty.setFormula("");
                        return duty;
                    }
            ).collect(Collectors.toList());

        if (toUpdate.size() > 0) {
            quotationDutyMapper.updateList(toUpdate, true);
        }
        return dutyListResp;
    }

    private void checkDutysAgentRatio(Long configId, List<FrontProductDutyInstanceDTO> frontProductDutyInstanceDTOList) {
        // 查询方案组
        QpQuotationConfig config = quotationConfigDataMapper.entity(QpQuotationConfig.class).selectOne(configId, true);
        QpQuotation quotation = quotationDataMapper.entity(QpQuotation.class).selectOne(config.getQuotationId(), true);
        QpQuotationInfo quotationInfo = quotationInfoDataMapper.entity(QpQuotationInfo.class).selectOne(quotation.getQuotationInfoId(), true);

        // 保司id
        Long insuranceConsumerId = quotationInfo.getInsuranceConsumerId();
        if (CollectionUtils.isEmpty(frontProductDutyInstanceDTOList)) return;

        // 查询对应保司的佣金比例规则
        ResponseVO<List<IgPolicyCommissionRules>> igPolicyCommissionRulesResponseVO = policyCommissionRulesClient.findByVendorIds(String.valueOf(insuranceConsumerId), 1, 1);
        if (CollectionUtils.isEmpty(igPolicyCommissionRulesResponseVO.getData())) return;

        // 校验佣金比例规则
        BigDecimal max = igPolicyCommissionRulesResponseVO.getData().get(0).getMax();
        BigDecimal minCrement = igPolicyCommissionRulesResponseVO.getData().get(0).getMinCrement();

        for (FrontProductDutyInstanceDTO frontProductDutyInstanceDTO : frontProductDutyInstanceDTOList) {
            if (frontProductDutyInstanceDTO.getDutyField() == null) return;
            if (frontProductDutyInstanceDTO.getDutyField().get("agent_ratio") == null) return;
            // 佣金比例
            String agentRatioStr = String.valueOf(frontProductDutyInstanceDTO.getDutyField().get("agent_ratio"));
            BigDecimal agentRatio = new BigDecimal(agentRatioStr);

            // 判断agentRatio转BigDecimal后是否超过了max
            if (agentRatio.compareTo(max) > 0) {
                throw new BusinessException(MessageUtil.get("b_b_quote_220"));
            }
            // 判断agentRatio是不是minCrement的整数倍
            if (agentRatio.remainder(minCrement).compareTo(BigDecimal.ZERO) != 0) {
                throw new BusinessException(MessageUtil.get("b_b_quote_221", minCrement));
            }
        }
    }

    @Override
    public ResponseVO saveDutyList(Long configId, List<ProductRuleDutyDTO> productRuleDutyDTO, Integer xAppKey) {
        ResponseVO<Object> dutyListResp = dutyClient.createDutyV2(6, configId, productRuleDutyDTO);
        if (dutyListResp.ok()) {
//            QpQuotationConfig config = quoteConfigDataMapper.entity(QpQuotationConfig.class).selectOne(configId, true);
//            if (config == null) {
//                return dutyListResp;
//            }
//            QpQuotation qpQuote = qpQuoteDataMapper.entity(QpQuotation.class).selectOne(config.getQuotationId(), true);
//            if (qpQuote == null) {
//                return dutyListResp;
//            }
//            QpQuotationInfo qpQuoteInfo = qpQuoteInfoDataMapper.entity(QpQuotationInfo.class).selectOne(qpQuote.getQuotationInfoId(), true);
//            if (qpQuoteInfo == null) {
//                return dutyListResp;
//            }
            //责任变更记录配置变更字段
//            if (!xAppKey.equals(RenewalCompareFlagEnum.NONCOMPARE.getCode()) && Objects.equals(qpQuoteInfo.getRenewFlag(), RenewFlagEnum.RENEW.getValue())) {
//                //记录前后台是否更新了责任
//                updateConfigUpdateFlag(configId, productRuleDutyDTOList, xAppKey);
//                for (ProductRuleDutyDTO productRuleDutyDTO : productRuleDutyDTOList) {
//                    productRuleDutyDTO.setOperation(null);
//                }
//                //处理前台责任更新成销售提交版本情况或者后台责任更新成历史责任版本情况配置上更字样未变化的问题
//                updateConfigUpdateFlag(configId, productRuleDutyDTOList, xAppKey.equals(SystemTabFlagEnum.QUOTATION.getCode()) ? SystemTabFlagEnum.CENTER_QUOTATION.getCode() : SystemTabFlagEnum.QUOTATION.getCode());
//            }
        }
        return dutyListResp;
    }

    @Override
    public ResponseVO saveDutyList2(Long configId, List<FrontProductDutyInstanceDTO> productRuleDutyDTOList, Integer xAppKey) {
        ResponseVO<Object> dutyListResp = dutyClient.createDutyForFront(6, configId, productRuleDutyDTOList);
        return dutyListResp;
    }

    private void updateConfigUpdateFlag(Long configId, List<ProductRuleDutyDTO> productRuleDutyDTOList, Integer xAppKey) {
//        List<ProductRuleDutyDTO> respProductRuleDutyDTOS;
//        respProductRuleDutyDTOS = quoteRenewService.compareRenewalDutyDetail(configId, productRuleDutyDTOList, xAppKey);
//        QpQuotationConfig qpQuoteConfig = new QpQuotationConfig();
//        qpQuoteConfig.setId(configId);
//        //配置更新标志
//        boolean updateFlag = false;
//        for (ProductRuleDutyDTO productRuleDutyDTO : respProductRuleDutyDTOS) {
//            if (productRuleDutyDTO.getOperation() != null) {
//                updateFlag = true;
//                break;
//            }
//        }
//        if (updateFlag) {
//            if (Objects.equals(xAppKey, SystemTabFlagEnum.QUOTATION.getCode())) {
//                qpQuoteConfig.setQuotationUpdateFlag(UpdateFlagEnum.UPDATE.getCode());
//            } else {
//                qpQuoteConfig.setCenterQuotationUpdateFlag(UpdateFlagEnum.UPDATE.getCode());
//            }
//        } else {
//            if (Objects.equals(xAppKey, SystemTabFlagEnum.QUOTATION.getCode())) {
//                qpQuoteConfig.setQuotationUpdateFlag(UpdateFlagEnum.DEFALT.getCode());
//            } else {
//                qpQuoteConfig.setCenterQuotationUpdateFlag(UpdateFlagEnum.DEFALT.getCode());
//            }
//        }
//        quoteConfigDataMapper.entity(QpQuotationConfig.class).updateOne(qpQuoteConfig, true);
    }

    @Override
    public List<QuotationDutyDTO> getQuotationConfigDutyList(Long quotationConfigId) {
        QpQuotationConfig quotationConfig = quotationConfigDataMapper.entity(QpQuotationConfig.class).selectOne(quotationConfigId, false);
        if (null == quotationConfig) {
            log.info("method:getQuotationConfigDutyList 配置不存在（无权限查询）, quoteConfigId：{}", quotationConfigId);
            return Lists.newArrayList();
        }

        // 查询配置责任信息
        List<QuotationDutyDTO> quotationDutyList = quotationDutyFeginService.getQuotationDutyList(quotationConfig.getId());
        // 填充分段信息
        fillQuotationDutySplit(quotationDutyList);
        return quotationDutyList;
    }

    /**
     * 自动保存推荐的责任数据
     *
     * @param quotationId
     */
    @Override
    public void autoSaveDuty(Long quotationId,List<Long> configs) {
        DataCondition<QpQuotationConfig> condition = new DataCondition<>();
        condition.eq("quotation_id", quotationId);
        //查询方案开启的配置
        List<QpQuotationConfig> qpQuoteConfigs = qpQuoteConfigDataMapper.entity(QpQuotationConfig.class).select(condition, true);
        //查询方案开启的配置
        QpQuotation quotation = qpQuotationDataMapper.entity(QpQuotation.class).selectOne(quotationId, true);
        List<String> addDutyTypes  =new ArrayList<>();
        List<String> delDutyTypes  =new ArrayList<>();
        if (CollectionUtils.isEmpty(qpQuoteConfigs)){
            log.info("quotationId:{}方案配置信息为空, 不进行处理:{}", quotationId,JacksonUtils.writeAsString(qpQuoteConfigs));
            return;
        }
        // 查询用户需要添加或删除的责任
        dutyCommonService.getQuotationAddOrDelDutyTypes(quotation, addDutyTypes, delDutyTypes);
        log.info("quotationId:{} 添加责任类型:{} 删除责任类型:{}",quotationId,addDutyTypes,delDutyTypes);
        Map<String, IgProductRO> igProductMap = dutyCommonService.getIgProductMap(addDutyTypes);
        // 查询方案的历史配置信息
        // core/duty/adapter/list_2?compare=0
        Map<String, List<FrontProductDutyInstanceDTO>> quoteConfigIdMap = getFrontProductListMap(qpQuoteConfigs);
        // 循环处理配置下的责任
        for (QpQuotationConfig qpQuoteConfig : qpQuoteConfigs) {
            if (quoteConfigIdMap.containsKey(qpQuoteConfig.getId().toString())) {
                List<FrontProductDutyInstanceDTO> dutyInstanceDTOList = quoteConfigIdMap.get(qpQuoteConfig.getId().toString());
                //  获取身故伤残的保额
                CurrencyAmount coverageAmount = dutyCommonService.getCoverageAmount(dutyInstanceDTOList);
                List<String> allTypeCode = dutyInstanceDTOList.stream().map(FrontProductDutyInstanceDTO::getProductTypeCode).collect(Collectors.toList());
                // 修改默认值
                dutyCommonService.setDutyValue(dutyInstanceDTOList,quotation.getExpandSelfFunded());
                // 添加责任
                if (!CollectionUtils.isEmpty(addDutyTypes)) {
                    addDutyTypes.stream().filter(x -> !allTypeCode.contains(x)).forEach(x -> {
                        dutyInstanceDTOList.addAll(dutyCommonService.getDutyListByDuty(Collections.singletonList(x), igProductMap,coverageAmount,quotation.getAddGrant(),quotation.getExpandSelfFunded()));
                    });
                }
                log.info("quoteId:{} quoteConfigId:{} ,删除责 任信息:{}", quotationId,  qpQuoteConfig.getId(),JacksonUtils.writeAsString(delDutyTypes));
                List<FrontProductDutyInstanceDTO>  newDutyInstanceDTOList=dutyInstanceDTOList.stream().filter(x -> !delDutyTypes.contains(x.getProductTypeCode())).collect(Collectors.toList());
                log.info("quoteId:{} quoteConfigId:{}自动保存责任信息:{}", quotationId , qpQuoteConfig.getId(),JacksonUtils.writeAsString(newDutyInstanceDTOList));
                ResponseVO objectResponseVO = quotationDutyService.saveDutyListAdapter(qpQuoteConfig.getId(), newDutyInstanceDTOList, 2);
                log.info("quoteId:{} quoteConfigId:{} 自动保存责任信息结果:{}",quotationId , qpQuoteConfig.getId(), JacksonUtils.writeAsString(objectResponseVO));

            }
        }
    }

    /**
     * 获取配置的责任信息
     * @param qpQuotationConfigs
     * @return
     */
    private Map<String, List<FrontProductDutyInstanceDTO>> getFrontProductListMap(List<QpQuotationConfig> qpQuotationConfigs) {
        ListParamDTO listParam=new ListParamDTO();
        listParam.setScene(2);
        listParam.setType(2);
        listParam.setIdList(qpQuotationConfigs.stream().map(QpQuotationConfig::getId).collect(Collectors.toList()));
        IdentityUtil.setRobotAuth();// 适配外部版查询责任
        ResponseVO<List<FrontProductDutyInstanceDTO>> resp = dutyClient.batchGetDutyListByConfigForFront(listParam.getScene(), 2, listParam);
        List<FrontProductDutyInstanceDTO> frontProductDutyInstanceDTOList = resp.getData();
        Map<String, List<FrontProductDutyInstanceDTO>> quoteConfigIdMap = frontProductDutyInstanceDTOList
                .stream().collect(Collectors.groupingBy(frontProductDutyInstanceDTO -> frontProductDutyInstanceDTO.getDutyField().get("quotation_config_id").toString()));
        return quoteConfigIdMap;
    }

    /**
     * 组装分段信息
     *
     * @param quotationDutyList
     */
    private void fillQuotationDutySplit(List<QuotationDutyDTO> quotationDutyList) {
        if (!quotationDutyList.isEmpty()) {
            List<String> dutyIds = quotationDutyList.stream().filter(v -> Property.SPLIT.getValue().equals(v.getProperty()))
                    .map(v -> v.getId().toString())
                    .collect(Collectors.toList());
            if (!dutyIds.isEmpty()) {
                // 创建条件
                DefaultBaseCondition<QpQuotationDutySplit> defaultBaseCondition = new DefaultBaseCondition();
                defaultBaseCondition.in(QpQuotationDutyConst.F_ID, dutyIds);
                Map<Long, List<QpQuotationDutySplit>> quotationDutySplitList = quotationDutySplitMapper.selectListByCondition(defaultBaseCondition).stream()
                        .collect(Collectors.groupingBy(QpQuotationDutySplit::getQuotationDutyId));
                // 整合数据
                quotationDutyList.forEach(v -> v.setDutySplit(quotationDutySplitList.getOrDefault(v.getId(), null)));
            } else {
                // 初始化数据
                quotationDutyList.forEach(v -> v.setDutySplit(Collections.emptyList()));
            }
        }
    }

    /**
     * 将逗号分隔的字符串转换为Long数组
     *
     * @param str 输入的字符串
     * @return 转换后的Long数组
     * @throws NumberFormatException 如果字符串中有不能转换为Long的部分
     */
    private static Long[] convertStringToLongArray(String str) throws NumberFormatException {
        // 分割字符串
        String[] parts = str.split(",");
        // 创建Long数组
        Long[] result = new Long[parts.length];
        // 遍历并转换
        for (int i = 0; i < parts.length; i++) {
            result[i] = Long.parseLong(parts[i].trim());  // 使用trim()去除首尾空白
        }
        return result;
    }
}
