package com.insgeek.business.quote.quotation.service;

import com.insgeek.business.quote.quotation.dto.*;
import com.insgeek.protocol.data.client.entity.IgPreferSupplier;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;

/**
 * @Description: 供应商管理
 * @Date: 2024-11-07  15:40
 * @Author: YuanSiYuan
 */
public interface SupplierService {
    /**
     * 匹配可承保供应商
     *
     * @param quotationId 报价ID
     * @return
     */
    List<SupplierInfoDto> matchSupplier(Long  quotationId,List<String> matchConditions);

    /**
     * 匹配保费规模
     * @param quotationInfoId
     * @return
     */
    BigDecimal matchPremiumScale(Long quotationInfoId);

    /**
     *  计算预测赔付率
     */
    BigDecimal predict(Long quotationInfoId);

    /**
     *   触发赔付预测
     */
    void triggerPredict(Long quotationInfoId);


    /**
     *  优选供应商
     */
    List<IgPreferSupplier> preferSupplier(Long quotationId);


    /**
     * 保存供应商
     * @param supplierReq
     */
    void saveSupplier(SupplierReq supplierReq);

    /**
     * 修改协议拥金
     * @param quotation_infoId
     * @param cooperationId
     * @param commissionRate
     */
    void updateCommissionRequire(Long quotation_infoId, Long cooperationId , BigDecimal commissionRate,BigDecimal commissionRequire);

    /**
     *  特定供应商列表
     * @return
     */
    List<SpecificSupplierDto> specificSupplierList(Long quotationInfoId);


    /**
     * 下载文件
     * @param quoteInfoId
     * @param type
     * @param response
     */
    void exportQuoteExcel(Long quoteInfoId, Long cooperationId,String type, HttpServletResponse response);


    /**
     * 更新协议佣金率
     * @param quotationInfoId
     * @param supplierId
     */
    void updateCommissionRequire(Long quotationInfoId, List<Long> supplierId);


    /**
     * 优选供应商数据排序
     */
    void updatePreferSupplierSort(Long quotationInfoId);


    /**
     * 查询净费信息
     * @param quotationInfoId
     * @param supplierId
     * @return
     */
    List<FeilvReturnDto> getSupplierNetFee(Long quotationInfoId , Long supplierId);


    /**
     * 更新特定供应商
     */
    Boolean updateSpecificSupplier(Long quotationInfoId,List<Long> cooperationIds,Integer type,Boolean initData);


    /**
     * 修复后协议佣金
     */
    void updateCommissionRate(List<Long> quotationInfoId);

    /**
     * 获取供应商信息
     * @return
     */
    List<CompanySupplierInfoDto> getSupplierInfo();


    /**
     * 手动出发供应商推荐逻辑
     */
    void createPreferSupplier(Long quotationInfoId);

}
