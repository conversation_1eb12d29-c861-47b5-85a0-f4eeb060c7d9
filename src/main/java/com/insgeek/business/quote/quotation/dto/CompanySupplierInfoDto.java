package com.insgeek.business.quote.quotation.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

/**
 * @Description:
 * @Date: 2025-08-19  18:46
 * @Author: <PERSON><PERSON><PERSON><PERSON><PERSON>
 */

@Data
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class CompanySupplierInfoDto {

    /**
     * 公司名称
     */
    private String companyName;
    /**
     * 公司ID
     */
    private String companyId;

    /**
     * 供应商ID
     */
    private Long cooperationInfoId;
    /**
     * 供应商名称
     */
    private String cooperationName;

}
