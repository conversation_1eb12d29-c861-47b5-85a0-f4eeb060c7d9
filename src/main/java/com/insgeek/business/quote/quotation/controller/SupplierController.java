package com.insgeek.business.quote.quotation.controller;

import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.quotation.dto.CompanySupplierInfoDto;
import com.insgeek.business.quote.quotation.dto.SupplierInfoDto;
import com.insgeek.business.quote.quotation.dto.SupplierReq;
import com.insgeek.business.quote.quotation.dto.SupplierUpdataReq;
import com.insgeek.business.quote.quotation.service.SupplierService;
import com.insgeek.protocol.data.client.entity.IgPreferSupplier;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description: 供应商匹配规则
 * @Date: 2024-11-11  13:59
 * @Author: YuanSiYuan
 */
@Slf4j
@Setter
@RestController
@RequestMapping("/quotation/supplier")
public class SupplierController {

    @Autowired
    SupplierService supplierService;

    /**
     * 匹配供应商
     */
    @PostMapping("/match")
    public ResponseVO matchSupplier(@RequestBody SupplierReq supplierReq) {
        List<SupplierInfoDto> supplierInfoDtoList = supplierService.matchSupplier(supplierReq.getQuotationInfoId(), supplierReq.getMatchConditions());
        return  ResponseVO.data(supplierInfoDtoList);
    }


    /**
     *  优选供应商
     */
    @GetMapping("/prefer")
    public ResponseVO preferSupplier(@RequestParam("quotation_info_id") Long quotationInfoId) {
        List<IgPreferSupplier> igPreferSupplierList = supplierService.preferSupplier(quotationInfoId);
        return  ResponseVO.data(igPreferSupplierList);
    }

    /**
     * 保存供应商信息
     */
    @PostMapping("/save")
    public ResponseVO saveSupplier(@RequestBody SupplierReq supplierReq){
        supplierReq.setType(1);
        supplierService.saveSupplier(supplierReq);
        return ResponseVO.data(null);
    }
    /**
     * 特定供应商列表
     */
    @GetMapping("/specific_supplier_list")
    public ResponseVO specificSupplierList(@RequestParam("quotation_info_id") Long quotationInfoId) {
        return ResponseVO.data(supplierService.specificSupplierList(quotationInfoId));
    }

    /**
     *  编辑协议佣金率
     */
    @PostMapping("/update_commission_rate")
    public ResponseVO updateCommissionRate(@RequestBody SupplierUpdataReq supplierReq) {
        supplierService.updateCommissionRequire(Long.valueOf(supplierReq.getQuotationInfoId()),Long.valueOf(supplierReq.getCooperationId()),null,new BigDecimal(supplierReq.getCommissionRequire()));
        return ResponseVO.data(null);
    }

    /**
     * 导出表格
     */
    @GetMapping("/export_excel")
    public void exportExcel(@RequestParam("quotation_info_id") Long quotationInfoId, @RequestParam("cooperation_id") Long cooperationId,@RequestParam("type") String type , HttpServletResponse response) throws IllegalAccessException {
        supplierService.exportQuoteExcel(quotationInfoId,cooperationId,type, response);
    }

    /**
     * 更新特定供应商
     */
    @PostMapping("/update_specific_supplier")
    public ResponseVO updateSpecificSupplier(@RequestBody SupplierReq supplierReq) {
        return ResponseVO.data(supplierService.updateSpecificSupplier(supplierReq.getQuotationInfoId(),supplierReq.getSpecificSupplierIds(),1,Boolean.FALSE));
    }


    /**
     * 计算保费规模
     */
    @GetMapping("/match_premium_scale")
    public ResponseVO matchPremiumScale(@RequestParam("quotation_info_id") Long quotationInfoId) {
        BigDecimal premiumScale = supplierService.matchPremiumScale(quotationInfoId);
        return ResponseVO.data(premiumScale);
    }

    /**
     * 预估赔付率
     * @param quotationInfoId
     * @return
     */
    @GetMapping("/predict")
    public ResponseVO predict(@RequestParam("quotation_info_id") Long quotationInfoId) {
        return ResponseVO.data(supplierService.predict(quotationInfoId));
    }


    /**
     * 更新特定供应商排序
     */
    @GetMapping("/update_specific_supplier_sort")
    public ResponseVO updateSpecificSupplierSort(@RequestParam("quotation_info_id") Long quotationInfoId) {
        supplierService.updatePreferSupplierSort(quotationInfoId);
        return ResponseVO.data(null);
    }

    /**
     * 修复协议佣金率
     */
    @GetMapping("/update_commission")
    public ResponseVO updateCommissionRequire(@RequestParam("quotation_info_id") List<Long> quotationInfoIds) {
        log.warn("修复协议佣金率 参数:{}",quotationInfoIds);
        supplierService.updateCommissionRate(quotationInfoIds);
        return null;
    }

    /**
     * 获取供应商列表
     */
    @GetMapping("/get_supplier_list")
    public ResponseVO getSupplierList(@RequestParam(value = "company_id",required = false) String companyId) {
        List<CompanySupplierInfoDto> supplierInfo = supplierService.getSupplierInfo();
        if (companyId != null){
            supplierInfo=supplierInfo.stream().filter(item -> item.getCompanyId().equals(companyId)).collect(Collectors.toList());
        }
        return ResponseVO.data(supplierInfo);
    }


    /**
     * 手动触发
     * @param quotationInfoId
     * @return
     */
    @GetMapping("/create/prefer/supplier")
    public ResponseVO updateCommissionRequire(@RequestParam("quotation_info_id") Long quotationInfoId) {
        supplierService.createPreferSupplier(quotationInfoId);
        return ResponseVO.data(null);
    }
}
