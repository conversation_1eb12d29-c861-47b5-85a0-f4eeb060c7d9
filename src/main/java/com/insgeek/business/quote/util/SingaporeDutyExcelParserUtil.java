package com.insgeek.business.quote.util;

import ch.qos.logback.core.util.StringCollectionUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.insgeek.boot.commons.exception.InsgeekException;
import com.insgeek.boot.web.consts.ResultCodeEnum;
import com.insgeek.boot.web.util.MessageUtil;
import com.insgeek.business.quote.sgp.dto.*;
import com.insgeek.protocol.data.client.enums.BenefitEnum;
import com.insgeek.protocol.insurance.dto.product.base.ProductItemKeyEnum;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontInstanceList;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontPreCondition;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontProductDutyInstanceDTO;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontProductGroupRuleList;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontProductRuleItemDTO;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontSubList;
import jodd.util.StringUtil;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.usermodel.DateUtil;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;
/**
 * @Description: 新加坡duty的excel责任解析工具类
 * @Date: 2025/8/22
 * @Param null: 
 **/
public class SingaporeDutyExcelParserUtil {
    private SingaporeDutyExcelParserUtil() {
    }
    // sheet 与 configType、复选框映射
//    private static final Map<String, String> SHEET_CONFIG_TYPE_MAP = new LinkedHashMap<>();
    private static final Map<String, String> SHEET_PRE_SELECTION_MAP = new LinkedHashMap<>();
    private static final Set<String> ALLOWED_PRE_CHECK_COLS = new HashSet<>();
    private static final Set<String> ALLOWED_PRE_CHECK_COLS_NORM = new HashSet<>(); // 小写匹配集合

    static {
        SHEET_PRE_SELECTION_MAP.put(BenefitEnum.GHS.getDesc(), BenefitEnum.GHS.getCode());
        SHEET_PRE_SELECTION_MAP.put(BenefitEnum.GMM.getDesc(), BenefitEnum.GMM.getCode());
        SHEET_PRE_SELECTION_MAP.put(BenefitEnum.GP.getDesc(), BenefitEnum.GP.getCode());
        SHEET_PRE_SELECTION_MAP.put(BenefitEnum.SP.getDesc(), BenefitEnum.SP.getCode());
        SHEET_PRE_SELECTION_MAP.put(BenefitEnum.GD.getDesc(), BenefitEnum.GD.getCode());
        SHEET_PRE_SELECTION_MAP.put(BenefitEnum.GTL.getDesc(), BenefitEnum.GTL.getCode());
        SHEET_PRE_SELECTION_MAP.put(BenefitEnum.GCI.getDesc(), BenefitEnum.GCI.getCode());
        SHEET_PRE_SELECTION_MAP.put(BenefitEnum.GPA.getDesc(), BenefitEnum.GPA.getCode());
        SHEET_PRE_SELECTION_MAP.put(BenefitEnum.GADD.getDesc(), BenefitEnum.GADD.getCode());
        SHEET_PRE_SELECTION_MAP.put(BenefitEnum.GDI.getDesc(), BenefitEnum.GDI.getCode());
        SHEET_PRE_SELECTION_MAP.put(BenefitEnum.GM.getDesc(), BenefitEnum.GM.getCode());
        SHEET_PRE_SELECTION_MAP.put(BenefitEnum.OTHER.getDesc(), BenefitEnum.OTHER.getCode());
        for (String v : SHEET_PRE_SELECTION_MAP.values()) {
            ALLOWED_PRE_CHECK_COLS.add(v);
            ALLOWED_PRE_CHECK_COLS_NORM.add(v.toLowerCase(Locale.ROOT));
        }
    }

    /**
     * 解析入口：接收 MultipartFile，转为标准的quotePlan
     */
    public static SingaporeDutyExcelPreDTO convertExcelToQuotePlan(MultipartFile file) throws Exception {
        try (InputStream is = file.getInputStream(); Workbook workbook = WorkbookFactory.create(is)) {

            // 1) 解析 Pre Selection
            Sheet preSelectionSheet = workbook.getSheet("Pre Selection");
            Map<String, SingaporeDutyExcelPlanDTO> planMap = parsePreSelection(preSelectionSheet);

            // 2) 遍历责任配置 sheet（仅解析被勾选的险种）
            for (Map.Entry<String, String> entry : SHEET_PRE_SELECTION_MAP.entrySet()) {
                String sheetName = entry.getKey();
                Sheet sheet = workbook.getSheet(sheetName);
                if (sheet == null) continue;

                String preColKey = SHEET_PRE_SELECTION_MAP.get(sheetName); // 如 GHS
                for (Map.Entry<String, SingaporeDutyExcelPlanDTO> singlePlanMap : planMap.entrySet()) {
                    String planCategoryKey = singlePlanMap.getKey();
                    SingaporeDutyExcelPlanDTO planDTO = singlePlanMap.getValue();
                    if (!planDTO.getInsuranceTypeDetailList().contains(preColKey)) {
                        continue; // 该方案未勾选该险种，不解析
                    }

                    SingaporeDutyExcelPlanConfigDTO configDTO = new SingaporeDutyExcelPlanConfigDTO();
                    configDTO.setPlanConfigName(sheetName);
                    configDTO.setInsuranceTypeDetail(SHEET_PRE_SELECTION_MAP.get(sheetName));
                    configDTO.setDutyList(parseDuty(sheet, planCategoryKey));
                    // 找到dutyList中productName为Sum Assured的元素，取出Sum Assured的value赋值给configDTO的sumAssured，并从dutyList中删除该元素
//                    setConfigSumAssured(configDTO);
                    // 找到dutyList中productName为remarks的元素，取出remarks的value赋值给configDTO的remarks，并从dutyList中删除该元素
                    setConfigRemarks(configDTO);
                    // 共享保额区域
                    configDTO.setSharedList(parseSharedLimit(sheet));
                    // 只返回存在责任信息的配置
                    if (CollectionUtil.isNotEmpty(configDTO.getDutyList())) {
                        planDTO.getPlanConfigList().add(configDTO);
                    }
                }
            }
            // 遍历所有的SharedList，根据sharedPlanName重新分组，然后将分组后的SharedList赋给planDTO的sharedList
            for (Map.Entry<String, SingaporeDutyExcelPlanDTO> singlePlanMap : planMap.entrySet()) {
                String planCategoryKey = singlePlanMap.getKey();
                SingaporeDutyExcelPlanDTO planDTO = singlePlanMap.getValue();
                List<SingaporeDutyExcelSharedDTO> allSharedList = new ArrayList<>();
                // 收集所有配置中的SharedList
                if (planDTO.getPlanConfigList() != null) {
                    for (SingaporeDutyExcelPlanConfigDTO configDTO : planDTO.getPlanConfigList()) {
                        if (configDTO.getSharedList() != null && !configDTO.getSharedList().isEmpty()) {
                            // 只保留当前方案相关的共享保额信息
                            List<SingaporeDutyExcelSharedDTO> currentPlanSharedList = configDTO.getSharedList().stream()
                                    .filter(sharedDTO -> planCategoryKey.equals(sharedDTO.getSharedPlanName()))
                                    .collect(Collectors.toList());
                            
                            // 将过滤后的共享保额信息保留在当前配置下
                            configDTO.setSharedList(currentPlanSharedList);
                            
                            // 收集所有共享保额信息用于后续分组
                            allSharedList.addAll(configDTO.getSharedList());
                        }
                    }
                }
            }

            SingaporeDutyExcelPreDTO result = new SingaporeDutyExcelPreDTO();
            result.setPlanList(new ArrayList<>(planMap.values()));
            return result;
        }
    }
    /**
     * @Description: 设置配置的总保额字段
     * @Date: 2025/8/26
     * @Param configDTO:
     **/
    private static void setConfigSumAssured(SingaporeDutyExcelPlanConfigDTO configDTO) {
        List<SingaporeDutyExcelDutyDTO> dutyList = configDTO.getDutyList();
        if (dutyList != null && !dutyList.isEmpty()) {
            Iterator<SingaporeDutyExcelDutyDTO> iterator = dutyList.iterator();
            while (iterator.hasNext()) {
                SingaporeDutyExcelDutyDTO dutyDTO = iterator.next();
                if ("Sum Assured".equalsIgnoreCase(dutyDTO.getProductName())) {
                    List<SingaporeDutyExcelDutyFieldDTO> fieldList = dutyDTO.getFieldList();
                    if (fieldList != null && !fieldList.isEmpty()) {
                        String sumAssuredValue = fieldList.get(0).getValue();
                        configDTO.setSumAssured(sumAssuredValue);
                    }
                    iterator.remove();
                    break;
                }
            }
        }
    }
    /**
     * @Description: 设置配置的总保额字段
     * @Date: 2025/8/26
     * @Param configDTO:
     **/
    private static void setConfigRemarks(SingaporeDutyExcelPlanConfigDTO configDTO) {
        List<SingaporeDutyExcelDutyDTO> dutyList = configDTO.getDutyList();
        if (dutyList != null && !dutyList.isEmpty()) {
            Iterator<SingaporeDutyExcelDutyDTO> iterator = dutyList.iterator();
            while (iterator.hasNext()) {
                SingaporeDutyExcelDutyDTO dutyDTO = iterator.next();
                if ("Remarks".equalsIgnoreCase(dutyDTO.getProductName())) {
                    List<SingaporeDutyExcelDutyFieldDTO> fieldList = dutyDTO.getFieldList();
                    if (fieldList != null && !fieldList.isEmpty()) {
                        String remarksValue = fieldList.get(0).getValue();
                        configDTO.setRemarks(remarksValue);
                    }
                    iterator.remove();
                    break;
                }
            }
        }
    }

    /**
     * 解析 Pre Selection 页：识别 PlanName / Category / 复选框列
     */
    private static Map<String, SingaporeDutyExcelPlanDTO> parsePreSelection(Sheet sheet) {
        Map<String, SingaporeDutyExcelPlanDTO> map = new LinkedHashMap<>();
        Set<String>errorPlanNameCategorySet = new HashSet<>();
        if (sheet == null) return map;

        Row headerRow = sheet.getRow(sheet.getFirstRowNum());
        if (headerRow == null) return map;

        int colPlanName = findHeaderIndex(headerRow, "PlanName");
        int colCategory = findHeaderIndex(headerRow, "Category");

        if (colPlanName == -1) {
            throw new IllegalStateException("Pre Selection 页缺少 PlanName 列头");
        }
        if (colCategory == -1) {
            colCategory = headerRow.getLastCellNum() - 1; // 容错
        }

        // ✅ 复选框列：用小写集合匹配
        List<Integer> checkCols = new ArrayList<>();
        for (int j = colPlanName + 1; j < colCategory; j++) {
            String headRaw = getCellString(headerRow.getCell(j));
            String headNorm = norm(headRaw);
            if (ALLOWED_PRE_CHECK_COLS_NORM.contains(headNorm)) {
                checkCols.add(j);
            }
        }

        for (int i = sheet.getFirstRowNum() + 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            String planNameRaw = getCellString(row.getCell(colPlanName));
            String planName = normalizePlanName(planNameRaw);
            if (isBlank(planName)) continue;

            String category = getCellString(row.getCell(colCategory));

            SingaporeDutyExcelPlanDTO dto = new SingaporeDutyExcelPlanDTO();
            dto.setPlanName(planName);
            dto.setEmployeeCategory(safeStr(category));
            dto.setPlanConfigList(new ArrayList<>());
            dto.setInsuranceTypeDetailList(new ArrayList<>());

            for (Integer idx : checkCols) {
                if (isChecked(row.getCell(idx))) {
                    String colName = norm(getCellString(headerRow.getCell(idx)));
                    if (!isBlank(colName)) {
                        // 用原始大小写（映射表中的值）存进去
                        String original = getCellString(headerRow.getCell(idx)).trim();
                        dto.getInsuranceTypeDetailList().add(original);
                    }
                }
            }
            String key = planName +"|"+ category;
            if (map.containsKey(key)){
                errorPlanNameCategorySet.add(key);
            }else {
                map.put(key, dto);
            }
        }
        if (!errorPlanNameCategorySet.isEmpty()) {
            String errorMessages = String.join(",", errorPlanNameCategorySet);
            throw new InsgeekException(ResultCodeEnum.FAILED.getCode(),MessageUtil.get("b_sg_excel_error_009", errorMessages));
        }
        return map;
    }

    /**
     *  解析责任信息（for 循环实现；合并单元格继承产品名；共享保额之前截断）
     * Coverage / Benefit → productName
     * Options / Fields → field.title
     * planName 对应列 → field.value
     */
    private static List<SingaporeDutyExcelDutyDTO> parseDuty(Sheet sheet, String planNameRaw) {
        List<SingaporeDutyExcelDutyDTO> dutyList = new ArrayList<>();
        if (sheet == null) return dutyList;

        Row headerRow = sheet.getRow(sheet.getFirstRowNum());
        if (headerRow == null) return dutyList;

        int colCoverage = findHeaderIndex(headerRow, "Coverage / Benefit");
        int colFields = findHeaderIndex(headerRow, "Options / Fields");

        String planName = normalizePlanName(planNameRaw);
        int planColIndex = findPlanColumnIndex(headerRow, planName);
        if (planColIndex == -1) {
            return dutyList; // 该 sheet 未配置该方案列
        }

        int sharedHeaderRow = findSharedHeaderRow(sheet);
        int lastDutyRow = (sharedHeaderRow == -1 ? sheet.getLastRowNum() : sharedHeaderRow - 1);

        String lastProductName = null;
        for (int i = sheet.getFirstRowNum() + 1; i <= lastDutyRow; i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;

            String currentProduct = colCoverage == -1 ? "" : getCellString(row.getCell(colCoverage));
            if (!isBlank(currentProduct)) {
                lastProductName = currentProduct.trim();
            }
            String productName = safeStr(lastProductName);
            if (isBlank(productName)) continue;

            String fieldTitle = colFields == -1 ? "" : getCellString(row.getCell(colFields));
            String planValue = getCellString(row.getCell(planColIndex));

            // 过滤纯空行
            if (isBlank(fieldTitle) && isBlank(planValue)) continue;

            SingaporeDutyExcelDutyDTO duty = null;
            for (SingaporeDutyExcelDutyDTO d : dutyList) {
                if (productName.equals(d.getProductName())) {
                    duty = d;
                    break;
                }
            }
            if (duty == null) {
                duty = new SingaporeDutyExcelDutyDTO();
                duty.setProductName(productName);
                duty.setFieldList(new ArrayList<>());
                dutyList.add(duty);
            }

            SingaporeDutyExcelDutyFieldDTO field = new SingaporeDutyExcelDutyFieldDTO();
            field.setTitle(safeStr(fieldTitle));
            field.setValue(safeStr(planValue));
            duty.getFieldList().add(field);
        }

        // 清理空字段 duty
        List<SingaporeDutyExcelDutyDTO> result = new ArrayList<>();
        for (SingaporeDutyExcelDutyDTO d : dutyList) {
            if (d.getFieldList() != null && !d.getFieldList().isEmpty()) {
                // 如果d的所有filed的value都为空，则删除该duty
                if (d.getFieldList().stream().allMatch(field -> isBlank(field.getValue()))) {
                    continue;
                }
                result.add(d);
            }
        }
        return result;
    }

    /**
     * 解析共享保额区域
     */
    private static List<SingaporeDutyExcelSharedDTO> parseSharedLimit(Sheet sheet) {
        List<SingaporeDutyExcelSharedDTO> sharedList = new ArrayList<>();
        if (sheet == null) return sharedList;

        int headerRowIdx = findSharedHeaderRow(sheet);
        if (headerRowIdx == -1) return sharedList;

        for (int i = headerRowIdx + 1; i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) break;

            String c0 = getCellString(row.getCell(0));
            String c1 = getCellString(row.getCell(1));
            String c2 = getCellString(row.getCell(2));

            if (isBlank(c0) && isBlank(c1) && isBlank(c2)) break;

            SingaporeDutyExcelSharedDTO s = new SingaporeDutyExcelSharedDTO();
            s.setSharedPlanName(safeStr(c0));
            s.setSharedLimit(safeStr(c1));
            if (!isBlank(c2)) {
                String normalized = c2.replace("，", ",").replace("、", ",");
                List<String> names = Arrays.stream(normalized.split(","))
                        .map(String::trim).filter(x -> !x.isEmpty()).collect(Collectors.toList());
                s.setDutyNameList(names);
            } else {
                s.setDutyNameList(Collections.emptyList());
            }
            sharedList.add(s);
        }
        return sharedList;
    }

    // ================= 工具方法 =================

    /** 查找表头列（严格匹配 + contains 容错） */
    private static int findHeaderIndex(Row headerRow, String expected) {
        if (headerRow == null) return -1;
        String exp = norm(expected);
        short last = headerRow.getLastCellNum();
        for (int i = 0; i < last; i++) {
            String val = norm(getCellString(headerRow.getCell(i)));
            if (val.equals(exp)) return i;
        }
        for (int i = 0; i < last; i++) {
            String val = norm(getCellString(headerRow.getCell(i)));
            if ((!val.isEmpty() && exp.contains(val)) || val.contains(exp)) {
                return i;
            }
        }
        return -1;
    }

    /** 找到方案列索引（列名=planName，数字方案名容错） */
    private static int findPlanColumnIndex(Row headerRow, String planNameNorm) {
        if (headerRow == null) return -1;
        short last = headerRow.getLastCellNum();
        for (int i = 0; i < last; i++) {
            String head = normalizePlanName(getCellString(headerRow.getCell(i)));
            if (!isBlank(head) && head.equalsIgnoreCase(planNameNorm)) {
                return i;
            }
        }
        for (int i = 0; i < last; i++) {
            String head = normalizePlanName(getCellString(headerRow.getCell(i)));
            if (!isBlank(head) && head.trim().equalsIgnoreCase(planNameNorm.trim())) {
                return i;
            }
        }
        return -1;
    }

    /** 共享保额表头定位：行内任意前三列同时包含 shared limit 与 coverage / benefit combination */
    private static int findSharedHeaderRow(Sheet sheet) {
        for (int i = sheet.getFirstRowNum(); i <= sheet.getLastRowNum(); i++) {
            Row row = sheet.getRow(i);
            if (row == null) continue;
            String c0 = norm(getCellString(row.getCell(0)));
            String c1 = norm(getCellString(row.getCell(1)));
            String c2 = norm(getCellString(row.getCell(2)));

            boolean hasShared =
                    c0.contains("shared limit") || c1.contains("shared limit") || c2.contains("shared limit");
            boolean hasCombo =
                    c0.contains("coverage / benefit combination") ||
                            c1.contains("coverage / benefit combination") ||
                            c2.contains("coverage / benefit combination");
            if (hasShared && hasCombo) {
                return i;
            }
        }
        return -1;
    }

    /** 统一读取单元格为字符串（公式取缓存值；数字去掉无意义小数；日期转 yyyy-MM-dd） */
    private static String getCellString(Cell cell) {
        if (cell == null) return "";
        CellType type = cell.getCellType();
        switch (type) {
            case STRING:
                return safeStr(cell.getStringCellValue());
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue() == null ? "" :
                            new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
                }
                double d = cell.getNumericCellValue();
                if (Math.floor(d) == d) {
                    return String.valueOf((long) d);
                } else {
                    String s = String.valueOf(d);
                    if (s.contains(".")) s = s.replaceAll("0+$", "").replaceAll("\\.$", "");
                    return s;
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                CellType cached = cell.getCachedFormulaResultType();
                if (cached == CellType.STRING) return safeStr(cell.getStringCellValue());
                if (cached == CellType.NUMERIC) {
                    if (DateUtil.isCellDateFormatted(cell)) {
                        return cell.getDateCellValue() == null ? "" :
                                new SimpleDateFormat("yyyy-MM-dd").format(cell.getDateCellValue());
                    }
                    double dv = cell.getNumericCellValue();
                    if (Math.floor(dv) == dv) return String.valueOf((long) dv);
                    String s = String.valueOf(dv);
                    if (s.contains(".")) s = s.replaceAll("0+$", "").replaceAll("\\.$", "");
                    return s;
                }
                if (cached == CellType.BOOLEAN) return String.valueOf(cell.getBooleanCellValue());
                return "";
            case BLANK:
            case _NONE:
            case ERROR:
            default:
                return "";
        }
    }

    /** 复选框判断：Y/YES/TRUE/1/√/☑/✔ 或布尔真/数值1 */
    private static boolean isChecked(Cell cell) {
        return StringUtil.isNotEmpty(getCellString(cell));
//        switch (cell.getCellType()) {
//            case BOOLEAN:
//                return cell.getBooleanCellValue();
//            case NUMERIC:
//                return cell.getNumericCellValue() == 1.0d;
//            case STRING:
//            case FORMULA:
//            default:
//                String v = norm(getCellString(cell));
//                return "y".equals(v) || "yes".equals(v) || "true".equals(v) ||
//                        "1".equals(v) || "√".equals(v) || "☑".equals(v) || "✔".equals(v) || "ü".equals(v);
//        }
    }

    /** 规范化方案名（去 .0） */
    private static String normalizePlanName(String s) {
        String t = safeStr(s).trim();
        if (t.matches("^-?\\d+\\.0+$")) {
            t = t.replaceAll("\\.0+$", "");
        }
        return t;
    }

    /** 小写去多空格 */
    private static String norm(String s) {
        if (s == null) return "";
        return s.trim().toLowerCase(Locale.ROOT).replaceAll("\\s+", " ");
        // 注意：不要在这里把 "/" 替换掉，表头需要保留它
    }

    private static boolean isBlank(String s) {
        return s == null || s.trim().isEmpty();
    }

    private static String safeStr(String s) {
        String value = s == null ? "" : s.trim();
        if (Objects.equals(value.toLowerCase(), "as charged")) {
            value = "99999999";
        }
        return value;
    }

    /**
     * @Description: 将FrontProductDutyInstanceDTO转为SingaporeDutyExcelDutyDTO
     * @Date: 2025/8/28
     * @Param frontProductDutyInstanceDTO: 
     **/
    public static SingaporeDutyExcelDutyDTO convertToSingaporeDutyExcelDutyDTO(FrontProductDutyInstanceDTO frontProductDutyInstanceDTO) {
        if (frontProductDutyInstanceDTO == null) {
            return null;
        }
        
        SingaporeDutyExcelDutyDTO dutyDTO = new SingaporeDutyExcelDutyDTO();
        
        // 设置产品名称
        dutyDTO.setProductName(frontProductDutyInstanceDTO.getName());
        
        // 设置配置ID
        if (frontProductDutyInstanceDTO.getDutyField() != null && 
            frontProductDutyInstanceDTO.getDutyField().containsKey("plan_config_id")) {
            Object planConfigIdObj = frontProductDutyInstanceDTO.getDutyField().get("plan_config_id");
            if (planConfigIdObj != null) {
                try {
                    dutyDTO.setPlanConfigId(Long.valueOf(planConfigIdObj.toString()));
                } catch (NumberFormatException e) {
                    // 忽略转换异常，保持planConfigId为null
                }
            }
        }
        
        // 转换字段列表
        List<SingaporeDutyExcelDutyFieldDTO> fieldList = new ArrayList<>();
        List<FrontProductGroupRuleList> productGroupRuleList = frontProductDutyInstanceDTO.getProductGroupRuleList();
        
        if (productGroupRuleList != null) {
            for (FrontProductGroupRuleList groupRuleList : productGroupRuleList) {
                if (groupRuleList == null) {
                    continue;
                }
                
                List<FrontInstanceList> instanceList = groupRuleList.getInstanceList();
                if (instanceList != null) {
                    for (FrontInstanceList instance : instanceList) {
                        if (instance == null) {
                            continue;
                        }
                        
                        // 处理子列表
                        List<FrontSubList> subList = instance.getSubList();
                        if (subList != null) {
                            for (FrontSubList sub : subList) {
                                if (sub == null) {
                                    continue;
                                }
                                
                                List<FrontProductRuleItemDTO> productRuleItemList = sub.getProductRuleItem();
                                if (productRuleItemList != null) {
                                    for (FrontProductRuleItemDTO ruleItem : productRuleItemList) {
                                        if (ruleItem == null) {
                                            continue;
                                        }
                                        // 只处理实际的值，不处理条件字段
                                        if (ProductItemKeyEnum.getAllKeys().contains(ruleItem.getItemKey())) {
                                            SingaporeDutyExcelDutyFieldDTO fieldDTO = new SingaporeDutyExcelDutyFieldDTO();
                                            fieldDTO.setTitle(ruleItem.getItemTitle());
                                            fieldDTO.setValue(convertItemValueToString(ruleItem.getItemValue()));
                                            fieldList.add(fieldDTO);
                                        }

                                    }
                                }
                            }
                        }
                        
                        // 处理前置条件
                        List<FrontPreCondition> preConditionList = instance.getPreCondition();
                        if (preConditionList != null) {
                            for (FrontPreCondition preCondition : preConditionList) {
                                if (preCondition == null) {
                                    continue;
                                }
                                
                                List<FrontProductRuleItemDTO> productRuleItemList = preCondition.getProductRuleItem();
                                if (productRuleItemList != null) {
                                    for (FrontProductRuleItemDTO ruleItem : productRuleItemList) {
                                        if (ruleItem == null) {
                                            continue;
                                        }
                                        
                                        SingaporeDutyExcelDutyFieldDTO fieldDTO = new SingaporeDutyExcelDutyFieldDTO();
                                        fieldDTO.setTitle(ruleItem.getItemTitle());
                                        fieldDTO.setValue(convertItemValueToString(ruleItem.getItemValue()));
                                        fieldList.add(fieldDTO);
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        dutyDTO.setFieldList(fieldList);
        return dutyDTO;
    }
    
    /**
     * 将itemValue转换为字符串表示
     * 
     * @param itemValue 原始值
     * @return 字符串表示
     */
    private static String convertItemValueToString(Object itemValue) {
        if (itemValue == null) {
            return "";
        }
        
        if (itemValue instanceof String) {
            return (String) itemValue;
        }
        
        if (itemValue instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) itemValue;
            if (map.containsKey("amount")) {
                return String.valueOf(map.get("amount"));
            }
            return map.toString();
        }
        
        return itemValue.toString();
    }
}
