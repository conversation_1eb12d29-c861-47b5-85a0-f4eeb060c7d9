package com.insgeek.business.quote.external.Insurance.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.json.JSONUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.insgeek.boot.commons.datetime.DateTimeUtil;
import com.insgeek.boot.commons.exception.InsgeekException;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.boot.commons.json.ObjectUtil;
import com.insgeek.boot.web.auth.dto.IdentityDto;
import com.insgeek.boot.web.pojo.dto.CurrencyAmount;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.contract.config.NacosQuoteCompanyDocking;
import com.insgeek.business.quote.backend.Enums.QuoteTypeEnum;
import com.insgeek.business.quote.backend.dto.quote.AutoCreatePlanExtDTO;
import com.insgeek.business.quote.backend.exception.BackendErrorMsgAndCode;
import com.insgeek.business.quote.backend.exception.QuoteErrorCode;
import com.insgeek.business.quote.backend.utils.PlanBasicDataUtil;
import com.insgeek.business.quote.backend.utils.PlanBasicDataV2Util;
import com.insgeek.business.quote.common.config.DefaultPlanInfo;
import com.insgeek.business.quote.common.dao.constant.QpQuoteDutySplitConst;
import com.insgeek.business.quote.common.dto.CoreChannelDto;
import com.insgeek.business.quote.common.dto.InsuranceQuoteKey;
import com.insgeek.business.quote.common.dto.QpQuoteContractCustomerDto;
import com.insgeek.business.quote.common.dto.QuoteRelevantDto;
import com.insgeek.business.quote.common.dto.param.CreatePlanParam;
import com.insgeek.business.quote.common.dto.result.CreatePlanResult;
import com.insgeek.business.quote.common.enums.QuoteSourceEnum;
import com.insgeek.business.quote.common.enums.dict.*;
import com.insgeek.business.quote.common.exception.QuoteException;
import com.insgeek.business.quote.common.feign.api.InsuranceEmployeeCategoryService;
import com.insgeek.business.quote.common.service.CustomerQuoteRelationService;
import com.insgeek.business.quote.common.service.QuoteContractCustomerService;
import com.insgeek.business.quote.common.service.TenantChannelProductService;
import com.insgeek.business.quote.common.service.impl.QuoteCurrencyUtilService;
import com.insgeek.business.quote.common.utils.CommonDataUtil;
import com.insgeek.business.quote.config.InsuranceTypeConfig;
import com.insgeek.business.quote.config.MedicalConfig;
import com.insgeek.business.quote.dto.InsuranceEmployeeCategoryDto;
import com.insgeek.business.quote.external.Insurance.service.InsuranceService;
import com.insgeek.business.quote.external.dto.ConfigFamilyGroup;
import com.insgeek.business.quote.quotation.dao.constant.QpQuotationDutySplitConst;
import com.insgeek.business.quote.quotation.service.QpQuotationNonInsuranceServicesInfoService;
import com.insgeek.business.quote.quotation.service.SpecialService;
import com.insgeek.business.quote.util.DateFormater;
import com.insgeek.business.standard.enums.AtuoCreatePlanStrategyTypeEnum;
import com.insgeek.components.orm.model.impl.bql.BqlMapper;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.components.orm.model.querydsl.BQLQueryFactory;
import com.insgeek.insurance.meta.utils.context.IdentityUtil;
import com.insgeek.protocol.business.group.client.OrganizationClient;
import com.insgeek.protocol.business.group.dto.IgGroupDTO;
import com.insgeek.protocol.common.client.SendMessageClient;
import com.insgeek.protocol.common.dto.SendMailDto;
import com.insgeek.protocol.common.dto.SendMessageDto;
import com.insgeek.protocol.data.client.dto.*;
import com.insgeek.protocol.data.client.entity.*;
import com.insgeek.protocol.data.client.entity.QpQuoteApprove;
import com.insgeek.protocol.insurance.auto_plan.client.AutoPlanClient;
import com.insgeek.protocol.insurance.auto_plan.dto.*;
import com.insgeek.protocol.insurance.client.*;
import com.insgeek.protocol.insurance.dto.geekplus.group.GroupDetailDTO;
import com.insgeek.protocol.insurance.dto.insurance.ReqBillSettleDto;
import com.insgeek.protocol.insurance.dto.insurance.response.AutoCreatePlanDTO;
import com.insgeek.protocol.insurance.dto.special.instance.ListParamDTO;
import com.insgeek.protocol.insurance.dto.special.instance.SpecialInstanceDetailDTO;
import com.insgeek.protocol.insurance.entity.IgSpecial;
import com.insgeek.protocol.insurance.enums.RiskRuleTypeEnum;
import com.insgeek.protocol.insurance.enums.SpecialSceneEnum;
import com.insgeek.protocol.insurance.vo.plan.*;
import com.insgeek.protocol.platform.common.client.BQLClient;
import com.insgeek.protocol.platform.common.dto.entity.*;
import com.insgeek.protocol.platform.common.dto.insurance.config.DutyDTO;
import com.insgeek.protocol.platform.common.dto.insurance.config.PlanConfigDTO;
import com.insgeek.protocol.platform.common.dto.insurance.config.PlanConfigQuoteDTO;
import com.insgeek.protocol.platform.common.enums.DefVendorOccEnum;
import com.querydsl.core.types.dsl.Expressions;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * @ClassName: InsuranceServiceImpl
 * @Description: 与木人桩交互
 * @Author: haifeng
 * @Date: 2022/8/29 20:33
 */
@Service
@Slf4j
@RefreshScope
@SuppressWarnings("all")
public class InsuranceServiceImpl implements InsuranceService {
    //  保险公司ID，目前只有现代
    static final Long COMPANY_ID = 92L;

    @Value("${insgeek.createPlan.minAge}")
    private Integer defaultMinAge;

    @Value("${insgeek.createPlan.maxAge}")
    private Integer defaultMaxAge;

    @Value("${insgeek.createPlan.avgAge}")
    private Integer defaultAvgAge;

    @Value("${insgeek.createPlan.addPercentage}")
    private Integer addPercentage;

    @Resource
    private MedicalConfig medicalConfig;

    @Resource
    private QuoteContractCustomerService quoteContractCustomerService;


    @Qualifier(value = "customerExecutorPool2")
    @Autowired
    private Executor threadPoolTaskExecutor;

    @Autowired
    private AutoPlanClient autoPlanClient;

    @Autowired
    private DefaultPlanInfo defaultPlanInfo;

    @Autowired
    private DataMapper<QpQuoteApprove> quoteApproveDataMapper;

    @SuppressWarnings("all")
    @Autowired
    private DataMapper<QpQuoteInfo> quoteInfoDataMapper;

    @Autowired
    private DataMapper<QpQuotationInfo> quotationInfoDataMapper;

    @SuppressWarnings("all")
    @Autowired
    private DataMapper<QpQuote> quoteDataMapper;

    @Autowired
    private DataMapper<QpQuotation> quotationDataMapper;
    @SuppressWarnings("all")
    @Autowired
    private DataMapper<QpQuoteConfig> quoteConfigDataMapper;

    @Autowired
    private DataMapper<QpQuotationConfig> quotationConfigDataMapper;

    @SuppressWarnings("all")
    @Autowired
    private DataMapper<QpQuoteDuty> quoteDutyDataMapper;

    @Autowired
    private DataMapper<QpQuotationDutySplit> qpQuotationDutySplitDataMapper;

    @Autowired
    private DataMapper<QpQuotationDuty> quotationDutyDataMapper;

    @SuppressWarnings("all")
    @Autowired
    private DataMapper<QpQuoteDutySplit> qpQuoteDutySplitDataMapper;

    @SuppressWarnings("all")
    @Autowired
    private DataMapper<QpCustomer> customerDataMapper;

    @SuppressWarnings("all")
    @Autowired
    private DataMapper<QpQuoteContractCustomer> quoteContractCustomerDataMapper;

    @SuppressWarnings("all")
    @Autowired
    private DataMapper<QpQuoteContract> quoteContractDataMapper;

    @Autowired
    DataMapper<QpContract> qpContractDataMapper;

    @Resource
    private CustomerQuoteRelationService customerQuoteRelationService;

    @Autowired
    private InsuranceClient insuranceClient;

    @Autowired
    private QuoteClient quoteClient;

    @Autowired
    private SpecialRulesClient specialRulesClient;

    @Resource
    private InsuranceTypeConfig insuranceTypeConfig;

    @Autowired
    private DataMapper<QpCustomerQuoteRelation> customerQuoteRelationMapper;

    @Autowired
    private TenantChannelProductService tenantChannelProductService;

    @Resource
    private QuoteCurrencyUtilService quoteCurrencyUtilService;


    @Autowired
    private PlanClient planClient;

    @Autowired
    private OrganizationClient organizationClient;

    @Autowired
    private DataMapper<IgProduct> productDataMapper;

    @Autowired
    private DataMapper<QpCustomerQuoteRelation> qpCustomerQuoteRelationDataMapper;

    @Resource
    private QpQuotationNonInsuranceServicesInfoService qpQuotationNonInsuranceServicesInfoService;

    @Autowired
    private BqlMapper<QpQuoteInfoGroupDTO> qpQuoteInfoGroupBqlMapper;

    @Autowired
    private SpecialService specialService;

    @Resource
    private BQLClient bqlClient;

    @Autowired
    private BQLQueryFactory bqlQueryFactory;

    @Autowired
    private DataMapper<QpQuotationConfigInsuRelation> quotationConfigInsuRelationDataMapper;

    @Autowired
    private InsuranceEmployeeCategoryService insuranceEmployeeCategoryService;

    @Resource
    NacosQuoteCompanyDocking nacosQuoteCompanyDocking;

    @Resource
    DataMapper<IgCompany> companyDataMapper;

    @Autowired
    private DataMapper<IgSpecial> igSpecialDataMapper;

    @Autowired
    private DataMapper<IgStandardOrder> igStandardOrderDataMapper;

    @Autowired
    private DataMapper<IgStandardOrderDetail> igStandardOrderDetailDataMapper;

    @Resource
    SendMessageClient sendMessageClient;

    @Resource
    SpecialInstanceClient specialInstanceClient;

    /**
     * 拼接方案唯一标识
     *
     * @param quoteId                 报价id
     * @param quoteContractCustomerId 报价合同下企业id
     * @return 拼接结果
     */
    public static String getQuoteCustomer(Long quoteId, Long quoteContractCustomerId) {
        InsuranceQuoteKey insuranceQuoteKey = new InsuranceQuoteKey(quoteId, quoteContractCustomerId);
        return insuranceQuoteKey.getQuoteAndContractCustomerId();
    }

    @Override
    public List<CreatePlanResult> createPlan(List<CreatePlanParam> param) {
        List<AutoCreatePlanDTO> resultError = Lists.newArrayList();
        // 获取所有方案
        // List<QpQuote> qpQuoteList = quoteDataMapper.entity(QpQuote.class).select(param.stream().map(CreatePlanParam::getQuoteId).collect(Collectors.toList()), true);//企业信息创建
        // 拆分主被方案
        // Map<Boolean, List<QpQuote>> quoteTypeList = qpQuoteList.stream().collect(Collectors.groupingBy(v -> v.getTargetPerson().contains("1")));
        // checkData(param, quoteTypeList);
        resultError.addAll(pushInsurancePlanNew(param));
        // 生成本人方案
       /* List<QpQuote> mainQuoteList = quoteTypeList.get(Boolean.TRUE);
        if (!CollectionUtils.isEmpty(mainQuoteList)) {
            if (log.isDebugEnabled()) {
                log.debug("createPlan-开始生成主被方案:{}", JSONUtil.toJsonStr(mainQuoteList));
            }
            List<Long> mainQuoteIds = mainQuoteList.stream().map(QpQuote::getId).collect(Collectors.toList());
            List<CreatePlanParam> mainParam = param.stream().filter(v -> mainQuoteIds.contains(v.getQuoteId())).collect(Collectors.toList());
            resultError.addAll(pushInsurancePlanNew(mainParam));
        }
        //生成连带方案
        List<QpQuote> relyQuoteList = quoteTypeList.get(Boolean.FALSE);
        if (!CollectionUtils.isEmpty(relyQuoteList)) {
            if (log.isDebugEnabled()) {
                log.debug("createPlan-开始生成连带方案:{}", JSONUtil.toJsonStr(relyQuoteList));
            }
            List<Long> relyQuoteIds = relyQuoteList.stream().map(QpQuote::getId).collect(Collectors.toList());
            List<CreatePlanParam> relyParam = param.stream().filter(v -> relyQuoteIds.contains(v.getQuoteId())).collect(Collectors.toList());
            resultError.addAll(pushInsurancePlanNew(relyParam));
        }*/
        log.info("createPlan-全部报价[{}]生成方案完成", JSONUtil.toJsonStr(param));
        if (!CollectionUtils.isEmpty(resultError)) {
            log.info("createPlan-全部报价[{}]生成方案失败", JSONUtil.toJsonStr(resultError));
            return analysisError(resultError);
        }
        log.info("createPlan-全部报价[{}]生成方案成功", JSONUtil.toJsonStr(param));
        return Lists.newArrayList();
    }

    /**
     * 错误数据解析
     *
     * @param resultError 错误信息
     * @return 1
     */
    private List<CreatePlanResult> analysisError(List<AutoCreatePlanDTO> resultError) {
        List<CreatePlanResult> results = Lists.newArrayList();
        resultError.forEach(v -> {
            CreatePlanResult planResult = new CreatePlanResult();
            InsuranceQuoteKey insuranceQuoteKey = new InsuranceQuoteKey(v.getQuoteCustomer());
            Long quoteId = insuranceQuoteKey.getQuoteId();
            planResult.setQuoteId(quoteId);
            QpQuote qpQuote = quoteDataMapper.entity(QpQuote.class).selectOne(quoteId, true);
            if (!Objects.isNull(qpQuote)) {
                planResult.setQuoteName(qpQuote.getName());
            }
            Long quoteContractCustomerId = insuranceQuoteKey.getQuoteContractCustomerId();
            planResult.setQuoteContractCustomerId(quoteContractCustomerId);
            QpQuoteContractCustomer qpQuoteContractCustomer = quoteContractCustomerDataMapper.entity(QpQuoteContractCustomer.class).selectOne(quoteContractCustomerId, true);
            if (!Objects.isNull(qpQuoteContractCustomer)) {
                planResult.setEnterpriseName(qpQuoteContractCustomer.getEnterpriseName());
            }
            planResult.setErrorMsg(v.getErrorCode() + ":" + v.getErrorMessage());
            results.add(planResult);
        });
        return results;
    }

    @Override
    public List<QpQuoteDuty> queryByCoreConfigIds(List<Long> planIds) {
        List<QpQuoteDuty> result = Lists.newArrayList();
        ResponseVO<Map<Long, List<PlanConfigQuoteDTO>>> mapResponseVO = quoteClient.planConfigInfoByPlan(planIds);
        if (Objects.isNull(mapResponseVO.getData())) {
            return Lists.newArrayList();
        }
        Map<Long, List<PlanConfigQuoteDTO>> data = mapResponseVO.getData();
        data.forEach((configId, planConfigs) -> planConfigs.forEach(planConfig -> {
            List<DutyDTO> duties = planConfig.getDuties();
            if (!CollectionUtils.isEmpty(duties)) {
                duties.forEach(v -> {
                    QpQuoteDuty duty = new QpQuoteDuty();
                    duty.setBusinessType(v.getTypeCode());
                    duty.setId(v.getId());
                    duty.setProductId(v.getProductTypeId());
                    duty.setQuoteConfigId(v.getPlanConfigId());
                    result.add(duty);
                });
            }
        }));
        return result;
    }

    /**
     * 创建方案
     *
     * @param paramList 入参
     */
    private List<AutoCreatePlanDTO> pushInsurancePlanNew(List<CreatePlanParam> paramList) {
        long time = DateTime.now().getTime();
        // 构建核心需要参数
        ExecutorCompletionService<AutoCreatePlanExtDTO> service = new ExecutorCompletionService<>(threadPoolTaskExecutor);
        IdentityDto userInfo = IdentityUtil.getUserInfo();
        log.debug("当前用户信息:{}", JSONUtil.toJsonStr(userInfo));
        log.debug("pushInsurancePlanNew.Time" + time + "-s:{}", DateTime.now().toMsStr());
        paramList.forEach(planParam -> service.submit(() -> {
            IdentityUtil.setAuth(userInfo);
            return autoCreate(planParam);
        }));
        List<AutoCreatePlanExtDTO> parmas = new ArrayList<>();
        for (int i = 0; i < paramList.size(); i++) {
            log.debug("pushInsurancePlanNew.Time" + time + " s-i:{}  {}", i, DateTime.now().toMsStr());
            try {
                Future<AutoCreatePlanExtDTO> take = service.take();
                AutoCreatePlanExtDTO autoCreatePlanExtDTO = take.get();
                parmas.add(autoCreatePlanExtDTO);
            } catch (Exception e) {
                StackTraceElement[] stackTrace = e.getStackTrace();
                String errorMsg = Arrays.stream(stackTrace).map(Object::toString).collect(Collectors.joining("\n"));
                errorMsg = e + ";" + errorMsg;
                log.error("报价方案生成方案前置参数异常-{}", errorMsg);
                CommonDataUtil.businessException(QuoteErrorCode.CREATE_PLAN_PARAMS_ERROR);
            }
            log.debug("pushInsurancePlanNew.Time" + time + " e-i:{}  {}", i, DateTime.now().toMsStr());

        }
        if (log.isDebugEnabled()) {
            log.debug("报价[{}]生成参数成功;详情:{}", JSONUtil.toJsonStr(paramList), JSONUtil.toJsonStr(parmas));
        }
        log.debug("pushInsurancePlanNew.Time" + time + "-e:{}", DateTime.now().toMsStr());
        // 请求创建方案 获取结果
        List<AutoCreatePlanExtDTO> result = requestInsuranceCreatePlans(parmas);
        log.debug("pushInsurancePlanNew.Time" + time + "-result:{}", DateTime.now().toMsStr());

        log.info("方案生成成功;即将进行后置操作");
        List<AutoCreatePlanDTO> resultError = Lists.newArrayList();
        Map<String, CreatePlanParam> createPlanParam = getCreatePlanParam(paramList);

        // 复制特约及数据关系处理
        ExecutorCompletionService<AutoCreatePlanDTO> serviceTy = new ExecutorCompletionService<>(threadPoolTaskExecutor);
        result.forEach(autoCreatePlanExtDTO -> serviceTy.submit(() -> {
            IdentityUtil.setAuth(userInfo);
            log.info("serviceTy.submit.IdentityUtil={}", JacksonUtils.writeAsString(IdentityUtil.getUserInfo()));
            return autoCreatePostUpdate(autoCreatePlanExtDTO, createPlanParam, time);
        }));
        for (int i = 0; i < result.size(); i++) {
            log.debug("pushInsurancePlanNew.Time" + time + " ty s-i:{}  {}", i, DateTime.now().toMsStr());
            try {
                Future<AutoCreatePlanDTO> take = serviceTy.take();
                AutoCreatePlanDTO autoCreatePlanDTO = take.get();
                if (!Objects.isNull(autoCreatePlanDTO)) {
                    resultError.add(autoCreatePlanDTO);
                }
            } catch (Exception e) {
                StackTraceElement[] stackTrace = e.getStackTrace();
                String errorMsg = Arrays.stream(stackTrace).map(Object::toString).collect(Collectors.joining("\n"));
                errorMsg = e + ";" + errorMsg;
                log.error("报价方案复制特约参数异常-{}", errorMsg);
                CommonDataUtil.businessException(QuoteErrorCode.CREATE_TY_PARAMS_ERROR, e.getMessage());
            }
            log.debug("pushInsurancePlanNew.Time" + time + " ty e-i:{}  {}", i, DateTime.now().toMsStr());

        }

       /* for (AutoCreatePlanExtDTO autoCreatePlanExtDTO : result) {
            AutoCreatePlanDTO autoCreatePlanDTO = autoCreatePostUpdate(autoCreatePlanExtDTO, createPlanParam, time);
            if (!Objects.isNull(autoCreatePlanDTO)) {
                resultError.add(autoCreatePlanDTO);
            }
        }*/
        log.debug("pushInsurancePlanNew.Time" + time + "-heand:{}", DateTime.now().toMsStr());
        return resultError;
    }

    /**
     * 创建生成方案参数
     *
     * @param param 方案id
     * @return 创建生成方案参数
     */
    private AutoCreatePlanExtDTO autoCreate(CreatePlanParam param) {
        log.debug("autoCreate.Time-1:{}", DateTime.now().toMsStr());
        QpQuote qpQuote = quoteDataMapper.entity(QpQuote.class).selectOne(param.getQuoteId(), true);
        QpQuoteContractCustomerDto qpQuoteContractCustomerDto = quoteContractCustomerService.queryById(param.getQuoteContractCustomerId());
        if (Objects.isNull(qpQuoteContractCustomerDto)) {
            throw new InsgeekException(QuoteErrorCode.DATA_IS_EMPTY.getMessage());
        }
        Long customerId = qpQuoteContractCustomerDto.getCustomerId();
        log.debug("线程内部用户信息:{}", JSONUtil.toJsonStr(IdentityUtil.getUserInfo()));
        QpCustomer qpCustomer = customerDataMapper.entity(QpCustomer.class).selectOne(customerId, true);
        log.debug("查询经济公司[{}]信息:{}", customerId, qpCustomer);

        // 报价信息
        QpQuoteInfo qpQuoteInfo = quoteInfoDataMapper.entity(QpQuoteInfo.class).selectOne(qpQuote.getQuoteInfoId(), true);
        if (Objects.isNull(qpQuoteInfo)) {
            throw new InsgeekException(QuoteErrorCode.DATA_IS_EMPTY.getMessage());
        }
        log.debug("autoCreate.Time-2:{}", DateTime.now().toMsStr());

        // 查询责任信息
        QpQuoteConfig qpQuoteConfigParams = new QpQuoteConfig().setQuoteId(param.getQuoteId());
        List<QpQuoteConfig> qpQuoteConfig = quoteConfigDataMapper.entity(QpQuoteConfig.class).select(qpQuoteConfigParams, true);
        Map<Long, QpQuoteConfig> configs = qpQuoteConfig.stream().collect(Collectors.toMap(QpQuoteConfig::getId, v -> v, (v1, v2) -> v1));

        List<Long> configIds = qpQuoteConfig.stream().map(QpQuoteConfig::getId).collect(Collectors.toList());
        DataCondition<QpQuoteDuty> dutyCondition = new DataCondition<>();
        dutyCondition.in("quote_config_id", configIds);
        List<QpQuoteDuty> qpQuoteDuties = quoteDutyDataMapper.entity(QpQuoteDuty.class).select(dutyCondition, true);
        // 配置-责任s
        Map<Long, List<QpQuoteDuty>> quoteDuties = qpQuoteDuties.stream().collect(Collectors.groupingBy(QpQuoteDuty::getQuoteConfigId));

        List<String> dutyIds = qpQuoteDuties.stream().map(QpQuoteDuty::getId).map(String::valueOf).collect(Collectors.toList());
        DataCondition<QpQuoteDutySplit> dutySplitDataCondition = new DataCondition<>();
        dutySplitDataCondition.in(QpQuoteDutySplitConst.F_QDI, dutyIds);
        List<QpQuoteDutySplit> qpQuoteDutySplits = qpQuoteDutySplitDataMapper.entity(QpQuoteDutySplit.class).select(dutySplitDataCondition, true);
        // 责任-责任拓展s
        Map<Long, List<QpQuoteDutySplit>> dutyIdAnddutySplits = qpQuoteDutySplits.stream().collect(Collectors.groupingBy(QpQuoteDutySplit::getQuoteDutyId));
        // 配置-责任ids
        Map<Long, List<Long>> configIdAndDutyIds = trans(quoteDuties);
        // 循环责任将 dutyIdAnddutySplits 转换为 configIdAnddutySplits
        Map<Long, List<QpQuoteDutySplit>> configIdAndDutySplitss = new HashMap<>();
        configIdAndDutyIds.forEach((configId, dutyIdss) -> {
            List<Long> dIds = configIdAndDutyIds.get(configId);
            List<QpQuoteDutySplit> newList = Lists.newArrayList();
            dIds.forEach(dutyId -> {
                if (!CollectionUtils.isEmpty(dutyIdAnddutySplits.get(dutyId))) {
                    newList.addAll(dutyIdAnddutySplits.get(dutyId));
                }
            });
            if (!CollectionUtils.isEmpty(newList)) {
                configIdAndDutySplitss.put(configId, newList);
            }
        });
        log.debug("autoCreate.Time-3:{}", DateTime.now().toMsStr());

        AutoCreatePlanExtDTO autoCreatePlanDTO = autoCreatePlanByConfigs(qpQuoteInfo, qpQuote, qpQuoteContractCustomerDto.getEnterpriseId(), configs, quoteDuties,
                configIdAndDutySplitss, qpCustomer, (Objects.isNull(param.getCompanyId()) || param.getCompanyId() <= 0L) ? COMPANY_ID : param.getCompanyId(), param);

        // 渠道重置参数
        if (Objects.equals(QuoteSourceEnum.CHANNEL.getCode(), qpQuoteInfo.getQuoteSourceFlag())) {
            setChannelData(param, autoCreatePlanDTO, dutyIdAnddutySplits);
        }
        log.debug("autoCreate.Time-4:{}", DateTime.now().toMsStr());
        log.info("[{}]方案参数生成成功", param.getQuoteId());
        return autoCreatePlanDTO;
    }

    /**
     * 渠道半品参数特殊处理
     */
    private void setChannelData(CreatePlanParam param, AutoCreatePlanExtDTO autoCreatePlanDTO, Map<Long, List<QpQuoteDutySplit>> dutyIdAnddutySplits) {
        QuoteRelevantDto quoteRelevantOne = quoteCurrencyUtilService.getQuoteRelevantOne(null, param.getQuoteId(), null);
        // 方案处理
        setChannelPlanData(param, autoCreatePlanDTO, quoteRelevantOne);
        // 配置处理
        setChannelPlanConfigData(param, autoCreatePlanDTO, quoteRelevantOne, dutyIdAnddutySplits);
        // 风控处理
        setChannelPlanConfigRulesData(param, autoCreatePlanDTO);
    }

    /**
     * 渠道标品重置风控
     *
     * @param param             1
     * @param autoCreatePlanDTO 创建方案
     */
    private void setChannelPlanConfigRulesData(CreatePlanParam param, AutoCreatePlanExtDTO autoCreatePlanDTO) {
        AutoCreatePlanVO autoCreatePlanParams = autoCreatePlanDTO.getAutoCreatePlanParams();
        InsuranceRulesRequestVO insuranceRulesInfo = autoCreatePlanParams.getInsuranceRulesInfo();
        // 获取核心配置的风控
        CoreChannelDto coreChannelData = param.getCoreChannelDto();
        InsuranceRulesRequestVO insuranceRulesRequestVO = ObjectUtil.covertObject(coreChannelData.getPlanConfigRules(), InsuranceRulesRequestVO.class);
        insuranceRulesRequestVO.setRelativeStartTimeString(insuranceRulesInfo.getRelativeStartTimeString());
        insuranceRulesRequestVO.setRelativeEndTimeString(insuranceRulesInfo.getRelativeEndTimeString());
       /* insuranceRulesRequestVO.setNotBirthCardAge(insuranceRulesInfo.getNotBirthCardAge());
        insuranceRulesRequestVO.setChildInsuredMinAge(insuranceRulesInfo.getChildInsuredMinAge());
        insuranceRulesRequestVO.setChildInsuredMaxAge(insuranceRulesInfo.getChildInsuredMaxAge());
        insuranceRulesRequestVO.setSpouseAgeDiff(insuranceRulesInfo.getSpouseAgeDiff());
        insuranceRulesRequestVO.setParentInsuredMinAge(insuranceRulesInfo.getParentInsuredMinAge());
        insuranceRulesRequestVO.setParentInsuredMaxAge(insuranceRulesInfo.getParentInsuredMaxAge());
        insuranceRulesRequestVO.setSkipOnlineInsurance(insuranceRulesInfo.getSkipOnlineInsurance());*/
        insuranceRulesRequestVO.setMaxPersonNumber(coreChannelData.getPlanConfigRules().getMaxPerson());
        autoCreatePlanParams.setInsuranceRulesInfo(insuranceRulesRequestVO);
    }

    /**
     * 方案处理
     *
     * @param param             传入参数
     * @param autoCreatePlanDTO 创建方案参数
     */
    private void setChannelPlanData(CreatePlanParam param, AutoCreatePlanExtDTO autoCreatePlanDTO, QuoteRelevantDto quoteRelevantOne) {
        AutoCreatePlanVO autoCreatePlanParams = autoCreatePlanDTO.getAutoCreatePlanParams();
        if (Objects.isNull(autoCreatePlanParams)) {
            return;
        }
        InsuranceInfoRequestVO basicInsuranceInfo = autoCreatePlanParams.getBasicInsuranceInfo();
        if (Objects.isNull(basicInsuranceInfo)) {
            return;
        }
        // 获取合同企业
        Long quoteContractCustomerId = param.getQuoteContractCustomerId();
        QpQuoteContractCustomerDto dto = quoteContractCustomerService.queryById(quoteContractCustomerId);
        GroupDetailDTO group = quoteCurrencyUtilService.getGroupById(dto.getEnterpriseId());

        // 核心系统中企业名称（方案名称中的企业名称加渠道）+标品名称（报价后台配置的标品名称）+年份（申请投保成功时所在的年）
        ZonedDateTime startTime = quoteRelevantOne.getQuote().getStartTime();
        basicInsuranceInfo.setPlanName(group.getRealname() + quoteRelevantOne.getQuoteInfo().getName() + startTime.getYear());
        if (Objects.equals(defaultPlanInfo.getType(), 1)) {
            basicInsuranceInfo.setPlanName(group.getRealname() + quoteRelevantOne.getQuoteInfo().getName() + startTime.getYear() + "-" + RandomUtil.randomString(4));
        }
        // 渠道类型
        basicInsuranceInfo.setChannelType("1");
        basicInsuranceInfo.setChannelId(String.valueOf(dto.getEnterpriseId()));


        // 根据职业类别值获取职业类别编码
        String occupationStr = (StringUtils.isBlank(quoteRelevantOne.getQuoteConfig().getOccupation().toString()) || "0".equals(quoteRelevantOne.getQuoteConfig().getOccupation().toString())) ? "3" : quoteRelevantOne.getQuoteConfig().getOccupation().toString();
        String occupation = DefVendorOccEnum.getVendorOcc(param.getCompanyId(), occupationStr);
        basicInsuranceInfo.setOccupationCode(occupation);
        // 业务性质 1渠道 ，2直销，3 询报价
        basicInsuranceInfo.setNature("1");

        // 非机构化特约
        basicInsuranceInfo.setConstract(StringUtils.isBlank(param.getCoreChannelDto().getPlanConfigStandard().getContractDesc()) ? " " : param.getCoreChannelDto().getPlanConfigStandard().getContractDesc());

        Long tenantChannelProductId = quoteRelevantOne.getQuoteInfo().getTenantChannelProductId();
        String tenantName = tenantChannelProductService.getTenantName(tenantChannelProductId);
        basicInsuranceInfo.setNatureChannelName(tenantName);

    }

    /**
     * 配置处理
     *
     * @param param             传入参数
     * @param autoCreatePlanDTO 创建方案参数
     * @param quoteRelevantOne  方案参数
     */
    private void setChannelPlanConfigData(CreatePlanParam param, AutoCreatePlanExtDTO autoCreatePlanDTO, QuoteRelevantDto quoteRelevantOne, Map<Long, List<QpQuoteDutySplit>> dutyIdAnddutySplits) {
        log.info("ChannelPlanConfigData-开始");
        // 获取渠道配置模板的配置信息
        PlanConfigDTO planConfig = param.getCoreChannelDto().getPlanConfig();
        PlanConfigBindDTO planConfigBindDTO = ObjectUtil.covertObject(planConfig, PlanConfigBindDTO.class);

        if (null == autoCreatePlanDTO.getAutoCreatePlanParams() || CollectionUtils.isEmpty(autoCreatePlanDTO.getAutoCreatePlanParams().getConfigBindList())) {
            log.warn("ChannelPlanConfigData-返回：{}", JacksonUtils.writeAsString(autoCreatePlanDTO));
            return;
        }

        PlanConfigBindDTO oldBinfConfig = autoCreatePlanDTO.getAutoCreatePlanParams().getConfigBindList().get(NumberUtils.INTEGER_ZERO);
        List<DutyDTO> duties = oldBinfConfig.getDuties();

        Map<Long, DutyDTO> pidMap = duties.stream().collect(Collectors.toMap(IgDuty::getPid, v -> v, (v1, v2) -> v1));


        planConfigBindDTO.setRelationId(quoteRelevantOne.getQuoteConfigId());
        planConfigBindDTO.setPlanConfigId(NumberUtils.LONG_ZERO);
        planConfigBindDTO.getDuties().forEach(v -> {
            v.setId(NumberUtils.LONG_ZERO);
            if (pidMap.containsKey(v.getPid())) {
                v.setRelationId(pidMap.get(v.getPid()).getRelationId());
                v.setIsPaymentSplit("1");
                List<IgDutySplit> dutySplits = pidMap.get(v.getPid()).getDutySplits();
                if (!CollectionUtils.isEmpty(dutySplits)) {
                    dutySplits.stream().forEach(v2 -> {
                        v2.setId(0L);
                        // v2.setDutyId(v.getRelationId());
                    });
                    v.setDutySplits(dutySplits);
                }
            }
            if (null != v.getDutySplits() && !v.getDutySplits().isEmpty()) {
                v.getDutySplits().forEach(v2 -> v2.setId(0L));
                if (dutyIdAnddutySplits.containsKey(v.getRelationId())) {
                    v.setRelationSplitList(dutyIdAnddutySplits.get(v.getRelationId()).stream().map(QpQuoteDutySplit::getId).collect(Collectors.toList()));
                }
            }
        });
        planConfigBindDTO.setJobCategory(quoteRelevantOne.getQuoteConfig().getOccupation());
        planConfigBindDTO.setId(NumberUtils.LONG_ZERO);
        planConfigBindDTO.setTemplate(String.valueOf(NumberUtils.INTEGER_ZERO));
        autoCreatePlanDTO.getAutoCreatePlanParams().setConfigBindList(Lists.newArrayList(planConfigBindDTO));
        log.info("ChannelPlanConfigData-结束");
    }

    private Map<Long, List<Long>> trans(Map<Long, List<QpQuoteDuty>> quoteDuties) {
        Map<Long, List<Long>> result = new HashMap<>();
        quoteDuties.forEach((configId, dutys) -> result.put(configId, dutys.stream().map(QpQuoteDuty::getId).collect(Collectors.toList())));
        return result;
    }

    private List<AutoCreatePlanExtDTO> requestInsuranceCreatePlans(List<AutoCreatePlanExtDTO> autoCreatePlanExtDTOs) {
        List<AutoCreatePlanVO> AutoCreatePlanVOs = autoCreatePlanExtDTOs.stream().map(AutoCreatePlanExtDTO::getAutoCreatePlanParams).collect(Collectors.toList());
        log.info("向核心推进送案参数 requestInsuranceCreatePlans-AutoCreatePlanVOs={}", JacksonUtils.writeAsString(AutoCreatePlanVOs));
        ResponseVO<List<AutoCreatePlanDTO>> autoCreatePlanDTOResponseVO = insuranceClient.batchCreatePlan(AutoCreatePlanVOs);
        if (!Objects.equals(autoCreatePlanDTOResponseVO.getCode(), 0)) {
            log.error("生成方案失败:{}", JSONUtil.toJsonStr(autoCreatePlanDTOResponseVO));
            throw new QuoteException(Integer.parseInt(BackendErrorMsgAndCode.CREATE_PLAN_ERROR.code()), autoCreatePlanDTOResponseVO.getMessage());
        }
        log.info("方案生成成功:{}", JSONUtil.toJsonStr(autoCreatePlanDTOResponseVO));

        List<AutoCreatePlanDTO> data = autoCreatePlanDTOResponseVO.getData();
        if (!CollectionUtils.isEmpty(data)) {
            Map<String, AutoCreatePlanDTO> quoteIdAndObj = data.stream().collect(Collectors.toMap(AutoCreatePlanDTO::getQuoteCustomer, v -> v, (v1, v2) -> v1));
            autoCreatePlanExtDTOs.forEach(v -> {
                if (quoteIdAndObj.containsKey(v.getAutoCreatePlanParams().getQuoteCustomer())) {
                    v.setAutoCreatePlanResult(quoteIdAndObj.get(v.getAutoCreatePlanParams().getQuoteCustomer()));
                }
            });
        }
        return autoCreatePlanExtDTOs;
    }

    /**
     * 更新报价合同数据
     *
     * @param autoCreatePlanExtDTO 返回参数
     */
    private AutoCreatePlanDTO autoCreatePostUpdate(AutoCreatePlanExtDTO autoCreatePlanExtDTO, Map<String, CreatePlanParam> createPlanParam, long time) {
        AutoCreatePlanDTO autoCreatePlanDTO = autoCreatePlanExtDTO.getAutoCreatePlanResult();
        if (autoCreatePlanDTO.getErrorCode() > 0) {
            return autoCreatePlanDTO;
        } else {

            InsuranceQuoteKey insuranceQuoteKey = new InsuranceQuoteKey(autoCreatePlanDTO.getQuoteCustomer());
            Long quoteId = insuranceQuoteKey.getQuoteId();
            Long planId = autoCreatePlanDTO.getPlanId();
            Long customerQuoteRelationId = customerQuoteRelationService.bindInsurancePlan(insuranceQuoteKey.getQuoteContractCustomerId(), quoteId, planId);
            if (!Objects.isNull(customerQuoteRelationId) && customerQuoteRelationId > 0L && createPlanParam.containsKey(autoCreatePlanDTO.getQuoteCustomer())) {
                CreatePlanParam createPlan = createPlanParam.get(autoCreatePlanDTO.getQuoteCustomer());
                customerQuoteRelationMapper.entity(QpCustomerQuoteRelation.class).updateOne(new QpCustomerQuoteRelation()
                        .setId(customerQuoteRelationId)
                        .setCompanyId(Objects.isNull(createPlan.getCompanyId()) ? 0L : createPlan.getCompanyId())
                        .setBizModeFlag(Objects.isNull(createPlan.getBizMode()) ? 0 : createPlan.getBizMode()));
            }
            log.debug("pushInsurancePlanNew.Time" + time + "-特约-s:{}", DateTime.now().toMsStr());
            ResponseVO<List<Long>> listResponseVO = specialRulesClient.syncFromQuote(quoteId, planId);
            if (!Objects.equals(listResponseVO.getCode(), 0)) {
                log.error("[{}]->[{}]特约同步至木人桩错误-{}", quoteId, planId, JSONUtil.toJsonStr(listResponseVO));
            }
            if (log.isDebugEnabled()) {
                log.debug("[{}]->[{}]同步木人桩特约结果:{}", quoteId, planId, JSONUtil.toJsonStr(listResponseVO));
            }
            log.debug("pushInsurancePlanNew.Time" + time + "-特约-e:{}", DateTime.now().toMsStr());
            return null;
        }
    }

    private Map<String, CreatePlanParam> getCreatePlanParam(List<CreatePlanParam> paramList) {
        HashMap<String, CreatePlanParam> map = new HashMap<>();
        for (CreatePlanParam v : paramList) {
            InsuranceQuoteKey insuranceQuoteKey = new InsuranceQuoteKey(v.getQuoteId(), v.getQuoteContractCustomerId());
            map.put(insuranceQuoteKey.getQuoteAndContractCustomerId(), v);
        }
        return map;
    }

    /**
     * 新编码
     *
     * @param code 1
     * @return 新编码
     */
    private String getCodePlugs(String code) {
        String[] split = code.split("-");
        if (split.length == 3) {
            String plugs = DateTime.now().toTimeStr().replaceAll(":", "");
            return split[0] + "-" + split[1] + plugs + "-" + split[2];
        }
        return code;
    }

    private AutoCreatePlanExtDTO autoCreatePlanByConfigs(QpQuoteInfo qpQuoteInfo, QpQuote qpQuote, Long groupId, Map<Long, QpQuoteConfig> configs, Map<Long, List<QpQuoteDuty>> duties, Map<Long, List<QpQuoteDutySplit>> dutySplits, QpCustomer customer, Long companyId, CreatePlanParam param) {
        log.info("默认方案配置信息为:{}", JSONUtil.toJsonStr(defaultPlanInfo));
        log.debug("autoCreatePlanByConfigs.Time-s:{}", DateTime.now().toMsStr());
        // 查询报价产品信息
        AutoCreatePlanVO autoCreatePlanVO = new AutoCreatePlanVO();

        // 设置基础信息
        // 申请id-业务
        Long applyId = defaultPlanInfo.getInsuranceApplyId();
        String planName = customer.getEnterpriseName() + "保障方案" + getCodePlugs(qpQuote.getQuoteCode());
        if (Objects.equals(defaultPlanInfo.getType(), 1)) {
            planName = planName + DateTime.now().toString(TimeZone.getDefault());
        }
        ZonedDateTime startTime = qpQuote.getStartTime();
        ZonedDateTime endTime = qpQuote.getEndTime();
        List<String> targetPerson = qpQuote.getTargetPerson();
        //【待确认】 职业编码 - id 或编码
        String occupationCode = "3";

        InsuranceInfoRequestVO basicInsuranceInfo = PlanBasicDataUtil.getDefaultInsuranceInfoRequestVO();

        if (Objects.equals(1, param.getBizMode()) || Objects.equals(2, param.getBizMode())) {
            // 北京维众
            basicInsuranceInfo.setContractType("1");
        }

        basicInsuranceInfo.setInsuranceApplyId(applyId);
        basicInsuranceInfo.setGroupId(groupId);
        basicInsuranceInfo.setCompanyId(companyId);
        basicInsuranceInfo.setPlanName(planName);
        basicInsuranceInfo.setStartTime(startTime);
        basicInsuranceInfo.setEndTime(endTime);
        basicInsuranceInfo.setTargetPerson(targetPerson);
        basicInsuranceInfo.setBizMode(String.valueOf(param.getBizMode()));
        // 如果是渠道
        if (Objects.equals(qpQuoteInfo.getQuoteSourceFlag(), QuoteSourceEnum.CHANNEL.getCode())) {
            basicInsuranceInfo.setNatureChannelName(tenantChannelProductService.getTenantName(qpQuoteInfo.getTenantChannelProductId()));
        }
        // if线下-else线上
        log.debug("autoCreatePlanByConfigs.PolicyOl InsuranceCompany-》{}，companyId-》{}", JacksonUtils.writeAsString(insuranceTypeConfig.getInsuranceCompany()), companyId);
        log.debug("autoCreatePlanByConfigs.PolicyOl !insuranceTypeConfig.getInsuranceCompany()-》{}", !insuranceTypeConfig.getInsuranceCompany().contains(companyId));
        if (!insuranceTypeConfig.getInsuranceCompany().contains(companyId)) {
            basicInsuranceInfo.setPolicyOl("0");
            basicInsuranceInfo.setPolicyModifyOl("0");
        } else {
            basicInsuranceInfo.setPolicyOl("1");
            basicInsuranceInfo.setPolicyModifyOl("1");
        }

        if (qpQuote.getQuoteType().equals(GeekPlusEnum.GEEK_PLUS.getInfoCode())) {
            basicInsuranceInfo.setPlanType("0");
        } else if (qpQuote.getQuoteType().equals(GeekPlusEnum.EMPLOYER.getInfoCode())) {
            basicInsuranceInfo.setPlanType("1");
        } else {
            basicInsuranceInfo.setPlanType("0");
        }

        // 设置风控信息
        InsuranceRulesRequestVO insuranceRulesInfo = PlanBasicDataUtil.getDefaultInsuranceRulesRequestVO();
        BigDecimal ratio = BigDecimal.ZERO;
        // 多配制
        if (configs.size() > 1) {
            insuranceRulesInfo.setMaxAge(defaultMaxAge);
            insuranceRulesInfo.setMinAge(defaultMinAge);
            insuranceRulesInfo.setAvgAge(defaultAvgAge);
            insuranceRulesInfo.setAvgAgeDefault(defaultAvgAge);
            int index = 0;
            for (QpQuoteConfig config : configs.values()) {
                if (!Objects.isNull(config)) {
                    index++;
                    ratio = ratio.add(config.getFemaleProportion());
                }
            }
            if (index > 0) {
                ratio = ratio.divide(new BigDecimal(index)).add(new BigDecimal(addPercentage)).setScale(0, BigDecimal.ROUND_HALF_DOWN);
            }
        } else {
            Object[] objects = configs.values().toArray();
            if (objects.length > 0) {
                QpQuoteConfig config = (QpQuoteConfig) objects[0];
                insuranceRulesInfo.setMaxAge(config.getMaxAge());
                insuranceRulesInfo.setMinAge(config.getMinAge());
                insuranceRulesInfo.setAvgAge(config.getAvgAge().intValue());
                insuranceRulesInfo.setAvgAgeDefault(config.getAvgAge().intValue());
                occupationCode = config.getOccupation().equals("1") ? "3" : config.getOccupation();
                ratio = config.getFemaleProportion().setScale(0, BigDecimal.ROUND_HALF_DOWN);
            }
        }
        log.debug("autoCreatePlanByConfigs.Time-2:{}", DateTime.now().toMsStr());

        // 根据职业类别值获取职业类别编码
        String occupation = DefVendorOccEnum.getVendorOcc(companyId, occupationCode);
        basicInsuranceInfo.setOccupationCode(occupation);

        if (Integer.valueOf(occupationCode) <= 3) {
            insuranceRulesInfo.setJobCategoryRule(Lists.newArrayList("1", "2", "3"));
        } else {
            ArrayList<String> jobCategoryRule = Lists.newArrayList();
            for (int i = 1; i <= Integer.valueOf(occupationCode); i++) {
                jobCategoryRule.add(String.valueOf(i));
            }
            insuranceRulesInfo.setJobCategoryRule(jobCategoryRule);
        }

        //【待确认】 最低参保人数 by Janloong_Doo 2021/8/31 6:45 下午
        insuranceRulesInfo.setMinInsureDefault(0);
        insuranceRulesInfo.setMinInsureNum(0);

        insuranceRulesInfo.setMaxRatioFemaleDefault(ratio.intValue());
        insuranceRulesInfo.setMaxRatioFemaleNum(ratio.intValue());
        // 女性占比 0关闭 1加人时判断 2减人时 3加减人都有
        insuranceRulesInfo.setAgeSwitch(Collections.singletonList("3"));
        // 参保人数范围 0关闭 1加人时判断 2减人时 3加减人都有
        insuranceRulesInfo.setInsureNumSwitch(Collections.singletonList("2"));
        // 女性占比范围 0关闭 1加人时判断 2减人 3加减人都有
        insuranceRulesInfo.setMaxSexRatioSwitch(Collections.singletonList("3"));
        // 设置医疗信息
        String yjyCode = qpQuote.getYjyCode();
        MedicalInfoVO medicalInfo = PlanBasicDataUtil.getDefaultMedicalInfoVO();
        medicalInfo.setServiceCode(yjyCode);
        log.debug("autoCreatePlanByConfigs.Time-3:{}", DateTime.now().toMsStr());

        // 设置财务信息
        String payType = qpQuote.getPayType();
        if (payType.equals("0")) {
            payType = "1";
        } else if (payType.equals("1")) {
            payType = "0";
        }
        Integer payPeriod = qpQuote.getPayPeriod();
        Integer payUnit = qpQuote.getPayUnit();
        Integer billUnit = qpQuote.getBillUnit();
        Integer billPeriod = qpQuote.getBillPeriod();
        ZonedDateTime firstPaymentTime = qpQuote.getFirstPaymentTime();
        if (Objects.equals(firstPaymentTime, 0L)) {
            firstPaymentTime = DateTimeUtil.endOfDay(DateTimeUtil.nowOfBusiness());
        }
        firstPaymentTime = DateTimeUtil.max(firstPaymentTime, startTime);
        FeeRulesInfoVO feeRulesInfo = PlanBasicDataUtil.getDefaultFeeRulesInfoVO();
        feeRulesInfo.setBillSettleId(0L);
        feeRulesInfo.setPayType(payType);
        feeRulesInfo.setPeriodType(String.valueOf(payUnit));
        feeRulesInfo.setPeriodValue(payPeriod);
        feeRulesInfo.setPrepaymentFlag("1");
        feeRulesInfo.setFirstPaymentEndDate(firstPaymentTime);

        ReqBillSettleDto reqBillSettleDto = new ReqBillSettleDto();

        ZonedDateTime endTimeCache = DateTimeUtil.max(startTime, DateTimeUtil.startOfDay(DateTimeUtil.nowOfBusiness()));
        ZonedDateTime dateTime = DateTimeUtil.offsetMonth(endTimeCache, billPeriod);
        reqBillSettleDto.setEndTime(dateTime);
        reqBillSettleDto.setPeriodType(String.valueOf(billUnit));
        reqBillSettleDto.setPeriodValue(billPeriod);
        reqBillSettleDto.setPayBillWorkday(30);
        feeRulesInfo.setBillSettleDetail(reqBillSettleDto);

        // 查询合同的数据创建人id
        Long xsId = 0L;
        QpQuoteContractCustomer qpQuoteContractCustomer = quoteContractCustomerDataMapper.entity(QpQuoteContractCustomer.class).selectOne(param.getQuoteContractCustomerId(), true);
        if (!Objects.isNull(qpQuoteContractCustomer)) {
            Long quoteContractId = qpQuoteContractCustomer.getQuoteContractId();
            QpQuoteContract qpQuoteContract = quoteContractDataMapper.entity(QpQuoteContract.class).selectOne(quoteContractId, true);
            if (!Objects.isNull(qpQuoteContract)) {
                xsId = qpQuoteContract.getCreatedBy();
            }
        }


        InsuranceStaffRelationVO defaultInsuranceStaffRelationVO = PlanBasicDataUtil.getDefaultInsuranceStaffRelationVO(String.valueOf(qpQuoteInfo.getRenewFlag()), xsId);
        log.debug("autoCreatePlanByConfigs.Time-4:{}", DateTime.now().toMsStr());

        // 设置绑定信息
        List<PlanConfigBindDTO> configBindList = PlanBasicDataUtil.bindPolicys(qpQuote, configs, duties, dutySplits, null, customer, defaultPlanInfo.getType());
        log.debug("autoCreatePlanByConfigs.Time-4:{}", DateTime.now().toMsStr());

        autoCreatePlanVO.setBasicInsuranceInfo(basicInsuranceInfo);
        autoCreatePlanVO.setInsuranceRulesInfo(insuranceRulesInfo);
        autoCreatePlanVO.setMedicalInfo(medicalInfo);
        autoCreatePlanVO.setFeeRulesInfo(feeRulesInfo);
        autoCreatePlanVO.setConfigBindList(configBindList);
        autoCreatePlanVO.setInsuranceStaffRelationInfo(defaultInsuranceStaffRelationVO);
        CreateCondition createCondition = new CreateCondition();
        createCondition.setAutoAddBusinessCode(false);
        createCondition.setAutoDeletePlan(true);
        createCondition.setVerifyInsurance(param.getVerifyInsurance());
        createCondition.setAutoInitBindParams(param.getAutoInitBindParams());
        createCondition.setCreateFeeRules(param.getCreateFeeRules());
        autoCreatePlanVO.setCreateCondition(createCondition);

        autoCreatePlanVO.setQuoteCustomer(getQuoteCustomer(qpQuote.getId(), param.getQuoteContractCustomerId()));

        AutoCreatePlanExtDTO autoCreatePlanExtDTO = new AutoCreatePlanExtDTO();
        autoCreatePlanExtDTO.setAutoCreatePlanParams(autoCreatePlanVO);
        autoCreatePlanExtDTO.setQpQuote(qpQuote);
        log.debug("autoCreatePlanByConfigs.Time-e:{}", DateTime.now().toMsStr());

        return autoCreatePlanExtDTO;
    }

    /**
     * 删除方案
     *
     * @param planIds 方案id
     */
    @Override
    public void removePlan(List<Long> planIds) {
        log.info("removePlan planIds={}", JacksonUtils.writeAsString(planIds));
        if (!CollectionUtils.isEmpty(planIds)) {
            IdentityUtil.setRobotAuth();
            ResponseVO<Object> objectResponseVO = planClient.deletePlan(planIds);
            IdentityUtil.setDefault();
            if (!objectResponseVO.ok()) {
                log.warn("删除核心方案失败-》 planIds={},objectResponseVO={}", JacksonUtils.writeAsString(planIds), JacksonUtils.writeAsString(objectResponseVO));
            }
        }
    }

    /**
     * 查询核心企业
     *
     * @param name 企业名称
     * @return 核心
     */
    @Override
    public ResponseVO queryGroupByName(String name) {
        IdentityUtil.setRobotAuth();
        return organizationClient.extendInfoByName(Lists.newArrayList(name));
    }

    /**
     * 查询核心企业
     *
     * @param id 企业名称
     * @return 核心
     */
    @Override
    public ResponseVO queryGroupById(Long id) {
        IdentityUtil.setRobotAuth();
        return organizationClient.extendInfo(Lists.newArrayList(id));
    }

    /**
     * 获取产品数据
     *
     * @param ids 产品id
     * @return 1
     */
    @Override
    public List<IgProduct> getIgProductsByIds(List<Long> ids) {
        return productDataMapper.entity(IgProduct.class).select(Lists.newArrayList(ids), true);
    }

    @Override
    public List<AutoCreatePlanResultDto> createPlanV2(CreatePlanParamDTO param, String autoCreatePlanStrategyType) {
        List<AutoCreatePlanDto> parmas = pushInsurancePlanV2(param, autoCreatePlanStrategyType);
        List<AutoCreatePlanResultDto> result = requestInsuranceCreatePlansV2(parmas);
        long time = DateTime.now().getTime();
        log.debug("pushInsurancePlanNew.Time" + time + "-result:{}", DateTime.now().toMsStr());
        log.info("createPlan-全部报价[{}]生成方案成功", JSONUtil.toJsonStr(result));
        return result;
    }

    /**
     * 向核心系统同步方案【包含分组逻辑】
     * @param param
     * @return
     */
    @Override
    public List<AutoCreatePlanResultDto> createPlanV2New(CreatePlanParamDTO param, String autoCreatePlanStrategyType) {

        // 1、创建方案
        List<AutoCreatePlanDto> parmams = pushInsurancePlanV2(param, autoCreatePlanStrategyType);

        // 2、构建报价系统配置ID查询参数
        List<Long> quotePlanConfigIds = parmams.stream()
                .flatMap(autoCreatePlanDto -> autoCreatePlanDto.getConfigBindList().stream())
                .map(ConfigBindDto::getQuotePlanConfigId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());

        // 3、根据配置ID查询方案下的标识
        List<InsuranceEmployeeCategoryDto> insuranceEmployeeCategory =
                insuranceEmployeeCategoryService.getInsuranceEmployeeCategory(quotePlanConfigIds);

        // 4、不执行分组，向核心系统推送方案数据
        if(CollectionUtils.isEmpty(insuranceEmployeeCategory)){
            log.warn("向核心系统同步方案==根据配置ID查询方案下的标识返回结果为空，执行未分组逻辑！" +
                            "param:{}，parmams:{}，quotePlanConfigIds:{}",
                    JSONUtil.toJsonStr(param), JSONUtil.toJsonStr(parmams),JSONUtil.toJsonStr(quotePlanConfigIds));
            List<AutoCreatePlanResultDto> result = requestInsuranceCreatePlansV2(parmams);
            return result;
        }

        // 5、将查询结果按照报价配置ID分组
        Map<Long, InsuranceEmployeeCategoryDto> employeeCategoryDtoMap = insuranceEmployeeCategory.stream()
                .collect(Collectors.toMap(
                        InsuranceEmployeeCategoryDto::getQuoteConfigId,
                        dto -> dto,
                        (existing, replacement) -> existing // 处理键冲突，保留第一个值
                ));

        // 6、重新分组后的请求参数
        List<AutoCreatePlanDto> reGroupParams = Lists.newArrayList();
        List<ConfigBindDto> allConfigBindList = Lists.newArrayList();

        // 7、将查询参数parmams按照核心系统方案模型中【员工职位级别】字段分组
        doGroup(parmams, allConfigBindList, employeeCategoryDtoMap, reGroupParams);

        // 8、向核心系统推送方案数据
        List<AutoCreatePlanResultDto> result = requestInsuranceCreatePlansV2(reGroupParams);
        long time = DateTime.now().getTime();
        log.debug("pushInsurancePlanNew.Time" + time + "-result:{}", DateTime.now().toMsStr());
        log.info("createPlan-全部报价[{}]生成方案成功", JSONUtil.toJsonStr(result));
        return result;
    }

    /**
     * 将查询参数parmams按照核心系统方案模型中【员工职位级别】字段分组
     * @param parmams
     * @param allConfigBindList
     * @param employeeCategoryDtoMap
     * @param reGroupParams
     */
    private static void doGroup(List<AutoCreatePlanDto> parmams, List<ConfigBindDto> allConfigBindList,
                                Map<Long, InsuranceEmployeeCategoryDto> employeeCategoryDtoMap, List<AutoCreatePlanDto> reGroupParams) {
        parmams.forEach(autoCreatePlanDto -> allConfigBindList.addAll(autoCreatePlanDto.getConfigBindList()));

        Map<String,Long> categoryIdMap = new HashMap<>();
        Map<String, List<ConfigBindDto>> configBindMap = allConfigBindList.stream()
                .filter(configBindDto -> configBindDto.getQuotePlanConfigId() != null)
                .collect(Collectors.groupingBy(configBindDto -> {
                    InsuranceEmployeeCategoryDto insuranceEmployeeCategoryDto = employeeCategoryDtoMap.get(configBindDto.getQuotePlanConfigId());
                    if(!categoryIdMap.containsKey(insuranceEmployeeCategoryDto.getInsuranceEmployeeCategory())) {
                        categoryIdMap.put(insuranceEmployeeCategoryDto.getInsuranceEmployeeCategory(),insuranceEmployeeCategoryDto.getQuotationId());
                    }
                    return insuranceEmployeeCategoryDto != null ? insuranceEmployeeCategoryDto.getInsuranceEmployeeCategory() : null;
                }));

        configBindMap.forEach((key, value) -> {
            if(StringUtils.isNotBlank(key)){
                String employeeCategory = key;
                Long quotePlanId = categoryIdMap.get(key);
                Optional<AutoCreatePlanDto> quoteOptional = parmams.stream().filter(item -> item.getQuotePlanId().equals(quotePlanId)).findFirst();
                if(quoteOptional.isPresent()) {
                    AutoCreatePlanDto filterAutoCreatePlanDto = quoteOptional.get();
                    filterAutoCreatePlanDto.setConfigBindList(value);
                    BasicInsuranceInfoDto basicInsuranceInfo = filterAutoCreatePlanDto.getBasicInsuranceInfo();
                    basicInsuranceInfo.setEmployeeCategory(employeeCategory);
                    filterAutoCreatePlanDto.setBasicInsuranceInfo(basicInsuranceInfo);
                    reGroupParams.add(filterAutoCreatePlanDto);
                }
            }
        });
    }


    /**
     * 创建方案
     *
     * @param createPlanParamDTO 入参
     */
    private List<AutoCreatePlanDto> pushInsurancePlanV2(CreatePlanParamDTO createPlanParamDTO, String autoCreatePlanStrategyType) {
        log.debug("核心推送自动生成方案入参:{}", JSONUtil.toJsonStr(createPlanParamDTO));
        Long quoteInfoId = createPlanParamDTO.getQuoteInfoId();
        if (quoteInfoId == null) {
            return new ArrayList<>();
        }

        // 查询报价info信息
        QpQuotationInfo quotationInfo = quotationInfoDataMapper.entity(QpQuotationInfo.class).selectOne(quoteInfoId, true);

        DataCondition<QpQuotation> quoteDataCondition = new DataCondition<>();
        quoteDataCondition.eq("quotation_info_id", quoteInfoId);
        List<QpQuotation> quoteList = quotationDataMapper.entity(QpQuotation.class).select(quoteDataCondition, true);
        long time = DateTime.now().getTime();
        // 构建核心需要参数
        ExecutorCompletionService<AutoCreatePlanDto> service = new ExecutorCompletionService<>(threadPoolTaskExecutor);
        IdentityDto userInfo = IdentityUtil.getUserInfo();
        log.debug("当前用户信息:{}", JSONUtil.toJsonStr(userInfo));
        log.debug("pushInsurancePlanNew.Time" + time + "-s:{}", DateTime.now().toMsStr());
        AtomicLong planNameAtomicLong = new AtomicLong();
        planNameAtomicLong.addAndGet(Long.parseLong(createPlanParamDTO.getPlanName()));
        boolean isGeekPlus = quoteList.stream().anyMatch(x -> x.getClientType() != null && x.getClientType().equals(ClientType.RECOMMEND.getValue()));
        /**
         * 1.“传统”和意外报价可以一键生成方案，多方案，一个方案一个配置（含内部版首期、外部版首期、内部版续期）
         *
         * 一个责任组就生成一个方案，多少个责任组就生成多少个方案
         *
         * 2.“极客+”报价可以一键生成方案，单方案，一个方案多个配置（含内部版首期、内部版续期）
         *
         * 只生成一个方案，一个责任组就生成一个配置
         *
         * 3.“雇主”报价可以一键生成方案，多方案，一个方案一个配置（含内部版首期、外部版首期、内部版续期）
         *
         * 一个责任组就生成一个方案，多少个责任组就生成多少个方案
         */
        List<AutoCreatePlanDto> parmas = new ArrayList<>();
        // TODO 方案名称处理
        if (isGeekPlus) {
            parmas.add(autoCreateV3(createPlanParamDTO, quoteList, planNameAtomicLong, autoCreatePlanStrategyType));
            // 处理预算档位
            customGeekPlusBenefit(parmas);
        } else {
            List<QpQuotationConfig> quotationConfigs = quotationConfigDataMapper.entity(QpQuotationConfig.class).select(new DataCondition<QpQuotationConfig>().in("quotation_id", quoteList.stream().map(x -> x.getId()).collect(Collectors.toList())));
            Map<Long, QpQuotation> IdToQuotation = quoteList.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));
            quotationConfigs.forEach(quoteConfig -> service.submit(() -> {
                IdentityUtil.setAuth(userInfo);
                return autoCreateV4(createPlanParamDTO, IdToQuotation.get(quoteConfig.getQuotationId()), quoteConfig, planNameAtomicLong, autoCreatePlanStrategyType);
            }));
            for (int i = 0; i < quotationConfigs.size(); i++) {
                log.debug("pushInsurancePlanNew.Time" + time + " s-i:{}  {}", i, DateTime.now().toMsStr());
                try {
                    Future<AutoCreatePlanDto> take = service.take();
                    AutoCreatePlanDto autoCreatePlanDto = take.get();
                    parmas.add(autoCreatePlanDto);
                } catch (ExecutionException e) {
                    log.error("报价方案生成方案前置参数异常-{}", e.getCause());
                    CommonDataUtil.businessException(QuoteErrorCode.CREATE_PLAN_PARAMS_ERROR);
                } catch (Exception e) {
                    log.error("报价方案生成方案前置参数异常-{}", e);
                    CommonDataUtil.businessException(QuoteErrorCode.CREATE_PLAN_PARAMS_ERROR);
                }
                log.debug("pushInsurancePlanNew.Time" + time + " e-i:{}  {}", i, DateTime.now().toMsStr());

            }
        }

        /**
         * 处理追溯期
         *
         * 1.查询追溯期特约
         * 2.修改追溯期参数
         */
        List<Long> quotationIds = quoteList.stream().map(QpQuotation::getId).collect(Collectors.toList());
        Integer maxRetrospectDaysSpecial = specialService.getMaxRetrospectDaysSpecial(SpecialSceneEnum.QUOTATION, ListParamDTO.TypeEnum.PLAN.getValue(), quotationIds);
        for (AutoCreatePlanDto parma : parmas) {
            // 重置追溯期
            parma.getBasicInsuranceInfo().setRetrospectDays(maxRetrospectDaysSpecial);
            // 设置自动投保参数
            parma.setIsAutoInsured(createPlanParamDTO.getIsAutoInsured());
            parma.setCreatedType(createPlanParamDTO.getCreatedType());
        }
        //  处理追溯期 cWe1G0Jg9Nd
        List<SpecialInstanceDetailDTO> specialInstanceDetailDTOList = specialService.getSpecialByApiKey(SpecialSceneEnum.QUOTATION, ListParamDTO.TypeEnum.PLAN.getValue(), quotationIds, Arrays.asList("cWe1G0Jg9Nd"));
        Integer days1_1 = specialInstanceDetailDTOList.stream().flatMap(category -> category.getSpecialLayout().getItemList()
                                                                                       .stream())
                                                 .filter(item -> "days1".equals(item.getItemKey()) && null != item.getItemValue())
                                                 .map(v -> Integer.parseInt(v.getItemValue().toString()))
                                                 .max(Integer::compareTo).orElse(0);
        Integer quoteType = quotationInfo.getQuoteType();
        if (quoteType.equals(QuoteTypeEnum.EMPLOYER.getCode())){
            log.warn("quoteInfoId:{}  核保追诉期天数 days1-1:{}",createPlanParamDTO.getQuoteInfoId(),days1_1);
            parmas.forEach(parma -> {
                parma.getBasicInsuranceInfo().setProsecutionPeriodDays(days1_1);
                if (parma.getBasicInsuranceInfo().getRetrospectDays()==0){
                    parma.getBasicInsuranceInfo().setRetrospectDays(days1_1);
                }else {
                    if (days1_1 > parma.getBasicInsuranceInfo().getRetrospectDays()) {
                        parma.getBasicInsuranceInfo().setRetrospectDays(days1_1);
                    }
                }
            });
        }

        if (log.isDebugEnabled()) {
            log.debug("报价[{}]生成参数成功;详情:{}", JSONUtil.toJsonStr(createPlanParamDTO), JSONUtil.toJsonStr(parmas));
        }
        log.debug("pushInsurancePlanNew.Time" + time + "-e:{}", DateTime.now().toMsStr());
        // 方案名称重复处理
        List<IgPlan> repeatNamePlanList = new ArrayList<>();
        if (isGeekPlus
                || quotationInfo.getQuoteType().equals(QuoteTypeEnum.TRADITION.getCode())
                || quotationInfo.getQuoteType().equals(QuoteTypeEnum.GEEK_PLUS.getCode())) {
            // 获取名称重复的方案
            List<String> configNameList = parmas.stream().map(AutoCreatePlanDto::getBasicInsuranceInfo).map(BasicInsuranceInfoDto::getPlanName).collect(Collectors.toList());
            ResponseVO<List<IgPlan>> responseVO = planClient.listPlanByName(configNameList);
            repeatNamePlanList = responseVO.getData();
        }
        for (AutoCreatePlanDto autoCreatePlanDto : parmas) {
            // 有名称重复的方案
            if (!CollectionUtils.isEmpty(repeatNamePlanList)) {
                LocalDateTime now = LocalDateTime.now();
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("MMddHHmmss");
                String currentDateTimeStr = now.format(formatter);

                // 找到对应的方案 给方案名加后缀时间戳
                repeatNamePlanList.stream()
                        .filter(plan -> plan.getPlanName().equals(autoCreatePlanDto.getBasicInsuranceInfo().getPlanName()))
                        .findFirst()
                        .ifPresent(plan -> {
                            autoCreatePlanDto.getBasicInsuranceInfo().setPlanName(autoCreatePlanDto.getBasicInsuranceInfo().getPlanName() + currentDateTimeStr);
                        });
            }
            // 设置首续期标识
            if (quotationInfo.getRenewFlag() != null) {
                autoCreatePlanDto.getBasicInsuranceInfo().setRenewFlag(String.valueOf(quotationInfo.getRenewFlag()));
            }
            // 国内标品设置为首期
            if (StringUtils.isNotEmpty(autoCreatePlanStrategyType)
                    && AtuoCreatePlanStrategyTypeEnum.STANDARD.getCode().equals(autoCreatePlanStrategyType)) {
                autoCreatePlanDto.getBasicInsuranceInfo().setRenewFlag("1");
            }
            // 结束时间设置 23：59：59
            if (autoCreatePlanDto.getBasicInsuranceInfo() != null && autoCreatePlanDto.getBasicInsuranceInfo().getEndTime() != null) {
                autoCreatePlanDto.getBasicInsuranceInfo().setEndTime(DateTimeUtil.endOfDay(autoCreatePlanDto.getBasicInsuranceInfo().getEndTime()));
            }
        }

        // 本批次依然有重复方案名的情况下的情况下再补两位随机数
        Set<String> planNameSet = new HashSet<>();
        LinkedHashSet<Integer> randomSet = new LinkedHashSet<>();
        for (AutoCreatePlanDto autoCreatePlanDto : parmas) {
            // 如果本次名称重复了那么添加2位随机数
            if (!planNameSet.add(autoCreatePlanDto.getBasicInsuranceInfo().getPlanName())) {
                // 保证生成的两位随机数不重复
                while (!randomSet.add(ThreadLocalRandom.current().nextInt(10, 100))) {
                }
                Integer lastRandom = getLastAddedElement(randomSet);
                autoCreatePlanDto.getBasicInsuranceInfo().setPlanName(autoCreatePlanDto.getBasicInsuranceInfo().getPlanName() + lastRandom);
            }
        }
        parmas.forEach(autoCreatePlanDto -> {
            autoCreatePlanDto.setSignConstractId(createPlanParamDTO.getSignConstractId());
        });
        parmas.get(0).setPlanName(createPlanParamDTO.getPlanName());

        // 方案扩扩亲属打标
        markFamilyConfig(parmas);
        return parmas;
    }

    /**
     * 方案扩扩亲属打标
     * @param parmas
     */
    private void markFamilyConfig(List<AutoCreatePlanDto> parmas) {

        for (AutoCreatePlanDto autoCreatePlanDto : parmas) {
            autoCreatePlanDto.getBasicInsuranceInfo().setIsExtendDependents("0");
            // 方案风控默认开启所有
            List<String> riskRuleTypeKeys = Arrays.stream(RiskRuleTypeEnum.values()).map(RiskRuleTypeEnum::getKey).collect(Collectors.toList());
            autoCreatePlanDto.getBasicInsuranceInfo().setRuleRange(riskRuleTypeKeys);
            for (ConfigBindDto configBindDto : autoCreatePlanDto.getConfigBindList()) {
                configBindDto.getPlanConfBaseInfo().setIsExtendDependents("0");
            }
        }

        // 1.获取所有的config
        // 查询config
        List<Long> configIds = parmas.stream().map(AutoCreatePlanDto::getConfigBindList).flatMap(Collection::stream).map(ConfigBindDto::getQuotePlanConfigId).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(configIds)) return;
        List<QpQuotationConfig> configs = quotationConfigDataMapper.entity(QpQuotationConfig.class).select(configIds, true);

        // 查询duty
        DataCondition<QpQuotationDuty> meDutydataCondition = new DataCondition<>();
        meDutydataCondition.in("quotation_config_id", configIds);
        meDutydataCondition.eq("business_type", "1322221");// 门诊分层责任（第二层）
        List<QpQuotationDuty> meFamilyDutys = quotationDutyDataMapper.entity(QpQuotationDuty.class).select(meDutydataCondition, true);
        if (CollectionUtil.isEmpty(meFamilyDutys)) return;// 无本人方案下的门诊分层责任

        // 查询特约
        List<Long> meFamilyDutyIds = meFamilyDutys.stream().map(QpQuotationDuty::getId).collect(Collectors.toList());
        // 查询本人下是否有绑定门诊分层责任扩展承保家属特约
        DataCondition<IgSpecial> meSpecialCondition = new DataCondition<>();
        meSpecialCondition.in("qp_quotation_duty_id", meFamilyDutyIds);
        meSpecialCondition.eq("business_type", "4Yk4VGBCU0x");// 门诊分层责任扩展承保家属
        List<IgSpecial> meIgSpecials = igSpecialDataMapper.entity(IgSpecial.class).select(meSpecialCondition, true);
        if (CollectionUtil.isEmpty(meIgSpecials)) return; // 无本人方案下的门诊分层责任扩展承保家属特约

        List<ConfigFamilyGroup> configFamilyGroupList = new ArrayList<>();
        for (QpQuotationConfig config : configs) {
            ConfigFamilyGroup configFamilyGroup = new ConfigFamilyGroup();
            configFamilyGroup.setConfigId(config.getId());
            // 1.标记这些config的门诊分层保额
            CurrencyAmount amount = getFcAmount(config.getId(), meFamilyDutys);
            configFamilyGroup.setAmount(amount);
            configFamilyGroup.setRelation(config.getRelation());// 人员类型

            // 方案名称
            for (AutoCreatePlanDto autoCreateDto : parmas) {
                if (autoCreateDto.getConfigBindList().stream().anyMatch(configBindDto -> configBindDto.getQuotePlanConfigId().equals(config.getId()))) {
                    configFamilyGroup.setName(autoCreateDto.getBasicInsuranceInfo().getPlanName());
                }
             }

            // 2.标记这些config有没有扩家属特约
            Boolean haveFamilySpecial = isHaveFamilySpecial(config.getId(), meFamilyDutys, meIgSpecials);
            if (amount != null && amount.getAmount() != null && haveFamilySpecial) {
                configFamilyGroupList.add(configFamilyGroup);
            }
        }

        // 3.把有扩亲属且保额一致的config的分到一组里
        Map<BigDecimal, List<Long>> configIdGroupMap = configFamilyGroupList.stream()
                .collect(Collectors.groupingBy(
                        group -> group.getAmount().getAmount(), // 按照 amount.getAmount() 分组
                        Collectors.mapping(ConfigFamilyGroup::getConfigId, Collectors.toList()) // 收集 configId 到 List
                ));


        // 4.遍历这个组出现的就打config亲属标记
        for (AutoCreatePlanDto autoCreatePlanDto : parmas) {
            for (ConfigBindDto configBindDto : autoCreatePlanDto.getConfigBindList()) {
                Long quotePlanConfigId = configBindDto.getQuotePlanConfigId();
                // 判断configIdGroupMap的values里有quotePlanConfigId
                if (configIdGroupMap.values().stream().anyMatch(list -> list.contains(quotePlanConfigId))) {
                    configBindDto.getPlanConfBaseInfo().setIsExtendDependents("1");
                    // 5.任意一个config被标记的时候，plan就打标记
                    autoCreatePlanDto.getBasicInsuranceInfo().setIsExtendDependents("1");
                    // 家属方案风控仅开启部分
                    // 在 configs 中找到id = quotePlanConfigId 的
                    QpQuotationConfig config = configs.stream().filter(c -> c.getId().equals(quotePlanConfigId)).findFirst().orElse(null);
                    if (config != null && !config.getRelation().equals(Relation.SELF.getValue())) {
                        List<String> familyRiskRuleTypeKeys = new ArrayList<>();
                        familyRiskRuleTypeKeys.add(RiskRuleTypeEnum.CAN_ADD.getKey());
                        familyRiskRuleTypeKeys.add(RiskRuleTypeEnum.CAN_REDUCE.getKey());
                        familyRiskRuleTypeKeys.add(RiskRuleTypeEnum.CAN_ADD_MULTI_GROUP.getKey());
                        familyRiskRuleTypeKeys.add(RiskRuleTypeEnum.HOLD_END_TIME.getKey());
                        autoCreatePlanDto.getBasicInsuranceInfo().setRuleRange(familyRiskRuleTypeKeys);
                    }

                    // 6.添加相同责任保额的其他关联configId
                    if (config != null && config.getRelation().equals(Relation.SELF.getValue())) {
                        // 找到configIdGroupMap的values中哪个List<Long> 包含quotePlanConfigId
                        Optional<Map.Entry<BigDecimal, List<Long>>> result = configIdGroupMap.entrySet()
                                .stream()
                                .filter(entry -> entry.getValue().contains(quotePlanConfigId))
                                .findFirst();
                        if (result.isPresent()) {
                            Map.Entry<BigDecimal, List<Long>> entry = result.get();
                            log.info("找到匹配的分组: 金额=" + entry.getKey() + ", ID 列表=" + entry.getValue());
                            // 在entry.getValue()中移除quotePlanConfigId
                            List<Long> extendDependentsList = new ArrayList<>();
                            extendDependentsList.addAll(entry.getValue());
                            extendDependentsList.remove(quotePlanConfigId);
                            configBindDto.getPlanConfBaseInfo().setExtendDependentsList(extendDependentsList);
                        } else {
                            log.info("未找到包含该 quotePlanConfigId 的分组");
                        }
                    }
                } else {
                    // 非扩亲属方案
                    configBindDto.getPlanConfBaseInfo().setIsExtendDependents("0");
                }
            }
        }
        // 5.发送分层责任扩亲属方案多方案匹配提醒邮件
        CompletableFuture.runAsync(() -> checkFamilyConfigStaffRepeat(configFamilyGroupList));
    }

    private void checkFamilyConfigStaffRepeat(List<ConfigFamilyGroup> configFamilyGroupList) {
        // 筛选出 configFamilyGroupList 中 relation = "1"的数据
        List<ConfigFamilyGroup> filteredList = configFamilyGroupList.stream()
                .filter(cfg -> Relation.SELF.getValue().equals(cfg.getRelation())) // 筛选条件
                .collect(Collectors.toList());
        if(CollectionUtil.isNotEmpty(filteredList)){
            Map<BigDecimal, List<String>> configIdGroupMap2 = filteredList.stream()
                    .collect(Collectors.groupingBy(
                            group -> group.getAmount().getAmount(), // 按照 amount.getAmount() 分组
                            Collectors.mapping(ConfigFamilyGroup::getName, Collectors.toList()) // 收集 configId 到 List
                    ));
            for (List<String> configNames : configIdGroupMap2.values()) {
                // size >1
                if (configNames.size() > 1) {
                    // 给文典、周洁发送提醒邮件
                    sendCheckFamilyConfigStaffRepeatMail( String.join(",", configNames));
                }
            }
        }
    }

    /**
     * 发送报价扩亲属重复绑定本人方案提醒邮件
     *
     * @param quotationInfo 邮件内容
     */
    private void sendCheckFamilyConfigStaffRepeatMail(String configNames) {

        try {

            SendMessageDto sendMessageDto = new SendMessageDto();
            SendMailDto sendMailDto = new SendMailDto();
            sendMailDto.setSubject("报价扩亲属重复绑定本人方案提醒");
            sendMailDto.setType(1);
            sendMailDto.setSmtpFlg(1);
            sendMessageDto.setType(2);
            //sendMailDto.setSecretSendReceiver(getBccAddress());
            sendMessageDto.setMailExtend(sendMailDto);
            sendMessageDto.setReceiver("<EMAIL>,<EMAIL>,<EMAIL>,<EMAIL>");
            HashMap<String, String> variables = new HashMap<>();
            variables.put("config_names", configNames);
            sendMessageDto.setVariables(variables);
            sendMessageDto.setMessageCode(206116L);
            log.debug("发送报价扩亲属重复绑定本人方案提醒邮件信息：{}", JacksonUtils.writeAsString(sendMessageDto));
            ResponseVO<Boolean> booleanResponseVO = sendMessageClient.smsSend(sendMessageDto);
            if (!Boolean.TRUE.equals(booleanResponseVO.getData())) {
                log.warn("报价扩亲属重复绑定本人方案提醒邮件提醒发送失败{}", booleanResponseVO.getMessage());
            }

        } catch (Exception e) {
            log.error("询报价-扩亲属重复绑定本人方案提醒邮件-通知邮件-发送失败：", e);
        }
    }

    @Override
    public CurrencyAmount getFcAmount(Long configId, List<QpQuotationDuty> meFamilyDutys) {
        if (CollectionUtil.isEmpty(meFamilyDutys)) {
            return null;
        }
        for (QpQuotationDuty meFamilyDuty : meFamilyDutys) {
            if (meFamilyDuty.getQuotationConfigId().equals(configId) && meFamilyDuty.getBusinessType().equals("1322221")) {
                return meFamilyDuty.getAmount();
            }
        }
        return null;
    }

    @Override
    public Boolean isHaveFamilySpecial(Long configId, List<QpQuotationDuty> meFamilyDutys, List<IgSpecial> meIgSpecials) {
        if (CollectionUtil.isEmpty(meFamilyDutys) || CollectionUtil.isEmpty(meIgSpecials)) {
            return false;
        }
        for (QpQuotationDuty meFamilyDuty : meFamilyDutys) {
            if (meFamilyDuty.getQuotationConfigId().equals(configId) && meFamilyDuty.getBusinessType().equals("1322221")) {
                for (IgSpecial meIgSpecial : meIgSpecials) {
                    if (meIgSpecial.getQpQuotationDutyId().equals(Long.valueOf(meFamilyDuty.getQpQuotationDutyId())) && meIgSpecial.getBusinessType().equals("4Yk4VGBCU0x")) {
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 实现极客+方案预算档位的定制
     * @link https://jira.insgeek.cn/browse/TOB-16006
     * @param autoCreatePlanList
     */
    public void customGeekPlusBenefit(List<AutoCreatePlanDto> autoCreatePlanList) {
        List<ConfigBindDto> configBindList = autoCreatePlanList.stream()
                .flatMap(v -> v.getConfigBindList().stream())
                .collect(Collectors.toList());

        for (ConfigBindDto configBindDto : configBindList) {
            CurrencyAmount basePrice = configBindDto.getPlanConfBaseInfo().getBasePrice();
            CurrencyAmount price = Optional.ofNullable(basePrice).orElse(CurrencyAmount.NULL);
            // 获取金额前缀
            BigDecimal prefixPrice = Optional.ofNullable(price.getAmount())
                    .orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP);
            configBindDto.getPlanConfBaseInfo().setBenefit(prefixPrice + configBindDto.getPlanConfBaseInfo().getOriginConfigName());
        }

        // 是否存在重复，如果重复，增加自增序号
        Map<String, List<ConfigBindDto>> configListGroupBy = configBindList.stream()
                .collect(Collectors.groupingBy(v -> v.getPlanConfBaseInfo().getBenefit()));

        for (Map.Entry<String, List<ConfigBindDto>> configEntry : configListGroupBy.entrySet()) {
            if (1 < configEntry.getValue().size()) {
                // 存在重复
                AtomicInteger atomicInteger = new AtomicInteger();
                for (ConfigBindDto configBindDto : configEntry.getValue()) {
                    String benefit = configBindDto.getPlanConfBaseInfo().getBenefit();
                    configBindDto.getPlanConfBaseInfo().setBenefit(benefit + atomicInteger.incrementAndGet());
                }
            }
        }
    }


    private static <T> T getLastAddedElement(LinkedHashSet<T> set) {
        // 使用迭代器从集合末尾开始遍历
        Iterator<T> iterator = set.iterator();
        T lastAdded = null;
        // 向前移动迭代器至末尾
        while (iterator.hasNext()) {
            lastAdded = iterator.next();
        }
        return lastAdded;
    }

    // 一堆quote生成一个核心方案
    private AutoCreatePlanDto autoCreateV3(CreatePlanParamDTO createPlanParamDTO, List<QpQuotation> quotes, AtomicLong planNameAtomicLong, String autoCreatePlanStrategyType) {
        log.debug("autoCreate.Time-1:{}", DateTime.now().toMsStr());
        // 报价信息
        QpQuotationInfo qpQuoteInfo = quotationInfoDataMapper.entity(QpQuotationInfo.class).selectOne(quotes.get(0).getQuotationInfoId(), true);
        if (Objects.isNull(qpQuoteInfo)) {
            throw new InsgeekException(QuoteErrorCode.DATA_IS_EMPTY.getMessage());
        }

        // 查询责任信息
        List<QpQuotationConfig> qpQuoteConfig = bqlQueryFactory.from(QQpQuotationConfig.qp_quotation_config)
                .select(
                        Expressions.stringPath("qp_quotation_config.*"),
                        QQpQuotationConfigInsuRelation.qp_quotation_config_insu_relation.insurance_config_id.as("insurance_config_id")
                )
                .leftJoin(QQpQuotationConfigInsuRelation.qp_quotation_config_insu_relation)
                .on(QQpQuotationConfig.qp_quotation_config.id.eq(QQpQuotationConfigInsuRelation.qp_quotation_config_insu_relation.quotation_config_id))
                .where(QQpQuotationConfig.qp_quotation_config.quotation_id.in(quotes.stream().map(x -> x.getId()).collect(Collectors.toList())))
                .findList(true, QpQuotationConfig.class);

        Map<Long, QpQuotationConfig> configs = qpQuoteConfig.stream().collect(Collectors.toMap(QpQuotationConfig::getId, v -> v, (v1, v2) -> v1));

        List<Long> configIds = qpQuoteConfig.stream().map(QpQuotationConfig::getId).collect(Collectors.toList());
        DataCondition<QpQuotationDuty> dutyCondition = new DataCondition<>();
        dutyCondition.in("quotation_config_id", configIds);
        List<QpQuotationDuty> qpQuoteDuties = quotationDutyDataMapper.entity(QpQuotationDuty.class).select(dutyCondition, true);
        // 配置-责任s
        Map<Long, List<QpQuotationDuty>> quoteDuties = qpQuoteDuties.stream().collect(Collectors.groupingBy(QpQuotationDuty::getQuotationConfigId));

        List<String> dutyIds = qpQuoteDuties.stream().map(QpQuotationDuty::getId).map(String::valueOf).collect(Collectors.toList());
        DataCondition<QpQuotationDutySplit> dutySplitDataCondition = new DataCondition<>();
        dutySplitDataCondition.in(QpQuotationDutySplitConst.F_QDI, dutyIds);
        List<QpQuotationDutySplit> qpQuoteDutySplits = qpQuotationDutySplitDataMapper.entity(QpQuotationDutySplit.class).select(dutySplitDataCondition, true);
        // 责任-责任拓展s
        Map<Long, List<QpQuotationDutySplit>> dutyIdAnddutySplits = qpQuoteDutySplits.stream().collect(Collectors.groupingBy(QpQuotationDutySplit::getQuotationDutyId));
        log.debug("autoCreate.Time-3:{}", DateTime.now().toMsStr());

        AutoCreatePlanDto autoCreatePlanDto = autoCreatePlanByConfigsV2(qpQuoteInfo, quotes.get(0), configs, quoteDuties,
                dutyIdAnddutySplits, createPlanParamDTO, planNameAtomicLong, true, quotes, autoCreatePlanStrategyType);

        // 渠道重置参数
        log.debug("autoCreate.Time-4:{}", DateTime.now().toMsStr());
        log.info("[{}]方案参数生成成功", qpQuoteInfo.getName());
        return autoCreatePlanDto;
    }

    // 一个配置生成一个核心方案
    private AutoCreatePlanDto autoCreateV4(CreatePlanParamDTO createPlanParamDTO, QpQuotation quote, QpQuotationConfig quoteConfig, AtomicLong planNameAtomicLong, String autoCreatePlanStrategyType) {
        log.debug("autoCreate.Time-1:{}", DateTime.now().toMsStr());
        // 报价信息
        QpQuotationInfo qpQuoteInfo = quotationInfoDataMapper.entity(QpQuotationInfo.class).selectOne(quote.getQuotationInfoId(), true);
        if (Objects.isNull(qpQuoteInfo)) {
            throw new InsgeekException(QuoteErrorCode.DATA_IS_EMPTY.getMessage());
        }
        // 查询责任信息
        List<QpQuotationConfig> qpQuoteConfig = bqlQueryFactory.from(QQpQuotationConfig.qp_quotation_config)
                .select(
                        Expressions.stringPath("qp_quotation_config.*"),
                        QQpQuotationConfigInsuRelation.qp_quotation_config_insu_relation.insurance_config_id.as("insurance_config_id")
                )
                .leftJoin(QQpQuotationConfigInsuRelation.qp_quotation_config_insu_relation)
                .on(QQpQuotationConfig.qp_quotation_config.id.eq(QQpQuotationConfigInsuRelation.qp_quotation_config_insu_relation.quotation_config_id))
                .where(QQpQuotationConfig.qp_quotation_config.id.eq(quoteConfig.getId()))
                .findList(true, QpQuotationConfig.class);

        Map<Long, QpQuotationConfig> configs = qpQuoteConfig.stream().collect(Collectors.toMap(QpQuotationConfig::getId, v -> v, (v1, v2) -> v1));

        List<Long> configIds = qpQuoteConfig.stream().map(QpQuotationConfig::getId).collect(Collectors.toList());
        DataCondition<QpQuotationDuty> dutyCondition = new DataCondition<>();
        dutyCondition.in("quotation_config_id", configIds);
        List<QpQuotationDuty> qpQuoteDuties = quotationDutyDataMapper.entity(QpQuotationDuty.class).select(dutyCondition, true);
        // 配置-责任s
        Map<Long, List<QpQuotationDuty>> quoteDuties = qpQuoteDuties.stream().collect(Collectors.groupingBy(QpQuotationDuty::getQuotationConfigId));

        List<String> dutyIds = qpQuoteDuties.stream().map(QpQuotationDuty::getId).map(String::valueOf).collect(Collectors.toList());
        DataCondition<QpQuotationDutySplit> dutySplitDataCondition = new DataCondition<>();
        dutySplitDataCondition.in(QpQuotationDutySplitConst.F_QDI, dutyIds);
        List<QpQuotationDutySplit> qpQuoteDutySplits = qpQuotationDutySplitDataMapper.entity(QpQuotationDutySplit.class).select(dutySplitDataCondition, true);
        // 责任-责任拓展
        Map<Long, List<QpQuotationDutySplit>> dutyIdAnddutySplits = qpQuoteDutySplits.stream().collect(Collectors.groupingBy(QpQuotationDutySplit::getQuotationDutyId));
        log.debug("autoCreate.Time-3:{}", DateTime.now().toMsStr());

        AutoCreatePlanDto autoCreatePlanDto = autoCreatePlanByConfigsV2(qpQuoteInfo, quote, configs, quoteDuties,
                dutyIdAnddutySplits, createPlanParamDTO, planNameAtomicLong, false, Lists.newArrayList(quote), autoCreatePlanStrategyType);

        // 渠道重置参数
        log.debug("autoCreate.Time-4:{}", DateTime.now().toMsStr());
        log.info("[{}]方案参数生成成功", quote.getId());
        return autoCreatePlanDto;
    }

    private List<AutoCreatePlanResultDto> requestInsuranceCreatePlansV2(List<AutoCreatePlanDto> autoCreatePlanDtos) {
        log.info("向核心推进送案参数 requestInsuranceCreatePlans-AutoCreatePlanVOs={}", JacksonUtils.writeAsString(autoCreatePlanDtos));
        ResponseVO<List<AutoCreatePlanResultDto>> autoCreatePlanResultDtoListResp = autoPlanClient.newBatchCreatePlan(autoCreatePlanDtos);
        if (!Objects.equals(autoCreatePlanResultDtoListResp.getCode(), 0)) {
            log.error("生成方案失败:{}", JSONUtil.toJsonStr(autoCreatePlanResultDtoListResp));
            throw new QuoteException(Integer.parseInt(BackendErrorMsgAndCode.CREATE_PLAN_ERROR.code()), autoCreatePlanResultDtoListResp.getMessage());
        }
        log.info("方案生成成功:{}", JSONUtil.toJsonStr(autoCreatePlanResultDtoListResp));

        List<AutoCreatePlanResultDto> data = autoCreatePlanResultDtoListResp.getData();
        return data;
    }

    @SuppressWarnings("AliControlFlowStatementWithoutBraces")
    private AutoCreatePlanDto autoCreatePlanByConfigsV2(QpQuotationInfo qpQuoteInfo, QpQuotation qpQuote, Map<Long, QpQuotationConfig> configs, Map<Long, List<QpQuotationDuty>> duties, Map<Long, List<QpQuotationDutySplit>> dutyIdAnddutySplits, CreatePlanParamDTO createPlanParamDTO, AtomicLong planNameAtomicLong, boolean isGeekPlus, List<QpQuotation> quotes, String autoCreatePlanStrategyType) {
        log.debug("autoCreatePlanByConfigs.Time-s:{}", DateTime.now().toMsStr());
        //--设置基础信息
        String occupation = qpQuote.getOccupation();
        log.info("该方案的职业类别:{}", occupation);
        String occupationCode = "";
        switch (occupation) {
            case "1":
                occupationCode = "三类";
                break;
            case "4":
                occupationCode = "四类";
                break;
            case "5":
                occupationCode = "五类";
                break;
            case "6":
                occupationCode = "六类";
                break;
            case "21":
                occupationCode = "三+类";
                break;
            case "24":
                occupationCode = "四+类";
                break;
            case "25":
                occupationCode = "五+类";
                break;
            case "26":
                occupationCode = "六+类";
                break;
            default:
                break;
        }
        // 自增序列号
        // String planNameAtomicLongValue = StrUtil.fillBefore(String.valueOf(planNameAtomicLong.incrementAndGet()), '0', 3);
        String planName = "";
        if (isGeekPlus) {
            // TOB-13715 极客+规则 “【极客+】”“客户名称”“保障期起始日期年份”"方案"，举例“【极客】北京维众人力资源有限公司2024方案”
            planName = "【极客+】" + createPlanParamDTO.getGroupName() + getLocalDate(createPlanParamDTO).getYear() + "方案";
        } else if (qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.TRADITION.getCode())
                || qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.GEEK_PLUS.getCode())) {
            // TOB-13715 传统意外规则 “客户名称”“保障期起始日期年份”“人员类型”"方案"“-”+“报价中的配置名称”，举例“北京维众人力资源有限公司2024本人方案-高管方案（限5人）”
            String configName = "";
            Optional<QpQuotationConfig> configNameOpt = configs.values().stream().findFirst();
            if (configNameOpt.isPresent()) {
                configName = configNameOpt.get().getName();
            } else {
                log.error("生成方案失败,配置列表为空:{}", JSONUtil.toJsonStr(configs));
                throw new InsgeekException(QuoteErrorCode.DATA_IS_EMPTY.getMessage());
            }
            planName = createPlanParamDTO.getGroupName() + getLocalDate(createPlanParamDTO).getYear() + Relation.getDescByCode(qpQuote.getRelation()) + "方案-" + configName;
        } else if (qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.EMPLOYER.getCode())) {
            List<QpQuotationDuty> qpQuoteDuties = duties.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
            List<String> businessTypes = qpQuoteDuties.stream().map(QpQuotationDuty::getBusinessType).collect(Collectors.toList());
            String sgcj = "";
            if (businessTypes.contains("2811000")) {
                Optional<QpQuotationDuty> sgcjDuty = qpQuoteDuties.stream().filter(item -> item.getBusinessType().equals("2811000")).findFirst();
                if (sgcjDuty.isPresent()) {
                    Integer amount = sgcjDuty.get().getAmount().getAmount().intValue();
                    if (Optional.ofNullable(amount).orElse(0) != 0 && amount.compareTo(10000) > 0) {
                        String amountStr = amount.toString();
                        sgcj = amountStr.substring(0, amountStr.length() - 4);
                    }
                }
            }
            String gzyl = "";
            if (businessTypes.contains("2812000")) {
                Optional<QpQuotationDuty> gzylDuty = qpQuoteDuties.stream().filter(item -> item.getBusinessType().equals("2812000")).findFirst();
                if (gzylDuty.isPresent()) {
                    Integer amount = gzylDuty.get().getAmount().getAmount().intValue();
                    if (Optional.ofNullable(amount).orElse(0) != 0 && amount.compareTo(10000) >= 0) {
                        String amountStr = amount.toString();
                        gzyl = amountStr.substring(0, amountStr.length() - 4);
                    }
                }
            }
            String gzscTenPercent = "";
            if (businessTypes.contains("2817000")) {
                gzscTenPercent = "10%";
            }
            String gzscFivePercent = "";
            if (businessTypes.contains("2818000")) {
                gzscFivePercent = "5%";
            }
            String gzscOnePercent = "";
            if (businessTypes.contains("2819000")) {
                gzscOnePercent = "1%";
            }
            String gzkz = "";
            if (businessTypes.contains("2813000")) {
                gzkz = "24h";
            }
            String preStr = "";
            if (StringUtils.isNotBlank(sgcj)) {
                preStr = sgcj;
            }
            if (StringUtils.isNotBlank(gzyl)) {
                if (StringUtils.isNotBlank(preStr)) {
                    preStr = preStr + "+" + gzyl;
                } else {
                    preStr = gzyl;
                }
            }
            String suffixStr = "";
            if (StringUtils.isNotBlank(gzscTenPercent)) {
                suffixStr = gzscTenPercent;
            }
            if (StringUtils.isNotBlank(gzscFivePercent)) {
                if (StringUtils.isNotBlank(suffixStr)) {
                    suffixStr = suffixStr + gzscFivePercent;
                } else {
                    suffixStr = gzscFivePercent;
                }
            }
            if (StringUtils.isNotBlank(gzscOnePercent)) {
                if (StringUtils.isNotBlank(suffixStr)) {
                    suffixStr = suffixStr + gzscOnePercent;
                } else {
                    suffixStr = gzscOnePercent;
                }
            }
            if (StringUtils.isNotBlank(gzkz)) {
                if (StringUtils.isNotBlank(suffixStr)) {
                    suffixStr = suffixStr + "/" + gzkz;
                } else {
                    suffixStr = gzkz;
                }
            }
            String resultStr = "";
            if (StringUtils.isNotBlank(preStr) && StringUtils.isNotBlank(suffixStr)) {
                resultStr = preStr + "/" + suffixStr;
            } else {
                resultStr = preStr + suffixStr;
            }
            // TOB-18964 是否含一次性就业津贴责任 2811002
            if (businessTypes.contains("2811002")) {
                resultStr = resultStr + "含就业津贴";
            }
            planName = getLocalDate(createPlanParamDTO).getMonthValue() + "-" + createPlanParamDTO.getGroupName() + getLocalDate(createPlanParamDTO).getYear() + "雇主" + "（" + resultStr + "）" + occupationCode;
        }
        BasicInsuranceInfoDto basicInsuranceInfoDto = PlanBasicDataV2Util.getDefaultBasicInsuranceInfoDto();
        basicInsuranceInfoDto.setInsuranceApplyId(createPlanParamDTO.getInsuranceApplyId());
        basicInsuranceInfoDto.setGroupId(createPlanParamDTO.getGroupId());
        basicInsuranceInfoDto.setPlanName(planName);
        if (qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.EMPLOYER.getCode())) {
            basicInsuranceInfoDto.setInsured(qpQuoteInfo.getInsured());
        }
        // 雇主超赔后，按照方案组层进行获取保司
        basicInsuranceInfoDto.setCompanyId(qpQuote.getCompanyId());
        basicInsuranceInfoDto.setCompanyCooperationInfoId(qpQuote.getCooperationInfoId());
        // todo qpQuoteApprove.getBusinessModel
        basicInsuranceInfoDto.setBizMode(BusinessModelEnum.BROKER.getValue().equals(qpQuoteInfo.getBusinessModel()) ? BasicInsuranceInfoDto.BIZ_MODE_4 : BasicInsuranceInfoDto.BIZ_MODE_2);

        // TOB-15506 【线上签约】自动生成方案，自动生成方案时，签约公司根据签约信息中的签约公司取值
        if (createPlanParamDTO.getSignConstractId() != null && createPlanParamDTO.getSignConstractId() != 0) {

            // 查询签约里的contract_geek_group_name
            QpContract qpContract = qpContractDataMapper.entity(QpContract.class).selectOne(createPlanParamDTO.getSignConstractId(), true);
            String contractGeekGroupName = qpContract.getContractGeekGroupName();

            if (StringUtils.isEmpty(contractGeekGroupName)) {
                basicInsuranceInfoDto.setContractType(basicInsuranceInfoDto.getBizMode().equals(BasicInsuranceInfoDto.BIZ_MODE_4) ? BasicInsuranceInfoDto.CONTRACT_TYPE_5 : BasicInsuranceInfoDto.CONTRACT_TYPE_1);
            } else if (contractGeekGroupName.equals("北京维众人力资源管理有限公司")) {
                basicInsuranceInfoDto.setContractType(BasicInsuranceInfoDto.CONTRACT_TYPE_1);// 北京维众人力资源管理有限公司
            } else if (contractGeekGroupName.equals("北京维众人力资源管理有限公司上海分公司")) {
                basicInsuranceInfoDto.setContractType(BasicInsuranceInfoDto.CONTRACT_TYPE_4);// 北京维众人力资源管理有限公司上海分公司
            } else if (contractGeekGroupName.equals("国联（北京）保险经纪有限公司")) {
                basicInsuranceInfoDto.setContractType(BasicInsuranceInfoDto.CONTRACT_TYPE_5);// 国联（北京）保险经纪有限公司
            }else{
                basicInsuranceInfoDto.setContractType(basicInsuranceInfoDto.getBizMode().equals(BasicInsuranceInfoDto.BIZ_MODE_4) ? BasicInsuranceInfoDto.CONTRACT_TYPE_5 : BasicInsuranceInfoDto.CONTRACT_TYPE_1);
            }
        } else {
            basicInsuranceInfoDto.setContractType(basicInsuranceInfoDto.getBizMode().equals(BasicInsuranceInfoDto.BIZ_MODE_4) ? BasicInsuranceInfoDto.CONTRACT_TYPE_5 : BasicInsuranceInfoDto.CONTRACT_TYPE_1);
        }

        if (qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.EMPLOYER.getCode())) {
            basicInsuranceInfoDto.setPlanType(BasicInsuranceInfoDto.PLAN_TYPE_1);
            // 处理雇主风控优化默认值
            String terminalEnterprise = qpQuoteInfo.getTerminalEnterprise();
            basicInsuranceInfoDto.setIncomeTaxPayer(qpQuoteInfo.getIncomeTaxPayer());
            basicInsuranceInfoDto.setTerminalEnterprise(terminalEnterprise);
            basicInsuranceInfoDto.setInsured(qpQuoteInfo.getInsured());
            basicInsuranceInfoDto.setEmploymentEnterprise(qpQuoteInfo.getEmploymentEnterprise());
            String guaranteePeriod = qpQuoteInfo.getGuaranteePeriod();
            if (StringUtils.isNotBlank(guaranteePeriod)){
                basicInsuranceInfoDto.setGuaranteePeriod(Integer.parseInt(guaranteePeriod));
            }

        } else {
            basicInsuranceInfoDto.setPlanType(BasicInsuranceInfoDto.PLAN_TYPE_0);
        }
        basicInsuranceInfoDto.setStartTime(createPlanParamDTO.getStartTime());
        basicInsuranceInfoDto.setEndTime(createPlanParamDTO.getEndTime());
        // 1-本人
        // 2-子女
        // 4-配偶
        // 8-父母
        List<String> targePerson = new ArrayList<>();
        for (QpQuotationConfig qpQuoteConfig : configs.values()) {
            String relation = "";
            // switch (qpQuoteConfig.getRelation()) {
            String configRelation = null;
            if (isGeekPlus) {
                configRelation = qpQuoteConfig.getRelation();
            } else {
                configRelation = qpQuote.getRelation();
            }
            switch (configRelation) {
                case "0":
                    relation = "1";
                    break;
                case "1":
                    relation = "2";
                    break;
                case "2":
                    relation = "4";
                    break;
                case "3":
                    relation = "8";
                    break;
                default:
                    break;
            }
            if (!targePerson.contains(relation)) {
                targePerson.add(relation);
            }
        }
        basicInsuranceInfoDto.setTargetPerson(targePerson);

        if (createPlanParamDTO.getSignConstractId() != null) {
            // 重新处理每个保司的线上线下场景
            // 是否包含特殊配置
            if (nacosQuoteCompanyDocking.getDocking().containsKey(qpQuote.getCompanyId()) && nacosQuoteCompanyDocking.getDocking().get(qpQuote.getCompanyId()).containsKey(qpQuoteInfo.getQuoteType())) {
                basicInsuranceInfoDto.setPolicyOl(String.valueOf(nacosQuoteCompanyDocking.getDocking().get(qpQuote.getCompanyId()).get(qpQuoteInfo.getQuoteType())));
                basicInsuranceInfoDto.setPolicyModifyOl(String.valueOf(nacosQuoteCompanyDocking.getDocking().get(qpQuote.getCompanyId()).get(qpQuoteInfo.getQuoteType())));
            } else {
                IgCompany igCompany = companyDataMapper.entity(IgCompany.class).selectOne(qpQuote.getCompanyId(), true);
                Integer insuredDocking = null == igCompany ? null : Integer.valueOf(igCompany.getInsuredDocking());
                basicInsuranceInfoDto.setPolicyOl(String.valueOf(insuredDocking));
                basicInsuranceInfoDto.setPolicyModifyOl(String.valueOf(insuredDocking));
            }
        } else {
            // 一件生成方案不取保司配置
            basicInsuranceInfoDto.setPolicyOl(String.valueOf(createPlanParamDTO.getPolicyOl()));
            basicInsuranceInfoDto.setPolicyModifyOl(String.valueOf(createPlanParamDTO.getPolicyModifyOl()));
        }
        // 职业类别
        switch (occupation) {
            case "1":
                if (qpQuote.getCompanyId().equals(92L)) {
                    //现代
                    basicInsuranceInfoDto.setOccupationCode("W0814001");
                } else if (qpQuote.getCompanyId().equals(101L)) {
                    //泰康
                    basicInsuranceInfoDto.setOccupationCode("00103004");
                } else if (qpQuote.getCompanyId().equals(99L)) {
                    //亚太
                    basicInsuranceInfoDto.setOccupationCode("00751010");
                } else {
                    basicInsuranceInfoDto.setOccupationCode("3");
                }
                basicInsuranceInfoDto.setOriginOccupationCode("3");
                break;
            case "21":
                if (qpQuote.getCompanyId().equals(92L)) {
                    //现代
                    basicInsuranceInfoDto.setOccupationCode("W0814001");
                } else if (qpQuote.getCompanyId().equals(101L)) {
                    //泰康
                    basicInsuranceInfoDto.setOccupationCode("00103004");
                } else if (qpQuote.getCompanyId().equals(99L)) {
                    //亚太
                    basicInsuranceInfoDto.setOccupationCode("00751010");
                } else {
                    basicInsuranceInfoDto.setOccupationCode("3");
                }
                basicInsuranceInfoDto.setOriginOccupationCode("3+");
                break;
            case "4":
                if (qpQuote.getCompanyId().equals(92L)) {
                    //现代
                    basicInsuranceInfoDto.setOccupationCode("W0802003");
                } else if (qpQuote.getCompanyId().equals(101L)) {
                    //泰康
                    basicInsuranceInfoDto.setOccupationCode("00701002");
                } else if (qpQuote.getCompanyId().equals(99L)) {
                    //亚太
                    basicInsuranceInfoDto.setOccupationCode("00701002");
                } else {
                    basicInsuranceInfoDto.setOccupationCode("4");
                }
                basicInsuranceInfoDto.setOriginOccupationCode("4");
                break;
            case "24":
                if (qpQuote.getCompanyId().equals(92L)) {
                    //现代
                    basicInsuranceInfoDto.setOccupationCode("W0802003");
                } else if (qpQuote.getCompanyId().equals(101L)) {
                    //泰康
                    basicInsuranceInfoDto.setOccupationCode("00701002");
                } else if (qpQuote.getCompanyId().equals(99L)) {
                    //亚太
                    basicInsuranceInfoDto.setOccupationCode("00701002");
                } else {
                    basicInsuranceInfoDto.setOccupationCode("4");
                }
                basicInsuranceInfoDto.setOriginOccupationCode("4+");
                break;
            case "5":
                if (qpQuote.getCompanyId().equals(92L)) {
                    //现代
                    basicInsuranceInfoDto.setOccupationCode("W0703003");
                } else if (qpQuote.getCompanyId().equals(101L)) {
                    //泰康
                    basicInsuranceInfoDto.setOccupationCode("00717003");
                } else if (qpQuote.getCompanyId().equals(99L)) {
                    //亚太
                    basicInsuranceInfoDto.setOccupationCode("00749023");
                } else {
                    basicInsuranceInfoDto.setOccupationCode("5");
                }
                basicInsuranceInfoDto.setOriginOccupationCode("5");
                break;
            case "25":
                if (qpQuote.getCompanyId().equals(92L)) {
                    //现代
                    basicInsuranceInfoDto.setOccupationCode("W0703003");
                } else if (qpQuote.getCompanyId().equals(101L)) {
                    //泰康
                    basicInsuranceInfoDto.setOccupationCode("00717003");
                } else if (qpQuote.getCompanyId().equals(99L)) {
                    //亚太
                    basicInsuranceInfoDto.setOccupationCode("00749023");
                } else {
                    basicInsuranceInfoDto.setOccupationCode("5");
                }
                basicInsuranceInfoDto.setOriginOccupationCode("5+");
                break;
            case "6":
                if (qpQuote.getCompanyId().equals(92L)) {
                    //现代
                    basicInsuranceInfoDto.setOccupationCode("W0802015");
                } else if (qpQuote.getCompanyId().equals(101L)) {
                    //泰康
                    basicInsuranceInfoDto.setOccupationCode("00302007");
                } else if (qpQuote.getCompanyId().equals(99L)) {
                    //亚太
                    basicInsuranceInfoDto.setOccupationCode("00302007");
                } else {
                    basicInsuranceInfoDto.setOccupationCode("6");
                }
                basicInsuranceInfoDto.setOriginOccupationCode("6");
                break;
            case "26":
                if (qpQuote.getCompanyId().equals(92L)) {
                    //现代
                    basicInsuranceInfoDto.setOccupationCode("W0802015");
                } else if (qpQuote.getCompanyId().equals(101L)) {
                    //泰康
                    basicInsuranceInfoDto.setOccupationCode("00302007");
                } else if (qpQuote.getCompanyId().equals(99L)) {
                    //亚太
                    basicInsuranceInfoDto.setOccupationCode("00302007");
                } else {
                    basicInsuranceInfoDto.setOccupationCode("6");
                }
                basicInsuranceInfoDto.setOriginOccupationCode("6+");
                break;
            default:
                break;
        }
        basicInsuranceInfoDto.setInsEntity(basicInsuranceInfoDto.getBizMode().equals(BasicInsuranceInfoDto.BIZ_MODE_4) ? BasicInsuranceInfoDto.INS_ENTITY_5 : BasicInsuranceInfoDto.INS_ENTITY_1);
        ResponseVO<List<IgGroupDTO>> listResponseVO = organizationClient.groupList(Collections.singletonList(createPlanParamDTO.getGroupId()));
        if (listResponseVO.ok() && !listResponseVO.getData().isEmpty()) {
            IgGroupDTO igGroupDTO = listResponseVO.getData().get(0);
            basicInsuranceInfoDto.setNature(igGroupDTO.getChannel() == 2 ? "3" : "2");
        }
        basicInsuranceInfoDto.setCustomerPayMode(String.valueOf(createPlanParamDTO.getCustomerPayMode()));
        basicInsuranceInfoDto.setInsuranceCompanyPayMode(String.valueOf(createPlanParamDTO.getInsuranceCompanyPayMode()));
        //--方案配置
        List<ConfigBindDto> configBindList = new ArrayList<>();
        Map<String, List<QpQuotationConfig>> relationConfigMap = configs.values().stream().collect(Collectors.groupingBy(QpQuotationConfig::getRelation));
        Map<Long, Integer> configIdToOrderMap = new HashMap<>();
        Boolean isGeekPlan = false;
        for (List<QpQuotationConfig> itemConfigList : relationConfigMap.values()) {
            for (int i = 0; i < itemConfigList.size(); i++) {
                configIdToOrderMap.put(itemConfigList.get(i).getId(), i + 1);
            }
        }
        List<SpecialFlagDto> specialConfigFlagList = new ArrayList<>();

        // 档位名称在同一个方案下如果重复后面加从1开始的自增序号。
        Set<String> benefitSet = new HashSet<>();
        AtomicLong configBenefitNameAtomicLong = new AtomicLong();
        for (QpQuotationConfig qpQuoteConfig : configs.values()) {
            // 判断是否有极客+
            if (Objects.equals(qpQuoteConfig.getClientType(), ClientType.RECOMMEND.getValue()) || Objects.equals(qpQuoteConfig.getClientType(), ClientType.UPGRADE.getValue())) {
                isGeekPlan = true;
            }

            ConfigBindDto configBindDto = new ConfigBindDto();
            configBindDto.setQuotePlanConfigId(qpQuoteConfig.getId());
            // 配置基本信息
            PlanConfBaseInfoDto planConfBaseInfo = new PlanConfBaseInfoDto();
            String configName = "";
            // 新加坡配置名称使用原始名称
            if (AtuoCreatePlanStrategyTypeEnum.STANDARD_SGP.getCode().equals(autoCreatePlanStrategyType)) {
                // 报价里的方案名称 “续期--QPG”
                configName = qpQuoteConfig.getName().replaceAll("续期-","");
            } else if (qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.GEEK_PLUS.getCode())) {
                configName = Relation.getDescByCode(qpQuote.getRelation()) + "方案" + configIdToOrderMap.get(qpQuoteConfig.getId());
            } else {
                configName = createPlanParamDTO.getGroupName() + getLocalDate(createPlanParamDTO).getYear() + Relation.getDescByCode(qpQuoteConfig.getRelation()) + "方案" + configIdToOrderMap.get(qpQuoteConfig.getId());
            }
            /**
             * 如果是续期从核心拉过来的配置，查询来源配置，提供给一键生成方案的参数，作为数据溯源
             * 1.通过查询qp_quotation_config_insu_relation得到
             * 2.可能存在查不到的情况，因为可能是续期拉过来之后，又进行了手动增加配置
             */
            planConfBaseInfo.setSourceId(qpQuoteConfig.getInsuranceConfigId());
            planConfBaseInfo.setOriginConfigName(qpQuoteConfig.getName());
            planConfBaseInfo.setName(configName);
            planConfBaseInfo.setClientType(qpQuoteConfig.getClientType());
            planConfBaseInfo.setJobCategory(PlanConfBaseInfoDto.JOB_CATEGORY_3);
            planConfBaseInfo.setRelation(qpQuoteConfig.getRelation());
            if (isGeekPlus) {
                LocalDate getLocalDate = getLocalDate(createPlanParamDTO);
                String medicalTypeStr = "";
                List<QpQuotationDuty> qpQuoteDutyList = duties.get(qpQuoteConfig.getId());
                List<String> businessTypeList = qpQuoteDutyList.stream().map(QpQuotationDuty::getBusinessType).collect(Collectors.toList());
                // 有社保
                List<String> ysbList = new ArrayList<>();
                ysbList.add("1321230");
                ysbList.add("1321220");
                ysbList.retainAll(businessTypeList);
                if (ysbList.size() > 0) {
                    medicalTypeStr = "有社保";
                }
                // 无社保
                if (StringUtils.isBlank(medicalTypeStr)) {
                    List<String> wsbList = new ArrayList<>();
                    wsbList.add("1321130");
                    wsbList.add("1321120");
                    wsbList.retainAll(businessTypeList);
                    if (wsbList.size() > 0) {
                        medicalTypeStr = "无社保";
                    }
                }
                // 意外
                if (StringUtils.isBlank(medicalTypeStr)) {
                    List<String> accidentList = new ArrayList<>();
                    accidentList.add("1321300");
                    accidentList.add("1322200");
                    accidentList.add("1112000");
                    accidentList.retainAll(businessTypeList);
                    if (accidentList.size() > 0) {
                        medicalTypeStr = "意外";
                    }
                }
                // 百万
                if (StringUtils.isBlank(medicalTypeStr)) {
                    List<String> millionList = new ArrayList<>();
                    millionList.add("1321930");
                    millionList.add("1321920");
                    millionList.retainAll(businessTypeList);
                    if (millionList.size() > 0) {
                        medicalTypeStr = "百万";
                    }
                }
                // 重疾
                if (StringUtils.isBlank(medicalTypeStr)) {
                    List<String> severeIllnessList = new ArrayList<>();
                    severeIllnessList.add("1311000");
                    severeIllnessList.add("1311400");
                    severeIllnessList.retainAll(businessTypeList);
                    if (severeIllnessList.size() > 0) {
                        medicalTypeStr = "重疾";
                    }
                }
                // 交通意外
                if (StringUtils.isBlank(medicalTypeStr)) {
                    List<String> trafficAccidentList = new ArrayList<>();
                    trafficAccidentList.add("1420010");
                    trafficAccidentList.add("1420020");
                    trafficAccidentList.add("1420030");
                    trafficAccidentList.add("1420040");
                    trafficAccidentList.add("1420050");
                    trafficAccidentList.retainAll(businessTypeList);
                    if (trafficAccidentList.size() > 0) {
                        medicalTypeStr = "交通意外";
                    }
                }
                // 档位名称
                planConfBaseInfo.setBenefit(qpQuoteConfig.getPrice() == null || qpQuoteConfig.getPrice().getAmount() == null ? "0" : qpQuoteConfig.getPrice().getAmount() + Relation.getDescByCode(qpQuoteConfig.getRelation()) + medicalTypeStr + getLocalDate.getYear());

                // 档位名称在同一个方案下如果重复后面加从1开始的自增序号。
                if (!benefitSet.add(planConfBaseInfo.getBenefit())) {
                    planConfBaseInfo.setBenefit(planConfBaseInfo.getBenefit() + String.valueOf(configBenefitNameAtomicLong.incrementAndGet()));
                }

            }
            Map<Long, QpQuotation> collect = quotes.stream().collect(Collectors.toMap(x -> x.getId(), x -> x));
            QpQuotation qpQuotation = collect.get(qpQuoteConfig.getQuotationId());

            if (isGeekPlus && qpQuotation.getClientType() != null && !qpQuotation.getClientType().equals(ClientType.RECOMMEND.getValue())) {
                planConfBaseInfo.setMaxPerson(qpQuotation.getMaxPerson());
            }

            planConfBaseInfo.setBasePrice(cn.hutool.core.util.ObjectUtil.isNull(qpQuoteConfig.getPrice()) ? null : qpQuoteConfig.getPrice());
            planConfBaseInfo.setContributionRate(cn.hutool.core.util.ObjectUtil.isNull(qpQuoteConfig.getValueContributionRate()) ? null : qpQuoteConfig.getValueContributionRate().doubleValue());
            planConfBaseInfo.setOutpatientContributionRate(qpQuoteConfig.getOutpatientContributionRate());
            planConfBaseInfo.setNonOutpatientContributionRate(qpQuoteConfig.getNonOutpatientContributionRate());
            planConfBaseInfo.setExcessDistributedRate(cn.hutool.core.util.ObjectUtil.isNull(qpQuoteConfig.getExcessValueAllocationRate()) ? null : qpQuoteConfig.getExcessValueAllocationRate().doubleValue());
            planConfBaseInfo.setExcessRate(cn.hutool.core.util.ObjectUtil.isNull(qpQuoteConfig.getExcessValueRatio()) ? null : qpQuoteConfig.getExcessValueRatio().doubleValue());
            if (qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.EMPLOYER.getCode())) {
                planConfBaseInfo.setPlanConfigType("1");
            } else {
                planConfBaseInfo.setPlanConfigType("0");
            }
            planConfBaseInfo.setCompanyId(qpQuote.getCompanyId());
            planConfBaseInfo.setRelatedConfigIds(qpQuoteConfig.getRelatedConfigIds());
            configBindDto.setPlanConfBaseInfo(planConfBaseInfo);
            // 责任
            List<QpQuotationDuty> qpQuoteDuties = duties.get(qpQuoteConfig.getId());
            if (CollectionUtils.isEmpty(qpQuoteDuties)) {
                qpQuoteDuties = Lists.newArrayList();
            }

            List<DutyInfoDto> dutyInfoList = qpQuoteDuties.stream().map(v -> {
                DutyInfoDto dutyDTO = new DutyInfoDto();
                dutyDTO.setProductType(v.getBusinessType());
                dutyDTO.setAmount(v.getAmount());// TODO 国际化待处理
                dutyDTO.setDeduction(v.getDeduction());
                dutyDTO.setMandayPrice(v.getPrice());// TODO 国际化待处理
                dutyDTO.setCostPrice(null);
                dutyDTO.setOnceDeduction(v.getOnceDeduction());
                dutyDTO.setOnceQuota(v.getOnceQuota());
                dutyDTO.setPayment(v.getPayment());
                dutyDTO.setStructureFlag(v.getStructureFlag());

                // 特殊情况：极客+方案仅保留报价佣金比例，且必填，若未填写，则点击生成方案时报错，报错文案为“佣金比例未填”
                if (isGeekPlus) {
                    if (createPlanParamDTO.getCommissionRatio() == null) {
                        log.error("生成方案失败:{}", "极客+模式佣金比例未填");
                        throw new QuoteException(Integer.parseInt(BackendErrorMsgAndCode.CREATE_PLAN_ERROR.code()), "极客+模式佣金比例未填");
                    }
                    // 报价层佣金比例
                    dutyDTO.setCommissionRatio(createPlanParamDTO.getCommissionRatio());
                } else {
                    if (createPlanParamDTO.getCommissionRatio() != null) {
                        // 报价层佣金比例
                        dutyDTO.setCommissionRatio(createPlanParamDTO.getCommissionRatio());
                    } else {
                        // 方案层佣金比例
                        Optional<CreatePlanParamDTO.ConfigCommissionRatio> configCommissionRatio = createPlanParamDTO.getConfigCommissionRatio().stream()
                                .filter(r -> r.getQuotationConfigId().equals(qpQuoteConfig.getId())).findFirst();
                        if (configCommissionRatio.isPresent()) {
                            dutyDTO.setCommissionRatio(configCommissionRatio.get().getCommissionRatio());
                        } else {
                            log.error("生成方案失败:{}", "方案佣金比例未填");
                            throw new QuoteException(Integer.parseInt(BackendErrorMsgAndCode.CREATE_PLAN_ERROR.code()), "方案佣金比例未填");
                        }
                    }
                }
                dutyDTO.setOtcRange(v.getOtcRange());
                dutyDTO.setMandayRate(v.getMandayRate());
                dutyDTO.setCostRate(v.getCostRate());
                dutyDTO.setMaxLimit(v.getMaxLimit());
                dutyDTO.setMinLimit(v.getMinLimit());
                dutyDTO.setTraceId(v.getTraceId());
                dutyDTO.setRelationId(v.getId());
                dutyDTO.setBusinessType(v.getBusinessType());
                dutyDTO.setPid(v.getProductId());
                dutyDTO.setPublicAmount(v.getPublicAmount());
                dutyDTO.setSplitInfoList(new ArrayList<>());
                if (dutyIdAnddutySplits.containsKey(v.getId())) {
                    List<QpQuotationDutySplit> qpQuoteDutySplits = dutyIdAnddutySplits.get(v.getId());
                    List<SplitInfoDto> splitInfoDtos = ObjectUtil.convertList(qpQuoteDutySplits, SplitInfoDto.class);
                    dutyDTO.setSplitInfoList(splitInfoDtos);
                    dutyDTO.setRelationSplitList(dutyIdAnddutySplits.get(v.getId()).stream().map(QpQuotationDutySplit::getId).collect(Collectors.toList()));
                }
                return dutyDTO;
            }).collect(Collectors.toList());
            configBindDto.setDutyInfoList(dutyInfoList);
            configBindList.add(configBindDto);
            //设置配置结构化标识
            SpecialFlagDto configSpecialFlag = new SpecialFlagDto().setTraceId(qpQuoteConfig.getId()).setFlag(String.valueOf(qpQuoteConfig.getStructureFlag()));
            specialConfigFlagList.add(configSpecialFlag);
        }

        //--风控信息
        InsuranceRulesInfoDto insuranceRulesInfoDto = PlanBasicDataV2Util.getDefaultInsuranceRulesInfoDto(isGeekPlan, qpQuote, qpQuoteInfo);
        convertInsuranceRulesInfoDto(qpQuoteInfo, qpQuote, configs, occupation, basicInsuranceInfoDto, isGeekPlan, insuranceRulesInfoDto, quotes, autoCreatePlanStrategyType);
        //--特别约定
        SpecialAgreementDto specialAgreementDto = new SpecialAgreementDto();
        // 这里需要使用本方案的quotation中companyId 对应特约
        if (!CollectionUtils.isEmpty(createPlanParamDTO.getSpecialAgreement())) {
            createPlanParamDTO.getSpecialAgreement().forEach(v -> {
                if (v.getCompanyId().equals(qpQuote.getCompanyId())) {
                    specialAgreementDto.setSpecialStr(v.getSpecialAgreementStr());
                }
            });
        }
        //specialAgreementDto.setSpecialStr(createPlanParamDTO.getSpecialStr());
        specialAgreementDto.setCreateFlag(false);
        specialAgreementDto.setFlags(specialConfigFlagList);

        //--医疗方案
        MedicalInfoDto medicalInfo = PlanBasicDataV2Util.getDefaultMedicalInfoDto();
        List<QpQuotationNonInsuranceServicesInfo> qpNonInsuranceServicesInfos = qpQuotationNonInsuranceServicesInfoService.queryByQuoteInfoId(qpQuoteInfo.getId());
        // 医加壹
        Optional<QpQuotationConfig> firstConfig = configs.values().stream().findFirst();
        if (CollUtil.isEmpty(qpNonInsuranceServicesInfos)) {
            // 其他模块

            if ((StringUtils.isBlank(qpQuoteInfo.getYjyCode())) || (qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.TRADITION.getCode()) && firstConfig.isPresent() && !firstConfig.get().getRelation().equals(Relation.SELF.getValue()))) {
                medicalInfo.setServiceCode("");
            } else {
                if (medicalConfig.getDict().stream().map(x -> x.getApiKey()).collect(Collectors.toSet()).contains(qpQuoteInfo.getYjyCode()))
                    medicalInfo.setServiceCode(qpQuoteInfo.getYjyCode());

            }
        } else {
            // 非保险模块
            Optional<QpQuotationNonInsuranceServicesInfo> onlineClinicEntity = qpNonInsuranceServicesInfos.stream().filter(item -> item.getDynamicField().equals("online_clinic")).findFirst();
            if (onlineClinicEntity.isPresent()) {
                if ((cn.hutool.core.util.ObjectUtil.isEmpty(onlineClinicEntity.get().getOnlineClinic()))
                        || (!isGeekPlus
                        && qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.TRADITION.getCode())
                        && firstConfig.isPresent() && !firstConfig.get().getRelation().equals(Relation.SELF.getValue()))
                ) {
                    medicalInfo.setServiceCode("");
                } else {
                    switch (onlineClinicEntity.get().getOnlineClinic()) {
                        case "1":
                            medicalInfo.setServiceCode(medicalConfig.getDict().get(0).getApiKey());
                            break;
                        case "2":
                            medicalInfo.setServiceCode(medicalConfig.getDict().get(1).getApiKey());
                            break;
                        case "3":
                            medicalInfo.setServiceCode(medicalConfig.getDict().get(2).getApiKey());
                            break;
                        default:
                            break;
                    }
                }
            }

            // 体检模块
            Optional<QpQuotationNonInsuranceServicesInfo> physicalMenuEntity = qpNonInsuranceServicesInfos.stream().filter(item -> item.getDynamicField().equals("physical_menu")).findFirst();
            if (physicalMenuEntity.isPresent()) {
                if ((cn.hutool.core.util.ObjectUtil.isEmpty(physicalMenuEntity.get().getPhysicalMenu()))
                        || (!isGeekPlus
                        // && qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.TRADITION.getCode())
                        && firstConfig.isPresent() && !firstConfig.get().getRelation().equals(Relation.SELF.getValue()))
                ) {
//                     medicalInfo.setServiceCode("");
                    medicalInfo.setPhysicalExamSupplier("");
                    medicalInfo.setPrivatePackage("");
                    // 默认套餐一
                    medicalInfo.setPublicPackage("");
                } else {
                    QpQuotationNonInsuranceServicesInfo qpQuotationNonInsuranceServicesInfo = physicalMenuEntity.get();
                    log.info("设置体检配置信息:{}", JacksonUtils.writeAsString(physicalMenuEntity.get()));
                    medicalInfo.setPhysicalExamSupplier(qpQuotationNonInsuranceServicesInfo.getPhysicalManufacturer());
                    medicalInfo.setPrivatePackage(qpQuotationNonInsuranceServicesInfo.getPhysicalMenu());
                    // 默认套餐一
                    medicalInfo.setPublicPackage("1");
                }

            }
        }

        //--财务信息
        FeeRulesInfoDto feeRulesInfo = new FeeRulesInfoDto();
        // todo qpQuoteApprove.getBusinessModel()
        if (BusinessModelEnum.BROKER.getValue().equals(qpQuoteInfo.getBusinessModel())) {
            GroupRuleDTO groupRules = new GroupRuleDTO();
            groupRules.setPayType(String.valueOf(createPlanParamDTO.getGroupRulePayType()));
            Long startTime = qpQuoteInfo.getGuaranteeStartDate().toInstant().toEpochMilli();
            Long preDayOfStartTime = DateFormater.getInstance(startTime).getOffset(-1).getBegin().getDate().getTime();
            Integer billUnit = qpQuote.getBillUnit();
            Integer billPeriod = qpQuote.getBillPeriod();

            //【渠道雇主标品】生成方案，应根据不同保障期间生成不同保全结算周期，保障期间半年及以下的同保障期间；保障期间大于半年的按半年
            if (qpQuote.getStandardOrderDetailId() != null) {
                IgStandardOrderDetail standardOrderDetail = igStandardOrderDetailDataMapper.entity(IgStandardOrderDetail.class).selectOne(qpQuote.getStandardOrderDetailId(), true);
                IgStandardOrder standardOrder = igStandardOrderDataMapper.entity(IgStandardOrder.class).selectOne(standardOrderDetail.getStandardOrderId(), true);
                if (standardOrder.getInsurancePeriod() != null) {
                    if (standardOrder.getInsurancePeriod() > 6) {
                        billPeriod = 6;
                    } else {
                        billPeriod = standardOrder.getInsurancePeriod();
                    }
                }
            }

            Long firstPaymentTime = Math.max(DateUtil.beginOfDay(DateTime.now()).getTime(), preDayOfStartTime);
            groupRules.setInitialEndTime(ZonedDateTime.ofInstant(Instant.ofEpochMilli(firstPaymentTime), qpQuoteInfo.getGuaranteeStartDate().getZone()));
            groupRules.setSettlementCycle(String.valueOf(billUnit));
            groupRules.setSettlementCycleValue(billPeriod);
            groupRules.setBillWaitingDay(30);
            feeRulesInfo.setGroupRules(groupRules);
            PolicyRuleDTO policyRules = new PolicyRuleDTO();
            policyRules.setPayType(String.valueOf(createPlanParamDTO.getPolicyRulePayType()));
            policyRules.setInitialEndTime(groupRules.getInitialEndTime());
            policyRules.setSettlementCycle(groupRules.getSettlementCycle());
            policyRules.setSettlementCycleValue(groupRules.getSettlementCycleValue());
            policyRules.setBillWaitingDay(groupRules.getBillWaitingDay());
            feeRulesInfo.setPolicyRules(policyRules);
        }


        //--销售人员
        Long id = qpQuoteInfo.getId();

        String sql = "select id,\n" +
                "created_by,\n" +
                "business_type,\n" +
                "tenant_id\n" +
                "  from qp_quote_info_group \n" +
                "  where quotation_info_group_id.qp_quotation_info_group.latest_quotation_info_id =%s";
        List<QpQuoteInfoGroupDTO> quoteGroupInfoList = qpQuoteInfoGroupBqlMapper.entity(QpQuoteInfoGroupDTO.class).execute(String.format(sql, id), true);

                // .alias("qqig")
                // .join(QpQuoteInfo.class, "qqi", "qqig.latest_quote_info_id=qqi.id")
                // .join(QpQuotationInfo.class, "qqai", "qqai.quote_info_id=qqi.id")
                // .fields(
                //         "qqig.id as id"
                //         , "qqig.latest_quote_info_id as latest_quote_info_id"
                //         , "qqig.quotation_info_group_id as quotation_info_group_id"
                // ).select(condition, true);
                ;
        QpQuoteInfoGroupDTO qpQuoteInfoGroup = quoteGroupInfoList.stream().findFirst().orElse(new QpQuoteInfoGroupDTO());
        InsuranceStaffRelationInfoDto insuranceStaffRelationInfo = PlanBasicDataV2Util.getDefaultInsuranceStaffRelationInfoDto();
        insuranceStaffRelationInfo.setBusinessNature(qpQuoteInfo.getRenewFlag().equals(RenewFlagEnum.FIRST.getValue()) ? InsuranceStaffRelationInfoDto.BUSINESS_NATURE_1 : InsuranceStaffRelationInfoDto.BUSINESS_NATURE_2);
        StaffPersonnelDto staffPersonnelDto = new StaffPersonnelDto();
        staffPersonnelDto.setUid(qpQuoteInfoGroup.getCreatedBy());
        // TOB-15700 【线上签约】外部询报价自动生成方案时，方案的销售人员取询价发起人所属经纪公司的渠道归属人
        List<Map<String, Object>> result = null;
        if (qpQuoteInfoGroup.getBusinessType().equals("external")) {
            // 如果是外部版报价设置为 询价发起人 所属经纪公司的渠道归属人
            String bql = "select user_id as user_id,user_name as user_name, created_at as created_at from qp_tenant_user where delete_flg = 0 and user_type = 1 and pt_tenant_id = " + qpQuoteInfoGroup.getTenantId();
            ResponseVO responseVO = bqlClient.selectData(bql);
            result = JacksonUtils.readValue(JacksonUtils.writeAsString(responseVO.getData()), new TypeReference<List<Map<String, Object>>>() {
            });
            if (!CollectionUtils.isEmpty(result)) {
                // 按created_at的值倒叙取第一个
                result.sort((o1, o2) -> {
                    DateTimeFormatter formatter = DateTimeFormatter.ISO_ZONED_DATE_TIME;
                    ZonedDateTime date1 = ZonedDateTime.parse((String) o1.get("created_at"), formatter);
                    ZonedDateTime date2 = ZonedDateTime.parse((String) o2.get("created_at"), formatter);
                    return date2.compareTo(date1);
                });
                staffPersonnelDto.setUid(Long.valueOf((String) result.get(0).get("user_id")));
            }
        }

        staffPersonnelDto.setRatio(new BigDecimal(100));
        staffPersonnelDto.setType(qpQuoteInfo.getRenewFlag().equals(RenewFlagEnum.FIRST.getValue()) ? StaffPersonnelDto.RATIO_2 : StaffPersonnelDto.RATIO_3);
        // 外部版如果渠道归属人为空，生成方案后的销售人员也应为空
        if (CollectionUtils.isEmpty(result) && qpQuoteInfoGroup.getBusinessType().equals("external")) {
            insuranceStaffRelationInfo.setStaffPersonnelList(null);
        } else {
            insuranceStaffRelationInfo.setStaffPersonnelList(Collections.singletonList(staffPersonnelDto));
        }
        AutoCreatePlanDto autoCreatePlanDto = new AutoCreatePlanDto();
        autoCreatePlanDto.setBasicInsuranceInfo(basicInsuranceInfoDto);
        autoCreatePlanDto.setConfigBindList(configBindList);
        autoCreatePlanDto.setInsuranceRulesInfo(insuranceRulesInfoDto);
        autoCreatePlanDto.setSpecialAgreement(specialAgreementDto);
        autoCreatePlanDto.setMedicalInfo(medicalInfo);
        autoCreatePlanDto.setFeeRulesInfo(feeRulesInfo);
        autoCreatePlanDto.setInsuranceStaffRelationInfo(insuranceStaffRelationInfo);
        autoCreatePlanDto.setQuotePlanId(qpQuote.getId());
        autoCreatePlanDto.setSpecialEdition(String.valueOf(qpQuoteInfo.getSpecialEdition()));
        // todo qpQuoteApprove.getBusinessModel()


        if (BusinessModelEnum.BROKER.getValue().equals(qpQuoteInfo.getBusinessModel())) {
            // 经代默认true
            autoCreatePlanDto.setAutoCreateBill(true);
            autoCreatePlanDto.setAutoCreatePolicy(true);
        } else {
            // 会员默认false
            autoCreatePlanDto.setAutoCreateBill(false);
            autoCreatePlanDto.setAutoCreatePolicy(false);
        }
        autoCreatePlanDto.setQuotationInfoId(qpQuoteInfo.getId());
        log.debug("autoCreatePlanByConfigs.Time-e:{}", DateTime.now().toMsStr());
        return autoCreatePlanDto;
    }

    //自动生成方案 方案风控组装
    private static void convertInsuranceRulesInfoDto(QpQuotationInfo qpQuoteInfo, QpQuotation qpQuote, Map<Long, QpQuotationConfig> configs, String occupation, BasicInsuranceInfoDto basicInsuranceInfoDto, Boolean isGeekPlan, InsuranceRulesInfoDto insuranceRulesInfoDto, List<QpQuotation> quotes, String autoCreatePlanStrategyType) {
        //【一键生成方案】极客+报价，生成方案后，最大年龄和最小年龄，应取多个企业增员-本人方案组中的最大年龄和最小年龄
        //【一键生成方案】极客+报价，生成方案后，女性占比，应取多个企业增员-本人方案组中的期初女性占比最大值

        //方案开关
        if (isGeekPlan) {
            if (quotes.stream().allMatch(x -> x.getRuleOpen() == null)) {
                insuranceRulesInfoDto.setPlanStatus("1");
            } else {
                //极客+按照员工配置取值，有开启就开启，全部关闭，默认关闭
                insuranceRulesInfoDto.setPlanStatus(quotes.stream()
                        //.filter(x -> x.getRelation() == Relation.SELF.getValue())
                        .anyMatch(x -> x.getRuleOpen() != null && x.getRuleOpen() == 1) ? "1" : "2");
            }
        } else {
            if (quotes.get(0).getRuleOpen() == null) {
                insuranceRulesInfoDto.setPlanStatus("1");
            } else {
                insuranceRulesInfoDto.setPlanStatus(String.valueOf(quotes.get(0).getRuleOpen()));
                String planStatus = insuranceRulesInfoDto.getPlanStatus();
                if (StringUtils.isNotEmpty(planStatus) && Integer.parseInt(planStatus) == 0) {
                    insuranceRulesInfoDto.setPlanStatus("2");
                }
            }
        }
        // 标品默认启用投保风控，方案风控禁用
        if (StringUtils.isNotEmpty(autoCreatePlanStrategyType)
                && AtuoCreatePlanStrategyTypeEnum.STANDARD.getCode().equals(autoCreatePlanStrategyType)) {
            insuranceRulesInfoDto.setPlanStatus("2");
        }
        // 年龄检查
        if (isGeekPlan) {
            insuranceRulesInfoDto.getAgeCheckRule().setAgeCheckMode(AgeCheckRuleDto.AGE_CHECK_MODE_31);
            QpQuotation maxAgeQuoteConfig = quotes.stream().filter(v -> v.getRelation().equals(Relation.SELF.getValue())).max(Comparator.comparing(QpQuotation::getMaxAge)).get();
            insuranceRulesInfoDto.getAgeCheckRule().setMaxAge(maxAgeQuoteConfig.getMaxAge());
            QpQuotation minAgeQuoteConfig = quotes.stream().filter(v -> v.getRelation().equals(Relation.SELF.getValue())).min(Comparator.comparing(QpQuotation::getMinAge)).get();
            insuranceRulesInfoDto.getAgeCheckRule().setMinAge(minAgeQuoteConfig.getMinAge());
            QpQuotation avgAgeQuoteConfig = quotes.stream().filter(v -> v.getRelation().equals(Relation.SELF.getValue())).filter(x -> x.getOpeningAvgAge() != null).max(Comparator.comparing(QpQuotation::getOpeningAvgAge)).get();
            insuranceRulesInfoDto.getAgeCheckRule().setAvgAgeDefault(Optional.ofNullable(avgAgeQuoteConfig.getOpeningAvgAge()).orElse(0).intValue());
            //子女投保最大年龄
            Optional<QpQuotation> max = quotes.stream().filter(x -> Relation.CHILD.getValue().equals(x.getRelation()) && x.getMaxAge() != null).max(Comparator.comparing(QpQuotation::getMaxAge));
            if (max.isPresent()) {
                insuranceRulesInfoDto.getDefaultRule().setChildInsuredMaxAge(max.get().getMaxAge());
            }
            //配偶最小年龄
            Optional<QpQuotation> spouseMin = quotes.stream().filter(x -> Relation.SPOUSE.getValue().equals(x.getRelation()) && x.getMinAge() != null).min(Comparator.comparing(QpQuotation::getMinAge));
            if (spouseMin.isPresent()) {
                insuranceRulesInfoDto.getDefaultRule().setSpouseInsuredMinAge(spouseMin.get().getMinAge());
            }
            //配偶最大年龄
            Optional<QpQuotation> spouseMax = quotes.stream().filter(x -> Relation.SPOUSE.getValue().equals(x.getRelation()) && x.getMaxAge() != null).max(Comparator.comparing(QpQuotation::getMaxAge));
            if (spouseMax.isPresent()) {
                insuranceRulesInfoDto.getDefaultRule().setSpouseInsuredMaxAge(spouseMax.get().getMaxAge());
            }
            //父母投保的最小年龄
            Optional<QpQuotation> parentMin = quotes.stream().filter(x -> Relation.PARENT.getValue().equals(x.getRelation()) && x.getMaxAge() != null).min(Comparator.comparing(QpQuotation::getMinAge));
            if (parentMin.isPresent()) {
                insuranceRulesInfoDto.getDefaultRule().setParentInsuredMinAge(parentMin.get().getMinAge());
            }
            //父母投保的最小年龄
            Optional<QpQuotation> parentMax = quotes.stream().filter(x -> Relation.PARENT.getValue().equals(x.getRelation()) && x.getMaxAge() != null).max(Comparator.comparing(QpQuotation::getMaxAge));
            if (parentMin.isPresent()) {
                insuranceRulesInfoDto.getDefaultRule().setParentInsuredMaxAge(parentMax.get().getMaxAge());
            }

        } else if (qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.EMPLOYER.getCode())) {
            //雇主方案

            insuranceRulesInfoDto.getAgeCheckRule().setAgeCheckMode(AgeCheckRuleDto.AGE_CHECK_MODE_31);
            insuranceRulesInfoDto.getAboutGroupRule().setOverageNum(qpQuote.getOverageNum());

            insuranceRulesInfoDto.getAgeCheckRule().setMaxAge(qpQuote.getMaxAge());
            insuranceRulesInfoDto.getAgeCheckRule().setMinAge(qpQuote.getMinAge());
            if (qpQuote.getOpeningAvgAge() != null) {
                insuranceRulesInfoDto.getAgeCheckRule().setAvgAgeDefault(qpQuote.getOpeningAvgAge().intValue());
            }
        } else {
            Optional<QpQuotation> qpQuotation = quotes.stream().findFirst();
            if (qpQuotation.isPresent()) {
                if (Relation.SELF.getValue().equals(qpQuotation.get().getRelation())) {
                    insuranceRulesInfoDto.getAgeCheckRule().setAgeCheckMode(AgeCheckRuleDto.AGE_CHECK_MODE_31);
                    insuranceRulesInfoDto.getAgeCheckRule().setMaxAge(qpQuotation.get().getMaxAge());
                    insuranceRulesInfoDto.getAgeCheckRule().setMinAge(qpQuotation.get().getMinAge());
                } else if (Relation.CHILD.getValue().equals(qpQuotation.get().getRelation())) {
                    insuranceRulesInfoDto.getAgeCheckRule().setAgeCheckMode(AgeCheckRuleDto.AGE_CHECK_MODE_12);
                    insuranceRulesInfoDto.getDefaultRule().setChildInsuredMaxAge(qpQuotation.get().getMaxAge());
                } else if (Relation.SPOUSE.getValue().equals(qpQuotation.get().getRelation())) {
                    insuranceRulesInfoDto.getAgeCheckRule().setAgeCheckMode(AgeCheckRuleDto.AGE_CHECK_MODE_11);
                    insuranceRulesInfoDto.getAgeCheckRule().setMaxAge(qpQuotation.get().getMaxAge());
                    insuranceRulesInfoDto.getAgeCheckRule().setMinAge(qpQuotation.get().getMinAge());
                } else {
                    insuranceRulesInfoDto.getAgeCheckRule().setAgeCheckMode(AgeCheckRuleDto.AGE_CHECK_MODE_11);
                    insuranceRulesInfoDto.getDefaultRule().setParentInsuredMaxAge(qpQuotation.get().getMaxAge());
                    insuranceRulesInfoDto.getDefaultRule().setParentInsuredMinAge(qpQuotation.get().getMinAge());
                }
            }
            insuranceRulesInfoDto.getAgeCheckRule().setAvgAgeDefault(Optional.ofNullable(qpQuotation.get().getOpeningAvgAge()).orElse(0).intValue());
        }
        // 实际平均年龄
        insuranceRulesInfoDto.getAgeCheckRule().setAvgAge(insuranceRulesInfoDto.getAgeCheckRule().getAvgAgeDefault());

        // 企业相关
        // 最低参保人数
        if (isGeekPlan) {
            QpQuotationConfig insuredCountQuoteConfig = configs.values().stream().filter(v -> v.getRelation().equals(Relation.SELF.getValue())).min(Comparator.comparing(QpQuotationConfig::getInsuredCount)).get();
            if (insuredCountQuoteConfig != null) {
                insuranceRulesInfoDto.getAboutGroupRule().setMinInsureDefault(Optional.ofNullable(insuredCountQuoteConfig.getOpeningInsuredCount()).orElse(0).intValue());
            }
//            Optional<QpQuotation> max = quotes.stream().
//                    filter(x -> x.getClientType() != ClientType.RECOMMEND.getValue() && x.getMaxPerson() != null).
//                    max(Comparator.comparing(QpQuotation::getMaxPerson));
//            if(max.isPresent()
//                    &&insuranceRulesInfoDto.getPlanStatus()!=null
//                    &&insuranceRulesInfoDto.getPlanStatus()==1
//            )
//            insuranceRulesInfoDto.getAboutGroupRule().setMaxPersonNumber(max.get().getMaxPerson());
        } else {
            if (qpQuote.getOpeningInsuredCount() != null) {
                insuranceRulesInfoDto.getAboutGroupRule().setMinInsureDefault(qpQuote.getOpeningInsuredCount().intValue());
            }
            Optional<QpQuotation> max = quotes.stream().
                    filter(x -> x.getMaxPerson() != null).
                    max(Comparator.comparing(QpQuotation::getMaxPerson));
            if (max.isPresent()
                    && !insuranceRulesInfoDto.getPlanStatus().isEmpty()
                    && insuranceRulesInfoDto.getPlanStatus().equals("1"))
                insuranceRulesInfoDto.getAboutGroupRule().setMaxPersonNumber(max.get().getMaxPerson());

        }
        insuranceRulesInfoDto.getAboutGroupRule().setMinInsureNum(insuranceRulesInfoDto.getAboutGroupRule().getMinInsureDefault());
        if (qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.TRADITION.getCode()) && !configs.values().stream().map(QpQuotationConfig::getRelation).collect(Collectors.toList()).contains(Relation.SELF.getValue())) {
            insuranceRulesInfoDto.getAboutGroupRule().setInsureNumSwitch(Collections.EMPTY_LIST);
        } else {
            insuranceRulesInfoDto.getAboutGroupRule().setInsureNumSwitch(CollUtil.newArrayList(AboutGroupRuleDto.INSURE_NUM_SWITCH_3));
        }

        //1）仅传统、意外本人默认添加该类风控，传统子女、父母、配偶默认不添加该类风控
        //（2）雇主默认不添加该类风控
        //传统极客+必带本人方案 判断不带本人的传统排除即可

        if (qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.GEEK_PLUS.getCode())
                || (qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.TRADITION.getCode()) && configs.values().stream().anyMatch(x -> x.getRelation().equals(Relation.SELF.getValue())))) {
            // 女性占比
            if (isGeekPlan) {
                Optional<QpQuotation> quote = quotes.stream().filter(v -> v.getRelation().equals(Relation.SELF.getValue()))
                        .filter(x -> x.getOpeningFemaleProportion() != null)
                        .max(Comparator.comparing(QpQuotation::getOpeningFemaleProportion));
                if (quote.isPresent()) {
                    BigDecimal femaleProportion = quote.get().getOpeningFemaleProportion();
                    if (femaleProportion != null){
                        // 低频团险女性占比用不低于控制方案风控 https://jira.insgeek.cn/browse/TOB-19744
                        if (qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.GEEK_PLUS.getCode())) {
                            insuranceRulesInfoDto.getAboutGroupRule().setMinRatioFemaleDefault(femaleProportion.intValue());
                        } else {
                            insuranceRulesInfoDto.getAboutGroupRule().setMaxRatioFemaleDefault(femaleProportion.intValue());
                        }
                    }
                }
            } else {
                Optional<QpQuotation> quote = quotes.stream().findFirst();
                if (quote.isPresent()) {
                    BigDecimal femaleProportion = quote.get().getOpeningFemaleProportion();
                    if (femaleProportion != null){
                        // 低频团险女性占比用不低于控制方案风控 https://jira.insgeek.cn/browse/TOB-19744
                        if (qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.GEEK_PLUS.getCode())) {
                            insuranceRulesInfoDto.getAboutGroupRule().setMinRatioFemaleDefault(femaleProportion.intValue());
                        } else {
                            insuranceRulesInfoDto.getAboutGroupRule().setMaxRatioFemaleDefault(femaleProportion.intValue());
                        }
                    }
                }
            }
        }
        // 低频团险女性占比用不低于控制方案风控 https://jira.insgeek.cn/browse/TOB-19744
        if (qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.GEEK_PLUS.getCode())) {
            insuranceRulesInfoDto.getAboutGroupRule().setMinRatioFemaleNum(insuranceRulesInfoDto.getAboutGroupRule().getMinRatioFemaleDefault());
        } else {
            insuranceRulesInfoDto.getAboutGroupRule().setMaxRatioFemaleNum(insuranceRulesInfoDto.getAboutGroupRule().getMaxRatioFemaleDefault());
        }
        if ((qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.TRADITION.getCode()) && !configs.values().stream().map(QpQuotationConfig::getRelation).collect(Collectors.toList()).contains(Relation.SELF.getValue())) || qpQuoteInfo.getQuoteType().equals(QuoteTypeEnum.EMPLOYER.getCode())) {
            insuranceRulesInfoDto.getAboutGroupRule().setMaxSexRatioSwitch(Collections.EMPTY_LIST);
        } else {
            insuranceRulesInfoDto.getAboutGroupRule().setMaxSexRatioSwitch(CollUtil.newArrayList(AboutGroupRuleDto.MAX_SEX_RATIO_SWITCH_3));
        }

        // 极客+设置首批批量
        if (isGeekPlan) {
            insuranceRulesInfoDto.getAboutGroupRule().setFirstBatch(AboutGroupRuleDto.FIRST_BATCH_0);
        } else {
            String relation = qpQuote.getRelation();
            Relation relationEnum = Relation.getEnumByCode(relation);

            switch (relationEnum) {
                case SELF:
                    insuranceRulesInfoDto.getAboutGroupRule().setFirstBatch(AboutGroupRuleDto.FIRST_BATCH_0);
                    break;
                default:
                case CHILD:
                case PARENT:
                case SPOUSE:
                    insuranceRulesInfoDto.getAboutGroupRule().setFirstBatch(AboutGroupRuleDto.FIRST_BATCH_0);
                    break;
            }
        }


        // 账单规则
        insuranceRulesInfoDto.getBillRule().setShowBillSwitch(basicInsuranceInfoDto.getBizMode().equals(BasicInsuranceInfoDto.BIZ_MODE_4) ? BillRuleDto.SHOW_BILL_SWITCH_0 : BillRuleDto.SHOW_BILL_SWITCH_1);
        // 职业规则
        if (Integer.valueOf(occupation) <= 3) {
            insuranceRulesInfoDto.getCareerRule().setJobCategoryRule(Lists.newArrayList("1", "2", "3"));
        } else if (Integer.valueOf(occupation) == 21) {
            insuranceRulesInfoDto.getCareerRule().setJobCategoryRule(Lists.newArrayList("1", "2", "3"));
        } else {
            ArrayList<String> jobCategoryRuleList = Lists.newArrayList();
            for (int i = 1; i <= Integer.valueOf(occupation); i++) {
                if (i <= 6) {
                    if (Integer.valueOf(occupation) < 21) {
                        jobCategoryRuleList.add(String.valueOf(i));
                    }
                } else if (i >= 21) {
                    jobCategoryRuleList.add(String.valueOf(i - 20));
                }
            }
            insuranceRulesInfoDto.getCareerRule().setJobCategoryRule(jobCategoryRuleList);
        }
    }

    private LocalDate getLocalDate(CreatePlanParamDTO createPlanParamDTO) {
        ZoneId timeZone = ZoneId.systemDefault();
        LocalDate getLocalDate = createPlanParamDTO.getStartTime().toInstant().atZone(timeZone).toLocalDate();
        return getLocalDate;
    }


    private Boolean isBroadCategories(Long companyId) {
        Long[] broadCategoriesArr = {102L, 72L, 105L, 104L, 103L, 106L};
        List<Long> broadCategoriesList = Arrays.asList(broadCategoriesArr);
        if (broadCategoriesList.contains(companyId)) {
            return true;
        }
        return false;
    }

    @Override
    public void autoCreatePlanResultNotification(List<AutoCreatePlanResultDto> autoCreatePlanResultDtoList) {
        for (AutoCreatePlanResultDto autoCreatePlanResultDto : autoCreatePlanResultDtoList) {
            if (autoCreatePlanResultDto.isFlag()) {
                QpCustomerQuoteRelation qpCustomerQuoteRelation = new QpCustomerQuoteRelation();
                qpCustomerQuoteRelation.setQuoteId(autoCreatePlanResultDto.getQuotePlanId());
                qpCustomerQuoteRelation.setInsuranceQuoteId(autoCreatePlanResultDto.getCorePlanId());
                qpCustomerQuoteRelationDataMapper.entity(QpCustomerQuoteRelation.class).insertOne(qpCustomerQuoteRelation);
            } else {
                CommonDataUtil.businessException(QuoteErrorCode.CREATE_PLAN_MSG, autoCreatePlanResultDto.getAutoCreatePlanErrorMsg());
            }
        }
    }


    @Override
    public List<IgInsurance> getInsuranceByPlanIds(List<Long> planIds){
        QIgInsurance igInsurance = QIgInsurance.ig_insurance;
        List<IgInsurance> igInsuranceList= bqlQueryFactory
                .select(Expressions.stringPath("ig_insurance.*"))
                .from(igInsurance)
                .where(igInsurance.plan_id.in(planIds))
                .findList(IgInsurance.class);
        return igInsuranceList;
    }
}
