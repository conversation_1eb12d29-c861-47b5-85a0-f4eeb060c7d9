package com.insgeek.business.quote.external.datasend.order.mq.consumer;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.exceptions.ExceptionUtil;
import com.google.common.collect.ImmutableMap;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.boot.mq.api.MQListener;
import com.insgeek.boot.mq.api.MessageExt;
import com.insgeek.business.quote.common.exception.QuoteException;
import com.insgeek.business.quote.external.datasend.order.assembler.OrderDataAssembler;
import com.insgeek.business.quote.external.datasend.order.config.OrderDataSendConfig;
import com.insgeek.business.quote.external.datasend.order.constant.OrderDataSendConstant;
import com.insgeek.business.quote.external.datasend.order.mq.consumer.abstracts.AbstractMqConsumer;
import com.insgeek.business.quote.external.datasend.order.pojo.dto.OrderSendDataDTO;
import com.insgeek.business.quote.external.datasend.order.pojo.dto.log.OrderDataSendLogDTO;
import com.insgeek.business.quote.external.datasend.order.sender.OrderDataSender;
import com.insgeek.business.standard.enums.StandardOrderStatusEnum;
import com.insgeek.insurance.meta.utils.context.IdentityUtil;
import com.insgeek.protocol.data.client.enums.BusinessSourceEnum;
import com.insgeek.protocol.data.client.message.dto.OrderDataSendEventDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 渠道订单数据消费者
 */
@Slf4j
@Component
@MQListener(channelName = OrderDataSendConstant.SEND_ORDER_DATA_CONSUMER)
public class OrderDataSendConsumer extends AbstractMqConsumer {

    @Resource
    private OrderDataSendConfig orderDataSendConfig;

    @Resource
    private OrderDataSender orderDataSender;

    @Resource
    private OrderDataAssembler orderDataAssembler;

    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public Boolean onMessage(String msgBody, MessageExt msgExt, String topic, String tag) {
        IdentityUtil.setRobotAuth();
        OrderDataSendEventDTO orderDataSendEventDTO = JacksonUtils.readValue(msgBody, OrderDataSendEventDTO.class);
        String channelId = orderDataSendEventDTO.getChannelId();
        String businessSource = orderDataSendEventDTO.getBusinessSource();

        // 构造发送配置映射
        ImmutableMap<String, Map<String, OrderDataSendConfig.SendConfig>> sendConfigMapping = constructSendConfigMapping();

        // 校验是否需要同步
        boolean isNeedSend = checkIsNeedSend(businessSource, channelId, sendConfigMapping);
        if (!isNeedSend) {
            log.info("业务来源: {}, 渠道id: {}，不需要同步，消息体内容: {}", businessSource, channelId, msgBody);
            return Boolean.TRUE;
        }

        // 获取本次发送相关的配置
        log.info("开始获取本次发送相关的配置，业务来源: {}, 渠道id: {}, 消息体内容: {}", businessSource, channelId, msgBody);
        OrderDataSendConfig.SendConfig sendConfig = sendConfigMapping.get(businessSource).get(channelId);

        // 根据基础信息构建日志实体
        log.info("开始根据基础信息构建日志实体，业务来源: {}, 渠道id: {}, 消息体内容: {}", businessSource, channelId, msgBody);
        OrderDataSendLogDTO orderDataSendLogDTO = buildOrderDataSendLogWithBasicInfo(sendConfig, orderDataSendEventDTO);

        // 获取要发送的订单数据
        log.info("开始获取要发送的订单数据，业务来源: {}, 渠道id: {}, 消息体内容: {}", businessSource, channelId, msgBody);
        OrderSendDataDTO orderSendDataDTO = getOrderSendData(orderDataSendEventDTO, orderDataSendLogDTO);

        // 发送数据
        log.info("开始发送数据，业务来源: {}, 渠道id: {}, 消息体内容: {}", businessSource, channelId, msgBody);
        sendData(sendConfig, orderSendDataDTO, orderDataSendLogDTO);

        // 记录发送日志
        mongoTemplate.insert(orderDataSendLogDTO);

        return Boolean.TRUE;
    }

    /**
     * 发送数据
     *
     * @param sendConfig          发送配置
     * @param orderSendDataDTO    要发送的订单数据
     * @param orderDataSendLogDTO 日志实体
     */
    private void sendData(OrderDataSendConfig.SendConfig sendConfig,
                          OrderSendDataDTO orderSendDataDTO,
                          OrderDataSendLogDTO orderDataSendLogDTO) {
        try {
            log.info("Data sending has started.");
            orderDataSender.sendData(sendConfig, orderSendDataDTO, orderDataSendLogDTO);
            log.info("Data sending has finished.");
        } catch (Exception e) {
            log.error("The send data failed. Error info: ", e);
            // 出现异常时记录异常日志
            recordExceptionLog(orderDataSendLogDTO, e, "发送数据时出现异常");
            throw new QuoteException(-1, "The send data failed.");
        }
    }

    /**
     * 获取要发送的订单数据
     *
     * @param orderDataSendEventDTO 消息数据
     * @param orderDataSendLogDTO   日志实体
     */
    private OrderSendDataDTO getOrderSendData(OrderDataSendEventDTO orderDataSendEventDTO,
                                              OrderDataSendLogDTO orderDataSendLogDTO) {
        log.info("订单数据开始组装，orderDataSendEventDTO: {}", JacksonUtils.writeAsString(orderDataSendEventDTO));
        try {
            OrderSendDataDTO orderSendDataDTO;
            // 如果是标品的订单，且状态为关闭，则从mongo中获取待支付状态的订单数据
            // 关闭状态的订单相关的数据会被删除，所以需要从mongo中获取
            if (BusinessSourceEnum.STANDARD.getCode().equals(orderDataSendEventDTO.getBusinessSource())
                    && StandardOrderStatusEnum.TIMEOUT_CLOSED.getDictKey().equals(orderDataSendEventDTO.getStatus())) {
                // 从mongo中获取待支付状态的发送日志
                OrderDataSendLogDTO sendLogDTO = getOrderDataSendLogFromMongo(orderDataSendEventDTO.getReferenceCode(), StandardOrderStatusEnum.TO_BE_PAID.getDictKey());
                // 从日志中获取发送的数据
                orderSendDataDTO = JacksonUtils.readValue((String) sendLogDTO.getSyncData(), OrderSendDataDTO.class);
                // 设置状态为关闭
                orderSendDataDTO.setStatus(StandardOrderStatusEnum.TIMEOUT_CLOSED.getDictKey());
            } else {
                orderSendDataDTO = orderDataAssembler.assembleOrderData(orderDataSendEventDTO);
            }
            // 组装数据
            log.info("订单数据组装结束，orderSendData: {}", JacksonUtils.writeAsString(orderSendDataDTO));
            return orderSendDataDTO;
        } catch (Exception e) {
            log.error("组装订单数据时出现异常，异常信息：", e);
            // 出现异常时记录异常日志
            recordExceptionLog(orderDataSendLogDTO, e, "组装数据时出现异常");
            throw new QuoteException(-1, "组装数据时出现异常");
        }
    }

    /**
     * 从mongo中获取标品指定状态的成功的订单数据
     *
     * @param orderCode 订单号
     * @param status    状态
     */
    private OrderDataSendLogDTO getOrderDataSendLogFromMongo(String orderCode, String status) {
        // 从mongo获取待支付状态的订单数据
        Query query = new Query();
        query.addCriteria(Criteria.where("syncOrderCode").is(Long.valueOf(orderCode))
                .and("syncOrderStatus").is(status)
                .and("businessSource").is(BusinessSourceEnum.STANDARD.getName())
                .and("isSuccessful").is(Boolean.TRUE));
        query.with(Sort.by(Sort.Direction.DESC, "syncTime"));
        query.limit(1);
        List<OrderDataSendLogDTO> orderDataSendLogDTOList = mongoTemplate.find(query, OrderDataSendLogDTO.class);
        OrderDataSendLogDTO sendLogDTO = orderDataSendLogDTOList.isEmpty() ? null : orderDataSendLogDTOList.get(0);
        // 如果未找到
        if (sendLogDTO == null) {
            log.error("No orders with to-be-paid status found, standardOrderId: {}", orderCode);
            throw new QuoteException(-1, "No orders with to-be-paid status found");
        }
        return sendLogDTO;
    }

    /**
     * 记录出现异常时的日志
     */
    private void recordExceptionLog(OrderDataSendLogDTO orderDataSendLogDTO, Exception e, String errorMessage) {
        // 出现异常时记录异常日志
        orderDataSendLogDTO.setIsSuccessful(Boolean.FALSE);
        orderDataSendLogDTO.setExceptionMsg(errorMessage + "，异常信息：" + ExceptionUtil.getMessage(e));
        mongoTemplate.insert(orderDataSendLogDTO);
    }


    /**
     * 构造数据发送日志实体的基本信息
     *
     * @param sendConfig            发送配置
     * @param orderDataSendEventDTO 消息数据
     */
    private OrderDataSendLogDTO buildOrderDataSendLogWithBasicInfo(OrderDataSendConfig.SendConfig sendConfig, OrderDataSendEventDTO orderDataSendEventDTO) {
        OrderDataSendLogDTO orderDataSendLogDTO = new OrderDataSendLogDTO();
        orderDataSendLogDTO.setSyncTime(DateUtil.now());
        orderDataSendLogDTO.setSyncUrl(sendConfig.getRequestInfo().getUrl());
        orderDataSendLogDTO.setSyncMethod(sendConfig.getRequestInfo().getMethod());
        orderDataSendLogDTO.setChannelId(Long.valueOf(orderDataSendEventDTO.getChannelId()));
        orderDataSendLogDTO.setBusinessSource(BusinessSourceEnum.getNameByCode(orderDataSendEventDTO.getBusinessSource()));
        orderDataSendLogDTO.setSyncOrderStatus(orderDataSendEventDTO.getStatus());
        orderDataSendLogDTO.setIsSuccessful(Boolean.TRUE);
        orderDataSendLogDTO.setSyncOrderCode(BusinessSourceEnum.STANDARD.getCode().equals(orderDataSendEventDTO.getBusinessSource())
                ? Long.valueOf(orderDataSendEventDTO.getReferenceCode()) : Long.valueOf(orderDataSendEventDTO.getQuotationCode()));
        return orderDataSendLogDTO;
    }

    /**
     * 校验是否需要同步
     */
    private boolean checkIsNeedSend(String businessSource, String channelId, ImmutableMap<String, Map<String, OrderDataSendConfig.SendConfig>> sendConfigMapping) {
        // 如果配置内包含该业务来源，且配置内包含该渠道id，则需要同步
        return sendConfigMapping.containsKey(businessSource) && sendConfigMapping.get(businessSource).containsKey(channelId);
    }

    /**
     * 构造发送配置映射，key -> 业务来源，value -> 具体配置
     * key -> 渠道id，value -> 具体配置
     */
    private ImmutableMap<String, Map<String, OrderDataSendConfig.SendConfig>> constructSendConfigMapping() {
        List<OrderDataSendConfig.SendConfig> domesticStandardConfigList =
                Optional.ofNullable(orderDataSendConfig.getDomesticStandard()).orElse(Collections.emptyList());
        Map<String, OrderDataSendConfig.SendConfig> domesticStandardConfigMapping = domesticStandardConfigList.stream()
                .collect(Collectors.toMap(
                        config -> config.getChannelInfo().getChannelId(),
                        Function.identity(),
                        (v1, v2) -> v1));
        List<OrderDataSendConfig.SendConfig> domesticQuotationConfigList =
                Optional.ofNullable(orderDataSendConfig.getDomesticQuotation()).orElse(Collections.emptyList());
        Map<String, OrderDataSendConfig.SendConfig> domesticQuotationConfigMapping = domesticQuotationConfigList.stream()
                .collect(Collectors.toMap(
                        config -> config.getChannelInfo().getChannelId(),
                        Function.identity(),
                        (v1, v2) -> v1));
        return ImmutableMap.<String, Map<String, OrderDataSendConfig.SendConfig>>builder()
                .put(BusinessSourceEnum.QUOTATION.getCode(), domesticQuotationConfigMapping)
                .put(BusinessSourceEnum.STANDARD.getCode(), domesticStandardConfigMapping)
                .build();
    }

}