package com.insgeek.business.quote.external.datasend.order.sender;

import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.business.quote.common.exception.QuoteException;
import com.insgeek.business.quote.external.datasend.order.config.OrderDataSendConfig;
import com.insgeek.business.quote.external.datasend.order.constant.OrderDataSendConstant;
import com.insgeek.business.quote.external.datasend.order.pojo.dto.OrderSendDataDTO;
import com.insgeek.business.quote.external.datasend.order.pojo.dto.UrlHolder;
import com.insgeek.business.quote.external.datasend.order.pojo.dto.log.OrderDataSendLogDTO;
import com.insgeek.business.quote.external.datasend.order.pojo.vo.OrderReturnResultVO;
import com.insgeek.business.quote.external.datasend.order.sender.authentication.handler.AuthResponseHandlerFactory;
import com.insgeek.business.quote.external.datasend.order.sender.authentication.privider.AuthInfoProviderFactory;
import com.insgeek.business.quote.external.datasend.order.sender.crypto.CryptoFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.Map;
import java.util.Objects;

/**
 * 订单数据发送器
 */
@Slf4j
@Component
public class OrderDataSender {

    @Resource(name = "externalRestTemplate")
    private RestTemplate restTemplate;

    @Resource
    private AuthInfoProviderFactory authInfoProviderFactory;

    @Resource
    private AuthResponseHandlerFactory authResponseHandlerFactory;

    @Resource
    private CryptoFactory cryptoFactory;

    /**
     * 发送数据
     *
     * @param sendConfig          发送配置
     * @param orderSendDataDTO    要发送的数据
     * @param orderDataSendLogDTO 日志实体
     */
    public void sendData(OrderDataSendConfig.SendConfig sendConfig, OrderSendDataDTO orderSendDataDTO, OrderDataSendLogDTO orderDataSendLogDTO) {
        log.info("The send data is started.");

        // 包装url
        UrlHolder urlHolder = UrlHolder.builder().url(sendConfig.getRequestInfo().getUrl()).build();

        // 请求头
        HttpHeaders headers = convertHttpHeader(sendConfig.getRequestInfo().getHeaders());

        // 获取认证信息
        Map<String, Object> authInfoMap = authInfoProviderFactory.getAuthInfo(sendConfig.getAuthenticationInfo(), orderSendDataDTO, sendConfig.getChannelInfo().getUniqueCode());

        // 处理认证信息
        authResponseHandlerFactory.handleAuthResponse(authInfoMap, urlHolder, headers, sendConfig.getChannelInfo().getUniqueCode());

        // 序列化要发送的订单数据
        String sendData = JacksonUtils.writeAsString(orderSendDataDTO);
        orderDataSendLogDTO.setSyncData(sendData);

        // 加密发送数据
        String encryptSendData = encryptSendData(sendConfig, sendData);
        if (Objects.nonNull(encryptSendData)) {
            orderDataSendLogDTO.setSyncEncryptData(encryptSendData);
        }

        // 发送请求
        String returnData = doSendData(urlHolder.getUrl(), sendConfig.getRequestInfo().getMethod(), Objects.nonNull(encryptSendData) ? encryptSendData : sendData, headers);
        orderDataSendLogDTO.setSyncResult(returnData);

        // 解密返回数据
        String decryptReturnData = decryptReturnData(sendConfig, returnData);
        if (Objects.nonNull(decryptReturnData)) {
            orderDataSendLogDTO.setSyncDecryptResult(decryptReturnData);
        }

        // 处理返回结果
        handleResult(Objects.nonNull(decryptReturnData) ? decryptReturnData : returnData, orderDataSendLogDTO);

        log.info("The send data is ended.");
    }

    /**
     * 处理返回结果
     *
     * @param returnData          返回数据
     * @param orderDataSendLogDTO 日志
     */
    private void handleResult(String returnData, OrderDataSendLogDTO orderDataSendLogDTO) {
        try {
            OrderReturnResultVO orderReturnResultVO = JacksonUtils.readValue(returnData, OrderReturnResultVO.class);
            if (!OrderDataSendConstant.SEND_SUCCESS_CODE.equals(orderReturnResultVO.getCode())) {
                // 记录异常信息
                orderDataSendLogDTO.setExceptionMsg("渠道系统返回失败，错误信息：" + orderReturnResultVO.getMessage());
                orderDataSendLogDTO.setIsSuccessful(Boolean.FALSE);
            }
            log.info("The handle result is successful.");
        } catch (Exception e) {
            log.error("Handle result error occurred. Error info: ", e);
            throw new QuoteException(-1, "Handle result error occurred. Error info: " + e.getMessage());
        }
    }

    /**
     * 解密返回数据
     *
     * @param sendConfig 发送配置
     * @param returnData 返回数据
     */
    private String decryptReturnData(OrderDataSendConfig.SendConfig sendConfig, String returnData) {
        // 如果需要解密
        if (sendConfig.getCryptoInfo().getIsNeedDecryptReturnData()) {
            try {
                String decryptData = cryptoFactory.decrypt(sendConfig.getCryptoInfo(), sendConfig.getChannelInfo().getUniqueCode(), returnData);
                log.info("The decryption return data is successful. decryptData: {}", decryptData);
                return decryptData;
            } catch (Exception e) {
                log.error("Decryption return data error occurred.", e);
                throw new QuoteException(-1, "Decryption error occurred. Error info: " + e.getMessage());
            }
        }
        return null;
    }

    /**
     * 发送请求
     *
     * @param url        请求地址
     * @param method     请求方法
     * @param sendData   要发送的数据
     * @param headers    请求头
     */
    private String doSendData(String url, String method, String sendData, HttpHeaders headers) {
        try {
            ResponseEntity<String> responseEntity = restTemplate.exchange(
                    url,
                    Objects.requireNonNull(HttpMethod.resolve(method)),
                    new HttpEntity<>(sendData, headers),
                    String.class
            );
            log.info("The send request is successful. url: {}, method: {}, headers: {}, requestParam: {}, response: {}",
                    url, method, headers, sendData, responseEntity.getBody());
            return responseEntity.getBody();
        } catch (Exception e) {
            log.error("The send request failed. url: {}, method: {}, headers: {}, requestParam: {}. Error info: ",
                    url, method, headers, sendData, e);
            throw new QuoteException(-1, "The send request failed. Error info: " + e.getMessage());
        }
    }

    /**
     * 加密发送数据
     *
     * @param sendConfig 发送配置
     * @param sendData   要发送的数据
     */
    private String encryptSendData(OrderDataSendConfig.SendConfig sendConfig, String sendData) {
        // 如果需要加密
        if (sendConfig.getCryptoInfo().getIsNeedEncryptSendData()) {
            try {
                String encryptData = cryptoFactory.encrypt(sendConfig.getCryptoInfo(), sendConfig.getChannelInfo().getUniqueCode(), sendData);
                log.info("The send data is encrypted. sendData: {}, encryptData: {}", sendData, encryptData);
                return encryptData;
            } catch (Exception e) {
                log.error("Encryption send data error occurred. ", e);
                throw new QuoteException(-1, "Encryption error occurred. Error info: " + e.getMessage());
            }
        }
        return null;
    }

    /**
     * 转换请求头
     *
     * @param httpHeaderMap 请求头
     */
    private HttpHeaders convertHttpHeader(Map<String, String> httpHeaderMap) {
        HttpHeaders headers = new HttpHeaders();
        for (Map.Entry<String, String> httpHeader : httpHeaderMap.entrySet()) {
            String key = httpHeader.getKey();
            String value = httpHeader.getValue();
            headers.add(key, value);
        }
        return headers;
    }

}
