package com.insgeek.business.quote.external.datasend.order.sender.authentication.handler;

import com.insgeek.business.quote.common.exception.QuoteException;
import com.insgeek.business.quote.external.datasend.order.pojo.dto.UrlHolder;
import com.insgeek.business.quote.external.datasend.order.sender.authentication.handler.strategy.AuthResponseHandler;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 认证信息响应处理工厂
 */
@Slf4j
@Component
@AllArgsConstructor
public class AuthResponseHandlerFactory {

    private final ConcurrentHashMap<String, AuthResponseHandler> authResponseHandlerMap = new ConcurrentHashMap<>();

    private final List<AuthResponseHandler> authResponseHandlerList;

    @PostConstruct
    public void init() {
        for (AuthResponseHandler authResponseHandler : authResponseHandlerList) {
            authResponseHandlerMap.put(authResponseHandler.getUniqueCode(), authResponseHandler);
        }
    }

    /**
     * 处理响应的认证信息
     *
     * @param authInfoMap 认证信息
     * @param urlHolder   请求地址
     * @param headers     请求头
     * @param uniqueCode  渠道唯一标识
     */
    public void handleAuthResponse(Map<String, Object> authInfoMap,
                                   UrlHolder urlHolder,
                                   HttpHeaders headers,
                                   String uniqueCode) {
        try {
            AuthResponseHandler authResponseHandler = authResponseHandlerMap.get(uniqueCode);
            if (authResponseHandler == null) {
                return;
            }
            authResponseHandler.handleAuthResponse(authInfoMap, urlHolder, headers);
            log.info("The handle auth response success. Channel flag: {}", uniqueCode);
        } catch (Exception e) {
            log.error("The handle auth response failed. Channel flag: {}. Error info: ", uniqueCode, e);
            throw new QuoteException(-1, "The handle auth response failed. Channel flag: " + uniqueCode + ". Error info: " + e.getMessage());
        }
    }

}
