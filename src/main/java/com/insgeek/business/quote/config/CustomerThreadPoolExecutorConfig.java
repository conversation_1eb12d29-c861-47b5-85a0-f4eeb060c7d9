package com.insgeek.business.quote.config;

import com.insgeek.boot.web.util.ThreadMdcUtil;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

@Configuration
public class CustomerThreadPoolExecutorConfig {
    public CustomerThreadPoolExecutorConfig() {
    }

    @Bean({"customerExecutorPool"})
    public Executor getCustomerExecutorPool() {
        ThreadPoolTaskExecutor executor = new ThreadMdcUtil.ThreadPoolTaskExecutorMdcWrapper();
        executor.setCorePoolSize(50);
        executor.setKeepAliveSeconds(60);
        executor.setMaxPoolSize(200);
        executor.setQueueCapacity(1000);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new ContextDecorator());
        executor.initialize();
        return executor;
    }

    @Bean({"customerExecutorPool2"})
    public Executor getCustomerExecutorPool2() {
        ThreadPoolTaskExecutor executor = new ThreadMdcUtil.ThreadPoolTaskExecutorMdcWrapper();
        executor.setCorePoolSize(10);
        executor.setKeepAliveSeconds(60);
        executor.setMaxPoolSize(16);
        executor.setQueueCapacity(5000);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new ContextDecorator());
        executor.initialize();
        return executor;
    }


    @Bean({"supplierNetCost"})
    public Executor getSupplierNetCost() {
        ThreadPoolTaskExecutor executor = new ThreadMdcUtil.ThreadPoolTaskExecutorMdcWrapper();
        executor.setCorePoolSize(10);
        executor.setKeepAliveSeconds(60);
        executor.setMaxPoolSize(16);
        executor.setQueueCapacity(5000);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new ContextDecorator());
        executor.initialize();
        return executor;
    }

    @Bean({"sgpGideExecutor"})
    public Executor getSgpGideExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadMdcUtil.ThreadPoolTaskExecutorMdcWrapper();
        executor.setCorePoolSize(10);
        executor.setKeepAliveSeconds(60);
        executor.setMaxPoolSize(16);
        executor.setQueueCapacity(5000);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new ContextDecorator());
        executor.initialize();
        return executor;
    }

    @Bean({"asyncExecutor"})
    public Executor asyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadMdcUtil.ThreadPoolTaskExecutorMdcWrapper();
        executor.setCorePoolSize(10);
        executor.setKeepAliveSeconds(60);
        executor.setMaxPoolSize(16);
        executor.setQueueCapacity(5000);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new ContextDecorator());
        executor.initialize();
        return executor;
    }

    /**
     * @Description: 新加坡方案批次创建责任线程池
     * @Date: 2025/9/10

     **/
    @Bean({"batchCreateDutyPool"})
    public Executor getBatchCreateDutyPool() {
        ThreadPoolTaskExecutor executor = new ThreadMdcUtil.ThreadPoolTaskExecutorMdcWrapper();
        executor.setCorePoolSize(10);
        executor.setKeepAliveSeconds(60);
        executor.setMaxPoolSize(16);
        executor.setQueueCapacity(5000);
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        executor.setTaskDecorator(new ContextDecorator());
        executor.initialize();
        return executor;
    }

}

