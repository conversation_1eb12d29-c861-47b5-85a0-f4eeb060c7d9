/*::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::
 : Copyright (c) 2022  All Rights Reserved.
 : ProjectName: insgeek-business-insurance
 : FileName: ContextDecorator.java
 : Author: jan<PERSON><PERSON><PERSON><PERSON>@gmail.com
 : Website: www.janloong.com
 : Date: 2022/12/12 上午12:46
 : LastModify: 2022/12/12 上午12:46
 :::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::::*/

package com.insgeek.business.quote.config;


import com.insgeek.boot.web.auth.dto.IdentityDto;
import com.insgeek.boot.web.auth.dto.TraceContextDto;
import com.insgeek.boot.web.context.TraceContext;
import com.insgeek.insurance.meta.utils.context.IdentityUtil;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.beans.BeanUtils;
import org.springframework.core.task.TaskDecorator;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import java.util.Map;

/**
 * <AUTHOR> href ="https://blog.janloong.com">Janloong Doo</a>
 * @version 1.0.0
 * @since 2022-12-09 16:23
 */
@Slf4j
public class ContextDecorator implements TaskDecorator {
    @Override
    public Runnable decorate(Runnable runnable) {
        try {
            RequestAttributes requestAttributes = RequestContextHolder.currentRequestAttributes();
            Map<String, String> copyOfContextMap = MDC.getCopyOfContextMap();
            IdentityDto userInfo = IdentityUtil.getUserInfo();
            TraceContextDto trace = TraceContext.getTrace();
            return () -> {
                try {
                    RequestContextHolder.setRequestAttributes(requestAttributes); // 1
                    MDC.setContextMap(copyOfContextMap);
                    IdentityDto identityDto = new IdentityDto();
                    BeanUtils.copyProperties(userInfo, identityDto);
                    IdentityUtil.setAuth(identityDto);
                    TraceContext.setTrace(trace);
                    log.info("链路ID:{}", trace.getTraceId());
                    runnable.run();
                } finally {
                    RequestContextHolder.resetRequestAttributes();
                    MDC.clear();

                }
            };
        } catch (Exception e) {
            return runnable;
        }
    }

}
