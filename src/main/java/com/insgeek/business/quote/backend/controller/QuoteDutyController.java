package com.insgeek.business.quote.backend.controller;

import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.backend.aggservice.QuoteDutyService;
import com.insgeek.business.quote.common.controller.BaseQuoteController;
import com.insgeek.business.quote.common.service.impl.QuoteConfigServiceImpl;
import com.insgeek.business.quote.enums.EntityKeyEnum;
import com.insgeek.business.quote.quotation.dto.ConfigAndDutyDto;
import com.insgeek.business.quote.quotation.dto.QuoteConfigDto;
import com.insgeek.business.quote.quotation.service.CommonServise;
import com.insgeek.insurance.meta.utils.context.IdentityUtil;
import com.insgeek.protocol.data.client.dto.QuoteDutyDTO;
import com.insgeek.protocol.data.client.dto.QuoteRenewalDutyCompareDTO;
import com.insgeek.protocol.data.client.dto.SaveDutyDto;
import com.insgeek.protocol.insurance.client.DutyClient;
import com.insgeek.protocol.insurance.dto.duty.request.ProductRuleDutyDTO;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontProductDutyInstanceDTO;
import com.insgeek.protocol.insurance.dto.special.instance.ListParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/v1/core")
public class QuoteDutyController extends BaseQuoteController {
    @Autowired
    private QuoteDutyService quoteDutyService;

    @Autowired
    private QuoteConfigServiceImpl quoteConfigService;

    @Autowired
    private CommonServise commonServise;

    @Autowired
    private DutyClient dutyClient;


    /**
     * 保存责任
     *
     * @param dutys
     * @return
     */
    @PostMapping("/{quote_config_id}/saveDuty")
    public ResponseVO saveDuty(@PathVariable("quote_config_id") Long quoteConfigId, @RequestBody List<QuoteDutyDTO> dutys) {
        quoteDutyService.saveDutys(quoteConfigId, dutys, new SaveDutyDto(true));
        return ResponseVO.builder().build();
    }

    @GetMapping("/product/{type}/{property}")
    public ResponseVO getQuoteDuty(@PathVariable(name = "type") String type,
                                   @PathVariable(name = "property") Integer property) {
        return ResponseVO.builder().data(quoteDutyService.getQuoteDuty(type, property)).build();
    }


    @GetMapping("/duty/getDutysByDutyIds")
    public ResponseVO getDutysByDutyIds(@RequestParam(name = "duty_ids") List<Long> dutyIds) {
        return ResponseVO.builder().data(quoteDutyService.getDutysByDutyIds(dutyIds)).build();
    }

    @PostMapping("/duty/list")
    public ResponseVO readDutyListByConfigIds(@RequestBody QuoteRenewalDutyCompareDTO quoteRenewalDutyCompareDTO) {
        return quoteDutyService.readDutyListByConfigIds(quoteRenewalDutyCompareDTO.getConfigIds(), quoteRenewalDutyCompareDTO.getRenewalCompareFlag());
    }

    /**
     * 废弃
     * @param quoteRenewalDutyCompareDTO
     * @return
     */
    @Deprecated
    @PostMapping("/duty/adapter/list")
    public ResponseVO readDutyListByConfigIdsNew(@RequestBody QuoteRenewalDutyCompareDTO quoteRenewalDutyCompareDTO) {
        return quoteDutyService.readDutyListByConfigIdsNew(quoteRenewalDutyCompareDTO.getConfigIds(), quoteRenewalDutyCompareDTO.getRenewalCompareFlag());
    }


    /**
     * 责任对比或详情接口，支持询/报价责任查询对比
     * @param listParam
     * @param compare
     * @return
     */
    @PostMapping("/duty/adapter/list_2")
    public ResponseVO readDutyVersion(@RequestBody ListParamDTO listParam, @RequestParam(value = "compare", defaultValue = "0") Integer compare) {
        IdentityUtil.setRobotAuth();// 适配外部版查询责任
        ResponseVO<List<FrontProductDutyInstanceDTO>> resp = dutyClient.batchGetDutyListByConfigForFront(listParam.getScene(), compare, listParam);
        return quoteDutyService.removeDutyFormula(resp, listParam);
    }

    @PostMapping("/duty/save/{id}")
    public ResponseVO<Object> saveDutyList(@PathVariable("id") Long configId,
                                           @Validated @RequestBody List<ProductRuleDutyDTO> productRuleDutyDTOs) {
        Integer xAppKey = getXAppKey();
        return quoteDutyService.saveDutyList(configId, productRuleDutyDTOs, xAppKey);
    }

    @PostMapping("/duty/adapter/save/{id}")
    public ResponseVO<Object> saveDutyListAdapter(@PathVariable("id") Long configId,
                                                  @Validated @RequestBody List<FrontProductDutyInstanceDTO> frontProductDutyInstanceDTOList) {
        Integer xAppKey = getXAppKey();
        return quoteDutyService.saveDutyListAdapter(configId, frontProductDutyInstanceDTOList, xAppKey);
    }

    @PostMapping("/duty/adapter/save/and/config")
    public ResponseVO<Object> saveDutyList2(@Validated @RequestBody ConfigAndDutyDto configAndDutyDto) {
        Integer xAppKey = getXAppKey();
        Long configId = quoteConfigService.getQuoteConfigId(configAndDutyDto.getQuoteId(), configAndDutyDto.getName());
        commonServise.setConfigData(configId, configAndDutyDto.getDutyInstanceList());
        ResponseVO<Object> objectResponseVO = quoteDutyService.saveDutyListAdapter(configId, configAndDutyDto.getDutyInstanceList(), xAppKey);
        if (!Objects.equals(objectResponseVO.getCode(), 0)) {
            quoteConfigService.deleteByIds(Arrays.asList(configId), EntityKeyEnum.QP_QUOTE_CONFIG.getVal());
        }
        return objectResponseVO;
    }


    /**
     * 保存责任信息
     * 根据选项 补充责任信息
     */
    @PostMapping("/duty/auto/save")
    public ResponseVO<Object> autoSaveDuty(@RequestBody QuoteConfigDto quoteConfigDto) {
        return quoteDutyService.autoSaveDuty(quoteConfigDto.getQuoteId(),quoteConfigDto.getPlanConfigIds());
    }

}
