package com.insgeek.business.quote.common.feign.external.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.insgeek.business.quote.common.dao.condition.DefaultBaseCondition;
import com.insgeek.business.quote.common.dao.condition.interfaces.BaseCondition;
import com.insgeek.business.quote.common.dao.constant.IgProductConst;
import com.insgeek.business.quote.common.feign.external.mapper.IgProductDtoMapper;
import com.insgeek.business.quote.common.feign.external.service.IgProductDtoService;
import com.insgeek.components.orm.model.querydsl.BQLQueryFactory;
import com.insgeek.protocol.data.client.dto.IgProductDto;
import com.insgeek.protocol.insurance.entity.model.QIgProduct;
import com.querydsl.core.types.dsl.Expressions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 * @ClassName IgProductDtoServiceImpl.java
 * @Description IgProductDtoServiceImpl
 * @createTime 2021年08月18日 15:04:00
 */
@Service
public class IgProductDtoServiceImpl implements IgProductDtoService {

    @Resource
    IgProductDtoMapper igProductDtoMapper;
    @Autowired
    private BQLQueryFactory bqlQueryFactory;

    @Override
    public List<IgProductDto> selectLikeProductName(String name) {
        BaseCondition<IgProductDto> igProductDtoCondition = new DefaultBaseCondition<>();
        igProductDtoCondition.like(IgProductConst.NAME, name);
        return igProductDtoMapper.selectListByCondition(igProductDtoCondition, false);
    }

    @Override
    public IgProductDto selectById(Long productId) {
        IgProductDto igProductDto = new IgProductDto();
        igProductDto.setId(productId);
        return igProductDtoMapper.selectOneByEntity(igProductDto, false);
    }

    @Override
    public List<IgProductDto> selectByIgProductPkgId(Long igProductPkgId) {
        IgProductDto igProductDto = new IgProductDto();
        igProductDto.setProductPkgId(igProductPkgId);
        return igProductDtoMapper.selectListByEntity(igProductDto, false);
    }

    /**
     * @Description: 根据产品名称查询产品id信息
     * 只查询状态为启用的产品
     * @Date: 2025/8/26
     * @Param null:
     **/
    @Override
    public List<IgProductDto> selectByProductName(List<String> productNameList) {
        if (CollectionUtil.isEmpty(productNameList)){
            return Collections.emptyList();
        }
        QIgProduct qIgProduct = QIgProduct.ig_product;
        return bqlQueryFactory.select(Expressions.stringPath("ig_product.*"))
                .from(qIgProduct)
                .where(qIgProduct.name.in(productNameList),qIgProduct.status.eq("1"))
                .findList(IgProductDto.class);
    }
}
