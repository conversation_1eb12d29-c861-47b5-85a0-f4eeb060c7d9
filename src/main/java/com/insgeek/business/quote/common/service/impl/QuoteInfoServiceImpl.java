package com.insgeek.business.quote.common.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.boot.web.auth.dto.IdentityDto;
import com.insgeek.boot.web.context.IdentityContext;
import com.insgeek.boot.web.vo.Pagination;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.backend.Enums.QuoteTypeEnum;
import com.insgeek.business.quote.backend.exception.QuoteErrorCode;
import com.insgeek.business.quote.common.dao.condition.DefaultBaseCondition;
import com.insgeek.business.quote.common.dao.condition.interfaces.BaseCondition;
import com.insgeek.business.quote.common.dao.condition.interfaces.OrderByCondition;
import com.insgeek.business.quote.common.dao.constant.QpFileDetailsConst;
import com.insgeek.business.quote.common.dao.constant.QpQuoteInfoConst;
import com.insgeek.business.quote.common.dao.mapper.QuoteInfoMapper;
import com.insgeek.business.quote.common.enums.CustomBusinessTypeEnum;
import com.insgeek.business.quote.common.enums.QuoteEnum;
import com.insgeek.business.quote.common.enums.dict.QuoteStatusEnum;
import com.insgeek.business.quote.common.feign.api.PlatformUserFeignService;
import com.insgeek.business.quote.common.service.*;
import com.insgeek.business.quote.common.utils.CommonDataUtil;
import com.insgeek.business.quote.dto.MetaDescriptionParam;
import com.insgeek.business.quote.dto.MetaDescriptionResult;
import com.insgeek.business.quote.enums.EntityKeyEnum;
import com.insgeek.business.quote.external.data.MetaServiceImpl;
import com.insgeek.business.quote.frontend.dto.manage.DrugListAttachmentDto;
import com.insgeek.business.quote.frontend.dto.manage.HealthAndMedicalServiceDetailDto;
import com.insgeek.business.quote.frontend.dto.manage.SalesStaffCoefficientProperties;
import com.insgeek.business.quote.frontend.enums.SpecialCityType;
import com.insgeek.business.quote.frontend.enums.SpecialEmployerCityType;
import com.insgeek.business.quote.quotation.dto.version.VersionContext;
import com.insgeek.business.quote.quotation.service.impl.AbstractQuotationService;
import com.insgeek.business.quote.util.CommonUtil;
import com.insgeek.components.orm.model.core.vo.Page;
import com.insgeek.components.orm.model.impl.bql.BqlCondition;
import com.insgeek.components.orm.model.impl.bql.BqlMapper;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.insurance.meta.utils.context.IdentityUtil;
import com.insgeek.protocol.business.group.client.OrganizationClient;
import com.insgeek.protocol.data.client.entity.*;
import com.insgeek.protocol.platform.common.dto.entity.IgGroupData;
import com.insgeek.protocol.platform.metadata.dto.ItemDto;
import com.insgeek.protocol.platform.user.dto.UserDto;
import jodd.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 对应 询报价{@link com.insgeek.business.quote.quotation.service.impl.QuotationInfoServiceImpl}
 * 复制部分 需要关注 {@link com.insgeek.business.quote.convert.aggservice.impl.outer.VersionHandlerConvertServiceImpl}
 * 升版本部分 需要关注 {@link com.insgeek.business.quote.convert.aggservice.impl.InfoConvertServiceImpl}
 *
 * <AUTHOR>
 * @title: QuoteInfoServiceImpl
 * @projectName insgeek-business-quote
 * @description: 报价操作实现类
 * @date 2023/1/3116:00
 */
@Slf4j
@Service
public class QuoteInfoServiceImpl extends AbstractQuotationService implements QuoteInfoService {

    @Autowired
    private DataMapper<QpQuoteInfoGroup> qpQuoteInfoGroupDataMapper;

    @Autowired
    private DataMapper<QpQuoteInfo> quoteInfoDataMapper;

    @Resource
    private QuoteInfoMapper quoteInfoMapper;

    @Resource
    private QpQuoteService quoteService;

    @Resource
    private MetaServiceImpl metaService;

    @Autowired
    private QuoteFileService quoteFileService;

    @Resource
    private DrugListAttachmentDto drugListAttachmentDto;

    @Resource
    private HealthAndMedicalServiceDetailDto healthAndMedicalServiceDetailDto;

    @Resource
    private SalesStaffCoefficientProperties salesStaffCoefficientProperties;

    @Resource
    private DataMapper<QpFileDetails> qpFileDetailsMapper;

    @Autowired
    private QpQuoteApproveService qpQuoteApproveService;

    @Autowired
    private DataMapper<QpQuoteInfoGroup> quoteInfoGroupMapper;

    @Autowired
    private OrganizationClient organizationClient;

    @Resource
    private QpCustomerService customerService;

    @Autowired
    private BqlMapper<QpQuoteInfo> qpQuoteInfoBqlMapper;

    @Resource
    private PlatformUserFeignService platformUserFeignService;

    @Autowired
    private DataMapper<QpCustomer> customerDataMapper;

    @Override
    public List<Long> removeByIds(List<Long> ids) {
        List<QpQuoteInfo> quoteInfos = quoteInfoDataMapper.entity(QpQuoteInfo.class).select(new ArrayList<>(ids), true);
        checkDeleteQuoteInfo(quoteInfos);
        quoteService.deleteRelationByType(quoteInfos.stream().map(QpQuoteInfo::getId).collect(Collectors.toList()), QuoteEnum.QUOTE_INFO.getCode());
        return quoteInfos.stream().map(QpQuoteInfo::getId).collect(Collectors.toList());
    }

    @Override
    public List<QpQuoteInfo> selectByIds(List<Long> ids) {
        return quoteInfoDataMapper.entity(QpQuoteInfo.class).select(new ArrayList<>(ids), true);
    }

    private void checkDeleteQuoteInfo(List<QpQuoteInfo> quoteInfos) {
        if (CollectionUtils.isEmpty(quoteInfos)) {
            CommonDataUtil.businessException(QuoteErrorCode.OPERATION_EMPTY);
        }
        for (QpQuoteInfo quoteInfo : quoteInfos) {
            if (!QuoteStatusEnum.INIT_STATUS.getValue().equals(quoteInfo.getStatus())) {
                CommonDataUtil.businessException(QuoteErrorCode.NOT_REMOVE);
            }
        }
    }

    //////////////////报价重构/////////////////////

    /**
     * @param param 结构化参数
     * @return 数据结构化对象 数据
     */
    @Override
    public List<MetaDescriptionResult> query(MetaDescriptionParam param) {
        check(param);
        QpQuoteInfo data = CommonUtil.convertToObject(QpQuoteInfo.class, param, false);
        List<QpQuoteInfo> dataList = quoteInfoDataMapper.entity(QpQuoteInfo.class).select(data, true);
        List<MetaDescriptionResult> results = CommonUtil.convertToListObject(Lists.newArrayList(dataList), defaultMap());
        quoteFileService.joinFiles(dataList.stream().map(QpQuoteInfo::getId).collect(Collectors.toList()), results, Lists.newArrayList(QpFileDetailsConst.BusType.QP_QUOTE_INFO.getCode()), null);
        return results;
    }

    @Override
    public MetaDescriptionResult queryContrastsByIds(List<Long> ids) {
        List<QpQuoteInfo> configs = quoteInfoDataMapper.entity(QpQuoteInfo.class).select(Lists.newArrayList(ids), true);
        if (CollectionUtil.isEmpty(configs)) {
            return MetaDescriptionResult.getInstance();
        }
        MetaDescriptionResult result = CommonUtil.multiVersionCompare(ids, configs, defaultMap());
        quoteFileService.joinValsFiles(configs.stream().map(QpQuoteInfo::getId).collect(Collectors.toList()), result, Lists.newArrayList(QpFileDetailsConst.BusType.QP_QUOTE_INFO.getCode()), null);
        return result;
    }

    /**
     * @param param 结构化参数
     * @return 数据结构化对象 数据 对比版
     */
    @Override
    public List<MetaDescriptionResult> queryContrast(MetaDescriptionParam param) {
        check(param);
        QpQuoteInfo quoteInfo = CommonUtil.convertToObject(QpQuoteInfo.class, param, false);
        assert quoteInfo != null;
        List<QpQuoteInfo> dataList = quoteInfoDataMapper.entity(QpQuoteInfo.class).select(quoteInfo, true);

        if (ObjectUtils.isEmpty(dataList)) {
            return new ArrayList<>();
        }

        List<MetaDescriptionResult> results = Lists.newArrayList();
        for (QpQuoteInfo data : dataList) {
            // 历史报价数量
            int count = calQuoteCount(data);
            data.setQuoteCount(count);
            long contractConditionCount = quoteContractCondition(data.getCustomerId(), 1, 1).getPage().getTotal();
            data.setContractConditionCount(Integer.parseInt(String.valueOf(contractConditionCount)));

            // 进行比较
            MetaDescriptionResult result = compare(param, data);

            //  添加报价 groupId
            QpQuoteInfoGroup group = quoteInfoGroupMapper.entity(QpQuoteInfoGroup.class).selectOne(data.getGroupId(), true);
            if (group != null && result != null) {
                result.addData("other_group_id", group.getQuotationInfoGroupId());
            }

            // 更新companyName
            if (data.getQuoteType().equals(QuoteTypeEnum.SINGAPORE.getCode()) && result != null) {
                QpCustomer qpCustomer = customerDataMapper.entity(QpCustomer.class).selectOne(data.getCustomerId(), true);
                result.addData("company_name", qpCustomer.getEnterpriseName());
            }

            results.add(result);
        }
        quoteFileService.joinFiles(dataList.stream().map(QpQuoteInfo::getId).collect(Collectors.toList()), results, Lists.newArrayList(QpFileDetailsConst.BusType.QP_QUOTE_INFO.getCode()), null);
        return results;

    }

    private MetaDescriptionResult compare(MetaDescriptionParam param, QpQuoteInfo data) {
        MetaDescriptionResult result = null;
        if (param.getCompare().equals(0)) {
            // 不比较
            result = CommonUtil.convertToObject(data, defaultMap());
        } else if (param.getCompare().equals(1)) {
            // 1 - 询价跟报价比/报价跟询价比
            VersionContext<QpQuoteInfo> previousVersion = previousVersionCompareService.findPreviousVersion(data);
            result = CommonUtil.convertToMdr(previousVersion.getCurrent(), previousVersion.getBefore(), defaultMap());
        } else if (param.getCompare().equals(2)) {
            // 2 - 询价自身的修改/报价自身的修改
            VersionContext<QpQuoteInfo> previousVersion = previousVersionCompareService.findPreviousVersion(data);
            result = CommonUtil.convertToMdr(previousVersion.getCurrent(), previousVersion.getAfter(), defaultMap());
        } else if (param.getCompare().equals(3)) {
            // 3 - 与核心续期对比
            VersionContext<QpQuoteInfo> previousVersion = previousVersionCompareService.findPreviousVersion(data);
            result = CommonUtil.convertToMdr(previousVersion.getCurrent(), previousVersion.getRenew(), defaultMap());
        }
        return result;
    }

    /**
     * 保存数据
     *
     * @param param 入参
     * @return id
     */
    @SuppressWarnings("Duplicates")
    @Override
    public Long saveAllowNull(MetaDescriptionParam param) {
        check(param);

        // 新加坡询报价要新增客户信息
        addCustomerInfo(param);


        //插入
        if (Objects.isNull(param.getId()) || param.getId() == 0L) {
            QpQuoteInfo quoteInfo = CommonUtil.convertToObject(QpQuoteInfo.class, param, false);
            assert quoteInfo != null;

            QpQuoteInfoGroup infoGroup = new QpQuoteInfoGroup();
            infoGroup.setBusinessType(matchBusinessType(quoteInfo));
            QpQuoteInfoGroup qpQuoteInfoGroup = qpQuoteInfoGroupDataMapper.entity(QpQuoteInfoGroup.class).insertOne(infoGroup);
            // 插入group的时候插入 Approve
            qpQuoteApproveService.insertNewStatusByQuote(qpQuoteInfoGroup.getId());

            Long id = infoGroup.getId();

            // 状态处理
            if (quoteInfo.getQuoteType().equals(QuoteTypeEnum.SINGAPORE.getCode())) {
                // 新加坡草稿状态
                quoteInfo.setStatus(QuoteStatusEnum.DRAFT.getValue());
            } else {
                // 待提交
                quoteInfo.setStatus(QuoteStatusEnum.INIT_STATUS.getValue());
            }
            quoteInfo.setGroupId(id);
            quoteInfo.setDs(QpQuoteInfoConst.Ds.DS_XJ.getCode());
            quoteInfo.setPublishVersionFlag(QpQuoteInfoConst.PublishVersionFlag.UNPUBLISHED.getCode());
            quoteInfo.setSalesUid(IdentityContext.getUserId());

            // 没有地区默认北京
            if (quoteInfo.getQuoteType().equals(QuoteTypeEnum.EMPLOYER.getCode())) {
                String cityCode = param.getStringValByKey(QpQuoteInfoConst.SPECIAL_CITY_CODE);
                // 省市转省
                if (StringUtil.isNotBlank(cityCode) && cityCode.contains("-")) {
                    cityCode = cityCode.split("-")[0];
                }
                boolean notExist = null == SpecialEmployerCityType.getDescByCode(cityCode);
                if (StringUtils.isEmpty(cityCode) || notExist) {
                    quoteInfo.setSpecialCityCode(SpecialEmployerCityType.BJ.getCode());
                } else {
                    quoteInfo.setSpecialCityCode(cityCode);// 截取省
                }
            } else {
                String cityCode = param.getStringValByKey(QpQuoteInfoConst.SPECIAL_CITY_CODE);
                boolean notExist = null == SpecialCityType.getDescByCode(cityCode);
                if (StringUtils.isEmpty(cityCode) || notExist) {
                    quoteInfo.setSpecialCityCode(SpecialCityType.BJ.getCode());
                } else {
                    quoteInfo.setSpecialCityCode(cityCode);
                }
            }

            // （医疗增值服务｜特殊产品报价）从核心系统获取企业的企业分类，企业稳定贡献值，企业即时贡献值
            if (quoteInfo.getQuoteType().equals(QuoteTypeEnum.MEDICAL_ADD_SERVICES.getCode())
                    || quoteInfo.getQuoteType().equals(QuoteTypeEnum.SPECIAL_PRODUCT.getCode())) {
                fixCompanyInfo(quoteInfo);
            }

            quoteInfo = quoteInfoDataMapper.entity(QpQuoteInfo.class).insertOne(quoteInfo);

            // 如果 前端该字段没有传值 代表需要自动复制 auto = true
            boolean setSalesStaffCoefficientAuto = quoteInfo.getSalesStaffCoefficient() == null;
            // 插入了 quoteInfo 后 的一些后置操作
            postInsertInfo(quoteInfo.getId(), setSalesStaffCoefficientAuto);

            infoGroup.setLatestQuoteInfoId(quoteInfo.getId());
            qpQuoteInfoGroupDataMapper.entity(QpQuoteInfoGroup.class).updateOne(infoGroup);

            param.setId(quoteInfo.getId());
            //保存文件
            saveFiles(param);
            // 非保险产品的文件
            if (!quoteInfo.getQuoteType().equals(QuoteTypeEnum.SINGAPORE.getCode())) {
                addFile(quoteInfo.getId());
            }

        } else {
            QpQuoteInfo quoteInfo = quoteInfoDataMapper.entity(QpQuoteInfo.class).selectOne(param.getId(), true);
            // 非待提交状态不允许修改 业务类型（员工非员工）
            if (!quoteInfo.getStatus().equals(QuoteStatusEnum.INIT_STATUS.getValue())
                    && !StringUtils.isEmpty(quoteInfo.getCustomBusinessType())
                    && !StringUtils.isEmpty(param.getStringValByKey(QpQuoteInfoConst.CUSTOM_BUSINESS_TYPE))
                    && !quoteInfo.getCustomBusinessType().equals(param.getIntegerValByKey(QpQuoteInfoConst.CUSTOM_BUSINESS_TYPE))
            ) {
                CommonDataUtil.businessException(QuoteErrorCode.QUOTE_CUSTOM_BUSINESS_TYPE_CAN_NOT_EDIT);
            }

            // 更新group_businessType(未提交报价之前允许修改流程类型)
            if (quoteInfo.getStatus().equals(QuoteStatusEnum.INIT_STATUS.getValue())) {
                QpQuoteInfoGroup infoGroup = new QpQuoteInfoGroup();
                quoteInfo.setCustomBusinessType(param.getIntegerValByKey(QpQuoteInfoConst.CUSTOM_BUSINESS_TYPE));
                infoGroup.setBusinessType(matchBusinessType(quoteInfo));
                infoGroup.setId(quoteInfo.getGroupId());
                qpQuoteInfoGroupDataMapper.entity(QpQuoteInfoGroup.class).updateOne(infoGroup, true);
            }

            Map<String, Object> stringObjectMap = CommonUtil.convertToMap(param, defaultMap());
            stringObjectMap.remove("status");// 更新时不更新状态字段
            quoteInfoDataMapper.entity(QpQuoteInfo.class).batchSuperUpdate(Lists.newArrayList(stringObjectMap), true);
            // 更新报价名称
            updateName(param.getId());
            //保存文件
            saveFiles(param);
        }
        return param.getId();
    }

    private void addCustomerInfo(MetaDescriptionParam param) {

        QpQuoteInfo quoteInfo = CommonUtil.convertToObject(QpQuoteInfo.class, param, false);
        if (quoteInfo == null) return;
        // 非新加坡询价不处理
        if (quoteInfo.getQuoteType() == null
                || StringUtils.isEmpty(quoteInfo.getCompanyName())
                || !quoteInfo.getQuoteType().equals(QuoteTypeEnum.SINGAPORE.getCode())
        ) {
            return;
        }
        String companyName = param.getStringValByKey("company_name");
        IdentityDto userInfo = IdentityUtil.getUserInfo();
        Long userId = userInfo.getUserId();
        // 判断是否需要新增客户
        DataCondition<QpCustomer> condition = new DataCondition<>();
        condition.eq("enterprise_name", companyName);
        condition.eq("created_by", userId);
        List<QpCustomer> qpCustomers = customerDataMapper.entity(QpCustomer.class).select(condition, true);

        if (CollectionUtils.isEmpty(qpCustomers)) {
            QpCustomer customer = new QpCustomer();
            customer.setEnterpriseName(quoteInfo.getCompanyName());
            customer.setNatureOfBusiness(quoteInfo.getNatureOfBusiness());
            customer.setUniqueEntityNumber(quoteInfo.getUniqueEntityNumber());
            customer.setNumberOfEmployees(quoteInfo.getNumberOfEmployees());
            customer.setOfficeLocations(quoteInfo.getOfficeLocations());
            customer.setContacts(quoteInfo.getContacts());
            customer.setDesignation(quoteInfo.getDesignation());
            customer.setMobilePhone(quoteInfo.getMobilePhone());
            customer.setMailbox(quoteInfo.getMailbox());

            customer = customerDataMapper.entity(QpCustomer.class).insertOne(customer);

            // 设置param的customerId
            param.addData("customer_id", customer.getId());
        } else {
            // 设置param的customerId
            param.addData("customer_id", qpCustomers.get(0).getId());
        }


    }

    private void fixCompanyInfo(QpQuoteInfo quoteInfo) {
        log.info("从核心系统获取企业的企业分类，企业稳定贡献值，企业即时贡献值入参:{}", quoteInfo.getCustomerId());

        // 从核心系统获取企业的企业分类，企业稳定贡献值，企业即时贡献值
        QpCustomer customer = customerService.selectById(quoteInfo.getCustomerId());
        if (!customer.getCustomerId().equals(NumberUtils.LONG_ZERO)) {
            ResponseVO<IgGroupData> igGroupDataResponseVO = organizationClient.groupInfo(customer.getCustomerId());
            log.info("从核心系统获取企业的企业分类，企业稳定贡献值，企业即时贡献值结果:{}", JacksonUtils.writeAsString(igGroupDataResponseVO));
            if (igGroupDataResponseVO.ok()) {
                IgGroupData igGroupData = JacksonUtils.readValue(JacksonUtils.writeAsString(igGroupDataResponseVO.getData()), new TypeReference<IgGroupData>() {
                });
                if (Objects.nonNull(igGroupData)) {
                    quoteInfo.setEnterpriseClassification(igGroupData.getGroupFlag());
                    quoteInfo.setEnterpriseStabilityContributionValue(igGroupData.getStableValue());
                    quoteInfo.setEnterpriseImmediateContributionValue(igGroupData.getInstantValue());
                    if (igGroupData.getStableValue() != null && igGroupData.getInstantValue() != null && igGroupData.getStableValue().compareTo(BigDecimal.ZERO) != 0) {
                        BigDecimal instantValue = igGroupData.getInstantValue();
                        BigDecimal bigDecimal = instantValue.subtract(igGroupData.getStableValue()).divide(igGroupData.getStableValue(), 4, RoundingMode.HALF_UP).multiply(new BigDecimal(100)).setScale(0, RoundingMode.UP);
                        quoteInfo.setCompanyStabilityContributionChangeRange(bigDecimal);
                    }
                }
            }
        }
    }

    /**
     * insert quoteInfo 后 的后置处理
     * 如 会更新 报价名称
     * 如 添加非保险产品的 文件
     */
    @Override
    public void postInsertInfo(Long quoteInfoId, boolean setSalesStaffCoefficientAuto) {
        // 更新报价名称
        QpQuoteInfo qi = quoteInfoDataMapper.entity(QpQuoteInfo.class).selectOne(quoteInfoId);
        // 销售人员系数
        // 高鹏飞和赵彩婷默认为1.2 其他为1
        if (setSalesStaffCoefficientAuto) {
            qi.setSalesStaffCoefficient(salesStaffCoefficientProperties.getSalesStaffCoefficientByUid(IdentityContext.getUserId()));
        }
        qi.setSalesUid(IdentityContext.getUserId());
        UserDto user = platformUserFeignService.findById(IdentityContext.getUserId());
        qi.setSalesName(user.getName());

        quoteInfoDataMapper.entity(QpQuoteInfo.class).updateOne(qi.setName(qi.getCompanyName() + "报价-" + qi.getCode()));
    }

    @Override
    public Long getLastVersionId(Long id) {
        QpQuoteInfo info = quoteInfoDataMapper.entity(QpQuoteInfo.class).selectOne(id, true);
        if (info == null) {
            return null;
        }
        QpQuoteInfoGroup group = qpQuoteInfoGroupDataMapper.entity(QpQuoteInfoGroup.class).selectOne(info.getGroupId(), true);
        if (group == null) {
            return null;
        }
        return group.getLatestQuoteInfoId();
    }

    private void updateName(Long quoteInfoId) {
        // 更新报价名称
        QpQuoteInfo qi = quoteInfoDataMapper.entity(QpQuoteInfo.class).selectOne(quoteInfoId, true);
        quoteInfoDataMapper.entity(QpQuoteInfo.class).updateOne(qi.setName(qi.getCompanyName() + "报价-" + qi.getCode()), true);
    }

    private void addFile(Long infoId) {
        //创建报价时关联当前企业小药箱附件oss_key和报价id
        QpFileDetails fileDetails = new QpFileDetails();
        fileDetails.setBusId(infoId);
        fileDetails.setBusType(QpFileDetailsConst.BusType.QP_QUOTE_INFO.getCode());
        fileDetails.setBusClassify(QpFileDetailsConst.BusClassify.QP_QUOTE_INFO_BC_1.getCode());
        fileDetails.setOssKey(drugListAttachmentDto.getDrugListAttachmentOSSKey());
        qpFileDetailsMapper.entity(QpFileDetails.class).insertOne(fileDetails);
        //创建报价时关联当前健康医疗服务具体内容一览表oss_key和报价id
        QpFileDetails healthAndMedicalServiceFileDetails = new QpFileDetails();
        healthAndMedicalServiceFileDetails.setBusId(infoId);
        healthAndMedicalServiceFileDetails.setBusType(QpFileDetailsConst.BusType.QP_QUOTE_INFO.getCode());
        healthAndMedicalServiceFileDetails.setBusClassify(QpFileDetailsConst.BusClassify.QP_QUOTE_INFO_BC_3.getCode());
        healthAndMedicalServiceFileDetails.setOssKey(healthAndMedicalServiceDetailDto.getHealthAndMedicalServiceDetailOSSKey());
        qpFileDetailsMapper.entity(QpFileDetails.class).insertOne(healthAndMedicalServiceFileDetails);
    }

    /**
     * internalNonEmployer 内部传统意外
     * internalEmployer    内部雇主
     * external    外部传统意外雇主已读
     *
     * @param data
     * @return
     */
    public String matchBusinessType(QpQuoteInfo data) {

        // 新加坡报价处理
        if (data.getQuoteType().equals(QuoteTypeEnum.SINGAPORE.getCode())) {
            return "defaultBusiType";
        }

        //tenantTag -> 1: 内部，2: 外部
        // 旧码表 quote_type
        //旧码表 1-传统   2-极客+    3-雇主
        //新码表 1-团险医疗方案 2-团险意外伤害 3-雇主方案
        if (data.getTenantTag().equals(2)) {
            // 外部
            return "external";
        } else if (data.getTenantTag().equals(1)) {
            // 这里存在 quote_type 隐患 , 老数据 quote_type = 2 代表极客＋流程, 新版询报价 quote_type = 2 代表 意外,列表页复制功能
            if (data.getQuoteType().equals(1) || data.getQuoteType().equals(2)) {
                // 内部传统意外
                return "internalNonEmployer";
            } else if (data.getQuoteType().equals(3)) {
                // 内部雇主
                return "internalEmployer";
            } else if (data.getQuoteType().equals(4)) {
                // 内部医疗
                return "internalMedical";
            } else if (data.getQuoteType().equals(6)
                    || data.getQuoteType().equals(7)
                    || data.getQuoteType().equals(8)
                    || data.getQuoteType().equals(9)
                    || data.getQuoteType().equals(10)) {
                // 内部特殊
                if (CustomBusinessTypeEnum.YG_FL_ZGD.getCode().equals(data.getCustomBusinessType())
                        || CustomBusinessTypeEnum.YG_FL_BC.getCode().equals(data.getCustomBusinessType())
                        || CustomBusinessTypeEnum.YG_TJ.getCode().equals(data.getCustomBusinessType())) {
                    //员工福利类，对应已有的传统报价流程
                    return "internalNonEmployer";
                } else {
                    //非员工福利类，对应已有的雇主报价流程
                    return "internalEmployer";
                }
            }
        }
        return null;
    }

    /**
     * 校验参数不能为空
     *
     * @param param 入参
     */
    private void check(MetaDescriptionParam param) {
        if (Objects.isNull(param)) {
            CommonDataUtil.businessException(QuoteErrorCode.DATA_IS_EMPTY);
        }
        assert param != null;
        if (param.getValByKey("comments") != null
                && String.valueOf(param.getValByKey("comments")).length() > 5000) {
            CommonDataUtil.businessException(QuoteErrorCode.COMMONS_TOO_LONG);
        }

        // 字段兼容处理
        if (param.getValByKey(QpQuoteInfoConst.INSURANCE_CONSUMER_ID) != null) {
            String insuranceConsumerId = param.getValByKey(QpQuoteInfoConst.INSURANCE_CONSUMER_ID).toString();
            if (StringUtils.isEmpty(insuranceConsumerId)) {
                param.addData(QpQuoteInfoConst.INSURANCE_CONSUMER_ID, null);
            }
        }
    }

    /**
     * @return 字段集合
     */
    private Map<String, ItemDto> defaultMap() {
        return metaService.getMapItemDtoByEntityKey(EntityKeyEnum.QP_QUOTE_INFO.getVal());
    }


    public int calQuoteCount(QpQuoteInfo data) {
        return getQuoteCountInfos(data).size();
    }

    public List<QpQuoteInfo> getQuoteCountInfos(QpQuoteInfo data) {
        Page<QpQuoteInfo> qpQuoteInfoPage = quoteCountInfos(data, 1, 10000);
        List<QpQuoteInfo> list = qpQuoteInfoPage.getData();
        return ObjectUtils.isEmpty(list) ? new ArrayList<>() : list;
    }


    /**
     * // 这个报价的含义可以是
     * 15）历史报价
     * 报价前台，历史报价点击查看可查看本销售+该企业名称发起的报价列表，所有状态报价，但不含本报价，传统、雇主、意外都能看到，超过20条可展示
     * 报价前台，历史报价点击查看，在查看列表点击任意一个报价的查看，可进入该报价详情页
     * 报价前台，历史报价可看的时间范围：报价创建日期且是去年的今天到今天
     * <p>
     * 报价后台，历史报价点击查看可查看企业下所有的报价/报价名称上区分雇主与补医，什么意思？待确认——报价后台可以查看该企业下的所有历史报价，展示的是报价列表，包括报价名称、报价类型、报价状态、提交时间，点击可查看报价详情
     * 报价后台，历史报价点击查看，在查看列表点击任意一个报价的查看，可进入该报价详情页
     * 报价后台，历史报价可看这个企业所有的报价，没有时间段限制，超过20条可展示
     */
    public Page<QpQuoteInfo> quoteCountInfos(QpQuoteInfo data, Integer page, Integer pageSize) {

        if (data == null) {
            return Page.<QpQuoteInfo>builder().build();
        }

        BaseCondition<QpQuoteInfo> dataCondition = new DefaultBaseCondition<>();
        QpQuoteInfo quoteInfo = new QpQuoteInfo();
        quoteInfo.setLastestVersion(true);
        quoteInfo.setCustomerId(data.getCustomerId());
        dataCondition.setEntity(quoteInfo);
        if (data.getId() != null) {
            dataCondition.notEq("id", String.valueOf(data.getId()));
        }
        dataCondition.orderBy("created_at", OrderByCondition.OrderByEnum.DESC);

        //单独设置分页参数,若不设置，按 1，1000 默认值进行查询
        dataCondition.pageQuery(page, pageSize);
        ResponseVO listResponseVO = quoteInfoMapper.selectPageListByCondition(dataCondition);
        if (listResponseVO.getData() == null) {
            return Page.<QpQuoteInfo>builder().build();
        }

        List<QpQuoteInfo> list = JacksonUtils.readValue(JacksonUtils.writeAsString(listResponseVO.getData()), new TypeReference<List<QpQuoteInfo>>() {
        });
        Pagination pagination = Pagination.builder().pageSize(pageSize).pageNum(page).total(listResponseVO.getPage().getTotal()).build();
        return Page.<QpQuoteInfo>builder().data(list).page(pagination).build();
    }

    /**
     * 查询签约条件开启的历史询价数量
     *
     * @param customerId
     * @param page
     * @param pageSize
     * @return
     */
    public Page<QpQuoteInfo> quoteContractCondition(Long customerId, Integer page, Integer pageSize) {
        String sql = "SELECT\n" +
                "\tqi.id as id,qi.name as name,qi.status as status,UNIX_TIMESTAMP(qi.created_at) * 1000 as created_at,qi.quote_type as quote_type,qi.renew_flag as renew_flag \n" +
                "FROM\n" +
                "\tqp_quote_info_group qig\n" +
                "\tLEFT JOIN qp_quote_info qi ON qi.id = qig.latest_quote_info_id \n" +
                "WHERE\n" +
                "\tqig.id IN (\n" +
                "\tSELECT\n" +
                "\t\tgroup_id\n" +
                "\tFROM\n" +
                "\t\tqp_quote_info \n" +
                "\tWHERE\n" +
                "\t\tcontract_condition = 1 and customer_id = %s\n" +
                "GROUP BY\n" +
                "\tgroup_id) AND qi.status NOT IN(0,1,9,8,16)";
        return qpQuoteInfoBqlMapper.entity(QpQuoteInfo.class).selectPage(String.format(sql, customerId), page, pageSize, true);
    }
}
