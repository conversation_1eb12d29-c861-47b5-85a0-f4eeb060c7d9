package com.insgeek.business.quote.sgp.strategy.person;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.insgeek.boot.commons.datetime.DateTimeUtil;
import com.insgeek.boot.commons.json.JacksonUtils;
import com.insgeek.business.quote.common.exception.QuoteException;
import com.insgeek.business.quote.sgp.dto.QuoteBenefitDto;
import com.insgeek.business.quote.sgp.dto.QuotePersonOverviewDto;
import com.insgeek.business.quote.sgp.enums.GenderEnum;
import com.insgeek.business.quote.sgp.enums.IdentityTypeEnum;
import com.insgeek.business.quote.sgp.enums.MemberTypeEnum;
import com.insgeek.business.quote.sgp.strategy.person.impl.GtlPersonOverviewStrategy;
import com.insgeek.business.quote.sgp.strategy.person.pojo.base.AgeProfileOfEmployeesBase;
import com.insgeek.business.quote.sgp.strategy.person.pojo.base.BasisOfCoverageBase;
import com.insgeek.business.quote.sgp.strategy.person.pojo.base.DetailsOfInsuredMembersBase;
import com.insgeek.business.quote.sgp.strategy.person.pojo.base.PersonOverviewBase;
import com.insgeek.protocol.data.client.entity.QpQuotePersons;
import com.insgeek.protocol.data.client.enums.BenefitEnum;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontInstanceList;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontProductDutyInstanceDTO;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontProductRuleItemDTO;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractQuotePersonOverviewStrategy implements QuotePersonOverviewStrategy {

    public static final String AS_CHARGED = "As Charged";

    public static final String AS_CHARGED_VALUE = "99999999";


    /**
     * 构建结果集
     * 将 PersonOverviewBase 的每个属性转换成 QpQuotePersonsOverview 对象
     */
    protected List<QuotePersonOverviewDto> buildQpQuotePersonsOverviewList(Long quoteInfoId,
                                                                           PersonOverviewBase personOverviewBase) {
        // 结果列表
        List<QuotePersonOverviewDto> resultList = new ArrayList<>();

        // 获取所有字段（包括 private 字段）
        Field[] fields = personOverviewBase.getClass().getDeclaredFields();

        for (Field field : fields) {
            // 设置可访问
            field.setAccessible(true);

            try {
                // 获取属性名
                String fieldName = field.getName();
                // 转换为蛇形命名
                String snakeCaseName = camelToSnake(fieldName);

                // 获取属性值
                Object value = field.get(personOverviewBase);

                // 构建结果对象
                QuotePersonOverviewDto overview = new QuotePersonOverviewDto();
                overview.setTag(getSupportBenefitType().toLowerCase(Locale.ROOT));
                overview.setQuoteInfoId(quoteInfoId);
                overview.setItemKey(snakeCaseName);

                // 判断属性值类型
                if (Objects.isNull(value)) {
                    overview.setValue("");
                } else if (isPrimitiveOrWrapper(value.getClass()) || value instanceof String) {
                    // 基本类型或字符串，直接转为字符串
                    overview.setValue(String.valueOf(value));
                } else {
                    // 对象或集合，转为 JSON 字符串
                    overview.setValue(JacksonUtils.writeAsString(value));
                }

                // 添加到结果集
                resultList.add(overview);
            } catch (Exception e) {
                log.error("Field conversion is exceptional", e);
                throw new QuoteException(-1, "Field conversion is exceptional");
            }
        }

        return resultList;
    }

    /**
     * 判断是否为基本类型或其包装类
     */
    private boolean isPrimitiveOrWrapper(Class<?> clazz) {
        return clazz.isPrimitive()
                || clazz == Boolean.class
                || clazz == Byte.class
                || clazz == Character.class
                || clazz == Short.class
                || clazz == Integer.class
                || clazz == Long.class
                || clazz == Float.class
                || clazz == Double.class;
    }

    /**
     * 驼峰转蛇形
     * 例: userName → user_name
     */
    private String camelToSnake(String str) {
        StringBuilder sb = new StringBuilder();
        for (char c : str.toCharArray()) {
            if (Character.isUpperCase(c)) {
                sb.append('_').append(Character.toLowerCase(c));
            } else {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 构建 Basis of Coverage
     */
    protected List<BasisOfCoverageBase> buildBasisOfCoverageBaseList(List<QuoteBenefitDto> benefitDtoList,
                                                                     List<QpQuotePersons> quotePersonsList,
                                                                     Map<Long, List<FrontProductDutyInstanceDTO>> frontProductListMap,
                                                                     String productTypeCode,
                                                                     Map<String, String> productMapping) {
        List<BasisOfCoverageBase> basisOfCoverageList = new ArrayList<>();
        List<String> employeeCategoryOptionList = QuoteBenefitDto.buildEmployeeCategoryOptionList(benefitDtoList);
        for (String category : employeeCategoryOptionList) {
            BasisOfCoverageBase basisOfCoverageBase = new BasisOfCoverageBase();
            basisOfCoverageBase.setCategoryOfEmployeesOccupation(category);

            List<QuoteBenefitDto> benefitInfoDtoList = benefitDtoList.stream()
                    .filter(benefitDto -> benefitDto.getQuoteCategory().equals(category))
                    .collect(Collectors.toList());

            // 取责任字段并拼接成字符串
            String basisOfCoverageSumInsured = buildBasisOfCoverageSumInsured(benefitInfoDtoList, frontProductListMap,
                    productTypeCode, productMapping);
            basisOfCoverageBase.setBasisOfCoverageSumInsured(basisOfCoverageSumInsured);

            // 计算人数
            List<QpQuotePersons> categoryPersonList = quotePersonsList.stream()
                    .filter(person -> person.getEmployeeCategory().equals(category))
                    .collect(Collectors.toList());
            basisOfCoverageBase.setNoOfEmployees(String.valueOf(categoryPersonList.size()));

            basisOfCoverageList.add(basisOfCoverageBase);
        }
        return basisOfCoverageList;
    }

    /**
     * 构建 Basis of Coverage 字符串
     */
    protected String buildBasisOfCoverageSumInsured(List<QuoteBenefitDto> benefitInfoDtoList,
                                                    Map<Long, List<FrontProductDutyInstanceDTO>> frontProductListMap,
                                                    String productTypeCode,
                                                    Map<String, String> productMapping) {
        // 获取quoteConfigId列表
        List<Long> quoteConfigList = benefitInfoDtoList.stream()
                .map(QuoteBenefitDto::getQuoteConfigId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        // 使用 Map 保存不同 productMap 对应的 itemValueList
        // key 为 productName，（要么没有这个key 如果有这个key 那么list里的每个元素一定非空）
        Map<String, List<String>> productItemValueMap = new HashMap<>();

        for (Long quoteConfigId : quoteConfigList) {
            if (frontProductListMap.containsKey(quoteConfigId)) {
                for (Map.Entry<String, String> productMap : productMapping.entrySet()) {
                    // 获取产品名称和字段名称
                    String productName = productMap.getKey();
                    String targetItemTitle = productMap.getValue();

                    // 获取当前 quoteConfigId 对应的产品责任实例列表
                    List<FrontProductDutyInstanceDTO> dutyInstanceDTOList = frontProductListMap.get(quoteConfigId);

                    // 获取规则项 DTO
                    FrontProductRuleItemDTO frontProductRuleItemDTO = getFrontProductRuleItemDTO(
                            dutyInstanceDTOList,
                            productTypeCode,
                            productName,
                            targetItemTitle
                    );

                    // 转换 itemValue 为字符串
                    String itemValue = convertItemValueToString(frontProductRuleItemDTO.getItemValue());
                    // 如果是空的，则跳过
                    if (StrUtil.isBlank(itemValue)) {
                        continue;
                    }

                    // Gtl险种定制规则
                    if (BenefitEnum.GTL.getCode().equals(getSupportBenefitType())
                            && GtlPersonOverviewStrategy.PRODUCT_NAME_2.equals(productName)
                            && GtlPersonOverviewStrategy.TARGET_ITEM_TITLE_2.equals(targetItemTitle)) {
                        // 将转换后的值放入 productName 对应的 itemValueList
                        List<String> itemValueList = productItemValueMap.computeIfAbsent(productName, k -> new ArrayList<>());
                        itemValueList.add(itemValue + "x BMS");
                        continue;
                    }

                    // 执行特殊逻辑转换
                    String afterConvertValue = handelSpecialItemValueConvert(itemValue);
                    // 如果是空的，则跳过
                    if (StrUtil.isBlank(afterConvertValue)) {
                        continue;
                    }

                    // 将转换后的值放入 productName 对应的 itemValueList
                    List<String> itemValueList = productItemValueMap.computeIfAbsent(productName, k -> new ArrayList<>());
                    itemValueList.add(afterConvertValue);
                }
            }
        }
        // Gtl险种定制规则
        if (BenefitEnum.GTL.getCode().equals(getSupportBenefitType())
                && productItemValueMap.containsKey(GtlPersonOverviewStrategy.PRODUCT_NAME_1)
                && productItemValueMap.containsKey(GtlPersonOverviewStrategy.PRODUCT_NAME_2)) {
            // 如果两个产品都选了，移除其中一个
            productItemValueMap.remove(GtlPersonOverviewStrategy.PRODUCT_NAME_1);
        }
        // 合并所有value
        List<String> valueList = productItemValueMap
                .values()
                .stream()
                .flatMap(List::stream)
                .collect(Collectors.toList());
        return valueList.stream().filter(StrUtil::isNotBlank).collect(Collectors.joining(";"));
    }

    /**
     * 处理特殊情况下的转换
     */
    private String handelSpecialItemValueConvert(String itemValue) {
        log.info("handelSpecialItemValueConvert itemValue:{}", itemValue);
        if (StrUtil.isBlank(itemValue) || !NumberUtil.isNumber(itemValue)) {
            return "";
        }
        // 将字符串转换为 BigDecimal
        BigDecimal numericValue = new BigDecimal(itemValue);

        // 定义比较基准值
        BigDecimal threshold = new BigDecimal(AS_CHARGED_VALUE);

        // 使用 compareTo 比较
        // 返回值：-1 表示小于，0 表示等于，1 表示大于
        if (numericValue.compareTo(threshold) == 0) {
            // 如果大于阈值，替换值
            itemValue = AS_CHARGED;
        }
        return itemValue;
    }

    /**
     * 获取字符串
     */
    protected String convertItemValueToString(Object itemValue) {
        log.info("convertItemValueToString itemValue:{}", itemValue);

        if (Objects.isNull(itemValue)) {
            return "";
        }

        // 处理货币类型
        if (itemValue instanceof LinkedHashMap) {
            LinkedHashMap<String, Object> currencyAmount = (LinkedHashMap<String, Object>) itemValue;

            // 先判断 Map 是否为空
            if (MapUtil.isEmpty(currencyAmount)) {
                return "";
            }

            // 获取 amount 值
            Object amountObj = currencyAmount.get("amount");

            // 判断 amount 是否为空
            if (Objects.isNull(amountObj) || StrUtil.isBlank(amountObj.toString())) {
                return "";
            }

            return amountObj.toString();
        }

        // 其他类型直接转字符串
        return String.valueOf(itemValue);
    }

    /**
     * 获取身故伤残的保额
     *
     * @param productTypeCode     责任类型编码
     * @param dutyInstanceDTOList 责任列表
     * @return
     */
    public FrontProductRuleItemDTO getFrontProductRuleItemDTO(List<FrontProductDutyInstanceDTO> dutyInstanceDTOList,
                                                              String productTypeCode,
                                                              String productName,
                                                              String targetItemTitle) {
        // 使用 AtomicReference 保存查找到的结果
        AtomicReference<FrontProductRuleItemDTO> frontProductRuleItemDTO = new AtomicReference<>(new FrontProductRuleItemDTO());
        // 查找匹配的产品责任实例
        Optional<FrontProductDutyInstanceDTO> optionalFrontProductDutyInstanceDTO = dutyInstanceDTOList.stream()
                .filter(x -> x.getProductTypeCode().equals(productTypeCode))
                .filter(x -> x.getName().equals(productName))
                .findFirst();
        if (optionalFrontProductDutyInstanceDTO.isPresent()) {
            FrontProductDutyInstanceDTO frontProductDutyInstanceDTO = optionalFrontProductDutyInstanceDTO.get();
            // 遍历产品规则组
            frontProductDutyInstanceDTO.getProductGroupRuleList().forEach(x -> {
                List<FrontInstanceList> instanceList = x.getInstanceList();
                // 遍历 instance
                instanceList.forEach(instance -> {
                    // 遍历 subList
                    instance.getSubList().forEach(sub -> {
                        List<FrontProductRuleItemDTO> productRuleItem = sub.getProductRuleItem();
                        // 查找匹配的 itemKey，如果存在则更新 AtomicReference
                        productRuleItem.stream()
                                .filter(item -> Objects.equals(item.getItemTitle(), targetItemTitle))
                                .findFirst()
                                .ifPresent(frontProductRuleItemDTO::set);
                    });
                });
            });
        }
        return frontProductRuleItemDTO.get();
    }

    /**
     * 只统计人数
     */
    protected List<AgeProfileOfEmployeesBase> buildEmployeeHeadcountAgeProfile(List<QpQuotePersons> quotePersonsList,
                                                                               List<QuoteBenefitDto> benefitDtoList) {
        return buildAgeProfileInternal(
                quotePersonsList,
                benefitDtoList,
                null,
                null,
                null,
                null,
                false
        );
    }

    /**
     * 构建 Age Profile of Employees，统计人数和总保额
     */
    protected List<AgeProfileOfEmployeesBase> buildEmployeeHeadcountAndSumInsuredAgeProfile(List<QpQuotePersons> quotePersonsList,
                                                                                            List<QuoteBenefitDto> benefitDtoList,
                                                                                            Map<Long, List<FrontProductDutyInstanceDTO>> frontProductListMap,
                                                                                            String productTypeCode,
                                                                                            String productName,
                                                                                            String targetItemTitle) {
        return buildAgeProfileInternal(
                quotePersonsList,
                benefitDtoList,
                frontProductListMap,
                productTypeCode,
                productName,
                targetItemTitle,
                true
        );
    }

    protected List<AgeProfileOfEmployeesBase> buildAgeProfileInternal(List<QpQuotePersons> quotePersonsList,
                                                                      List<QuoteBenefitDto> benefitDtoList,
                                                                      Map<Long, List<FrontProductDutyInstanceDTO>> frontProductListMap,
                                                                      String productTypeCode,
                                                                      String productName,
                                                                      String targetItemTitle,
                                                                      boolean calculateSumInsured) {
        // 这里的benefitDtoList只包含单一险种，包含多个险种的不支持计算
        Map<Long, QuoteBenefitDto> configMapping = QuoteBenefitDto.buildConfigMapping(benefitDtoList);

        // 定义年龄段列表
        List<String> ageBandList = Arrays.asList(
                "16-30",
                "31-35",
                "36-40",
                "41-45",
                "46-50",
                "51-55",
                "56-60",
                "61-65",
                "66-70"
        );

        // 用 Map 存储统计结果，key = 年龄段，value = AgeProfileOfEmployeesBase
        Map<String, AgeProfileOfEmployeesBase> ageProfileMap = new LinkedHashMap<>();
        for (String ageBand : ageBandList) {
            AgeProfileOfEmployeesBase profile = new AgeProfileOfEmployeesBase();
            profile.setAgeBand(ageBand);
            profile.setNoOfEmployeesMale("");
            profile.setNoOfEmployeesFemale("");
            profile.setTotalSumInsuredMale("");
            profile.setTotalSumInsuredFemale("");
            ageProfileMap.put(ageBand, profile);
        }

        // 遍历员工，直接落到对应的年龄段
        for (QpQuotePersons person : quotePersonsList) {
            ZonedDateTime birthday = person.getDateOfBirth();
            // 计算年龄
            long age = DateUtil.betweenYear(
                    DateUtil.date(birthday.toInstant().toEpochMilli()),
                    DateUtil.date(DateTimeUtil.now(true).toInstant().toEpochMilli()),
                    false
            );
            // 获取性别
            String gender = person.getGender();
            // 找到对应的年龄段
            String band = getAgeBand(age, ageBandList);
            if (band != null) {
                AgeProfileOfEmployeesBase profile = ageProfileMap.get(band);

                // 人数统计
                if (GenderEnum.M.getValue().equals(gender)) {
                    profile.setNoOfEmployeesMale(increase(StrUtil.isBlank(profile.getNoOfEmployeesMale()) ? "0" : profile.getNoOfEmployeesMale(), 1));
                } else {
                    profile.setNoOfEmployeesFemale(increase(StrUtil.isBlank(profile.getNoOfEmployeesFemale()) ? "0" : profile.getNoOfEmployeesFemale(), 1));
                }

                // 总保额统计
                if (calculateSumInsured) {
                    List<Long> configIdList = person.getQuoteConfigIdList();
                    // 从险种列表中找到对应的险种
                    Long configId = configIdList.stream()
                            .filter(configMapping::containsKey)
                            .findFirst()
                            .orElse(null);

                    if (Objects.nonNull(configId)) {
                        List<FrontProductDutyInstanceDTO> dutyInstanceDTOList = frontProductListMap.get(configId);
                        FrontProductRuleItemDTO frontProductRuleItemDTO = getFrontProductRuleItemDTO(dutyInstanceDTOList, productTypeCode, productName, targetItemTitle);
                        String itemValue = convertItemValueToString(frontProductRuleItemDTO.getItemValue());
                        if (StrUtil.isBlank(itemValue)) {
                            continue;
                        }
                        if (GenderEnum.M.getValue().equals(gender)) {
                            profile.setTotalSumInsuredMale(increase(StrUtil.isBlank(profile.getTotalSumInsuredMale()) ? "0" : profile.getTotalSumInsuredMale(), Integer.parseInt(itemValue)));
                        } else {
                            profile.setTotalSumInsuredFemale(increase(StrUtil.isBlank(profile.getTotalSumInsuredFemale()) ? "0" : profile.getTotalSumInsuredFemale(), Integer.parseInt(itemValue)));
                        }
                    }
                }
            }
        }

        List<AgeProfileOfEmployeesBase> ageProfileList = new ArrayList<>(ageProfileMap.values());
        // 构建 Total 行
        AgeProfileOfEmployeesBase totalProfile = buildTotalAgeProfile(ageProfileList, calculateSumInsured);
        // 添加到列表末尾
        ageProfileList.add(totalProfile);

        return ageProfileList;
    }

    /**
     * 构建 Total 行
     */
    private AgeProfileOfEmployeesBase buildTotalAgeProfile(List<AgeProfileOfEmployeesBase> ageProfileList,
                                                           boolean calculateSumInsured) {
        // 计算总数
        int totalMale = 0;
        int totalFemale = 0;
        int totalSumMale = 0;
        int totalSumFemale = 0;

        for (AgeProfileOfEmployeesBase profile : ageProfileList) {
            // 男性人数
            if (StrUtil.isNotBlank(profile.getNoOfEmployeesMale())) {
                totalMale += Integer.parseInt(profile.getNoOfEmployeesMale());
            }
            // 女性人数
            if (StrUtil.isNotBlank(profile.getNoOfEmployeesFemale())) {
                totalFemale += Integer.parseInt(profile.getNoOfEmployeesFemale());
            }

            if (calculateSumInsured) {
                // 男性保额
                if (StrUtil.isNotBlank(profile.getTotalSumInsuredMale())) {
                    totalSumMale += Integer.parseInt(profile.getTotalSumInsuredMale());
                }
                // 女性保额
                if (StrUtil.isNotBlank(profile.getTotalSumInsuredFemale())) {
                    totalSumFemale += Integer.parseInt(profile.getTotalSumInsuredFemale());
                }
            }
        }

        // 创建 Total 行
        AgeProfileOfEmployeesBase totalProfile = new AgeProfileOfEmployeesBase();
        totalProfile.setAgeBand("Total");
        totalProfile.setNoOfEmployeesMale(totalMale == 0 ? "" : String.valueOf(totalMale));
        totalProfile.setNoOfEmployeesFemale(totalFemale == 0 ? "" : String.valueOf(totalFemale));
        totalProfile.setTotalSumInsuredMale(calculateSumInsured ? (totalSumMale == 0 ? "" : String.valueOf(totalSumMale)) : "");
        totalProfile.setTotalSumInsuredFemale(calculateSumInsured ? (totalSumFemale == 0 ? "" : String.valueOf(totalSumFemale)) : "");

        return totalProfile;
    }

    /**
     * 工具方法：将字符串数字转换为 int 加上增量后返回字符串
     */
    private String increase(String origin, int delta) {
        return String.valueOf(Integer.parseInt(origin) + delta);
    }


    /**
     * 根据年龄返回对应的年龄段
     */
    protected String getAgeBand(long age, List<String> ageBandList) {
        for (String band : ageBandList) {
            String[] bounds = band.split("-");
            int lower = Integer.parseInt(bounds[0]);
            int upper = Integer.parseInt(bounds[1]);
            if (age >= lower && age <= upper) {
                return band;
            }
        }
        return null;
    }

    /**
     * 构建 Details of Insured Members（Sgp）
     */
    protected List<DetailsOfInsuredMembersBase> buildSgpDetailsOfInsuredMembersList(List<QuoteBenefitDto> benefitDtoList,
                                                                                    List<QpQuotePersons> quotePersonsList) {
        return buildDetailsOfInsuredMembersBaseList(benefitDtoList, quotePersonsList,
                CollUtil.newHashSet(IdentityTypeEnum.SC, IdentityTypeEnum.SPR), true);
    }

    /**
     * 构建 Details of Insured Members（Foreigners）
     */
    protected List<DetailsOfInsuredMembersBase> buildForeignersDetailsOfInsuredMembersList(List<QuoteBenefitDto> benefitDtoList,
                                                                                           List<QpQuotePersons> quotePersonsList) {
        return buildDetailsOfInsuredMembersBaseList(benefitDtoList, quotePersonsList,
                CollUtil.newHashSet(IdentityTypeEnum.EP, IdentityTypeEnum.S_PASS, IdentityTypeEnum.WP), false);
    }

    /**
     * 构建 Details of Insured Members
     */
    private List<DetailsOfInsuredMembersBase> buildDetailsOfInsuredMembersBaseList(List<QuoteBenefitDto> benefitDtoList,
                                                                                   List<QpQuotePersons> quotePersonsList,
                                                                                   Set<IdentityTypeEnum> includedTypeEnumSet,
                                                                                   boolean isSgp) {
        // 构建quoteId映射，单一险种情况下，一个quoteId只对应一个configId，即一个QuoteBenefitDto对象
        Map<Long, QuoteBenefitDto> singleQuoteMapping = QuoteBenefitDto.buildSingleQuoteMapping(benefitDtoList);
        // 构建config映射
        Map<Long, List<QpQuotePersons>> configIdToPersonListMap = buildConfigIdToPersonListMap(quotePersonsList);
        ArrayList<DetailsOfInsuredMembersBase> detailsOfInsuredMembersBaseList = new ArrayList<>();

        for (Map.Entry<Long, QuoteBenefitDto> singleQuoteMap : singleQuoteMapping.entrySet()) {
            QuoteBenefitDto quoteBenefitDto = singleQuoteMap.getValue();

            DetailsOfInsuredMembersBase detailsOfInsuredMembersBase = new DetailsOfInsuredMembersBase();

            // 设置方案名称与类别
            detailsOfInsuredMembersBase.setNoOfEmployeesPlan(quoteBenefitDto.getQuoteName());
            detailsOfInsuredMembersBase.setNoOfEmployeesCategory(quoteBenefitDto.getQuoteCategory());

            // 填充人员数量
            fillPersonCount(configIdToPersonListMap, quoteBenefitDto, detailsOfInsuredMembersBase, includedTypeEnumSet, isSgp);

            detailsOfInsuredMembersBaseList.add(detailsOfInsuredMembersBase);
        }

        return detailsOfInsuredMembersBaseList;
    }

    /**
     * 填充人员数量
     */
    private void fillPersonCount(Map<Long, List<QpQuotePersons>> configIdToPersonListMap,
                                 QuoteBenefitDto quoteBenefitDto,
                                 DetailsOfInsuredMembersBase detailsOfInsuredMembersBase,
                                 Set<IdentityTypeEnum> includedTypeEnumSet,
                                 boolean isSgp) {
        // 统一清空所有字段
        detailsOfInsuredMembersBase.setEmployeeOnly("");
        detailsOfInsuredMembersBase.setEmployeeAndSpouse("");
        detailsOfInsuredMembersBase.setEmployeeAndChildren("");
        detailsOfInsuredMembersBase.setEmployeeAndFamily("");

        // 获取当前方案下的人员列表
        List<QpQuotePersons> currentPlanPersonsList = configIdToPersonListMap.get(quoteBenefitDto.getQuoteConfigId());

        if (CollectionUtil.isEmpty(currentPlanPersonsList)) {
            return;
        }

        // 获取要统计的人员列表（只统计 includedTypeEnumSet 中的身份类型）
        List<QpQuotePersons> personList;
        if (isSgp) {
            // sgp的人员数量统计需要统计为空的人员
            personList = currentPlanPersonsList.stream()
                    .filter(person -> isIdentityTypeInScope(person.getIdentityType(), includedTypeEnumSet)
                            || Objects.isNull(person.getIdentityType()))
                    .collect(Collectors.toList());
        } else {
            // foreigners的人员数量统计需要统计非空人员
            personList = currentPlanPersonsList.stream()
                    .filter(person -> isIdentityTypeInScope(person.getIdentityType(), includedTypeEnumSet))
                    .collect(Collectors.toList());
        }

        // 关系类型为员工本人的人数
        int employeeCount = 0;
        // 关系类型为员工配偶的人数
        int spouseCount = 0;
        // 关系类型为员工子女的人数
        int childCount = 0;

        // 遍历人员并统计数量
        for (QpQuotePersons qpQuotePersons : personList) {
            // 获取人员关系类型
            MemberTypeEnum memberTypeEnum = MemberTypeEnum.getEnumByValue(qpQuotePersons.getMemberType());

            // 标记是否需要统计
            boolean needCalc = false;

            // sgp的人员数量统计
            if (isSgp) {
                // 如果没有填写人员关系类型，则默认为员工本人
                if (Objects.isNull(memberTypeEnum)) {
                    memberTypeEnum = MemberTypeEnum.E;
                }
                needCalc = true;
            } else {
                // foreigners的人员数量统计
                // 如果填写了人员关系类型，才统计
                if (Objects.nonNull(memberTypeEnum)) {
                    needCalc = true;
                }
            }

            // 如果需要计算，则根据人员关系类型进行对应数量的累加
            if (needCalc) {
                switch (memberTypeEnum) {
                    case E:
                        employeeCount++;
                        break;
                    case S:
                        spouseCount++;
                        break;
                    case C:
                        childCount++;
                        break;
                    default:
                        break;
                }
            }
        }


        // 标记每类成员是否存在
        boolean hasEmployee = employeeCount > 0;
        boolean hasSpouse = spouseCount > 0;
        boolean hasChild = childCount > 0;

        // 根据组合设置对应字段
        if (hasEmployee && !hasSpouse && !hasChild) {
            // 只有员工
            detailsOfInsuredMembersBase.setEmployeeOnly(String.valueOf(employeeCount));
        } else if (hasEmployee && hasSpouse && !hasChild) {
            // 员工 + 配偶
            detailsOfInsuredMembersBase.setEmployeeAndSpouse(String.valueOf(employeeCount + spouseCount));
        } else if (!hasEmployee && hasSpouse && !hasChild) {
            // 只有配偶
            detailsOfInsuredMembersBase.setEmployeeAndSpouse(String.valueOf(spouseCount));
        } else if (hasEmployee && !hasSpouse && hasChild) {
            // 员工 + 子女
            detailsOfInsuredMembersBase.setEmployeeAndChildren(String.valueOf(employeeCount + childCount));
        } else if (!hasEmployee && !hasSpouse && hasChild) {
            // 只有子女
            detailsOfInsuredMembersBase.setEmployeeAndChildren(String.valueOf(childCount));
        } else if (hasEmployee && hasSpouse && hasChild) {
            // 员工 + 配偶 + 子女
            detailsOfInsuredMembersBase.setEmployeeAndFamily(String.valueOf(employeeCount + spouseCount + childCount));
        } else if (!hasEmployee && hasSpouse && hasChild) {
            // 配偶 + 子女
            detailsOfInsuredMembersBase.setEmployeeAndFamily(String.valueOf(spouseCount + childCount));
        } else {
            // 兜底处理：没有任何成员
            detailsOfInsuredMembersBase.setEmployeeOnly("");
            detailsOfInsuredMembersBase.setEmployeeAndSpouse("");
            detailsOfInsuredMembersBase.setEmployeeAndChildren("");
            detailsOfInsuredMembersBase.setEmployeeAndFamily("");
        }
    }

    /**
     * 判断身份类型是否在统计范围
     */
    private boolean isIdentityTypeInScope(String identityTypeCode, Set<IdentityTypeEnum> includedTypeEnumSet) {
        IdentityTypeEnum identityTypeEnum = IdentityTypeEnum.getEnumByValue(identityTypeCode);
        return Objects.nonNull(identityTypeEnum) && includedTypeEnumSet.contains(identityTypeEnum);
    }

    /**
     * 构建人员和config的关联关系
     */
    private Map<Long, List<QpQuotePersons>> buildConfigIdToPersonListMap(List<QpQuotePersons> quotePersonsList) {
        return quotePersonsList.stream()
                // 过滤掉 quoteConfigIdList 为 null 的数据，防止空指针异常
                .filter(person -> person.getQuoteConfigIdList() != null)
                // 使用 flatMap 打平结构
                // 因为每个 person 有多个 configId，我们需要把一个 person 拆成多个 (configId, person) 对
                // 例如：personA 有 [101, 102] -> 变成两个条目: (101, personA), (102, personA)
                .flatMap(person ->
                        person.getQuoteConfigIdList().stream()
                                .map(configId -> new AbstractMap.SimpleEntry<>(configId, person))
                )
                // 使用 groupingBy 按 configId（即 entry.getKey()）分组
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        // 对每个分组内的元素（即每个 configId 下的所有 person）
                        // 把 value（即 person）提取出来，并收集为一个 List<QpQuotePersons>
                        Collectors.mapping(
                                Map.Entry::getValue,
                                Collectors.toList()
                        )
                ));
    }


}