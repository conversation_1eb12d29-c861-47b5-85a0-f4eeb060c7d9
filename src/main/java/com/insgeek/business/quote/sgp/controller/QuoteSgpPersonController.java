package com.insgeek.business.quote.sgp.controller;


import com.insgeek.boot.web.consts.ResultCodeEnum;
import com.insgeek.boot.web.util.MessageUtil;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.sgp.dto.*;
import com.insgeek.business.quote.sgp.service.QuoteSgpPersonService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/v1/sgp/person")
public class QuoteSgpPersonController {

    @Resource
    private QuoteSgpPersonService quoteSgpPersonService;

    /**
     * 下载上传人员模版
     */
    @GetMapping("/template/download")
    public void downloadTemplateFile(HttpServletResponse response, @RequestParam("quote_info_id") Long quoteInfoId) {
        quoteSgpPersonService.downloadTemplateFile(response, quoteInfoId);
    }

    /**
     * 上传人员清单
     */
    @PostMapping("/template/upload")
    public ResponseVO<QpPersonCheckResultDto> uploadTemplateFile(@RequestParam("quote_info_id") Long quoteInfoId, @RequestParam("file") MultipartFile file) {
        return ResponseVO.<QpPersonCheckResultDto>builder()
                .data(quoteSgpPersonService.uploadTemplateFile(quoteInfoId, file))
                .build();
    }

    /**
     * 保存人员清单
     */
    @PostMapping("/save")
    public ResponseVO<QuoteSavePersonResultDto> savePerson(@RequestBody QuoteSavePersonDto quoteSavePersonDto) {
        QuoteSavePersonResultDto quoteSavePersonResultDto = quoteSgpPersonService.savePerson(quoteSavePersonDto);
        // 校验是否有错误，有错误返回错误状态码
        if (quoteSavePersonResultDto.getPersonCheckResult().isHasError()) {
            return ResponseVO.<QuoteSavePersonResultDto>builder()
                    .code(ResultCodeEnum.VALIDATION_FAIL.getCode())
                    .data(quoteSavePersonResultDto)
                    .message(MessageUtil.get("b_b_quote_sgp_052"))
                    .build();
        }
        return ResponseVO.<QuoteSavePersonResultDto>builder()
                .data(quoteSavePersonResultDto)
                .build();
    }

    /**
     * 查询人员清单
     */
    @GetMapping("/list")
    public ResponseVO<QpPersonCheckResultDto> getQuotePersonList(@RequestParam("quote_info_id") Long quoteInfoId) {
        return ResponseVO.<QpPersonCheckResultDto>builder()
                .data(quoteSgpPersonService.getQuotePersonList(quoteInfoId))
                .build();
    }

    /**
     * 查询人员清单的险种下拉列表
     */
    @GetMapping("/benefit/dropdown/list")
    public ResponseVO<Map<String, Map<String, List<String>>>> getQuoteBenefitDropdownList(@RequestParam("quote_info_id") Long quoteInfoId) {
        return ResponseVO.<Map<String, Map<String, List<String>>>>builder()
                .data(quoteSgpPersonService.getQuoteBenefitDropdownList(quoteInfoId))
                .build();
    }

    /**
     * 查询统计信息
     */
    @GetMapping("/overview/list")
    public ResponseVO<List<QuotePersonOverviewDto>> getQuotePersonOverviewList(@RequestParam("quote_info_id") Long quoteInfoId) {
        return ResponseVO.<List<QuotePersonOverviewDto>>builder()
                .data(quoteSgpPersonService.getQuotePersonOverviewList(quoteInfoId))
                .build();
    }

    /**
     * 保存统计信息
     */
    @PostMapping("/overview/save")
    public ResponseVO<Long> savePersonOverview(@RequestBody QuoteSavePersonOverviewDto quoteSavePersonOverviewDto) {
        return ResponseVO.<Long>builder()
                .data(quoteSgpPersonService.savePersonOverview(quoteSavePersonOverviewDto))
                .build();
    }

}
