package com.insgeek.business.quote.sgp.dto;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import com.insgeek.boot.web.pojo.dto.CurrencyAmount;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description:
 * @Date: 2025-09-04  14:42
 * @Author: Yuan<PERSON><PERSON><PERSON><PERSON>
 */
@Data
@Accessors(chain = true)
@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
public class HealthInfoDto {

    /**
     *  披露类型
     */
    private String disclosureType;

    /**
     *  内容
     */
    private String content;

    /**
     *  保险总额
     */
    private CurrencyAmount totalSumInsured;

    /**
     *  人数
     */
    private String number;
}
