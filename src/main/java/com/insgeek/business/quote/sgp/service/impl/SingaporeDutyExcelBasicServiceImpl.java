package com.insgeek.business.quote.sgp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.insgeek.business.quote.sgp.dto.SgDutyQueryDto;
import com.insgeek.business.quote.sgp.dto.SingaporeDutyExcelPlanConfigDTO;
import com.insgeek.business.quote.sgp.dto.SingaporeDutyExcelPlanDTO;
import com.insgeek.business.quote.sgp.service.SingaporeDutyExcelBasicService;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.components.orm.model.querydsl.BQLQueryFactory;
import com.insgeek.protocol.data.client.entity.*;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontProductDutyInstanceDTO;
import com.insgeek.protocol.insurance.entity.QIgProductType;
import com.insgeek.protocol.insurance.entity.model.QIgProduct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * @Description: 新加坡责任Excel基础服务实现类
 * @Author: shl
 * @Date: 2025/8/27
 **/
@Service
@Slf4j
public class SingaporeDutyExcelBasicServiceImpl implements SingaporeDutyExcelBasicService {

    @Autowired
    private BQLQueryFactory bqlQueryFactory;
    @Autowired
    private DataMapper<QpQuote> qpQuoteDataMapper;
    @Autowired
    private DataMapper<QpQuoteConfig> qpQuoteConfigDataMapper;
    @Autowired
    private DataMapper<QpQuoteInfo> qpQuoteInfoDataMapper;

    /**
     * @Description: 根据报价id和历史标识查询已完成的方案
     * @Date: 2025/9/1
     * @Param null:
     **/
    @Override
    public List<Long> queryPlanByQuote(Long quoteInfoId, String historyFlag) {
        if (quoteInfoId == null) {
            return Collections.emptyList();
        }
        QQpQuote qpQuote = QQpQuote.qp_quote;
        return bqlQueryFactory.select(qpQuote.id.as("plan_id"))
                .from(qpQuote)
                .where(qpQuote.quote_info_id.eq(quoteInfoId), qpQuote.history_flag.eq(historyFlag))
                .findList(true, Long.class);
    }

    /**
     * @return 方案列表
     * @Description: 根据询价ID列表查询方案列表
     * @Date: 2025/8/27
     * @Param quoteIdList: 询价ID列表
     **/
    @Override
    public List<SingaporeDutyExcelPlanDTO> queryQuotePlanList(List<Long> quoteIdList) {
        if (quoteIdList == null || quoteIdList.isEmpty()) {
            return Collections.emptyList();
        }
        QQpQuote qpQuote = QQpQuote.qp_quote;
        return bqlQueryFactory.select(
                        qpQuote.id.as("plan_id"),
                        qpQuote.name.as("plan_name"),
                        qpQuote.quote_info_id.as("quote_info_id"),
                        qpQuote.category.as("employee_category"),
                        qpQuote.history_flag.as("history_flag"))
                .from(qpQuote)
                .where(qpQuote.id.in(quoteIdList))
                .findList(true, SingaporeDutyExcelPlanDTO.class);
    }

    @Override
    public List<SingaporeDutyExcelPlanDTO> queryQuotationPlanList(List<Long> quotationIdList) {
        if (quotationIdList == null || quotationIdList.isEmpty()) {
            return Collections.emptyList();
        }
        QQpQuotation qpQuotation = QQpQuotation.qp_quotation;
        return bqlQueryFactory.select(
                        qpQuotation.id.as("plan_id"),
                        qpQuotation.name.as("plan_name"),
                        qpQuotation.quotation_info_id.as("quotation_info_id"),
                        qpQuotation.category.as("employee_category"),
                        qpQuotation.history_flag.as("history_flag"))
                .from(qpQuotation)
                .where(qpQuotation.id.in(quotationIdList))
                .findList(true, SingaporeDutyExcelPlanDTO.class);
    }

    /**
     * @return void
     * @Description: 更新询价方案
     * @Date: 2025/8/27
     * @Param singaporeDutyExcelPlanDTO: 新加坡责任Excel方案DTO
     **/
    @Override
    public void saveOrUpdateQuotePlan(SingaporeDutyExcelPlanDTO singaporeDutyExcelPlanDTO) {
        if (singaporeDutyExcelPlanDTO == null) {
            return;
        }
        QpQuote qpQuote = new QpQuote();
        qpQuote.setName(singaporeDutyExcelPlanDTO.getPlanName());
        qpQuote.setCategory(singaporeDutyExcelPlanDTO.getEmployeeCategory());
        if (singaporeDutyExcelPlanDTO.getPlanId() != null) {
            qpQuote.setId(singaporeDutyExcelPlanDTO.getPlanId());
            qpQuoteDataMapper.entity(QpQuote.class).updateOne(qpQuote);
        } else {
            qpQuoteDataMapper.entity(QpQuote.class).insertOne(qpQuote);
            singaporeDutyExcelPlanDTO.setPlanId(qpQuote.getId());
        }


    }

    @Override
    public void batchSaveOrUpdateQuotePlan(List<SingaporeDutyExcelPlanDTO> singaporeDutyExcelPlanDTOs) {
        if (CollectionUtil.isEmpty(singaporeDutyExcelPlanDTOs)) {
            return;
        }
        List<QpQuote> saveList = new ArrayList<>();
        List<QpQuote> updateList = new ArrayList<>();
        for (SingaporeDutyExcelPlanDTO singaporeDutyExcelPlanDTO : singaporeDutyExcelPlanDTOs) {
            QpQuote qpQuote = new QpQuote();
            qpQuote.setName(singaporeDutyExcelPlanDTO.getPlanName());
            qpQuote.setQuoteInfoId(singaporeDutyExcelPlanDTO.getQuoteInfoId());
            qpQuote.setCategory(singaporeDutyExcelPlanDTO.getEmployeeCategory());
            qpQuote.setHistoryFlag(singaporeDutyExcelPlanDTO.getHistoryFlag());
            if (singaporeDutyExcelPlanDTO.getPlanId() != null) {
                qpQuote.setId(singaporeDutyExcelPlanDTO.getPlanId());
                updateList.add(qpQuote);
            } else {
                saveList.add(qpQuote);
            }
        }
        // 更新前端传来的带id的方案
        if (CollectionUtil.isNotEmpty(updateList)) {
            qpQuoteDataMapper.entity(QpQuote.class).updateAll(updateList);
        }
        if (CollectionUtil.isNotEmpty(saveList)) {
            qpQuoteDataMapper.entity(QpQuote.class).insertAll(saveList);
        }
        // 为新增的方案，设置ID
        for (QpQuote qpQuote : saveList) {
            singaporeDutyExcelPlanDTOs.stream().filter(singaporeDutyExcelPlanDTO -> (singaporeDutyExcelPlanDTO.getPlanName() + singaporeDutyExcelPlanDTO.getEmployeeCategory()).equals(qpQuote.getName() + qpQuote.getCategory())).findFirst().ifPresent(singaporeDutyExcelPlanDTO -> singaporeDutyExcelPlanDTO.setPlanId(qpQuote.getId()));
        }
    }


    /**
     * @return 配置列表
     * @Description: 根据询价ID列表查询配置列表
     * @Date: 2025/8/27
     * @Param quoteIdList: 询价ID列表
     **/
    @Override
    public List<SingaporeDutyExcelPlanConfigDTO> queryQuoteConfigList(List<Long> quoteIdList) {
        if (quoteIdList == null || quoteIdList.isEmpty()) {
            return Collections.emptyList();
        }
        QQpQuoteConfig qQpQuoteConfig = QQpQuoteConfig.qp_quote_config;
        return bqlQueryFactory.select(
                        qQpQuoteConfig.id.as("plan_config_id"),
                        qQpQuoteConfig.quote_id.as("plan_id"),
                        qQpQuoteConfig.name.as("plan_config_name"),
                        qQpQuoteConfig.remarks.as("remarks"),
                        qQpQuoteConfig.participation.as("participation"),
                        qQpQuoteConfig.total_amount.as("sum_assured"),
                        qQpQuoteConfig.benefit.as("insurance_type_detail"))
                .from(qQpQuoteConfig)
                .where(qQpQuoteConfig.quote_id.in(quoteIdList))
                .findList(true, SingaporeDutyExcelPlanConfigDTO.class);

    }

    @Override
    public List<SingaporeDutyExcelPlanConfigDTO> queryQuotationConfigList(List<Long> quotationIdList) {
        if (quotationIdList == null || quotationIdList.isEmpty()) {
            return Collections.emptyList();
        }
        QQpQuotationConfig qQpQuotationConfig = QQpQuotationConfig.qp_quotation_config;
        return bqlQueryFactory.select(
                        qQpQuotationConfig.id.as("plan_config_id"),
                        qQpQuotationConfig.quotation_id.as("plan_id"),
                        qQpQuotationConfig.name.as("plan_config_name"),
                        qQpQuotationConfig.remarks.as("remarks"),
                        qQpQuotationConfig.participation.as("participation"),
                        qQpQuotationConfig.total_amount.as("sum_assured"),
                        qQpQuotationConfig.benefit.as("insurance_type_detail"))
                .from(qQpQuotationConfig)
                .where(qQpQuotationConfig.quotation_id.in(quotationIdList))
                .findList(true, SingaporeDutyExcelPlanConfigDTO.class);

    }

    /**
     * @Description: 查询询价关联的方案id
     * @Date: 2025/9/4
     * @Param quoteInfoId:询价id
     * @Param historyFlag:是否为历史方案
     **/
    @Override
    public List<Long> queryQuoteList(Long quoteInfoId, String historyFlag) {
        if (quoteInfoId == null) {
            return Collections.emptyList();
        }
        QQpQuote qpQuote = QQpQuote.qp_quote;
        return bqlQueryFactory.select(qpQuote.id.as("plan_id"))
                .from(qpQuote)
                .where(qpQuote.quote_info_id.eq(quoteInfoId), qpQuote.history_flag.eq(historyFlag))
                .findList(true, Long.class);

    }


    /**
     * @Description: 保存or更新询价配置
     * @Date: 2025/8/27
     * @Param singaporeDutyExcelPlanConfigDTO: 新加坡责任Excel配置DTO
     **/
    @Override
    public void saveOrUpdateQuoteConfig(SingaporeDutyExcelPlanConfigDTO singaporeDutyExcelPlanConfigDTO) {
        if (singaporeDutyExcelPlanConfigDTO == null) {
            return;
        }
        QpQuoteConfig qpQuoteConfig = new QpQuoteConfig();
        qpQuoteConfig.setName(singaporeDutyExcelPlanConfigDTO.getPlanConfigName());
        qpQuoteConfig.setBenefit(singaporeDutyExcelPlanConfigDTO.getInsuranceTypeDetail());
        qpQuoteConfig.setRemarks(singaporeDutyExcelPlanConfigDTO.getRemarks());
        qpQuoteConfig.setParticipation(singaporeDutyExcelPlanConfigDTO.getParticipation());
        if (singaporeDutyExcelPlanConfigDTO.getPlanConfigId() != null) {
            qpQuoteConfig.setId(singaporeDutyExcelPlanConfigDTO.getPlanConfigId());
            qpQuoteConfigDataMapper.entity(QpQuoteConfig.class).updateOne(qpQuoteConfig);
        } else {
            qpQuoteConfigDataMapper.entity(QpQuoteConfig.class).insertOne(qpQuoteConfig);
            singaporeDutyExcelPlanConfigDTO.setPlanConfigId(qpQuoteConfig.getId());
        }
    }

    /**
     * @Description: 批量保存or更新询询价配置
     * @Date: 2025/8/28
     * @Param singaporeDutyExcelPlanConfigDTOs:
     **/
    @Override
    public void batchSaveOrUpdateQuoteConfig(List<SingaporeDutyExcelPlanConfigDTO> singaporeDutyExcelPlanConfigDTOs) {
        if (CollectionUtil.isEmpty(singaporeDutyExcelPlanConfigDTOs)) {
            return;
        }
        List<QpQuoteConfig> saveList = new ArrayList<>();
        List<Map<String, Object>> updateList = new ArrayList<>();
        for (SingaporeDutyExcelPlanConfigDTO configDTO : singaporeDutyExcelPlanConfigDTOs) {
            QpQuoteConfig qpQuoteConfig = new QpQuoteConfig();
            qpQuoteConfig.setQuoteId(configDTO.getPlanId());
            qpQuoteConfig.setName(configDTO.getPlanConfigName());
            qpQuoteConfig.setBenefit(configDTO.getInsuranceTypeDetail());
            qpQuoteConfig.setRemarks(configDTO.getRemarks());
            qpQuoteConfig.setParticipation(configDTO.getParticipation());
//            qpQuoteConfig.setTotalAmount(configDTO.getSumAssured());
            if (configDTO.getPlanConfigId() != null) {
                qpQuoteConfig.setId(configDTO.getPlanConfigId());
                Map<String,Object> updateMap = new HashMap<>();
                updateMap.put("name", qpQuoteConfig.getName());
                updateMap.put("benefit", qpQuoteConfig.getBenefit());
                updateMap.put("remarks", qpQuoteConfig.getRemarks());
                updateMap.put("participation", qpQuoteConfig.getParticipation());
//                updateMap.put("total_amount", qpQuoteConfig.getTotalAmount());
                updateMap.put("id", qpQuoteConfig.getId());
                updateList.add(updateMap);
            } else {
                saveList.add(qpQuoteConfig);
            }
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            qpQuoteConfigDataMapper.entity(QpQuoteConfig.class).batchSuperUpdate(updateList);
        }
        if (CollectionUtil.isNotEmpty(saveList)) {
            qpQuoteConfigDataMapper.entity(QpQuoteConfig.class).insertAll(saveList);
        }
        // 为新增的配置，设置ID
        for (QpQuoteConfig qpQuoteConfig : saveList) {
            singaporeDutyExcelPlanConfigDTOs.stream()
                    .filter(dto -> dto.getPlanConfigName().equals(qpQuoteConfig.getName()))
                    .findFirst()
                    .ifPresent(dto ->{
                        // 设置配置的id
                        dto.setPlanConfigId(qpQuoteConfig.getId());
                        // 为共享保额设置配置id信息
                        if (CollectionUtil.isNotEmpty(dto.getSharedList())){
                            dto.getSharedList().forEach(shared -> shared.setPlanConfigId(qpQuoteConfig.getId()));
                        }
                        // 为责任设置配置id
                        for (FrontProductDutyInstanceDTO dutyDTO : dto.getFrontProductDutyInstanceList()) {
                            if (dutyDTO != null) {
                                if (dutyDTO.getDutyField() == null) {
                                    dutyDTO.setDutyField(new HashMap<>());
                                }
                                dutyDTO.getDutyField().put("plan_config_id", dto.getPlanConfigId());
                            }
                        }
                    });
        }

        // 为新增的配置，设置ID
        for (Map<String, Object> qpQuoteConfig : updateList) {
            singaporeDutyExcelPlanConfigDTOs.stream()
                    .filter(dto -> dto.getPlanConfigName().equals(qpQuoteConfig.get("name")))
                    .findFirst()
                    .ifPresent(dto ->{
                        // 设置配置的id
                        dto.setPlanConfigId(Long.parseLong(qpQuoteConfig.get("id").toString()));
                        // 为共享保额设置配置id信息
                        if (CollectionUtil.isNotEmpty(dto.getSharedList())){
                            dto.getSharedList().forEach(shared -> shared.setPlanConfigId(Long.parseLong(qpQuoteConfig.get("id").toString())));
                        }
                        // 为责任设置配置id
                        for (FrontProductDutyInstanceDTO dutyDTO : dto.getFrontProductDutyInstanceList()) {
                            if (dutyDTO != null) {
                                if (dutyDTO.getDutyField() == null) {
                                    dutyDTO.setDutyField(new HashMap<>());
                                }
                                dutyDTO.getDutyField().put("plan_config_id", dto.getPlanConfigId());
                            }
                        }
                    });
        }
    }

    /**
     * @Description: 批量删除配置信息
     * @Date: 2025/8/29
     * @Param deleteConfigIdList:
     **/
    @Override
    public void batchDeleteQuotePlan(List<Long> deleteQuoteIdList) {
        qpQuoteDataMapper.entity(QpQuote.class).deleteAll(deleteQuoteIdList);
    }

    /**
     * @Description: 批量删除配置信息
     * @Date: 2025/8/29
     * @Param deleteConfigIdList:
     **/
    @Override
    public void batchDeleteQuoteConfig(List<Long> deleteConfigIdList) {
        qpQuoteConfigDataMapper.entity(QpQuoteConfig.class).deleteAll(deleteConfigIdList);
    }

    @Override
    public List<SgDutyQueryDto> queryQuoteDutyList(List<Long> quoteInfoIds) {
        if (quoteInfoIds == null){
            return Collections.emptyList();
        }
        QQpQuoteDuty qpQuoteDuty = QQpQuoteDuty.qp_quote_duty;
        QQpQuote qpQuote = QQpQuote.qp_quote;
        QQpQuoteConfig qpQuoteConfig = QQpQuoteConfig.qp_quote_config;
        QIgProduct qIgProduct = QIgProduct.ig_product;
        return bqlQueryFactory.select(qpQuoteDuty.quote_config_id.as("quote_config_id"), qpQuoteDuty.id.as("id"), qpQuoteDuty.product_id.as("product_id"),qIgProduct.name.as("product_name"))
                .from(qpQuote)
                .leftJoin(qpQuoteConfig)
                .on(qpQuote.id.eq(qpQuoteConfig.quote_id))
                .leftJoin(qpQuoteDuty)
                .on(qpQuoteConfig.id.eq(qpQuoteDuty.quote_config_id))
                .leftJoin(qIgProduct)
                .on(qpQuoteDuty.product_id.eq(qIgProduct.id))
                .where(qpQuote.quote_info_id.in(quoteInfoIds))
                .findList(true, SgDutyQueryDto.class);
    }

    @Override
    public List<SgDutyQueryDto> queryQuotationDutyList(List<Long> quotationInfoIds) {
        if (quotationInfoIds == null){
            return Collections.emptyList();
        }
        QQpQuotationDuty qpQuotationDuty = QQpQuotationDuty.qp_quotation_duty;
        QQpQuotation qpQuotation = QQpQuotation.qp_quotation;
        QQpQuotationConfig qpQuotationConfig = QQpQuotationConfig.qp_quotation_config;
        QIgProduct qIgProduct = QIgProduct.ig_product;
        return bqlQueryFactory.select(qpQuotationDuty.quotation_config_id.as("quotation_config_id"), qpQuotationDuty.id.as("id"), qpQuotationDuty.product_id.as("product_id"),qIgProduct.name.as("product_name"))
                .from(qpQuotation)
                .leftJoin(qpQuotationConfig)
                .on(qpQuotation.id.eq(qpQuotationConfig.quotation_id))
                .leftJoin(qpQuotationDuty)
                .on(qpQuotationConfig.id.eq(qpQuotationDuty.quotation_config_id))
                .leftJoin(qIgProduct)
                .on(qpQuotationDuty.product_id.eq(qIgProduct.id))
                .where(qpQuotation.quotation_info_id.in(quotationInfoIds))
                .findList(true, SgDutyQueryDto.class);
    }

    @Override
    public List<String> queryProductTypeNameByPidList(List<Long> pidList){
        if (CollectionUtil.isEmpty(pidList)){
            return Collections.emptyList();
        }
        QIgProduct qIgProduct = QIgProduct.ig_product;
        QIgProductType qIgProductType = QIgProductType.ig_product_type;
        return bqlQueryFactory.selectDistinct(qIgProductType.name.as("name"))
                .from(qIgProduct)
                .leftJoin(qIgProductType)
                .on(qIgProduct.product_type_id.eq(qIgProductType.id))
                .where(qIgProduct.pid.in(pidList))
                .findList(true,String.class);

    }

}