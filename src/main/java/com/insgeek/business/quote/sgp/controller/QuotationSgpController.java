package com.insgeek.business.quote.sgp.controller;

import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.sgp.dto.SingaporeDutyExcelPlanDTO;
import com.insgeek.business.quote.sgp.service.QuotationSgpService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Description: 方案组详情
 * @Date: 2025-02-08  16:04
 * @Author: eddy.li
 */
@RestController
@RequestMapping("/v1/sgp/quote")
@Slf4j
public class QuotationSgpController {

    @Resource
    private QuotationSgpService quotationSgpService;


    @GetMapping("/compare")
    public ResponseVO compare(@RequestParam("quotation_id") Long quotationId) {
        SingaporeDutyExcelPlanDTO singaporeDutyExcelPlanDTO = quotationSgpService.compare(quotationId);
        return ResponseVO.builder().data(singaporeDutyExcelPlanDTO).build();
    }

    @GetMapping("/quotations")
    public ResponseVO quotations(@RequestParam("quotation_info_id") Long quotationInfoId) {
        List<SingaporeDutyExcelPlanDTO> list = quotationSgpService.quotations(quotationInfoId);
        return ResponseVO.builder().data(list).build();
    }

    @PostMapping("/save")
    public ResponseVO saveQuotation(@RequestBody SingaporeDutyExcelPlanDTO singaporeDutyExcelPlanDTO) {
        quotationSgpService.saveQuotation(singaporeDutyExcelPlanDTO);
        return ResponseVO.builder().build();
    }

}