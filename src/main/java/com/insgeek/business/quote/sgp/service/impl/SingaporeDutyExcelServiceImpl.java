package com.insgeek.business.quote.sgp.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.resource.ResourceUtil;
import com.alibaba.fastjson.JSON;
import com.insgeek.boot.commons.exception.InsgeekException;
import com.insgeek.boot.web.consts.ResultCodeEnum;
import com.insgeek.boot.web.pojo.dto.CurrencyAmount;
import com.insgeek.boot.web.util.MessageUtil;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.common.feign.external.service.IgProductDtoService;
import com.insgeek.business.quote.sgp.config.SGDutyConfig;
import com.insgeek.business.quote.sgp.config.dto.AssociatedSameNameConfigDto;
import com.insgeek.business.quote.sgp.config.dto.MutualExclusionConfigDto;
import com.insgeek.business.quote.sgp.config.dto.MutualExclusionItemDto;
import com.insgeek.business.quote.sgp.config.dto.MutualExclusionProductDto;
import com.insgeek.business.quote.sgp.config.dto.ProductConfigDto;
import com.insgeek.business.quote.sgp.dto.*;
import com.insgeek.business.quote.sgp.enums.YesNoEnum;
import com.insgeek.business.quote.sgp.service.SingaporeDutyExcelBasicService;
import com.insgeek.business.quote.sgp.service.SingaporeDutyExcelService;
import com.insgeek.business.quote.util.CommonUtil;
import com.insgeek.business.quote.util.SingaporeDutyExcelExportUtil;
import com.insgeek.business.quote.util.SingaporeDutyExcelParserUtil;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.insurance.meta.utils.context.IdentityUtil;
import com.insgeek.protocol.data.client.dto.IgProductDto;
import com.insgeek.protocol.data.client.entity.*;
import com.insgeek.protocol.insurance.client.DutyClient;
import com.insgeek.protocol.insurance.client.ProductUIClient;
import com.insgeek.protocol.insurance.client.SpecialInstanceClient;
import com.insgeek.protocol.insurance.dto.product.base.ProductGroupRuleDTO;
import com.insgeek.protocol.insurance.dto.product.base.ProductItemTypeEnum;
import com.insgeek.protocol.insurance.dto.product.request.ProductQueryParam;
import com.insgeek.protocol.insurance.dto.product.request.front.*;
import com.insgeek.protocol.insurance.dto.product.response.IgProductRO;
import com.insgeek.protocol.insurance.dto.special.instance.ListParamDTO;
import com.insgeek.protocol.insurance.enums.SharedSpecialTypeEnum;
import com.insgeek.protocol.insurance.enums.SpecialSceneEnum;
import com.insgeek.protocol.insurance.vo.special.SharedSpecialListRequestVO;
import com.insgeek.protocol.insurance.vo.special.SharedSpecialResponseVO;
import com.insgeek.protocol.insurance.vo.special.SharedSpecialRuleVO;
import com.insgeek.protocol.insurance.vo.special.SharedSpecialSaveRequestVO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: shl
 * @Date: 2025/8/22
 **/
@Service
@Slf4j
public class SingaporeDutyExcelServiceImpl implements SingaporeDutyExcelService {


    @Autowired
    private DataMapper<QpQuotationInfo> quotationInfoDataMapper;
    @Autowired
    private DataMapper<QpQuotationInfoGroup> quotationInfoGroupDataMapper;
    @Autowired
    private DataMapper<QpQuoteInfoGroup> quoteInfoGroupDataMapper;
    @Autowired
    private DataMapper<QpQuoteInfo> quoteInfoDataMapper;
    @Autowired
    private DataMapper<QpQuote> quoteDataMapper;
    @Autowired
    private DataMapper<QpQuotation> quotationDataMapper;
    @Autowired
    private DataMapper<QpQuotationConfig> quotationConfigDataMapper;

    @Autowired
    private IgProductDtoService igProductDtoService;

    @Autowired
    private ProductUIClient productUIClient;

    @Autowired
    private DutyClient dutyClient;

    @Autowired
    private SingaporeDutyExcelBasicService sgpDutyExcelBasicService;

    @Autowired
    private SGDutyConfig sgDutyConfig;
    @Autowired
    private SpecialInstanceClient specialInstanceClient;
    @Resource(name = "batchCreateDutyPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * @Description: 下载模板文件
     * @Date: 2025/8/26
     * @Param response:
     **/
    @Override
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        // 从resources/template目录下获取模板文件
        InputStream inputStream = ResourceUtil.getStream("template/batch_import_duty_template.xlsm");
        // 设置响应头
        response.setContentType("application/vnd.ms-excel.sheet.macroEnabled.12");
        response.setHeader("Content-Disposition", "attachment; filename=batch_import_duty_template.xlsm");

        // 将文件内容写入响应流
        try (OutputStream outputStream = response.getOutputStream()) {
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }
            outputStream.flush();
        } catch (IOException e) {
            throw new InsgeekException(ResultCodeEnum.FAILED.getCode(), e.getMessage());
        } finally {
            if (inputStream != null) {
                inputStream.close();
            }
        }
    }

    /**
     * @Description: 将excel解析为报价方案，用于前端展示
     * @Date: 2025/8/26
     * @Param file:
     **/
    @Override
    public List<SingaporeDutyExcelPlanDTO> convertExcelToQuotePlan(MultipartFile file) throws InsgeekException {
        try {
            //将excel解析为标准的方案、配置、责任层级结构
            SingaporeDutyExcelPreDTO singaporeDutyExcelPreDTO = null;
            try {
                singaporeDutyExcelPreDTO = SingaporeDutyExcelParserUtil.convertExcelToQuotePlan(file);
            } catch (Exception e) {
                throw new InsgeekException(ResultCodeEnum.FAILED.getCode(), e.getMessage());
            }
            List<SingaporeDutyExcelPlanDTO> planList = singaporeDutyExcelPreDTO.getPlanList();
            // 遍历planList，组装List<FrontProductDutyInstanceDTO> frontProductDutyInstanceList
            fillFrontDutyUIList(planList);
            // 产品配置基本规则校验（主附险、方案名重复、方案共享保额重复、字段值基础校验等规则）
            validateMasterSlaveInsurance(planList);
            // 为plan的error字段赋值，方便前端判断
            hasErrors(planList);
            return singaporeDutyExcelPreDTO.getPlanList();
        } catch (Exception e) {
            if (e instanceof InsgeekException) {
                throw (InsgeekException) e;
            } else {
                throw new InsgeekException(ResultCodeEnum.FAILED.getCode(), MessageUtil.get("b_sg_excel_error_017"));
            }
        }
    }

    /**
     * @Description: 页面提交——方案、配置、责任创建与更新
     * @Date: 2025/8/27
     **/
    @Override
    public boolean submitExcelToQuotePlan(List<SingaporeDutyExcelPlanDTO> singaporeDutyExcelPlanDTOS) {
        // 筛选出需要被删除的方案ID，在所有操作完成后，执行删除动作
        List<Long> needDeleteIdList = deleteNoNeedPlan(singaporeDutyExcelPlanDTOS);
        // 产品配置基本规则校验（主附险、方案名重复、方案共享保额重复、字段值基础校验等规则）
        validateMasterSlaveInsurance(singaporeDutyExcelPlanDTOS);
        // 判断如果配置存在错误或者字段存在错误，则返回错误
        if (hasErrors(singaporeDutyExcelPlanDTOS)) {
            return false;
        }
        // 不存在错误，则创建方案、配置、责任
        // 记录新创建的实体ID，用于回滚操作（只记录新创建的，不包括更新的）
        List<Long> createdPlanIds = new ArrayList<>();
        List<Long> createdConfigIds = new ArrayList<>();
        // 记录原始方案ID，用于区分哪些是新创建的
        Set<Long> originalPlanIds = new HashSet<>();
        for (SingaporeDutyExcelPlanDTO planDTO : singaporeDutyExcelPlanDTOS) {
            if (planDTO.getPlanId() != null) {
                originalPlanIds.add(planDTO.getPlanId());
            }
        }
        // 记录原始配置ID，用于区分哪些是新创建的
        Set<Long> originalConfigIds = new HashSet<>();
        for (SingaporeDutyExcelPlanDTO planDTO : singaporeDutyExcelPlanDTOS) {
            if (planDTO.getPlanConfigList() != null) {
                for (SingaporeDutyExcelPlanConfigDTO configDTO : planDTO.getPlanConfigList()) {
                    if (configDTO.getPlanConfigId() != null) {
                        originalConfigIds.add(configDTO.getPlanConfigId());
                    }
                }
            }
        }
        try {
            // 批量创建方案并补全方案id
            sgpDutyExcelBasicService.batchSaveOrUpdateQuotePlan(singaporeDutyExcelPlanDTOS);
            // 记录新创建的方案ID，用于可能的回滚
            for (SingaporeDutyExcelPlanDTO planDTO : singaporeDutyExcelPlanDTOS) {
                // 如果原来的planId为null，而现在有了ID，则说明是新创建的
                if (planDTO.getPlanId() != null && !originalPlanIds.contains(planDTO.getPlanId())) {
                    createdPlanIds.add(planDTO.getPlanId());
                }
            }
            for (SingaporeDutyExcelPlanDTO planDTO : singaporeDutyExcelPlanDTOS) {
                // 补全每个配置的方案id
                List<SingaporeDutyExcelPlanConfigDTO> planConfigList = planDTO.getPlanConfigList();
                for (SingaporeDutyExcelPlanConfigDTO planConfigDTO : planConfigList) {
                    planConfigDTO.setPlanId(planDTO.getPlanId());
                }
                // 批量创建配置并补全配置id
                sgpDutyExcelBasicService.batchSaveOrUpdateQuoteConfig(planConfigList);
                // 记录新创建的配置ID，用于可能的回滚
                for (SingaporeDutyExcelPlanConfigDTO configDTO : planConfigList) {
                    // 如果原来的configId为null，而现在有了ID，则说明是新创建的
                    if (configDTO.getPlanConfigId() != null && !originalConfigIds.contains(configDTO.getPlanConfigId())) {
                        createdConfigIds.add(configDTO.getPlanConfigId());
                    }
                }
                // 多线程批次创建每个配置的责任信息
                saveAllDutyInfo(planConfigList);
            }
            // 保存所有共享保额信息
            saveAllSharedLimitInfo(singaporeDutyExcelPlanDTOS);
            // 删除多余的方案
            if (CollectionUtil.isNotEmpty(needDeleteIdList)) {
                sgpDutyExcelBasicService.batchDeleteQuotePlan(needDeleteIdList);
            }
            return true;
        } catch (Exception e) {
            log.error("批量保存新加坡定制方案信息失败，开始回滚操作", e);
            // 发生异常时，进行回滚操作，只回滚新创建的方案和配置
            rollbackOperations(createdPlanIds, createdConfigIds);
            if (e instanceof InsgeekException) {
                throw e;
            } else {
                throw new InsgeekException(ResultCodeEnum.FAILED.getCode(), MessageUtil.get("b_sg_excel_error_012"));
            }
        }
    }

    private void saveAllDutyInfo(List<SingaporeDutyExcelPlanConfigDTO> planConfigList) {
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (SingaporeDutyExcelPlanConfigDTO planConfigDTO : planConfigList) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                Long configId = planConfigDTO.getPlanConfigId();
                List<FrontProductDutyInstanceDTO> frontProductDutyInstanceList = planConfigDTO.getFrontProductDutyInstanceList();
                // 将每个责任的dutyField中添加productTypeName,其值为productTypeName
                if (frontProductDutyInstanceList != null) {
                    frontProductDutyInstanceList.forEach(duty -> {
                        if (duty.getDutyField() != null && duty.getProductTypeName() != null) {
                            duty.getDutyField().put("productTypeName", duty.getProductTypeName());
                        }
                    });
                    ResponseVO<Object> dutyForFront = dutyClient.createDutyForFront(2, configId, frontProductDutyInstanceList);
                    CommonUtil.getResponseData(dutyForFront);
                }
            }, threadPoolTaskExecutor);
            futures.add(future);
        }
        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * @Description: 调用核心保存共享保额信息
     * @Date: 2025/9/8
     * @Param singaporeDutyExcelPlanDTOS:
     **/
    private void saveAllSharedLimitInfo(List<SingaporeDutyExcelPlanDTO> singaporeDutyExcelPlanDTOS) {
        // 获取保存的责任id与产品名称的映射
        List<Long> quoteInfoIdList = singaporeDutyExcelPlanDTOS.stream().map(SingaporeDutyExcelPlanDTO::getQuoteInfoId).collect(Collectors.toList());
        List<SgDutyQueryDto> sgDutyQueryDtoList = sgpDutyExcelBasicService.queryQuoteDutyList(quoteInfoIdList);
        // sgDutyQueryDtoList 根据构建configId-productName和dutyId的映射关系
        Map<String, Long> dutyIdProductNameMap = new HashMap<>();
        sgDutyQueryDtoList.forEach(sgDutyQueryDto -> {
            dutyIdProductNameMap.put(sgDutyQueryDto.getQuoteConfigId() + "-" + sgDutyQueryDto.getProductName(), sgDutyQueryDto.getId());
        });
        // 遍历所有方案下所有配置的共享保额区域
        List<SharedSpecialRuleVO> dataList = new ArrayList<>();
        for (SingaporeDutyExcelPlanDTO planDTO : singaporeDutyExcelPlanDTOS) {
            List<SingaporeDutyExcelPlanConfigDTO> planConfigList = planDTO.getPlanConfigList();
            for (SingaporeDutyExcelPlanConfigDTO planConfigDTO : planConfigList) {
                Long planConfigId = planConfigDTO.getPlanConfigId();
                List<SingaporeDutyExcelSharedDTO> sharedList = planConfigDTO.getSharedList();
                if (planConfigId != null) {
                    if (CollectionUtil.isNotEmpty(sharedList)) {
                        for (SingaporeDutyExcelSharedDTO sharedDTO : sharedList) {
                            // 构建调用保存特约内容的参数SharedSpecialRuleVO,其中
                            SharedSpecialRuleVO sharedSpecialRuleVO = new SharedSpecialRuleVO();
                            sharedSpecialRuleVO.setRelationId(planConfigId.toString());
                            sharedSpecialRuleVO.setShareApiKey(SharedSpecialTypeEnum.GENERAL_LIABILITY_SHARED_AMOUNT.getCode());
                            List<Long> sharedIdList = new ArrayList<>();
                            for (String dutyName : sharedDTO.getDutyNameList()) {
                                sharedIdList.add(dutyIdProductNameMap.get(sharedDTO.getPlanConfigId() + "-" + dutyName));
                            }
                            sharedSpecialRuleVO.setIdList(sharedIdList);
                            // 遍历枚举类的参数列表，设置需要传递的map参数
                            Map<String, Object> shareDataMap = new HashMap<>();
                            shareDataMap.put(SharedSpecialTypeEnum.ItemRangeEnum.CRAYF2I6BFD_AMOUNT.getItemKey(), CurrencyAmount.valueOf(Long.parseLong(sharedDTO.getSharedLimit()), IdentityUtil.getUserInfo().getBusinessCurrency()));
                            shareDataMap.put(SharedSpecialTypeEnum.ItemRangeEnum.CRAYF2I6BFD_RELATION_ID.getItemKey(), planConfigId.toString());
                            sharedSpecialRuleVO.setShareData(shareDataMap);
                            dataList.add(sharedSpecialRuleVO);
                        }
                    }
                }
            }
        }
        SharedSpecialSaveRequestVO sharedSpecialSaveRequestVO = new SharedSpecialSaveRequestVO();
        sharedSpecialSaveRequestVO.setScene(SpecialSceneEnum.QUOTE.getValue());
        sharedSpecialSaveRequestVO.setShareList(dataList);
        try {
            specialInstanceClient.saveSharedSpecial(sharedSpecialSaveRequestVO);
        } catch (Exception e) {
            log.error("保存共享保额区域失败", e);
            throw new InsgeekException(ResultCodeEnum.FAILED.getCode(), MessageUtil.get("b_sg_excel_error_011"));
        }
    }

    /**
     * @Description: 查询当前询价下的所有方案id信息，然后对比前端传来的需要更新的方案ID;准备最后删除其中前端没有传来的方案
     * @Date: 2025/9/4
     * @Param updateList:
     **/
    private List<Long> deleteNoNeedPlan(List<SingaporeDutyExcelPlanDTO> singaporeDutyExcelPlanDTOS) {
        List<Long> allQuoteId = sgpDutyExcelBasicService.queryQuoteList(singaporeDutyExcelPlanDTOS.get(0).getQuoteInfoId(), singaporeDutyExcelPlanDTOS.get(0).getHistoryFlag());
        // 找出需要删除的方案ID（在数据库中存在但前端没有传入的）
        List<Long> updateQuoteIds = singaporeDutyExcelPlanDTOS.stream()
                .map(SingaporeDutyExcelPlanDTO::getPlanId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return allQuoteId.stream()
                .filter(id -> !updateQuoteIds.contains(id))
                .collect(Collectors.toList());
    }

    /**
     * 回滚已创建的方案和配置;更新后的信息不做处理。创建的需要回滚，保证保存责任异常时可重复提交
     *
     * @param planIds   已创建的方案ID列表
     * @param configIds 已创建的配置ID列表
     */
    private void rollbackOperations(List<Long> planIds, List<Long> configIds) {
        try {
            // 回滚配置
            if (CollectionUtil.isNotEmpty(configIds)) {
                sgpDutyExcelBasicService.batchDeleteQuoteConfig(configIds);
                for (Long configId : configIds) {
                    log.warn("需要回滚配置ID: {}", configId);
                }
            }

            // 回滚方案
            if (CollectionUtil.isNotEmpty(planIds)) {
                sgpDutyExcelBasicService.batchDeleteQuotePlan(planIds);
                for (Long planId : planIds) {
                    log.warn("需要回滚方案ID: {}", planId);
                }
            }
        } catch (Exception rollbackException) {
            log.error("回滚操作失败", rollbackException);
        }
    }

    /**
     * @Description: 判断产品字段基本规则是否正确
     * @Date: 2025/8/27
     * @Param singaporeDutyExcelPreDTO:
     **/
    private void validProductFieldBasicRule(List<SingaporeDutyExcelPlanDTO> planList) {
        if (CollectionUtil.isNotEmpty(planList)) {
            for (SingaporeDutyExcelPlanDTO planDTO : planList) {
                if (planDTO.getPlanConfigList() != null) {
                    for (SingaporeDutyExcelPlanConfigDTO planConfigDTO : planDTO.getPlanConfigList()) {
                        validateFrontDutyUIList(planConfigDTO.getFrontProductDutyInstanceList());
                    }
                }
            }
        }
    }

    /**
     * @Description: 根据excel上传信息与产品基本信息，填充配置层的frontProductDutyInstanceList
     * @Date: 2025/8/26
     * @Param planList:
     * @Param productNameMap:
     **/
    private void fillFrontDutyUIList(List<SingaporeDutyExcelPlanDTO> planList) {
        // 根据解析出的责任，获取产品基本信息
        List<IgProductRO> productROList = queryProductBasicInfoByName(planList);
        // 获取产品名称对应的产品信息,key的逻辑为IgProductRO.getProductTypeName-IgProductRO.getName,IgProductRO
        Map<String, IgProductRO> productTypeNameMap = productROList.stream().collect(Collectors.toMap(product -> product.getProductTypeName() + "-" + product.getName(), product -> product));

        for (SingaporeDutyExcelPlanDTO planDTO : planList) {
            List<SingaporeDutyExcelPlanConfigDTO> planConfigList = planDTO.getPlanConfigList();
            for (SingaporeDutyExcelPlanConfigDTO planConfigDTO : planConfigList) {
                List<FrontProductDutyInstanceDTO> frontProductDutyInstanceList = new ArrayList<>();
                for (SingaporeDutyExcelDutyDTO dutyDTO : planConfigDTO.getDutyList()) {
                    String key = planConfigDTO.getPlanConfigName() + "-" + dutyDTO.getProductName();
                    IgProductRO igProductRO = productTypeNameMap.get(key);
                    FrontProductDutyInstanceDTO frontProductDutyInstanceDTO = convertExcelDutyToDutyUIDTO(dutyDTO, igProductRO);
                    if (Objects.nonNull(frontProductDutyInstanceDTO)) {
                        frontProductDutyInstanceList.add(frontProductDutyInstanceDTO);
                    }
                }
                // 设置转换后的FrontProductDutyInstanceList
                planConfigDTO.setFrontProductDutyInstanceList(frontProductDutyInstanceList);
                // 删除dutyList
                planConfigDTO.setDutyList(null);
            }
        }
    }

    /**
     * @Description: 根据singaporeDutyExcelPlanDTO和product，组装FrontProductDutyInstanceDTO frontProductDutyInstanceList
     * @Date: 2025/8/26
     * @Param singaporeDutyExcelPlanDTO:
     * @Param product:
     **/
    private FrontProductDutyInstanceDTO convertExcelDutyToDutyUIDTO(SingaporeDutyExcelDutyDTO singaporeDutyExcelPlanDTO, IgProductRO product) {
        if (singaporeDutyExcelPlanDTO == null || product == null) {
            return null;
        }

        FrontProductDutyInstanceDTO frontProductDutyInstanceDTO = new FrontProductDutyInstanceDTO();

        // 复制IgProductRO的基本信息到FrontProductDutyInstanceDTO
        copyProductInfo(frontProductDutyInstanceDTO, product);
        // 设置dutyField相关内容
        Map<String, Object> dutyFieldMap = new HashMap<>();
        dutyFieldMap.put("pid", product.getId());
        dutyFieldMap.put("status", product.getStatus());
        dutyFieldMap.put("property", product.getProperty());
        dutyFieldMap.put("product_type_name", product.getProductTypeName());
        dutyFieldMap.put("type", product.getProductTypeCode());
        dutyFieldMap.put("business_type", product.getProductTypeCode());
        dutyFieldMap.put("product_name", product.getName());
        dutyFieldMap.put("delete_flg", product.getDeleteFlg());
        frontProductDutyInstanceDTO.setDutyField(dutyFieldMap);

        // 设置特定字段
        frontProductDutyInstanceDTO.setProductTypeCode(product.getProductTypeCode());
        frontProductDutyInstanceDTO.setProductTypeName(product.getProductTypeName());
        frontProductDutyInstanceDTO.setDutyType(product.getDutyType());
        frontProductDutyInstanceDTO.setContrastFlag(0);
        frontProductDutyInstanceDTO.setChangeFlag(0);

        // 根据SingaporeDutyExcelDutyDTO设置产品名称
        frontProductDutyInstanceDTO.setName(singaporeDutyExcelPlanDTO.getProductName());

        // 处理Excel中的字段值，映射到productGroupRuleList中的item_title和item_value
        List<ProductGroupRuleDTO> productGroupRuleList = product.getProductGroupRuleList();
        if (productGroupRuleList != null && singaporeDutyExcelPlanDTO.getFieldList() != null) {
            // 遍历字段列表，将值映射到对应的规则项中
            Map<String, String> fieldMap = new HashMap<>();
            for (SingaporeDutyExcelDutyFieldDTO field : singaporeDutyExcelPlanDTO.getFieldList()) {
                fieldMap.put(field.getTitle(), field.getValue());
            }

            // 使用参考实现中的方式转换productGroupRuleList
            List<FrontProductGroupRuleList> frontProductGroupRuleLists = new ArrayList<>();
            for (ProductGroupRuleDTO groupRuleDTO : productGroupRuleList) {
                FrontProductGroupRuleList frontGroupRuleList = new FrontProductGroupRuleList();
                frontGroupRuleList.setIsSplit(groupRuleDTO.isSplitInstance());
                List<FrontInstanceList> instanceList = new ArrayList<>();
                // 判断是否存在前置条件，如果有，则处理前置条件的分段情况；默认前置条件在excel中上传的格式为
                // 用户需要在Ward Type中按顺序输入多选的类型，
                // 然后在Amount Per Day中按顺序输入对应的值，如Ward Type输入A,A,B1,C，B2 ，Amount Per Day中输入100,200,500,20,300；
                // 表示输入了五行数据，分别是A类型100，A类型200，B1类型500，C类型20，B2类型300
                if (Boolean.TRUE.equals(frontGroupRuleList.getIsSplit())) {
                    // 将所有fieldMap的值根据逗号拆分为数组
                    Map<String,String[]> fieldMapArray = new HashMap<>();
                    for (Map.Entry<String, String> entry : fieldMap.entrySet()) {
                        fieldMapArray.put(entry.getKey(), entry.getValue().split(","));
                    }
                    // 取fieldMapArray中最短的数组长度进行遍历，防止数组越界
                    int minLength = Integer.MAX_VALUE;
                    for (String[] values : fieldMapArray.values()) {
                        minLength = Math.min(minLength, values.length);
                    }
                    // 依次取每个map中的对应的key和下标对应的值，组成新的数据，遍历构建FrontInstanceList
                    for (int i = 0; i < minLength; i++) {
                        Map<String,String> frontInstanceMap = new HashMap<>();
                        for (Map.Entry<String, String[]> entry : fieldMapArray.entrySet()) {
                            frontInstanceMap.put(entry.getKey(), entry.getValue()[i]);
                        }
                        FrontInstanceList frontInstanceList = getFrontInstanceList(groupRuleDTO, frontInstanceMap);
                        instanceList.add(frontInstanceList);
                    }
                } else {
                    FrontInstanceList frontInstanceList = getFrontInstanceList(groupRuleDTO, fieldMap);
                    instanceList.add(frontInstanceList);
                }
                frontGroupRuleList.setInstanceList(instanceList);
                frontProductGroupRuleLists.add(frontGroupRuleList);
            }

            frontProductDutyInstanceDTO.setProductGroupRuleList(frontProductGroupRuleLists);
        }

        return frontProductDutyInstanceDTO;
    }
    /**
     * @Description: 设置产品实例值信息
     * @Date: 2025/9/28
     * @Param groupRuleDTO:
     * @Param fieldMap:
     **/
    private static FrontInstanceList getFrontInstanceList(ProductGroupRuleDTO groupRuleDTO, Map<String, String> fieldMap) {
        // 创建FrontInstanceList并设置属性
        FrontInstanceList frontInstanceList = new FrontInstanceList();
        frontInstanceList.setGroupBusinessType(groupRuleDTO.getGroupBusinessType());
        frontInstanceList.setGroupBusinessTypeId(groupRuleDTO.getGroupBusinessTypeId());
        frontInstanceList.setGroupBusinessTypeName(groupRuleDTO.getGroupBusinessTypeName());
        frontInstanceList.setGroupBusinessTypeTitle(groupRuleDTO.getGroupBusinessTypeTitle());
        frontInstanceList.setSplitInstance(groupRuleDTO.isSplitInstance());

        // 处理subList
        List<FrontSubList> frontSubLists = BeanUtil.copyToList(groupRuleDTO.getSubList(), FrontSubList.class);
        if (frontSubLists != null) {
            for (FrontSubList frontSubList : frontSubLists) {
                if (frontSubList.getProductRuleItem() != null) {
                    for (FrontProductRuleItemDTO ruleItem : frontSubList.getProductRuleItem()) {
                        String title = ruleItem.getItemTitle();
                        if (fieldMap.containsKey(title)) {
                            setItemValueByCheck(fieldMap, ruleItem, title);
                        }
                    }
                }
            }
        }
        frontInstanceList.setSubList(frontSubLists);

        // 处理前置条件
        List<FrontPreCondition> frontPreConditions = BeanUtil.copyToList(groupRuleDTO.getPreCondition(), FrontPreCondition.class);
        if (frontPreConditions != null) {
            for (FrontPreCondition frontPreCondition : frontPreConditions) {
                if (frontPreCondition.getProductRuleItem() != null) {
                    for (FrontProductRuleItemDTO ruleItem : frontPreCondition.getProductRuleItem()) {
                        String itemKey = ruleItem.getItemTitle();
                        if (fieldMap.containsKey(itemKey)) {
                            setItemValueByCheck(fieldMap, ruleItem, itemKey);
                        }
                    }
                }
            }
        }
        frontInstanceList.setPreCondition(frontPreConditions);
        return frontInstanceList;
    }
    /**
     * @Description: 校验输入的值是否合法，并赋值
     * @Date: 2025/9/28
     * @Param fieldMap:用户输入的field与值的映射
     * @Param ruleItem:产品工厂字段
     * @Param title:产品字段名或itemKey
     **/
    private static void setItemValueByCheck(Map<String, String> fieldMap, FrontProductRuleItemDTO ruleItem, String title) {
        String inputValue = fieldMap.get(title);
        if (StringUtils.isEmpty(inputValue)){
            return;
        }
        // 根据产品字段的类型，将用户输入的值转为对应的数据类型，如果转换失败，则直接设置is_error和error_msg
        if (ruleItem.getItemType() == ProductItemTypeEnum.CURRENCY.getCode()) {
            // 如果输入的值是数字，则将值转为BigDecimal;否则将值设置为空，并设置错误信息
            if (NumberUtils.isNumber(inputValue)) {
                CurrencyAmount amount = CurrencyAmount.builder().amount(new BigDecimal(inputValue)).currency(IdentityUtil.getUserInfo().getBusinessCurrency()).build();
                ruleItem.setItemValue(amount);
            } else {
                ruleItem.setIsError(true);
                ruleItem.setErrorMsg(MessageUtil.get("b_sg_excel_error_004"));
            }
        } else if (ruleItem.getItemType() == ProductItemTypeEnum.DECIMAL.getCode()) {
            // 判断输入的是否为数字，不是则报错
            if (NumberUtils.isNumber(inputValue)) {
                ruleItem.setItemValue(new BigDecimal(inputValue));
            } else {
                ruleItem.setIsError(true);
                ruleItem.setErrorMsg(MessageUtil.get("b_sg_excel_error_004"));
            }
        } else {
            ruleItem.setItemValue(inputValue);
        }
    }

    /**
     * 通过反射拷贝对象属性
     *
     * @param target 目标对象
     * @param source 源对象
     */
    private void copyProperties(Object target, Object source) {
        if (target == null || source == null) {
            return;
        }

        Class<?> targetClass = target.getClass();
        Class<?> sourceClass = source.getClass();

        // 获取源对象的所有方法
        java.lang.reflect.Method[] sourceMethods = sourceClass.getMethods();
        for (java.lang.reflect.Method sourceMethod : sourceMethods) {
            String methodName = sourceMethod.getName();
            // 只处理getter方法
            if (methodName.startsWith("get") && !methodName.equals("getClass") && sourceMethod.getParameterCount() == 0) {
                try {
                    // 获取属性名
                    String fieldName = methodName.substring(3);
                    fieldName = Character.toLowerCase(fieldName.charAt(0)) + fieldName.substring(1);

                    // 获取值
                    Object value = sourceMethod.invoke(source);

                    // 查找setter方法
                    String setterName = "set" + Character.toUpperCase(fieldName.charAt(0)) + fieldName.substring(1);
                    java.lang.reflect.Method setterMethod = null;
                    try {
                        setterMethod = targetClass.getMethod(setterName, sourceMethod.getReturnType());
                        // 调用setter方法
                        setterMethod.invoke(target, value);
                    } catch (NoSuchMethodException e) {
                        // 忽略没有对应setter方法的属性
                    }
                } catch (Exception e) {
                    // 忽略反射异常
                }
            }
        }
    }

    /**
     * 复制产品基本信息
     */
    private void copyProductInfo(FrontProductDutyInstanceDTO target, IgProductRO source) {
        if (target == null || source == null) {
            return;
        }

        target.setId(source.getId());
        target.setBusinessType(source.getBusinessType());
        target.setName(source.getName());
        target.setOtcRange(source.getOtcRange());
        target.setPromotionName(source.getPromotionName());
        target.setStatus(source.getStatus());
        target.setType(source.getType());
        target.setProductPkgId(source.getProductPkgId());
        target.setProductTypeId(source.getProductTypeId());
        target.setProperty(source.getProperty());
        target.setChild(source.getChild());
        target.setCreatedBy(source.getCreatedBy());
        target.setCode(source.getCode());
        target.setRenewal(source.getRenewal());
        target.setPlatform(source.getPlatform());
        target.setDeleteFlg(source.getDeleteFlg());
        target.setIcon(source.getIcon());
        target.setProductType(source.getProductType());
        target.setPid(source.getPid());
        target.setProductGroupId(source.getProductGroupId());
    }

    //基于excel上传的产品名称，查询对应的产品基本信息
    private List<IgProductRO> queryProductBasicInfoByName(List<SingaporeDutyExcelPlanDTO> singaporeExcelPlanList) {
        // 取出所有方案下的所有List<SingaporeDutyExcelDutyDTO> dutyList的productName
        List<String> productNameList = singaporeExcelPlanList.stream()
                .filter(Objects::nonNull)
                .map(SingaporeDutyExcelPlanDTO::getPlanConfigList)
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .map(config -> config.getDutyList())
                .filter(Objects::nonNull)
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .map(SingaporeDutyExcelDutyDTO::getProductName)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        // 根据产品名称查询产品中心获取产品pid
        List<IgProductDto> productList = igProductDtoService.selectByProductName(productNameList);
        List<Long> pidList = productList.stream()
                .map(IgProductDto::getId)
                .collect(Collectors.toList());
        // 根据产品名称查询产品中心获取产品信息
        ProductQueryParam productQueryParam = new ProductQueryParam();
        productQueryParam.setDataList(pidList);
        ResponseVO<List<IgProductRO>> listResponseVO = productUIClient.batchGetDetail(productQueryParam);
        return CommonUtil.getResponseData(listResponseVO);

    }

    /**
     * @Description: 根据方案导出为Excel文件
     * @Date: 2025/8/25
     * @Param planId:
     * @Param file: 暂时用上传文件代替查询文件
     **/
    @Override
    public MultipartFile exportPlanExcel(ExcelExportReqDto excelExportReqDto, String fileName, HttpServletResponse response) throws InsgeekException, IOException {
        SingaporeDutyExcelPreDTO targetDto = new SingaporeDutyExcelPreDTO();
        // 查询所有的方案组
        DataCondition<QpQuotation> condition = new DataCondition<>();
        if (excelExportReqDto.getQuotationInfoId() != null) {
            condition.eq("quotation_info_id", excelExportReqDto.getQuotationInfoId());
        } else {
            condition.in("id", excelExportReqDto.getQuotationIds());
        }
        List<QpQuotation> qpQuotations = quotationDataMapper.entity(QpQuotation.class).select(condition, true);
        // 过滤历史方案和原始方案
        if (excelExportReqDto.getQuotationInfoId() != null) {
            qpQuotations = qpQuotations.stream()
                    .filter(quotation -> quotation.getCompanyId() != null)
                    .filter(quotation -> quotation.getHistoryFlag().equals(YesNoEnum.NO.getValue()))
                    .collect(Collectors.toList());
        }
        List<Long> planIdList = qpQuotations.stream()
                .map(QpQuotation::getId)
                .collect(Collectors.toList());

        // 根据planIdList查询对应的产品信息和配置信息
        List<SingaporeDutyExcelPlanDTO> planList = queryQuotationFinishPlan(planIdList);
        targetDto.setPlanList(planList);
        // 将以上责任数据List<FrontProductDutyInstanceDTO> frontProductDutyInstanceList转为List<SingaporeDutyExcelDutyDTO> dutyList
        for (SingaporeDutyExcelPlanDTO plan : planList) {
            List<SingaporeDutyExcelPlanConfigDTO> planConfigList = plan.getPlanConfigList();
            for (SingaporeDutyExcelPlanConfigDTO planConfig : planConfigList) {
                List<FrontProductDutyInstanceDTO> frontProductDutyInstanceList = planConfig.getFrontProductDutyInstanceList();
                List<SingaporeDutyExcelDutyDTO> dutyList = new ArrayList<>();
                for (FrontProductDutyInstanceDTO frontProductDutyInstanceDTO : frontProductDutyInstanceList) {
                    SingaporeDutyExcelDutyDTO dutyDTO = SingaporeDutyExcelParserUtil.convertToSingaporeDutyExcelDutyDTO(frontProductDutyInstanceDTO);
                    dutyList.add(dutyDTO);
                }
                planConfig.setDutyList(dutyList);
            }
        }

        // targetDto转换成多方案并列的格式
        SingaporeMultiplePlansExcelDTO singaporeMultiplePlansExcelDTO = makeMultiplePlansExcelDto(targetDto);

        // 导出excel
        // 3. 用 ByteArrayOutputStream 导出，不落地文件
        try (ByteArrayOutputStream bos = new ByteArrayOutputStream()) {
            SingaporeDutyExcelExportUtil.exportToStream(singaporeMultiplePlansExcelDTO, bos);
            if (response != null) {
                // 设置响应头
                response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                response.setHeader("Content-Disposition", "attachment; filename=export.xlsx");
                // 将bos写入响应流
                bos.writeTo(response.getOutputStream());
            } else {
                byte[] excelBytes = bos.toByteArray();
                // 模式二：返回 MultipartFile
                return new MockMultipartFile(
                        "file",                         // 表单字段名
                        fileName,                      // 文件名
                        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // MIME 类型
                        excelBytes                     // 文件内容
                );
            }
        } catch (IOException e) {
            throw new InsgeekException(ResultCodeEnum.FAILED.getCode(), e.getMessage());
        }
        return null;
    }

    private SingaporeMultiplePlansExcelDTO makeMultiplePlansExcelDto(SingaporeDutyExcelPreDTO targetDto) {
        log.info("targetDto:{}", JSON.toJSON(targetDto));

        SingaporeMultiplePlansExcelDTO singaporeMultiplePlansExcelDTO = new SingaporeMultiplePlansExcelDTO();

        String[] planName = new String[targetDto.getPlanList().size()];
        String[] premium = new String[targetDto.getPlanList().size()];

        List<SingaporeMultiplePlansExcelDTO.ExcelConfig> excelConfigList = new ArrayList<>();

        int planIndex = 0;

        for (SingaporeDutyExcelPlanDTO plan : targetDto.getPlanList()) {
            // planName中添加名称
            planName[planIndex] = plan.getPlanName() + "-" + plan.getEmployeeCategory();
            // 总保费
            premium[planIndex] = getPremium(plan.getPlanId());

            for (SingaporeDutyExcelPlanConfigDTO c : plan.getPlanConfigList()) {
                // configList中筛选出benefit = c.getInsuranceTypeDetail()的数据
                Optional<SingaporeMultiplePlansExcelDTO.ExcelConfig> optional = excelConfigList.stream()
                        .filter(config -> config.getBenefit().equals(c.getInsuranceTypeDetail())).findFirst();
                SingaporeMultiplePlansExcelDTO.ExcelConfig excelConfig = null;
                if (optional.isPresent()) {
                    // 如果这个险种配置已经存在直接获取
                    excelConfig = optional.get();
                } else {
                    // 如果这个险种配置不存在则创建一个
                    excelConfig = new SingaporeMultiplePlansExcelDTO.ExcelConfig();
                    excelConfig.setBenefit(c.getInsuranceTypeDetail());
                    excelConfigList.add(excelConfig);
                }

                // 设置备注
                if (excelConfig.getRemarks() == null) {
                    excelConfig.setRemarks(new String[targetDto.getPlanList().size()]);
                }
                excelConfig.getRemarks()[planIndex] = c.getRemarks();

                // 设置共享保额
                if (excelConfig.getSpecialList() == null) {
                    excelConfig.setSpecialList(new String[targetDto.getPlanList().size()]);
                }
                StringBuilder special = new StringBuilder();
                if (!CollectionUtils.isEmpty(c.getSharedList())) {
                    List<String> parts = new ArrayList<>();
                    for (SingaporeDutyExcelSharedDTO shared : c.getSharedList()) {
                        String part = String.join(",", shared.getDutyNameList()) +
                                "\n" +
                                "limit:" +
                                shared.getSharedLimit();
                        parts.add(part);
                    }
                    // 用 \n\n 连接每组，完美控制间隔，首尾无多余空行
                    special.append(String.join("\n\n", parts));
                }
                excelConfig.getSpecialList()[planIndex] = special.toString();

                // 获取待渲染到excel的责任列表
                List<SingaporeMultiplePlansExcelDTO.ExcelDuty> dutyList = excelConfig.getDutyList();
                if (CollectionUtils.isEmpty(dutyList)) {
                    dutyList = new ArrayList<>();
                    excelConfig.setDutyList(dutyList);
                }
                for (SingaporeDutyExcelDutyDTO duty : c.getDutyList()) {
                    for (SingaporeDutyExcelDutyFieldDTO filed : duty.getFieldList()) {
                        // dutyList中筛选出productName = duty.getProductName()的数据
                        Optional<SingaporeMultiplePlansExcelDTO.ExcelDuty> dutyOptional = dutyList.stream()
                                .filter(d -> d.getProductName().equals(duty.getProductName())
                                        && d.getField().equals(filed.getTitle())).findFirst();
                        if (dutyOptional.isPresent()) {
                            // 设置上对责任的值
                            dutyOptional.get().getValue()[planIndex] = filed.getValue();
                        } else {
                            // 新建一个责任
                            SingaporeMultiplePlansExcelDTO.ExcelDuty excelDuty = new SingaporeMultiplePlansExcelDTO.ExcelDuty();
                            excelDuty.setProductName(duty.getProductName());
                            excelDuty.setField(filed.getTitle());
                            if (excelDuty.getValue() == null)
                                excelDuty.setValue(new String[targetDto.getPlanList().size()]);
                            excelDuty.getValue()[planIndex] = (filed.getValue());
                            dutyList.add(excelDuty);
                        }
                    }
                }

            }
            planIndex++;
        }
        singaporeMultiplePlansExcelDTO.setPlanName(planName);
        singaporeMultiplePlansExcelDTO.setPremium(premium);
        singaporeMultiplePlansExcelDTO.setConfigList(excelConfigList);

        // dutyList中的productName = 'Sum Assured'的元素提到第一位
        singaporeMultiplePlansExcelDTO.getConfigList().forEach(config -> {
            if (config.getDutyList() != null) {
                // dutyList中的productName = 'Sum Assured'的元素提到第一位
                config.getDutyList().sort(Comparator.comparing(d -> d.getProductName().equals("Sum Assured") ? 0 : 1));
            }
        });
        // json格式打印singaporeMultiplePlansExcelDTO
        log.info("singaporeMultiplePlansExcelDTO:{}", JSON.toJSON(singaporeMultiplePlansExcelDTO));
        return singaporeMultiplePlansExcelDTO;
    }

    private String getPremium(Long quotationId) {
        // 查询方案信息
        DataCondition<QpQuotationConfig> configCondition = new DataCondition<>();
        configCondition.eq("quotation_id", quotationId);
        List<QpQuotationConfig> quotationConfigList = quotationConfigDataMapper.entity(QpQuotationConfig.class).select(configCondition, true);

        if (quotationConfigList == null) return "";
        BigDecimal totalPrice = quotationConfigList.stream()
                .filter(Objects::nonNull)
                .map(QpQuotationConfig::getPrice)
                .filter(Objects::nonNull)
                .map(CurrencyAmount::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        CurrencyAmount allPrice = new CurrencyAmount(totalPrice, quotationConfigList.get(0).getPrice().getCurrency());
        if (allPrice.getAmount() == null) return "";
        return allPrice.getAmount().toString();
    }

    @Override
    public SingaporeDutyExcelDutyDTO convertDutyToExportDuty(FrontProductDutyInstanceDTO frontProductDutyInstanceDTO) {
        return SingaporeDutyExcelParserUtil.convertToSingaporeDutyExcelDutyDTO(frontProductDutyInstanceDTO);
    }

    /**
     * @Description: 根据报价id获取已完成方案信息
     * @Date: 2025/9/2
     * @Param quoteInfoId: 报价id
     * @Param historyFlag: 历史方案标识 0否1是
     **/
    @Override
    public List<SingaporeDutyExcelPlanDTO> queryFinishPlanByQuote(Long quoteInfoId, String historyFlag) {
        // 根据报价id查询已完成方案id
        List<Long> quoteIdList = sgpDutyExcelBasicService.queryPlanByQuote(quoteInfoId, historyFlag);
        return this.queryFinishPlan(quoteIdList);
    }

    /**
     * @Description: 根据方案id列表查询已完成的方案
     * @Date: 2025/8/27
     * @Param planIdList:
     **/
    @Override
    public List<SingaporeDutyExcelPlanDTO> queryFinishPlan(List<Long> planIdList) {
        // 根据planIdList查询已完成的方案
        if (CollectionUtils.isEmpty(planIdList)) {
            return new ArrayList<>();
        }
        List<SingaporeDutyExcelPlanDTO> planDTOList = sgpDutyExcelBasicService.queryQuotePlanList(planIdList);
        if (CollectionUtils.isEmpty(planDTOList)) {
            return planDTOList;
        }
        // 转为planId-dto的map，方便后续赋值configDto
        Map<Long, SingaporeDutyExcelPlanDTO> planMap = convertPlanListToMap(planDTOList);
        // 根据planIdList查询方案下的配置信息，并分配给对应的planDto
        List<SingaporeDutyExcelPlanConfigDTO> planConfigDTOList = sgpDutyExcelBasicService.queryQuoteConfigList(planIdList);
        if (CollectionUtils.isEmpty(planConfigDTOList)) {
            return planDTOList;
        }
        assignConfigsToPlans(planConfigDTOList, planMap);
        // 查询责任信息并分配给对应的配置
        List<Long> configList = extractConfigIds(planConfigDTOList);
        if (CollectionUtils.isEmpty(configList)) {
            return planDTOList;
        }
        assignDutiesToConfigs(2, configList, planConfigDTOList);
        // 查询共享保额配置信息，转为标准格式，并分配给对应的配置
        try {
            assignSharedsToConfigs(true, configList, planDTOList);
        } catch (Exception e) {
            log.error("查询共享保额信息异常", e);
        }
        return planDTOList;
    }

    /**
     * @Description: TODO
     * @Date: 2025/9/8
     * @Param planDTOList:
     **/
    private void assignSharedsToConfigs(boolean isQuote, List<Long> configList, List<SingaporeDutyExcelPlanDTO> planDTOList) {
        SharedSpecialListRequestVO sharedSpecialListRequestVO = new SharedSpecialListRequestVO();
        // 转换为List<String>
        List<String> stringList = configList.stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
        sharedSpecialListRequestVO.setRelationIdList(stringList);
        if (isQuote) {
            sharedSpecialListRequestVO.setScene(SpecialSceneEnum.QUOTE.getValue());
        } else {
            sharedSpecialListRequestVO.setScene(SpecialSceneEnum.QUOTATION.getValue());
        }
        ResponseVO<List<SharedSpecialResponseVO>> listResponseVO = specialInstanceClient.querySharedSpecialList(sharedSpecialListRequestVO);
        List<SharedSpecialResponseVO> responseData = CommonUtil.getResponseData(listResponseVO);
        List<SgDutyQueryDto> sgDutyQueryDtoList = new ArrayList<>();
        if (isQuote) {
            // 收集planDTOList所有的quoteInfoId
            List<Long> quoteInfoIdList = planDTOList.stream().map(SingaporeDutyExcelPlanDTO::getQuoteInfoId).collect(Collectors.toList());
            sgDutyQueryDtoList = sgpDutyExcelBasicService.queryQuoteDutyList(quoteInfoIdList);
        } else {
            List<Long> quotationInfoIdList = planDTOList.stream().map(SingaporeDutyExcelPlanDTO::getQuotationInfoId).collect(Collectors.toList());
            sgDutyQueryDtoList = sgpDutyExcelBasicService.queryQuotationDutyList(quotationInfoIdList);
        }
        // 将查询出的共享保额信息转为singaporeDutyExcelSharedDTO,并分配给对应的配置
        // 构建dutyId到产品名称的映射
        Map<Long, String> dutyIdToProductNameMap = new HashMap<>();
        sgDutyQueryDtoList.forEach(sgDutyQueryDto -> {
            dutyIdToProductNameMap.put(sgDutyQueryDto.getId(), sgDutyQueryDto.getProductName());
        });

        // 按配置ID分组共享保额信息
        Map<Long, List<SharedSpecialResponseVO>> sharedByConfigId = responseData.stream()
                .collect(Collectors.groupingBy(shared -> Long.valueOf(shared.getRelationId())));

        // 遍历所有方案和配置，分配共享保额信息
        for (SingaporeDutyExcelPlanDTO planDTO : planDTOList) {
            for (SingaporeDutyExcelPlanConfigDTO configDTO : planDTO.getPlanConfigList()) {
                Long configId = configDTO.getPlanConfigId();
                List<SharedSpecialResponseVO> sharedList = sharedByConfigId.get(configId);

                if (CollectionUtil.isNotEmpty(sharedList)) {
                    List<SingaporeDutyExcelSharedDTO> excelSharedList = new ArrayList<>();
                    for (SharedSpecialResponseVO shared : sharedList) {
                        SingaporeDutyExcelSharedDTO excelShared = new SingaporeDutyExcelSharedDTO();
                        excelShared.setPlanConfigId(configId);

                        // 从共享数据中获取保额值
                        Map<String, Object> shareData = shared.getShareData();
                        if (shareData != null && shareData.containsKey(SharedSpecialTypeEnum.ItemRangeEnum.CRAYF2I6BFD_AMOUNT.getItemKey())) {
                            Object amountObj = shareData.get(SharedSpecialTypeEnum.ItemRangeEnum.CRAYF2I6BFD_AMOUNT.getItemKey());
                            if (Objects.nonNull(amountObj) && amountObj instanceof Map) {
                                String amount = ((Map<String, String>) amountObj).get(SharedSpecialTypeEnum.ItemRangeEnum.CRAYF2I6BFD_AMOUNT.getItemKey());
                                excelShared.setSharedLimit(amount);
                            }
                        }

                        // 设置共享计划名称（使用配置ID作为默认值）
                        excelShared.setSharedPlanName(planDTO.getPlanName() + "|" + planDTO.getEmployeeCategory());

                        // 构建责任名称列表
                        List<String> dutyNames = new ArrayList<>();
                        List<Long> dutyIds = shared.getIdList();
                        if (CollectionUtil.isNotEmpty(dutyIds)) {
                            for (Long dutyId : dutyIds) {
                                String productName = dutyIdToProductNameMap.get(dutyId);
                                if (productName != null) {
                                    dutyNames.add(productName);
                                }
                            }
                        }
                        excelShared.setDutyNameList(dutyNames);

                        excelSharedList.add(excelShared);
                    }
                    configDTO.setSharedList(excelSharedList);
                }
            }
        }
    }

    /**
     * 将方案列表转换为以planId为键的Map
     *
     * @param planDTOList 方案列表
     * @return 以planId为键的Map
     */
    private Map<Long, SingaporeDutyExcelPlanDTO> convertPlanListToMap(List<SingaporeDutyExcelPlanDTO> planDTOList) {
        return planDTOList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(SingaporeDutyExcelPlanDTO::getPlanId, Function.identity(), (existing, replacement) -> existing));
    }

    /**
     * 将配置列表分配给对应的方案
     *
     * @param planConfigDTOList 配置列表
     * @param planMap           方案Map
     */
    private void assignConfigsToPlans(List<SingaporeDutyExcelPlanConfigDTO> planConfigDTOList,
                                      Map<Long, SingaporeDutyExcelPlanDTO> planMap) {
        // 转为planId-configList的映射
        Map<Long, List<SingaporeDutyExcelPlanConfigDTO>> planConfigMap = new HashMap<>();
        for (SingaporeDutyExcelPlanConfigDTO configDTO : planConfigDTOList) {
            if (configDTO == null) {
                continue;
            }
            Long planId = configDTO.getPlanId();
            if (planId == null) {
                continue;
            }
            planConfigMap.computeIfAbsent(planId, k -> new ArrayList<>()).add(configDTO);
        }

        // 将配置列表分配给对应的方案
        for (Map.Entry<Long, List<SingaporeDutyExcelPlanConfigDTO>> entry : planConfigMap.entrySet()) {
            Long planId = entry.getKey();
            List<SingaporeDutyExcelPlanConfigDTO> configList = entry.getValue();
            SingaporeDutyExcelPlanDTO planDTO = planMap.get(planId);
            if (planDTO != null) {
                planDTO.setPlanConfigList(configList);
            }
        }
    }

    /**
     * 从配置列表中提取配置ID
     *
     * @param planConfigDTOList 配置列表
     * @return 配置ID列表
     */
    public List<Long> extractConfigIds(List<SingaporeDutyExcelPlanConfigDTO> planConfigDTOList) {
        return planConfigDTOList.stream()
                .filter(config -> config != null && config.getPlanConfigId() != null)
                .map(SingaporeDutyExcelPlanConfigDTO::getPlanConfigId)
                .collect(Collectors.toList());
    }

    /**
     * 查询责任信息并分配给对应的配置
     *
     * @param configList        配置ID列表
     * @param planConfigDTOList 配置列表
     */
    private void assignDutiesToConfigs(Integer scene, List<Long> configList, List<SingaporeDutyExcelPlanConfigDTO> planConfigDTOList) {
        ListParamDTO params = new ListParamDTO();
        params.setScene(scene);
        params.setType(2);
        params.setIdList(configList);
        ResponseVO<List<FrontProductDutyInstanceDTO>> listResponseVO = dutyClient.batchGetDutyListByConfigForFront(2, 0, params);
        List<FrontProductDutyInstanceDTO> frontProductDutyInstanceList = CommonUtil.getResponseData(listResponseVO);
        if (CollectionUtils.isEmpty(frontProductDutyInstanceList)) {
            return;
        }

        // 根据FrontProductDutyInstanceDTO中的参数Map<String, Object> dutyField中的key为plan_config_id，
        // 将frontProductDutyInstanceList转为planConfigId-dutyList的map
        Map<Long, List<FrontProductDutyInstanceDTO>> dutyInstanceMap = buildDutyInstanceMap(frontProductDutyInstanceList);

        // 将责任列表分配给对应的配置
        for (SingaporeDutyExcelPlanConfigDTO configDTO : planConfigDTOList) {
            if (configDTO == null) {
                continue;
            }

            Long configId = configDTO.getPlanConfigId();
            if (configId == null) {
                continue;
            }

            List<FrontProductDutyInstanceDTO> dutyList = dutyInstanceMap.get(configId);
            if (dutyList != null) {
                configDTO.setFrontProductDutyInstanceList(dutyList);
            }
        }
    }

    /**
     * 构建责任实例映射
     *
     * @param frontProductDutyInstanceList 责任实例列表
     * @return 以配置ID为键的责任实例映射
     */
    public Map<Long, List<FrontProductDutyInstanceDTO>> buildDutyInstanceMap(
            List<FrontProductDutyInstanceDTO> frontProductDutyInstanceList) {
        Map<Long, List<FrontProductDutyInstanceDTO>> dutyInstanceMap = new HashMap<>();
        for (FrontProductDutyInstanceDTO dutyInstance : frontProductDutyInstanceList) {
            if (dutyInstance == null) {
                continue;
            }

            Map<String, Object> dutyField = dutyInstance.getDutyField();
            if (MapUtils.isEmpty(dutyField) || !dutyField.containsKey("plan_config_id")) {
                continue;
            }

            Object planConfigIdObj = dutyField.get("plan_config_id");
            if (planConfigIdObj == null) {
                continue;
            }

            Long planConfigId;
            try {
                planConfigId = Long.valueOf(planConfigIdObj.toString());
            } catch (NumberFormatException e) {
                continue;
            }

            dutyInstanceMap.computeIfAbsent(planConfigId, k -> new ArrayList<>()).add(dutyInstance);
        }
        return dutyInstanceMap;
    }

    public Map<Long, List<FrontProductDutyInstanceDTO>> buildDutyInstanceMapByViewPlanConfigId(
            List<FrontProductDutyInstanceDTO> frontProductDutyInstanceList) {
        Map<Long, List<FrontProductDutyInstanceDTO>> dutyInstanceMap = new HashMap<>();
        for (FrontProductDutyInstanceDTO dutyInstance : frontProductDutyInstanceList) {
            if (dutyInstance == null) {
                continue;
            }

            Map<String, Object> dutyField = dutyInstance.getDutyField();
            if (MapUtils.isEmpty(dutyField) || !dutyField.containsKey("view_plan_config_id")) {
                continue;
            }

            Object planConfigIdObj = dutyField.get("view_plan_config_id");
            if (planConfigIdObj == null) {
                continue;
            }

            Long planConfigId;
            try {
                planConfigId = Long.valueOf(planConfigIdObj.toString());
            } catch (NumberFormatException e) {
                continue;
            }

            dutyInstanceMap.computeIfAbsent(planConfigId, k -> new ArrayList<>()).add(dutyInstance);
        }
        return dutyInstanceMap;
    }

    /**
     * @Description: 验证FrontProductDutyInstanceDTO列表中的规则
     * @Date: 2025/8/27
     * @Param frontProductDutyInstanceList:
     **/
    private void validateFrontDutyUIList(List<FrontProductDutyInstanceDTO> frontProductDutyInstanceList) {
        if (frontProductDutyInstanceList == null || frontProductDutyInstanceList.isEmpty()) {
            return;
        }

        for (FrontProductDutyInstanceDTO dutyInstance : frontProductDutyInstanceList) {
            if (dutyInstance == null) {
                continue;
            }

            // 遍历产品规则列表
            List<FrontProductGroupRuleList> productGroupRuleList = dutyInstance.getProductGroupRuleList();
            if (productGroupRuleList != null) {
                for (FrontProductGroupRuleList groupRuleList : productGroupRuleList) {
                    if (groupRuleList == null) {
                        continue;
                    }

                    // 遍历实例列表
                    List<FrontInstanceList> instanceList = groupRuleList.getInstanceList();
                    if (instanceList != null) {
                        for (FrontInstanceList instance : instanceList) {
                            if (instance == null) {
                                continue;
                            }

                            // 遍历子列表
                            List<FrontSubList> subList = instance.getSubList();
                            if (subList != null) {
                                for (FrontSubList sub : subList) {
                                    if (sub == null) {
                                        continue;
                                    }

                                    // 遍历产品规则项
                                    List<FrontProductRuleItemDTO> productRuleItemList = sub.getProductRuleItem();
                                    if (productRuleItemList != null) {
                                        for (FrontProductRuleItemDTO ruleItem : productRuleItemList) {
                                            if (ruleItem == null) {
                                                continue;
                                            }

                                            validateRuleItem(ruleItem);
                                        }
                                    }
                                }
                            }

                            // 遍历前置条件列表
                            List<FrontPreCondition> preConditionList = instance.getPreCondition();
                            if (preConditionList != null) {
                                for (FrontPreCondition preCondition : preConditionList) {
                                    if (preCondition == null) {
                                        continue;
                                    }

                                    // 遍历产品规则项
                                    List<FrontProductRuleItemDTO> productRuleItemList = preCondition.getProductRuleItem();
                                    if (productRuleItemList != null) {
                                        for (FrontProductRuleItemDTO ruleItem : productRuleItemList) {
                                            if (ruleItem == null) {
                                                continue;
                                            }

                                            validateRuleItem(ruleItem);
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * @Description: 验证单个规则项
     * @Date: 2025/8/27
     * @Param ruleItem:
     **/
    private void validateRuleItem(FrontProductRuleItemDTO ruleItem) {

        // 1. 如果当前字段的is_must = 1,则item_value不能为空
        if (Integer.valueOf(1).equals(ruleItem.getIsMust())) {
            if (ruleItem.getItemValue() == null || isEmptyItemValue(ruleItem.getItemValue())) {
                ruleItem.setIsError(Boolean.TRUE);
                ruleItem.setErrorMsg(MessageUtil.get("b_sg_excel_error_003"));
                return;
            }
        }
        // 2. 判断值类型是否正确，当item_key为quota，payment，deduction时，需要保证输入的值item_value是数字
        if (Objects.equals(ruleItem.getItemKey(), "quota") || Objects.equals(ruleItem.getItemKey(), "payment") || Objects.equals(ruleItem.getItemKey(), "deduction")) {
            // 不为空时，需要判断输入的是否为数字
            if (!isEmptyItemValue(ruleItem.getItemValue())) {
                // 如果为货币类型，则校验输入的值是否为货币类型，不是则报错
                if (ProductItemTypeEnum.CURRENCY.getCode() == ruleItem.getItemType() && !StringUtils.isEmpty(ruleItem.getItemValue())) {
                    if (ruleItem.getItemValue() instanceof Map) {
                        Map<String, String> itemValue = (Map<String, String>) ruleItem.getItemValue();
                        if (!StringUtils.isEmpty(itemValue.get("amount")) && !NumberUtils.isNumber(String.valueOf(itemValue.get("amount")))) {
                            ruleItem.setIsError(true);
                            ruleItem.setErrorMsg(MessageUtil.get("b_sg_excel_error_004"));
                        }
                    }
                }
                // 如果为数字类型，则校验输入的值是否为数字类型，不是则报错
                if (ProductItemTypeEnum.DECIMAL.getCode() == ruleItem.getItemType() && !StringUtils.isEmpty(ruleItem.getItemValue())) {
                    if (!NumberUtils.isNumber(String.valueOf(ruleItem.getItemValue()))) {
                        ruleItem.setIsError(true);
                        ruleItem.setErrorMsg(MessageUtil.get("b_sg_excel_error_004"));
                    }
                }
            }
        }
        Map<String, Object> extendMap = ruleItem.getExtendMap();

        // 3. 如果当前字段开启了字段规则校验：item_validate_rule false未开启true开启
        // 如果extendMap中包含规则校验，且当前字段设置了min_value和max_value，则判断item_value在两者之间
        if (extendMap != null && extendMap.containsKey("item_validate_rule") && (boolean) extendMap.get("item_validate_rule")) {
            Integer itemValue = parseItemValue(ruleItem.getItemValue());
            if (itemValue != null) {
                Integer minValue = ruleItem.getMinValue();
                if (minValue != null) {
                    if (itemValue < minValue) {
                        ruleItem.setIsError(Boolean.TRUE);
                        ruleItem.setErrorMsg(MessageUtil.get("b_sg_excel_error_min_value", minValue));
                        return;
                    }
                }
                Integer maxValue = ruleItem.getMaxValue();
                if (maxValue != null) {
                    if (itemValue > maxValue) {
                        ruleItem.setIsError(Boolean.TRUE);
                        ruleItem.setErrorMsg(MessageUtil.get("b_sg_excel_error_max_value", maxValue));
                    }
                }
            }
        }

        // 4. 如果当前字段开启了其他规则校验：item_other_rule 0否1是
        // 设置了常用选项，即：extend_map中的field_options,且item_value有值，则判断则判断item_value是否在常用值内
//        if (extendMap != null && extendMap.containsKey("item_other_rule") && Integer.valueOf(1).equals(extendMap.get("item_other_rule"))) {
//            Object fieldOptions = extendMap.get("field_options");
//            if (fieldOptions instanceof List) {
//                List<?> options = (List<?>) fieldOptions;
//                Object itemValue = ruleItem.getItemValue();
//                if (!isEmptyItemValue(itemValue) && CollectionUtil.isNotEmpty(options) && !options.contains(getStringValue(itemValue))) {
//                    ruleItem.setIsError(Boolean.TRUE);
//                    ruleItem.setErrorMsg(MessageUtil.get("b_sg_excel_error_006",options));
//                }
//            }
//        }
    }

    /**
     * @return boolean
     * @Description: 判断item_value是否为空
     * @Date: 2025/8/27
     * @Param itemValue:
     **/
    private boolean isEmptyItemValue(Object itemValue) {
        if (itemValue == null) {
            return true;
        }

        if (itemValue instanceof String) {
            return ((String) itemValue).trim().isEmpty();
        }

        if (itemValue instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) itemValue;
            return map.isEmpty() || (map.size() == 1 && map.containsKey("amount") && ("0".equals(String.valueOf(map.get("amount"))) || StringUtils.isEmpty(String.valueOf(map.get("amount")))));
        }

        return false;
    }

    /**
     * @return Integer
     * @Description: 解析item_value为整数值
     * @Date: 2025/8/27
     * @Param itemValue:
     **/
    private Integer parseItemValue(Object itemValue) {
        if (itemValue == null) {
            return null;
        }

        if (itemValue instanceof String) {
            try {
                return Integer.valueOf((String) itemValue);
            } catch (NumberFormatException e) {
                return null;
            }
        }

        if (itemValue instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) itemValue;
            Object amount = map.get("amount");
            if (amount instanceof String) {
                try {
                    return Integer.valueOf((String) amount);
                } catch (NumberFormatException e) {
                    return null;
                }
            }
        }

        return null;
    }

    /**
     * @return String
     * @Description: 获取item_value的字符串值
     * @Date: 2025/8/27
     * @Param itemValue:
     **/
    private String getStringValue(Object itemValue) {
        if (itemValue == null) {
            return "";
        }

        if (itemValue instanceof String) {
            return (String) itemValue;
        }

        if (itemValue instanceof Map) {
            Map<?, ?> map = (Map<?, ?>) itemValue;
            Object amount = map.get("amount");
            if (amount != null) {
                return String.valueOf(amount);
            }
        }

        return itemValue.toString();
    }

    /**
     * @Description: 产品配置基本规则校验（主附险、方案名重复、方案共享保额重复等规则）
     * @Date: 2025/8/27
     * @Param planList:
     **/
    private void validateMasterSlaveInsurance(List<SingaporeDutyExcelPlanDTO> planList) {
        if (CollectionUtil.isEmpty(planList)) {
            throw new InsgeekException(ResultCodeEnum.FAILED.getCode(), MessageUtil.get("b_sg_excel_error_001"));
        }
        // 限制最大单次创建的方案数
        if (planList.size() > sgDutyConfig.getMaxPlanCount()) {
            throw new InsgeekException(ResultCodeEnum.FAILED.getCode(), MessageUtil.get("b_sg_excel_error_002"));
        }
        // 校验每个方案下的配置是否为空，为空则抛出异常，提示哪个方案下的配置为空
        for (SingaporeDutyExcelPlanDTO plan : planList) {
            if (CollectionUtil.isEmpty(plan.getPlanConfigList())) {
                throw new InsgeekException(ResultCodeEnum.FAILED.getCode(), MessageUtil.get("b_sg_excel_error_016", plan.getPlanName() + "|" + plan.getEmployeeCategory()));
            }
        }
        // 判断所有的方案名称+category是否重复，抛出异常，提示出所有的重复的具体内容
        validMutilationNameCategory(planList);
        // 校验产品主附险规则
        vaildMasterSlaveProductRule(planList);
        // 校验共享保额是否存在重复的组合
        validateRepeatShareAmountDuty(planList);
        // 产品字段基本规则校验
        validProductFieldBasicRule(planList);
        // 校验产品互斥和产品字段互斥规则
        validProductMutualExclusionRule(planList);
        // 根据nacos配置，为同名产品赋值
        setSameNameProductValue(planList);

    }
    /**
     * @Description: 校验产品互斥和产品字段互斥规则
     * 互斥配置来源于nacos配置
     * @Date: 2025/9/29
     * @Param planList:
     **/
    private void validProductMutualExclusionRule(List<SingaporeDutyExcelPlanDTO> planList) {
        // 空指针检查
        if (CollectionUtil.isEmpty(planList) || sgDutyConfig == null ||
            sgDutyConfig.getMutualExclusionCheckConfig() == null) {
            return;
        }

        // 1. 校验产品互斥规则
        validateProductMutualExclusion(planList);

        // 2. 校验产品字段互斥规则
        validateProductFieldMutualExclusion(planList);
    }

    /**
     * @Description: 校验产品互斥规则
     * @Date: 2025/9/29
     * @Param planList:
     **/
    private void validateProductMutualExclusion(List<SingaporeDutyExcelPlanDTO> planList) {
        List<MutualExclusionProductDto> mutualExclusionProductList =
            sgDutyConfig.getMutualExclusionCheckConfig().getMutualExclusionProduct();

        if (CollectionUtil.isEmpty(mutualExclusionProductList)) {
            return;
        }

        List<String> conflictMessages = new ArrayList<>();

        // 遍历每个方案
        for (SingaporeDutyExcelPlanDTO plan : planList) {
            if (plan == null || CollectionUtil.isEmpty(plan.getPlanConfigList())) {
                continue;
            }

            // 遍历每个产品互斥配置
            for (MutualExclusionProductDto mutualExclusionProduct : mutualExclusionProductList) {
                if (mutualExclusionProduct == null ||
                    StringUtils.isEmpty(mutualExclusionProduct.getProductType()) ||
                    CollectionUtil.isEmpty(mutualExclusionProduct.getProductSet())) {
                    continue;
                }

                // 查找对应产品类型的配置
                SingaporeDutyExcelPlanConfigDTO targetConfig = findConfigByProductType(plan, mutualExclusionProduct.getProductType());
                if (targetConfig == null || CollectionUtil.isEmpty(targetConfig.getFrontProductDutyInstanceList())) {
                    continue;
                }

                // 检查每个互斥产品集合
                for (List<String> productSet : mutualExclusionProduct.getProductSet()) {
                    if (CollectionUtil.isEmpty(productSet) || productSet.size() < 2) {
                        continue;
                    }

                    // 检查是否存在互斥的产品组合
                    List<String> existingProducts = findExistingProducts(targetConfig, productSet);
                    if (existingProducts.size() >= 2) {
                        String conflictMessage = String.format("%s|%s: %s",
                            plan.getPlanName(),
                            plan.getEmployeeCategory(),
                            String.join(" , ", existingProducts));
                        conflictMessages.add(conflictMessage);
                    }
                }
            }
        }

        // 如果存在冲突，抛出异常
        if (CollectionUtil.isNotEmpty(conflictMessages)) {
            String errorMessages = String.join(";\n", conflictMessages);
            throw new InsgeekException(ResultCodeEnum.FAILED.getCode(),
                MessageUtil.get("b_sg_excel_error_018", errorMessages));
        }
    }

    /**
     * @Description: 校验产品字段互斥规则
     * @Date: 2025/9/29
     * @Param planList:
     **/
    private void validateProductFieldMutualExclusion(List<SingaporeDutyExcelPlanDTO> planList) {
        List<MutualExclusionItemDto> mutualExclusionItemList =
            sgDutyConfig.getMutualExclusionCheckConfig().getMutualExclusionItem();

        if (CollectionUtil.isEmpty(mutualExclusionItemList)) {
            return;
        }

        // 遍历每个方案
        for (SingaporeDutyExcelPlanDTO plan : planList) {
            if (plan == null || CollectionUtil.isEmpty(plan.getPlanConfigList())) {
                continue;
            }

            // 遍历每个字段互斥配置
            for (MutualExclusionItemDto mutualExclusionItem : mutualExclusionItemList) {
                if (mutualExclusionItem == null ||
                    StringUtils.isEmpty(mutualExclusionItem.getProductType()) ||
                    CollectionUtil.isEmpty(mutualExclusionItem.getProductConfig())) {
                    continue;
                }

                // 查找对应产品类型的配置
                SingaporeDutyExcelPlanConfigDTO targetConfig = findConfigByProductType(plan, mutualExclusionItem.getProductType());
                if (targetConfig == null || CollectionUtil.isEmpty(targetConfig.getFrontProductDutyInstanceList())) {
                    continue;
                }

                // 检查每个产品配置的字段互斥
                for (ProductConfigDto productConfig : mutualExclusionItem.getProductConfig()) {
                    if (productConfig == null ||
                        StringUtils.isEmpty(productConfig.getProductName()) ||
                        CollectionUtil.isEmpty(productConfig.getFieldSet())) {
                        continue;
                    }

                    // 查找对应的产品实例
                    FrontProductDutyInstanceDTO productInstance = findProductInstanceByName(targetConfig, productConfig.getProductName());
                    if (productInstance == null) {
                        continue;
                    }

                    // 检查每个字段集合的互斥关系
                    validateFieldSetMutualExclusion(productInstance, productConfig.getFieldSet());
                }
            }
        }
    }

    /**
     * @Description: 根据产品类型查找配置
     * @Date: 2025/9/29
     * @Param plan: 方案
     * @Param productType: 产品类型
     * @return: 配置
     **/
    private SingaporeDutyExcelPlanConfigDTO findConfigByProductType(SingaporeDutyExcelPlanDTO plan, String productType) {
        if (plan == null || CollectionUtil.isEmpty(plan.getPlanConfigList()) || StringUtils.isEmpty(productType)) {
            return null;
        }

        for (SingaporeDutyExcelPlanConfigDTO config : plan.getPlanConfigList()) {
            if (config != null && productType.equals(config.getPlanConfigName())) {
                return config;
            }
        }
        return null;
    }

    /**
     * @Description: 查找配置中存在的产品
     * @Date: 2025/9/29
     * @Param config: 配置
     * @Param productSet: 产品集合
     * @return: 存在的产品列表
     **/
    private List<String> findExistingProducts(SingaporeDutyExcelPlanConfigDTO config, List<String> productSet) {
        List<String> existingProducts = new ArrayList<>();

        if (config == null || CollectionUtil.isEmpty(config.getFrontProductDutyInstanceList()) ||
            CollectionUtil.isEmpty(productSet)) {
            return existingProducts;
        }

        for (String productName : productSet) {
            if (StringUtils.isEmpty(productName)) {
                continue;
            }

            for (FrontProductDutyInstanceDTO instance : config.getFrontProductDutyInstanceList()) {
                if (instance != null && productName.equals(instance.getName())) {
                    existingProducts.add(productName);
                    break;
                }
            }
        }

        return existingProducts;
    }

    /**
     * @Description: 根据产品名称查找产品实例
     * @Date: 2025/9/29
     * @Param config: 配置
     * @Param productName: 产品名称
     * @return: 产品实例
     **/
    private FrontProductDutyInstanceDTO findProductInstanceByName(SingaporeDutyExcelPlanConfigDTO config, String productName) {
        if (config == null || CollectionUtil.isEmpty(config.getFrontProductDutyInstanceList()) ||
            StringUtils.isEmpty(productName)) {
            return null;
        }

        for (FrontProductDutyInstanceDTO instance : config.getFrontProductDutyInstanceList()) {
            if (instance != null && productName.equals(instance.getName())) {
                return instance;
            }
        }
        return null;
    }

    /**
     * @Description: 校验字段集合的互斥关系
     * @Date: 2025/9/29
     * @Param productInstance: 产品实例
     * @Param fieldSetList: 字段集合列表
     **/
    private void validateFieldSetMutualExclusion(FrontProductDutyInstanceDTO productInstance, List<List<String>> fieldSetList) {
        if (productInstance == null || CollectionUtil.isEmpty(fieldSetList) ||
            CollectionUtil.isEmpty(productInstance.getProductGroupRuleList())) {
            return;
        }

        // 遍历每个字段集合
        for (List<String> fieldSet : fieldSetList) {
            if (CollectionUtil.isEmpty(fieldSet) || fieldSet.size() < 2) {
                continue;
            }

            // 查找有值的互斥字段
            List<FrontProductRuleItemDTO> conflictFields = findConflictFields(productInstance, fieldSet);

            // 如果存在多个有值的互斥字段，设置错误信息
            if (conflictFields.size() >= 2) {
                setMutualExclusionErrors(conflictFields);
            }
        }
    }

    /**
     * @Description: 查找有值的互斥字段
     * @Date: 2025/9/29
     * @Param productInstance: 产品实例
     * @Param fieldSet: 字段集合
     * @return: 有值的字段列表
     **/
    private List<FrontProductRuleItemDTO> findConflictFields(FrontProductDutyInstanceDTO productInstance, List<String> fieldSet) {
        List<FrontProductRuleItemDTO> conflictFields = new ArrayList<>();

        if (productInstance == null || CollectionUtil.isEmpty(fieldSet) ||
            CollectionUtil.isEmpty(productInstance.getProductGroupRuleList())) {
            return conflictFields;
        }

        // 遍历产品组规则列表
        for (FrontProductGroupRuleList groupRule : productInstance.getProductGroupRuleList()) {
            if (groupRule == null || CollectionUtil.isEmpty(groupRule.getInstanceList())) {
                continue;
            }

            // 遍历实例列表
            for (FrontInstanceList instance : groupRule.getInstanceList()) {
                if (instance == null) {
                    continue;
                }

                // 检查子列表中的字段
                conflictFields.addAll(findConflictFieldsInSubList(instance.getSubList(), fieldSet));

                // 检查前置条件中的字段
                conflictFields.addAll(findConflictFieldsInPreCondition(instance.getPreCondition(), fieldSet));
            }
        }

        return conflictFields;
    }

    /**
     * @Description: 在子列表中查找有值的互斥字段
     * @Date: 2025/9/29
     * @Param subList: 子列表
     * @Param fieldSet: 字段集合
     * @return: 有值的字段列表
     **/
    private List<FrontProductRuleItemDTO> findConflictFieldsInSubList(List<FrontSubList> subList, List<String> fieldSet) {
        List<FrontProductRuleItemDTO> conflictFields = new ArrayList<>();

        if (CollectionUtil.isEmpty(subList) || CollectionUtil.isEmpty(fieldSet)) {
            return conflictFields;
        }

        for (FrontSubList sub : subList) {
            if (sub == null || CollectionUtil.isEmpty(sub.getProductRuleItem())) {
                continue;
            }

            for (FrontProductRuleItemDTO ruleItem : sub.getProductRuleItem()) {
                if (ruleItem == null || StringUtils.isEmpty(ruleItem.getItemTitle())) {
                    continue;
                }

                // 如果字段在互斥集合中且有值，添加到冲突列表
                if (fieldSet.contains(ruleItem.getItemTitle()) && hasValue(ruleItem)) {
                    conflictFields.add(ruleItem);
                }
            }
        }

        return conflictFields;
    }

    /**
     * @Description: 在前置条件中查找有值的互斥字段
     * @Date: 2025/9/29
     * @Param preConditionList: 前置条件列表
     * @Param fieldSet: 字段集合
     * @return: 有值的字段列表
     **/
    private List<FrontProductRuleItemDTO> findConflictFieldsInPreCondition(List<FrontPreCondition> preConditionList, List<String> fieldSet) {
        List<FrontProductRuleItemDTO> conflictFields = new ArrayList<>();

        if (CollectionUtil.isEmpty(preConditionList) || CollectionUtil.isEmpty(fieldSet)) {
            return conflictFields;
        }

        for (FrontPreCondition preCondition : preConditionList) {
            if (preCondition == null || CollectionUtil.isEmpty(preCondition.getProductRuleItem())) {
                continue;
            }

            for (FrontProductRuleItemDTO ruleItem : preCondition.getProductRuleItem()) {
                if (ruleItem == null || StringUtils.isEmpty(ruleItem.getItemTitle())) {
                    continue;
                }

                // 如果字段在互斥集合中且有值，添加到冲突列表
                if (fieldSet.contains(ruleItem.getItemTitle()) && hasValue(ruleItem)) {
                    conflictFields.add(ruleItem);
                }
            }
        }

        return conflictFields;
    }

    /**
     * @Description: 判断字段是否有值
     * @Date: 2025/9/29
     * @Param ruleItem: 规则项
     * @return: 是否有值
     **/
    private boolean hasValue(FrontProductRuleItemDTO ruleItem) {
        if (ruleItem == null || ruleItem.getItemValue() == null) {
            return false;
        }

        // 使用现有的isEmptyItemValue方法判断
        return !isEmptyItemValue(ruleItem.getItemValue());
    }

    /**
     * @Description: 为互斥字段设置错误信息
     * @Date: 2025/9/29
     * @Param conflictFields: 冲突字段列表
     **/
    private void setMutualExclusionErrors(List<FrontProductRuleItemDTO> conflictFields) {
        if (CollectionUtil.isEmpty(conflictFields) || conflictFields.size() < 2) {
            return;
        }

        // 构建所有冲突字段的名称列表
        List<String> allFieldNames = new ArrayList<>();
        for (FrontProductRuleItemDTO field : conflictFields) {
            if (field != null && !StringUtils.isEmpty(field.getItemTitle())) {
                allFieldNames.add(field.getItemTitle());
            }
        }

        // 为每个冲突字段设置错误信息
        for (FrontProductRuleItemDTO field : conflictFields) {
            if (field == null || (field.getIsError() != null && field.getIsError())) {
                // 如果字段已经有错误信息，跳过避免重复设置
                continue;
            }

            // 构建其他冲突字段的名称列表（排除当前字段）
            List<String> otherFieldNames = new ArrayList<>();
            for (String fieldName : allFieldNames) {
                if (!fieldName.equals(field.getItemTitle())) {
                    otherFieldNames.add(fieldName);
                }
            }

            // 设置错误标志和错误信息
            if (CollectionUtil.isNotEmpty(otherFieldNames)) {
                field.setIsError(true);
                String otherFieldNamesStr = String.join("、", otherFieldNames);
                field.setErrorMsg(MessageUtil.get("b_sg_excel_error_018", otherFieldNamesStr));
            }
        }
    }

    /**
     * @Description: 获取nacos配置中的同名产品字段
     * @Date: 2025/9/29
     * @Param planList:
     **/
    private void setSameNameProductValue(List<SingaporeDutyExcelPlanDTO> planList) {
        // 空指针检查
        if (CollectionUtil.isEmpty(planList) || sgDutyConfig == null ||
            sgDutyConfig.getMutualExclusionCheckConfig() == null ||
            CollectionUtil.isEmpty(sgDutyConfig.getMutualExclusionCheckConfig().getAssociatedSameNameConfig())) {
            return;
        }

        // 获取同名产品配置
        List<AssociatedSameNameConfigDto> associatedSameNameConfigList =
            sgDutyConfig.getMutualExclusionCheckConfig().getAssociatedSameNameConfig();

        // 遍历每个方案
        for (SingaporeDutyExcelPlanDTO plan : planList) {
            if (plan == null || CollectionUtil.isEmpty(plan.getPlanConfigList())) {
                continue;
            }

            // 遍历每个同名产品配置
            for (AssociatedSameNameConfigDto sameNameConfig : associatedSameNameConfigList) {
                if (sameNameConfig == null ||
                    StringUtils.isEmpty(sameNameConfig.getProductName()) ||
                    CollectionUtil.isEmpty(sameNameConfig.getProductTypeList()) ||
                    sameNameConfig.getProductTypeList().size() < 2) {
                    continue;
                }

                // 获取第一个产品类型作为源
                String sourceProductType = sameNameConfig.getProductTypeList().get(0);

                // 查找源产品类型下的同名产品实例
                FrontProductDutyInstanceDTO sourceInstance = findProductInstance(plan, sourceProductType, sameNameConfig.getProductName());
                if (sourceInstance == null) {
                    continue;
                }

                // 将源产品的值复制到其他产品类型下的同名产品
                for (int i = 1; i < sameNameConfig.getProductTypeList().size(); i++) {
                    String targetProductType = sameNameConfig.getProductTypeList().get(i);
                    FrontProductDutyInstanceDTO targetInstance = findProductInstance(plan, targetProductType, sameNameConfig.getProductName());

                    if (targetInstance != null) {
                        copyProductInstanceValues(sourceInstance, targetInstance);
                    }
                }
            }
        }
    }

    /**
     * @Description: 查找指定产品类型下的指定产品名称的责任实例
     * @Date: 2025/9/29
     * @Param plan: 方案
     * @Param productType: 产品类型
     * @Param productName: 产品名称
     * @return: 责任实例
     **/
    private FrontProductDutyInstanceDTO findProductInstance(SingaporeDutyExcelPlanDTO plan, String productType, String productName) {
        if (plan == null || CollectionUtil.isEmpty(plan.getPlanConfigList()) ||
            StringUtils.isEmpty(productType) || StringUtils.isEmpty(productName)) {
            return null;
        }

        for (SingaporeDutyExcelPlanConfigDTO config : plan.getPlanConfigList()) {
            if (config == null || !productType.equals(config.getPlanConfigName()) ||
                CollectionUtil.isEmpty(config.getFrontProductDutyInstanceList())) {
                continue;
            }

            for (FrontProductDutyInstanceDTO instance : config.getFrontProductDutyInstanceList()) {
                if (instance != null && productName.equals(instance.getName())) {
                    return instance;
                }
            }
        }
        return null;
    }

    /**
     * @Description: 复制源产品实例的值到目标产品实例
     * @Date: 2025/9/29
     * @Param sourceInstance: 源产品实例
     * @Param targetInstance: 目标产品实例
     **/
    private void copyProductInstanceValues(FrontProductDutyInstanceDTO sourceInstance, FrontProductDutyInstanceDTO targetInstance) {
        if (sourceInstance == null || targetInstance == null) {
            return;
        }

        try {
            // 复制产品组规则列表
            if (CollectionUtil.isNotEmpty(sourceInstance.getProductGroupRuleList()) &&
                CollectionUtil.isNotEmpty(targetInstance.getProductGroupRuleList())) {

                copyProductGroupRuleListValues(sourceInstance.getProductGroupRuleList(), targetInstance.getProductGroupRuleList());
            }
        } catch (Exception e) {
            log.warn("复制产品实例值时发生异常: {}", e.getMessage(), e);
        }
    }

    /**
     * @Description: 复制产品组规则列表的值
     * @Date: 2025/9/29
     * @Param sourceGroupRuleList: 源产品组规则列表
     * @Param targetGroupRuleList: 目标产品组规则列表
     **/
    private void copyProductGroupRuleListValues(List<FrontProductGroupRuleList> sourceGroupRuleList,
                                               List<FrontProductGroupRuleList> targetGroupRuleList) {
        if (CollectionUtil.isEmpty(sourceGroupRuleList) || CollectionUtil.isEmpty(targetGroupRuleList)) {
            return;
        }

        // 遍历源和目标的产品组规则列表
        for (int i = 0; i < Math.min(sourceGroupRuleList.size(), targetGroupRuleList.size()); i++) {
            FrontProductGroupRuleList sourceGroupRule = sourceGroupRuleList.get(i);
            FrontProductGroupRuleList targetGroupRule = targetGroupRuleList.get(i);

            if (sourceGroupRule == null || targetGroupRule == null) {
                continue;
            }

            copyInstanceListValues(sourceGroupRule.getInstanceList(), targetGroupRule.getInstanceList());
        }
    }

    /**
     * @Description: 复制实例列表的值
     * @Date: 2025/9/29
     * @Param sourceInstanceList: 源实例列表
     * @Param targetInstanceList: 目标实例列表
     **/
    private void copyInstanceListValues(List<FrontInstanceList> sourceInstanceList, List<FrontInstanceList> targetInstanceList) {
        if (CollectionUtil.isEmpty(sourceInstanceList) || CollectionUtil.isEmpty(targetInstanceList)) {
            return;
        }

        // 遍历源和目标的实例列表
        for (int i = 0; i < Math.min(sourceInstanceList.size(), targetInstanceList.size()); i++) {
            FrontInstanceList sourceInstance = sourceInstanceList.get(i);
            FrontInstanceList targetInstance = targetInstanceList.get(i);

            if (sourceInstance == null || targetInstance == null) {
                continue;
            }

            // 复制子列表的值
            copySubListValues(sourceInstance.getSubList(), targetInstance.getSubList());

            // 复制前置条件的值
            copyPreConditionValues(sourceInstance.getPreCondition(), targetInstance.getPreCondition());
        }
    }

    /**
     * @Description: 复制子列表的值
     * @Date: 2025/9/29
     * @Param sourceSubList: 源子列表
     * @Param targetSubList: 目标子列表
     **/
    private void copySubListValues(List<FrontSubList> sourceSubList, List<FrontSubList> targetSubList) {
        if (CollectionUtil.isEmpty(sourceSubList) || CollectionUtil.isEmpty(targetSubList)) {
            return;
        }

        // 遍历源和目标的子列表
        for (int i = 0; i < Math.min(sourceSubList.size(), targetSubList.size()); i++) {
            FrontSubList sourceSub = sourceSubList.get(i);
            FrontSubList targetSub = targetSubList.get(i);

            if (sourceSub == null || targetSub == null) {
                continue;
            }

            copyProductRuleItemValues(sourceSub.getProductRuleItem(), targetSub.getProductRuleItem());
        }
    }

    /**
     * @Description: 复制前置条件的值
     * @Date: 2025/9/29
     * @Param sourcePreConditionList: 源前置条件列表
     * @Param targetPreConditionList: 目标前置条件列表
     **/
    private void copyPreConditionValues(List<FrontPreCondition> sourcePreConditionList, List<FrontPreCondition> targetPreConditionList) {
        if (CollectionUtil.isEmpty(sourcePreConditionList) || CollectionUtil.isEmpty(targetPreConditionList)) {
            return;
        }

        // 遍历源和目标的前置条件列表
        for (int i = 0; i < Math.min(sourcePreConditionList.size(), targetPreConditionList.size()); i++) {
            FrontPreCondition sourcePreCondition = sourcePreConditionList.get(i);
            FrontPreCondition targetPreCondition = targetPreConditionList.get(i);

            if (sourcePreCondition == null || targetPreCondition == null) {
                continue;
            }

            copyProductRuleItemValues(sourcePreCondition.getProductRuleItem(), targetPreCondition.getProductRuleItem());
        }
    }

    /**
     * @Description: 复制产品规则项的值
     * @Date: 2025/9/29
     * @Param sourceRuleItemList: 源产品规则项列表
     * @Param targetRuleItemList: 目标产品规则项列表
     **/
    private void copyProductRuleItemValues(List<FrontProductRuleItemDTO> sourceRuleItemList, List<FrontProductRuleItemDTO> targetRuleItemList) {
        if (CollectionUtil.isEmpty(sourceRuleItemList) || CollectionUtil.isEmpty(targetRuleItemList)) {
            return;
        }

        // 遍历源产品规则项列表
        for (FrontProductRuleItemDTO sourceRuleItem : sourceRuleItemList) {
            if (sourceRuleItem == null || StringUtils.isEmpty(sourceRuleItem.getItemTitle())) {
                continue;
            }

            // 在目标列表中查找同名字段（根据item_title匹配）
            for (FrontProductRuleItemDTO targetRuleItem : targetRuleItemList) {
                if (targetRuleItem == null || StringUtils.isEmpty(targetRuleItem.getItemTitle())) {
                    continue;
                }

                // 如果找到同名字段，复制item_value
                if (sourceRuleItem.getItemTitle().equals(targetRuleItem.getItemTitle())) {
                    targetRuleItem.setItemValue(sourceRuleItem.getItemValue());
                    break; // 找到匹配的字段后跳出内层循环
                }
            }
        }
    }

    /**
     * @Description: 校验产品主附险规则
     * @Date: 2025/9/10
     * @Param planList:
     **/
    private void vaildMasterSlaveProductRule(List<SingaporeDutyExcelPlanDTO> planList) {
        for (SingaporeDutyExcelPlanDTO plan : planList) {
            if (plan == null || plan.getPlanConfigList() == null) {
                continue;
            }

            // 检查每个配置是否存在主险
            for (SingaporeDutyExcelPlanConfigDTO config : plan.getPlanConfigList()) {
                if (config == null) {
                    continue;
                }

                // 清空之前的错误信息
                if (config.getCommonErrorList() == null) {
                    config.setCommonErrorList(new ArrayList<>());
                } else {
                    config.getCommonErrorList().clear();
                }

                // 如果是附加险类型，检查是否存在对应的主险
                if (isAdditionalInsurance(config.getInsuranceTypeDetail())) {
                    if (!hasMasterInsurance(plan.getPlanConfigList(), config.getInsuranceTypeDetail())) {
                        config.getCommonErrorList().add(MessageUtil.get("b_sg_excel_error_007"));
                    }
                }
            }


        }
    }

    /**
     * @Description: 判断所有的方案名称+category是否重复，抛出异常，提示出所有的重复的具体内容
     * @Date: 2025/9/4
     * @Param planList:
     **/
    private void validMutilationNameCategory(List<SingaporeDutyExcelPlanDTO> planList) {
        Set<String> planNameCategorySet = new HashSet<>();
        Set<String> errorPlanNameCategorySet = new HashSet<>();
        for (SingaporeDutyExcelPlanDTO plan : planList) {
            if (plan == null) {
                continue;
            }
            String planNameCategory = plan.getPlanName() + "|" + plan.getEmployeeCategory();
            if (planNameCategorySet.contains(planNameCategory)) {
                errorPlanNameCategorySet.add(planNameCategory);
            } else {
                planNameCategorySet.add(planNameCategory);
            }
        }
        if (!errorPlanNameCategorySet.isEmpty()) {
            String errorMessages = String.join(",", errorPlanNameCategorySet);
            throw new InsgeekException(ResultCodeEnum.FAILED.getCode(), MessageUtil.get("b_sg_excel_error_009", errorMessages));
        }
    }

    /**
     * @return boolean
     * @Description: 判断是否为附加险
     * @Date: 2025/8/27
     * @Param insuranceType:
     **/
    private boolean isAdditionalInsurance(String insuranceType) {
        if (insuranceType == null || insuranceType.isEmpty()) {
            return false;
        }

        // 附加险类型
        switch (insuranceType) {
            case "SP":
            case "GP":
            case "GM":
            case "GMM":
            case "GD":
            case "GCI":
                return true;
            default:
                return false;
        }
    }

    /**
     * @return boolean
     * @Description: 检查是否存在主险
     * @Date: 2025/8/27
     * @Param configList:
     * @Param additionalType:
     **/
    private boolean hasMasterInsurance(List<SingaporeDutyExcelPlanConfigDTO> configList, String additionalType) {
        if (configList == null || configList.isEmpty() || additionalType == null || additionalType.isEmpty()) {
            return false;
        }

        // 根据附加险类型确定需要的主险类型
        String requiredMasterType = getRequiredMasterType(additionalType);

        // 检查是否存在对应的主险
        if (requiredMasterType != null) {
            for (SingaporeDutyExcelPlanConfigDTO config : configList) {
                if (config != null && Objects.equals(requiredMasterType, config.getInsuranceTypeDetail())) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * @return String
     * @Description: 根据附加险类型获取所需的主险类型
     * @Date: 2025/8/27
     * @Param additionalType:
     **/
    private String getRequiredMasterType(String additionalType) {
        if (additionalType == null || additionalType.isEmpty()) {
            return null;
        }

        switch (additionalType) {
            case "SP":
            case "GP":
            case "GM":
            case "GMM":
            case "GD":
                return "GHS"; // 这些附加险需要ghs作为主险
            case "GCI":
                return "GTL"; // gci需要gtl作为主险
            default:
                return null;
        }
    }

    /**
     * @return boolean
     * @Description: 检查配置或字段是否存在错误
     * @Date: 2025/8/27
     * @Param planList:
     **/
    private boolean hasErrors(List<SingaporeDutyExcelPlanDTO> planList) {
        if (planList == null || planList.isEmpty()) {
            return false;
        }

        for (SingaporeDutyExcelPlanDTO plan : planList) {
            if (plan == null || plan.getPlanConfigList() == null) {
                continue;
            }

            for (SingaporeDutyExcelPlanConfigDTO config : plan.getPlanConfigList()) {
                if (config == null) {
                    continue;
                }

                // 检查配置级别是否有错误
                if (config.getCommonErrorList() != null && !config.getCommonErrorList().isEmpty()) {
                    plan.setError(true);
                    continue;
                }

                // 检查责任字段是否有错误
                if (config.getFrontProductDutyInstanceList() != null) {
                    for (FrontProductDutyInstanceDTO dutyInstance : config.getFrontProductDutyInstanceList()) {
                        if (dutyInstance == null) {
                            continue;
                        }

                        // 遍历产品规则列表检查错误
                        List<FrontProductGroupRuleList> productGroupRuleList = dutyInstance.getProductGroupRuleList();
                        if (productGroupRuleList != null) {
                            for (FrontProductGroupRuleList groupRuleList : productGroupRuleList) {
                                if (groupRuleList == null) {
                                    continue;
                                }

                                // 遍历实例列表
                                List<FrontInstanceList> instanceList = groupRuleList.getInstanceList();
                                if (instanceList != null) {
                                    for (FrontInstanceList instance : instanceList) {
                                        if (instance == null) {
                                            continue;
                                        }

                                        // 检查子列表中的规则项错误
                                        if (hasRuleItemErrors(instance.getSubList())) {
                                            plan.setError(true);
                                            continue;
                                        }

                                        // 检查前置条件中的规则项错误
                                        if (hasPreConditionErrors(instance.getPreCondition())) {
                                            return true;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        return planList.stream().anyMatch(plan -> plan.isError());
    }

    /**
     * @return boolean
     * @Description: 检查子列表中的规则项是否存在错误
     * @Date: 2025/8/27
     * @Param subList:
     **/
    private boolean hasRuleItemErrors(List<FrontSubList> subList) {
        if (subList == null) {
            return false;
        }

        for (FrontSubList sub : subList) {
            if (sub == null) {
                continue;
            }

            List<FrontProductRuleItemDTO> productRuleItemList = sub.getProductRuleItem();
            if (productRuleItemList != null) {
                for (FrontProductRuleItemDTO ruleItem : productRuleItemList) {
                    if (ruleItem != null && Boolean.TRUE.equals(ruleItem.getIsError())) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * @return boolean
     * @Description: 检查前置条件中的规则项是否存在错误
     * @Date: 2025/8/27
     * @Param preConditionList:
     **/
    private boolean hasPreConditionErrors(List<FrontPreCondition> preConditionList) {
        if (preConditionList == null) {
            return false;
        }

        for (FrontPreCondition preCondition : preConditionList) {
            if (preCondition == null) {
                continue;
            }

            List<FrontProductRuleItemDTO> productRuleItemList = preCondition.getProductRuleItem();
            if (productRuleItemList != null) {
                for (FrontProductRuleItemDTO ruleItem : productRuleItemList) {
                    if (ruleItem != null && Boolean.TRUE.equals(ruleItem.getIsError())) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    /**
     * @Description: 判断同一个配置下的共享保额责任组合是否重复
     * 逻辑：遍历所有方案的所有配置下的sharedList,判断每个list中的共享保额责任组合是否重复
     * 先判断每个list中的元素是否重复;存在异常则以 throw new InsgeekException(MessageUtil.get("b_sg_excel_error_002"));抛出错误；提示语为"planConfigName下存在重复共享责任"
     * 然后判断是否存在共享责任组合的list，存在则throw new InsgeekException(MessageUtil.get("b_sg_excel_error_002"));抛出错误；提示语为"planConfigName下存在重复共享责任组合"
     * @Date: 2025/9/6
     * @Param planList:
     **/
    private void validateRepeatShareAmountDuty(List<SingaporeDutyExcelPlanDTO> planList) {
        if (planList == null || planList.isEmpty()) {
            return;
        }
        // 错误集合
        Set<String> repeatedErrorSet = new HashSet<>();
        Set<String> noExistErrorSet = new HashSet<>();

        for (SingaporeDutyExcelPlanDTO plan : planList) {
            if (plan.getPlanConfigList() == null || plan.getPlanConfigList().isEmpty()) {
                continue;
            }

            for (SingaporeDutyExcelPlanConfigDTO config : plan.getPlanConfigList()) {
                List<SingaporeDutyExcelSharedDTO> sharedList = config.getSharedList();
                if (sharedList == null || sharedList.isEmpty()) {
                    continue;
                }

                // 1. 检查每个sharedList中的元素是否重复（基于sharedPlanName和sharedLimit）
                for (SingaporeDutyExcelSharedDTO shared : sharedList) {
                    if (shared.getSharedPlanName() == null || CollectionUtil.isEmpty(shared.getDutyNameList())) {
                        continue;
                    }
                    // 判断getDutyNameList是否存在重复的值
                    if (shared.getDutyNameList().size() != shared.getDutyNameList().stream().distinct().count()) {
                        repeatedErrorSet.add(config.getPlanConfigName() + "-" + shared.getSharedPlanName());
                    }
                }

                // 2. 检查是否存在共享责任组合的list重复（基于dutyNameList）
                Set<String> sharedCombinationKeys = new HashSet<>();
                for (SingaporeDutyExcelSharedDTO shared : sharedList) {
                    if (shared.getDutyNameList() == null || shared.getDutyNameList().isEmpty()) {
                        continue;
                    }
                    // 对dutyNameList进行排序以确保顺序不影响比较结果
                    List<String> sortedDutyNames = new ArrayList<>(shared.getDutyNameList());
                    Collections.sort(sortedDutyNames);
                    String elementKey = shared.getSharedPlanName() + "|" + shared.getSharedLimit();

                    // 构建shardPlanName+dutyNameList的唯一标识
                    String combinationKey = shared.getSharedPlanName() + String.join("|", sortedDutyNames);
                    if (sharedCombinationKeys.contains(combinationKey)) {
                        repeatedErrorSet.add(config.getPlanConfigName() + "-" + shared.getSharedPlanName());
                    } else {
                        sharedCombinationKeys.add(combinationKey);
                    }
                }
                // 3.校验共享保额中选择的责任是否存在于duty中，防止用户实际选择的共享保额责任不存在于上传的责任列表中
                List<FrontProductDutyInstanceDTO> frontProductDutyInstanceList = config.getFrontProductDutyInstanceList();
                Set<String> distinctProductName = frontProductDutyInstanceList.stream().map(FrontProductDutyInstanceDTO::getName).filter(Objects::nonNull).collect(Collectors.toSet());
                for (SingaporeDutyExcelSharedDTO shared : sharedList) {
                    if (shared.getDutyNameList() == null || shared.getDutyNameList().isEmpty()) {
                        continue;
                    }
                    for (String dutyName : shared.getDutyNameList()) {
                        if (!distinctProductName.contains(dutyName)) {
                            noExistErrorSet.add(config.getPlanConfigName() + "-" + shared.getSharedPlanName());
                        }
                    }
                }

            }
        }
        if (CollectionUtil.isNotEmpty(repeatedErrorSet)) {
            String errorMessages = String.join(",", repeatedErrorSet);
            throw new InsgeekException(ResultCodeEnum.FAILED.getCode(), MessageUtil.get("b_sg_excel_error_010", errorMessages));
        }
        if (CollectionUtil.isNotEmpty(noExistErrorSet)) {
            String errorMessages = String.join(",", noExistErrorSet);
            throw new InsgeekException(ResultCodeEnum.FAILED.getCode(), MessageUtil.get("b_sg_excel_error_015", errorMessages));
        }
    }


    /**
     * @Description: 根据方案id列表查询已完成的方案
     * @Date: 2025/8/27
     * @Param planIdList:
     **/
    public List<SingaporeDutyExcelPlanDTO> queryQuotationFinishPlan(List<Long> planIdList) {
        // 根据planIdList查询已完成的方案
        if (CollectionUtils.isEmpty(planIdList)) {
            return new ArrayList<>();
        }
        List<SingaporeDutyExcelPlanDTO> planDTOList = sgpDutyExcelBasicService.queryQuotationPlanList(planIdList);
        if (CollectionUtils.isEmpty(planDTOList)) {
            return planDTOList;
        }
        // 转为planId-dto的map，方便后续赋值configDto
        Map<Long, SingaporeDutyExcelPlanDTO> planMap = convertPlanListToMap(planDTOList);
        // 根据planIdList查询方案下的配置信息，并分配给对应的planDto
        List<SingaporeDutyExcelPlanConfigDTO> planConfigDTOList = sgpDutyExcelBasicService.queryQuotationConfigList(planIdList);
        if (CollectionUtils.isEmpty(planConfigDTOList)) {
            return planDTOList;
        }
        assignConfigsToPlans(planConfigDTOList, planMap);
        // 查询责任信息并分配给对应的配置
        List<Long> configList = extractConfigIds(planConfigDTOList);
        if (CollectionUtils.isEmpty(configList)) {
            return planDTOList;
        }
        assignDutiesToConfigs(3, configList, planConfigDTOList);
        // 查询共享保额配置信息，转为标准格式，并分配给对应的配置
        try {
            assignSharedsToConfigs(false, configList, planDTOList);
        } catch (Exception e) {
            log.error("查询共享保额信息异常", e);
        }
        return planDTOList;
    }
}