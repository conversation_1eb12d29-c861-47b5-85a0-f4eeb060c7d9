package com.insgeek.business.quote.sgp.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import com.insgeek.boot.web.util.MessageUtil;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.common.dao.constant.QpFileDetailsConst;
import com.insgeek.business.quote.common.exception.QuoteException;
import com.insgeek.business.quote.common.service.QuoteFileService;
import com.insgeek.business.quote.sgp.constant.BizConstants;
import com.insgeek.business.quote.sgp.dto.*;
import com.insgeek.business.quote.sgp.enums.*;
import com.insgeek.business.quote.sgp.mapper.QpQuotePersonsMapper;
import com.insgeek.business.quote.sgp.mapper.QpQuotePersonsOverviewMapper;
import com.insgeek.business.quote.sgp.mapper.QuoteSgpMapper;
import com.insgeek.business.quote.sgp.service.QuoteSgpPersonService;
import com.insgeek.business.quote.sgp.strategy.person.QuotePersonOverviewStrategy;
import com.insgeek.business.quote.sgp.strategy.person.QuotePersonOverviewStrategyFactory;
import com.insgeek.business.quote.sgp.validator.PersonValidator;
import com.insgeek.business.quote.util.CommonUtil;
import com.insgeek.business.quote.util.ExcelUtil;
import com.insgeek.business.quote.util.ZonedDateTimeUtil;
import com.insgeek.insurance.meta.utils.context.IdentityUtil;
import com.insgeek.protocol.common.client.FileClient;
import com.insgeek.protocol.common.dto.UploadResultDto;
import com.insgeek.protocol.data.client.entity.QpFileDetails;
import com.insgeek.protocol.data.client.entity.QpQuotePersons;
import com.insgeek.protocol.data.client.entity.QpQuotePersonsOverview;
import com.insgeek.protocol.data.client.enums.BenefitEnum;
import com.insgeek.protocol.insurance.client.DutyClient;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontProductDutyInstanceDTO;
import com.insgeek.protocol.insurance.dto.special.instance.ListParamDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.ss.util.CellReference;
import org.apache.poi.ss.util.CellUtil;
import org.apache.poi.xssf.usermodel.*;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class QuoteSgpPersonServiceImpl implements QuoteSgpPersonService {

    @Resource
    private QuoteSgpMapper quoteSgpMapper;

    @Resource
    private QpQuotePersonsMapper qpQuotePersonsMapper;

    @Resource
    private QpQuotePersonsOverviewMapper qpQuotePersonsOverviewMapper;

    @Resource
    private QuoteFileService quoteFileService;

    @Resource
    private DutyClient dutyClient;

    @Resource
    private FileClient fileClient;

    @Resource
    private PersonValidator personValidator;

    @Resource
    private QuotePersonOverviewStrategyFactory quotePersonOverviewStrategyFactory;

    @Override
    public void downloadTemplateFile(HttpServletResponse response, Long quoteInfoId) {
        // 根据quoteInfoId查询相关信息
        List<QuoteBenefitDto> quoteBenefitList = quoteSgpMapper.getQuoteBenefitList(quoteInfoId);
        if (CollectionUtil.isEmpty(quoteBenefitList)) {
            throw new QuoteException(MessageUtil.get("b_b_quote_sgp_051"));
        }
        try (InputStream in = new ClassPathResource(BizConstants.PersonsTemplate.TEMPLATE_PATH).getInputStream();
             XSSFWorkbook wb = new XSSFWorkbook(in);
             ServletOutputStream out = response.getOutputStream()) {

            // 获取模板第一个 Sheet
            XSSFSheet sheet = wb.getSheetAt(0);

            // 查找 Employee Category 列索引
            int empCategoryCol = ExcelUtil.findColumnByHeader(sheet, BizConstants.PersonsTemplate.HEADER_ROW - 1, BizConstants.PersonsTemplate.EMPLOYEE_CATEGORY_TITLE);
            // 查找 Effective Date 列索引
            int effectiveDateCol = ExcelUtil.findColumnByHeader(sheet, BizConstants.PersonsTemplate.HEADER_ROW - 1, BizConstants.PersonsTemplate.FIXED_SUBHEADER);

            // 构建 Coverage benefit 列表
            List<String> benefitList = QuoteBenefitDto.buildCoverageBenefitList(quoteBenefitList);
            // 构建动态列下拉列表选项
            Map<String, Map<String, List<String>>> categoryPlanMap = QuoteBenefitDto.buildCategoryAndBenefitMapping(quoteBenefitList);
            // 构建 Employee Category 下拉选项
            List<String> employeeCategoryOptionList = QuoteBenefitDto.buildEmployeeCategoryOptionList(quoteBenefitList);
            // 构建Employee Category map映射
            Map<String, String> employeeCategoryMapping = buildEmployeeCategoryCustomMapping(employeeCategoryOptionList);

            // 生成下拉选项隐藏 Sheet 和命名范围
            createHiddenSheetAndNamedRanges(wb, categoryPlanMap, employeeCategoryMapping);
            // 生成 Employee Category 下拉选项隐藏sheet，这里主要是为了后续公式里做转换
            createEmployeeCategoryHiddenSheet(wb, employeeCategoryMapping);

            // 为 Employee Category 列添加下拉列表
            addDropdownForEmployeeCategory(sheet, empCategoryCol, employeeCategoryOptionList);

            // 重建动态 Coverage Information 列
            rebuildCoverageColumns(wb, sheet, effectiveDateCol, benefitList);
            // 为动态列添加下拉列表
            addDropdownsForPlans(sheet, effectiveDateCol + 1, benefitList.size(), empCategoryCol);

            // 设置响应头并写出Excel
            String fileName = URLEncoder.encode("census_template.xlsx", StandardCharsets.UTF_8.name());
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename*=UTF-8''" + fileName);
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("UTF-8");

            wb.write(out);
            out.flush();
        } catch (Exception e) {
            log.error("模版文件下载异常", e);
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_sgp_019"));
        }
    }

    @Override
    public QpPersonCheckResultDto uploadTemplateFile(Long quoteInfoId, MultipartFile file) {
        // 模版校验
        checkTemplate(quoteInfoId, file);
        // 读取Excel文件数据
        List<Map<String, Object>> excelFileData = readExcelFileData(file);
        // 无人员数据时，返回错误
        if (CollectionUtil.isEmpty(excelFileData)) {
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_sgp_046"));
        }
        // 保存文件
        savePersonFile(quoteInfoId, file);
        // 数据校验
        return checkExcelFileData(quoteInfoId, file, excelFileData);
    }

    /**
     * 保存文件
     */
    private void savePersonFile(Long quoteInfoId, MultipartFile file) {
        // 先删除旧文件
        quoteFileService.deleteFiles(QpFileDetailsConst.BusType.QP_QUOTE_INFO.getCode(),
                quoteInfoId, Math.toIntExact(QpFileDetailsConst.BusClassify.QP_QUOTE_INFO_BC_102.getCode()));
        // 上传到oss
        List<UploadResultDto> uploadResultDtoList = upLoadFileToOss(file);
        String ossKey = uploadResultDtoList.get(0).getKey();
        // 保存文件
        QpFileDetails qpFileDetails = new QpFileDetails();
        qpFileDetails.setBusId(quoteInfoId);
        qpFileDetails.setBusType(QpFileDetailsConst.BusType.QP_QUOTE_INFO.getCode());
        qpFileDetails.setBusClassify(QpFileDetailsConst.BusClassify.QP_QUOTE_INFO_BC_102.getCode());
        qpFileDetails.setOssKey(ossKey);
        quoteFileService.saveFiles(QpFileDetailsConst.BusType.QP_QUOTE_INFO.getCode(), quoteInfoId, QpFileDetailsConst.BusClassify.QP_QUOTE_INFO_BC_102.getCode(), Collections.singletonList(qpFileDetails));
    }

    private List<UploadResultDto> upLoadFileToOss(MultipartFile multipartFile) {
        // 将生成的文件上传到OSS
        ResponseVO<List<UploadResultDto>> response = fileClient.uploadFiles(
                new MultipartFile[]{multipartFile},
                "quote",
                "quote_info"
        );
        log.info("文件上传完成，返回结果：{}", response);
        return CommonUtil.getResponseData(response);
    }

    @Override
    public QuoteSavePersonResultDto savePerson(QuoteSavePersonDto quoteSavePersonDto) {
        // 获取险种信息列表
        List<QuoteBenefitDto> quoteBenefitList = quoteSgpMapper.getQuoteBenefitList(quoteSavePersonDto.getQuoteInfoId());

        // 校验人员信息
        QpPersonCheckResultDto personCheckResult = checkPersonList(quoteSavePersonDto, quoteBenefitList);
        // 判断是否有错误，有错误直接返回
        if (personCheckResult.isHasError()) {
            // 构建返回结果对象
            QuoteSavePersonResultDto savePersonResult = new QuoteSavePersonResultDto();
            savePersonResult.setPersonCheckResult(personCheckResult);
            savePersonResult.setQuoteInfoId(quoteSavePersonDto.getQuoteInfoId());
            return savePersonResult;
        }

        // 构建人员列表
        List<QpQuotePersons> quotePersonList = buildQuotePersonList(quoteSavePersonDto, quoteBenefitList);

        // 构建统计信息
        List<QuotePersonOverviewDto> quotePersonsOverviewDtoList = buildQuotePersonsOverviewList(quoteSavePersonDto.getQuoteInfoId(), quoteBenefitList, quotePersonList);
        List<QpQuotePersonsOverview> quotePersonsOverviewList = convertToQuotePersonsOverviewList(quotePersonsOverviewDtoList);

        // 保存数据
        saveData(quoteSavePersonDto.getQuoteInfoId(), quotePersonList, quotePersonsOverviewList);

        QuoteSavePersonResultDto savePersonResult = new QuoteSavePersonResultDto();
        savePersonResult.setQuoteInfoId(quoteSavePersonDto.getQuoteInfoId());
        savePersonResult.setPersonCheckResult(personCheckResult);
        return savePersonResult;
    }

    @Override
    public QpPersonCheckResultDto getQuotePersonList(Long quoteInfoId) {
        // 查询人员列表
        List<QpQuotePersons> quotePersonList = qpQuotePersonsMapper.getQuotePersonList(quoteInfoId);
        // 根据quoteInfoId查询相关信息
        List<QuoteBenefitDto> quoteBenefitList = quoteSgpMapper.getQuoteBenefitList(quoteInfoId);
        // 询价单下面的所有险种列表
        List<String> benefitList = quoteBenefitList.stream().map(QuoteBenefitDto::getBenefit).distinct().collect(Collectors.toList());
        // 根据查询出的人员清单结果构建前端需要的结构
        List<Map<String, QpPersonCheckResultDto.FieldDetail>> personalList = new ArrayList<>();
        for (QpQuotePersons person : quotePersonList) {
            Map<String, QpPersonCheckResultDto.FieldDetail> personalInfo = buildPersonalInfo(person, quoteBenefitList, benefitList);
            personalList.add(personalInfo);
        }
        // 构建结果对象
        QpPersonCheckResultDto personCheckResultDto = new QpPersonCheckResultDto();
        personCheckResultDto.setPersonalList(personalList);
        boolean hasError = personCheckResultDto.hasError();
        personCheckResultDto.setHasError(hasError);
        return personCheckResultDto;
    }

    @Override
    public Map<String, Map<String, List<String>>> getQuoteBenefitDropdownList(Long quoteInfoId) {
        List<QuoteBenefitDto> quoteBenefitList = quoteSgpMapper.getQuoteBenefitList(quoteInfoId);
        return QuoteBenefitDto.buildCategoryAndBenefitMapping(quoteBenefitList);
    }

    /**
     * 构建人员信息
     */
    private Map<String, QpPersonCheckResultDto.FieldDetail> buildPersonalInfo(QpQuotePersons person, List<QuoteBenefitDto> quoteBenefitList, List<String> benefitList) {
        Map<String, QpPersonCheckResultDto.FieldDetail> personalInfo = new LinkedHashMap<>();
        personalInfo.put(BizConstants.PersonsTemplate.MEMBER_LAST_NAME, QpPersonCheckResultDto.FieldDetail.success(person.getMemberLastName()));
        personalInfo.put(BizConstants.PersonsTemplate.MEMBER_GIVEN_NAME, QpPersonCheckResultDto.FieldDetail.success(person.getMemberGivenName()));
        personalInfo.put(BizConstants.PersonsTemplate.MEMBER_TYPE, QpPersonCheckResultDto.FieldDetail.success(MemberTypeEnum.getCodeByValue(person.getMemberType())));
        personalInfo.put(BizConstants.PersonsTemplate.IDENTITY_TYPE, QpPersonCheckResultDto.FieldDetail.success(IdentityTypeEnum.getCodeByValue(person.getIdentityType())));
        personalInfo.put(BizConstants.PersonsTemplate.IDENTITY_NUMBER, QpPersonCheckResultDto.FieldDetail.success(person.getIdentityNumber()));
        personalInfo.put(BizConstants.PersonsTemplate.DATE_OF_BIRTH, QpPersonCheckResultDto.FieldDetail.success(ZonedDateTimeUtil.zonedDateTimeToStr(person.getDateOfBirth())));
        personalInfo.put(BizConstants.PersonsTemplate.GENDER, QpPersonCheckResultDto.FieldDetail.success(GenderEnum.getCodeByValue(person.getGender())));
        personalInfo.put(BizConstants.PersonsTemplate.EMPLOYEE_ID_NO, QpPersonCheckResultDto.FieldDetail.success(person.getEmployeeIdNo()));
        personalInfo.put(BizConstants.PersonsTemplate.NATIONALITY, QpPersonCheckResultDto.FieldDetail.success(person.getNationality()));
        personalInfo.put(BizConstants.PersonsTemplate.COUNTRY_OF_RESIDENCE, QpPersonCheckResultDto.FieldDetail.success(person.getCountryOfResidence()));
        personalInfo.put(BizConstants.PersonsTemplate.MARITAL_STATUS, QpPersonCheckResultDto.FieldDetail.success(MaritalStatusEnum.getCodeByValue(person.getMaritalStatus())));
        personalInfo.put(BizConstants.PersonsTemplate.OCCUPATIONAL_CLASS, QpPersonCheckResultDto.FieldDetail.success(OccupationalClassEnum.getCodeByValue(person.getOccupationalClass())));
        personalInfo.put(BizConstants.PersonsTemplate.EMPLOYMENT_DATE, QpPersonCheckResultDto.FieldDetail.success(ZonedDateTimeUtil.zonedDateTimeToStr(person.getEmploymentDate())));
        personalInfo.put(BizConstants.PersonsTemplate.EMPLOYEE_CATEGORY, QpPersonCheckResultDto.FieldDetail.success(person.getEmployeeCategory()));
        personalInfo.put(BizConstants.PersonsTemplate.EFFECTIVE_DATE, QpPersonCheckResultDto.FieldDetail.success(ZonedDateTimeUtil.zonedDateTimeToStr(person.getEffectiveDate())));
        // 处理动态列
        List<Long> configIdList = person.getQuoteConfigIdList();
        Map<String, QuoteBenefitDto> configIdBenefitMapping = quoteBenefitList.stream()
                .filter(quoteBenefit -> configIdList.contains(quoteBenefit.getQuoteConfigId()))
                .collect(Collectors.toMap(
                        QuoteBenefitDto::getBenefit,
                        Function.identity(),
                        (oldValue, newValue) -> oldValue
                ));
        for (String benefit : benefitList) {
            if (configIdBenefitMapping.containsKey(benefit)) {
                personalInfo.put(benefit.toLowerCase(Locale.ROOT), QpPersonCheckResultDto.FieldDetail.success(configIdBenefitMapping.get(benefit).getQuoteName()));
            } else {
                personalInfo.put(benefit.toLowerCase(Locale.ROOT), QpPersonCheckResultDto.FieldDetail.success(null));
            }
        }
        return personalInfo;
    }

    /**
     * 转换为QpQuotePersonsOverview列表
     */
    private List<QpQuotePersonsOverview> convertToQuotePersonsOverviewList(List<QuotePersonOverviewDto> quotePersonsOverviewDtoList) {
        List<QpQuotePersonsOverview> quotePersonsOverviewList = new ArrayList<>();
        for (QuotePersonOverviewDto quotePersonOverviewDto : quotePersonsOverviewDtoList) {
            QpQuotePersonsOverview qpQuotePersonsOverview = new QpQuotePersonsOverview();
            qpQuotePersonsOverview.setQuoteInfoId(quotePersonOverviewDto.getQuoteInfoId());
            qpQuotePersonsOverview.setTag(quotePersonOverviewDto.getTag());
            qpQuotePersonsOverview.setItemKey(quotePersonOverviewDto.getItemKey());
            qpQuotePersonsOverview.setValue(quotePersonOverviewDto.getValue());
            quotePersonsOverviewList.add(qpQuotePersonsOverview);
        }
        return quotePersonsOverviewList;
    }

    @Override
    public List<QuotePersonOverviewDto> getQuotePersonOverviewList(Long quoteInfoId) {
        List<QuotePersonOverviewDto> quotePersonOverviewList = qpQuotePersonsOverviewMapper.getQuotePersonOverviewList(quoteInfoId);
        // 如果首次查询统计信息为空，则构建一份险种统计信息
        if (CollectionUtil.isEmpty(quotePersonOverviewList)) {
            // 获取险种信息列表
            List<QuoteBenefitDto> quoteBenefitList = quoteSgpMapper.getQuoteBenefitList(quoteInfoId);
            // 构建人员统计信息
            quotePersonOverviewList = buildQuotePersonsOverviewList(quoteInfoId, quoteBenefitList, Collections.emptyList());
        }
        return quotePersonOverviewList;
    }

    @Override
    public Long savePersonOverview(QuoteSavePersonOverviewDto quoteSavePersonOverviewDto) {
        List<QpQuotePersonsOverview> insertList = new ArrayList<>();
        for (QuotePersonOverviewDto quotePersonOverviewDto : quoteSavePersonOverviewDto.getPersonOverviewList()) {
            QpQuotePersonsOverview qpQuotePersonsOverview = new QpQuotePersonsOverview();
            qpQuotePersonsOverview.setQuoteInfoId(quoteSavePersonOverviewDto.getQuoteInfoId());
            qpQuotePersonsOverview.setTag(quotePersonOverviewDto.getTag());
            qpQuotePersonsOverview.setItemKey(quotePersonOverviewDto.getItemKey());
            qpQuotePersonsOverview.setValue(quotePersonOverviewDto.getValue());

            insertList.add(qpQuotePersonsOverview);
        }
        if (CollectionUtil.isNotEmpty(insertList)) {
            // 先删除在保存
            qpQuotePersonsOverviewMapper.deleteByQuoteId(quoteSavePersonOverviewDto.getQuoteInfoId());
            qpQuotePersonsOverviewMapper.insertAll(insertList);
        }
        return quoteSavePersonOverviewDto.getQuoteInfoId();
    }

    /**
     * 构建人员统计信息列表
     */
    private List<QuotePersonOverviewDto> buildQuotePersonsOverviewList(Long quoteInfoId,
                                                                       List<QuoteBenefitDto> quoteBenefitList,
                                                                       List<QpQuotePersons> quotePersonList) {
        // 获取quoteConfigId列表
        List<Long> quoteConfigIdList = quoteBenefitList.stream()
                .map(QuoteBenefitDto::getQuoteConfigId)
                .filter(Objects::nonNull)
                .distinct()
                .collect(Collectors.toList());
        // 获取配置的责任信息
        Map<Long, List<FrontProductDutyInstanceDTO>> frontProductListMap = getFrontProductListMap(quoteConfigIdList);
        // 根据configId分组
        Map<Long, QuoteBenefitDto> configBenefitMapping = QuoteBenefitDto.buildConfigMapping(quoteBenefitList);
        // 根据benefit分组
        Map<String, List<QpQuotePersons>> benefitPersonMapping = groupByBenefit(quotePersonList, configBenefitMapping);

        // 构建险种列表
        List<String> benefitList = QuoteBenefitDto.buildCoverageBenefitList(quoteBenefitList);

        // 根据险种走策略生成统计信息
        List<QuotePersonOverviewDto> quotePersonsOverviewList = new ArrayList<>();
        for (String benefit : benefitList) {
            // 获取人员列表
            List<QpQuotePersons> quotePersonsList = benefitPersonMapping.getOrDefault(benefit, Collections.emptyList());
            // 过滤
            List<QuoteBenefitDto> benefitDtoList = quoteBenefitList.stream().filter(q -> q.getBenefit().equals(benefit)).collect(Collectors.toList());
            // 构建统计信息
            List<QuotePersonOverviewDto> personsOverviewList = quotePersonOverviewStrategyFactory.buildPersonOverview(quoteInfoId, benefit, benefitDtoList, quotePersonsList, frontProductListMap);
            quotePersonsOverviewList.addAll(personsOverviewList);
        }

        // 个别情况需要特殊合并
        List<QuotePersonOverviewDto> personOverviewDtoList = buildGroupOutpatientInsurancePersonOverviewList(quoteInfoId, configBenefitMapping, quoteBenefitList, quotePersonList, frontProductListMap);
        quotePersonsOverviewList.addAll(personOverviewDtoList);
        return quotePersonsOverviewList;
    }

    /**
     * 构建 Group Outpatient Insurance
     */
    private List<QuotePersonOverviewDto> buildGroupOutpatientInsurancePersonOverviewList(Long quoteInfoId,
                                                                                         Map<Long, QuoteBenefitDto> configBenefitMapping,
                                                                                         List<QuoteBenefitDto> quoteBenefitList,
                                                                                         List<QpQuotePersons> quotePersonList,
                                                                                         Map<Long, List<FrontProductDutyInstanceDTO>> frontProductListMap) {
        // 需要合并的险种编码
        Set<String> benefitCodeSet = CollUtil.newHashSet(
                BenefitEnum.GP.getCode(),
                BenefitEnum.SP.getCode(),
                BenefitEnum.GD.getCode()
        );
        // 将要合并的configId过滤出来
        Set<Long> configIdSet = configBenefitMapping.entrySet()
                .stream()
                .filter(entry -> benefitCodeSet.contains(entry.getValue().getBenefit()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toSet());
        if (CollectionUtil.isEmpty(configIdSet)) {
            return Collections.emptyList();
        }
        // 对应的人员合并（含去重逻辑）
        List<QpQuotePersons> quotePersonsList = quotePersonList.stream()
                // quoteConfigIdList 不为空 且 与 configIdSet 有交集
                .filter(person -> CollectionUtil.isNotEmpty(person.getQuoteConfigIdList())
                        && person.getQuoteConfigIdList().stream()
                        .anyMatch(configIdSet::contains))
                .collect(Collectors.toList());
        // 对应的险种信息合并
        List<QuoteBenefitDto> benefitDtoList = quoteBenefitList.stream()
                .filter(q -> benefitCodeSet.contains(q.getBenefit()))
                .collect(Collectors.toList());
        // 构建统计信息
        QuotePersonOverviewStrategy personOverviewStrategy = quotePersonOverviewStrategyFactory.getPersonOverviewStrategyByType(BizConstants.PersonOverviewStrategyCustomSupportType.GROUP_OUTPATIENT_INSURANCE);
        return personOverviewStrategy.buildPersonOverview(quoteInfoId, benefitDtoList, quotePersonsList, frontProductListMap);
    }

    /**
     * 获取配置的责任信息
     */
    private Map<Long, List<FrontProductDutyInstanceDTO>> getFrontProductListMap(List<Long> quoteConfigIdList) {
        ListParamDTO listParam = new ListParamDTO();
        listParam.setScene(2);
        listParam.setType(2);
        listParam.setIdList(quoteConfigIdList);
        // 适配外部版查询责任
        IdentityUtil.setRobotAuth();
        ResponseVO<List<FrontProductDutyInstanceDTO>> resp = dutyClient.batchGetDutyListByConfigForFront(listParam.getScene(), 2, listParam);
        List<FrontProductDutyInstanceDTO> frontProductDutyInstanceDTOList = CommonUtil.getResponseData(resp);
        return frontProductDutyInstanceDTOList
                .stream().collect(Collectors.groupingBy(frontProductDutyInstanceDTO -> Long.valueOf(String.valueOf(frontProductDutyInstanceDTO.getDutyField().get("quote_config_id")))));
    }

    /**
     * 保存数据
     */
    private void saveData(Long quoteInfoId,
                          List<QpQuotePersons> quotePersonList,
                          List<QpQuotePersonsOverview> quotePersonsOverviewList) {
        // 先删除再保存
        qpQuotePersonsMapper.deleteByQuoteInfoId(quoteInfoId);
        qpQuotePersonsOverviewMapper.deleteByQuoteId(quoteInfoId);
        if (CollectionUtil.isNotEmpty(quotePersonList)) {
            qpQuotePersonsMapper.insertAll(quotePersonList);
        }
        if (CollectionUtil.isNotEmpty(quotePersonsOverviewList)) {
            qpQuotePersonsOverviewMapper.insertAll(quotePersonsOverviewList);
        }
    }

    /**
     * 按照 QpQuotePersons 中的 quoteConfigIdList 对应的 benefit 进行分组
     *
     * @param quotePersonsList QpQuotePersons 对象列表
     * @param configMapping    Map，key = configId，value = QuoteBenefitDto
     * @return Map，key = benefit 字段，value = 包含该 benefit 的 QpQuotePersons 列表
     */
    public Map<String, List<QpQuotePersons>> groupByBenefit(List<QpQuotePersons> quotePersonsList,
                                                            Map<Long, QuoteBenefitDto> configMapping) {

        return quotePersonsList.stream()
                // 将每个 QpQuotePersons 对象的 quoteConfigIdList 展开成 (configId, person) 键值对
                .flatMap(person -> {
                    List<Long> configIdList = person.getQuoteConfigIdList();
                    if (CollectionUtil.isEmpty(configIdList)) {
                        // 如果当前对象的 quoteConfigIdList 为空，则返回空流，防止报错
                        return Stream.empty();
                    }
                    // 将每个 configId 和对应 person 对象组成键值对
                    return configIdList.stream()
                            .map(configId -> new AbstractMap.SimpleEntry<>(configId, person));
                })
                // 过滤掉 configMapping 中不存在的 configId，避免空指针
                .filter(entry -> configMapping.containsKey(entry.getKey()))
                // 按照 configId 对应的 benefit 字段进行分组
                .collect(Collectors.groupingBy(
                        entry -> configMapping.get(entry.getKey()).getBenefit(),  // key = benefit
                        Collectors.mapping(
                                // value = QpQuotePersons 对象
                                Map.Entry::getValue,
                                // 收集成列表
                                Collectors.toList()
                        )
                ));
    }

    /**
     * 构建人员列表
     */
    private List<QpQuotePersons> buildQuotePersonList(QuoteSavePersonDto quoteSavePersonDto,
                                                      List<QuoteBenefitDto> quoteBenefitList) {
        // 构建险种信息映射
        Map<String, QuoteBenefitDto> benefitMapping = QuoteBenefitDto.buildBenefitMapping(quoteBenefitList);

        List<Map<String, Object>> personalList = quoteSavePersonDto.getPersonalList();
        List<String> benefitList = buildBenefitList(personalList);
        List<QpQuotePersons> quotePersonList = new ArrayList<>();
        for (int index = 0; index < personalList.size(); index++) {
            QpQuotePersons qpQuotePersons = buildQuotePerson(index, quoteSavePersonDto.getQuoteInfoId(), personalList.get(index), benefitList, benefitMapping);
            quotePersonList.add(qpQuotePersons);
        }
        return quotePersonList;
    }

    /**
     * 构建人员对象
     */
    private QpQuotePersons buildQuotePerson(int index,
                                            Long quoteInfoId,
                                            Map<String, Object> personalInfo,
                                            List<String> benefitList,
                                            Map<String, QuoteBenefitDto> benefitMapping) {
        QpQuotePersons qpQuotePersons = new QpQuotePersons();
        qpQuotePersons.setQuoteInfoId(quoteInfoId);
        qpQuotePersons.setSerialNo(String.valueOf(index));
        qpQuotePersons.setMemberLastName(MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.MEMBER_LAST_NAME));
        qpQuotePersons.setMemberGivenName(MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.MEMBER_GIVEN_NAME));
        qpQuotePersons.setMemberType(MemberTypeEnum.getValueByCode(MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.MEMBER_TYPE)));
        qpQuotePersons.setIdentityType(IdentityTypeEnum.getValueByCode(MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.IDENTITY_TYPE)));
        qpQuotePersons.setIdentityNumber(MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.IDENTITY_NUMBER));
        qpQuotePersons.setDateOfBirth(ZonedDateTimeUtil.zonedDateTimeOf(MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.DATE_OF_BIRTH)));
        qpQuotePersons.setGender(GenderEnum.getValueByCode(MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.GENDER)));
        qpQuotePersons.setEmployeeIdNo(MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.EMPLOYEE_ID_NO));
        qpQuotePersons.setNationality(MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.NATIONALITY));
        qpQuotePersons.setCountryOfResidence(MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.COUNTRY_OF_RESIDENCE));
        qpQuotePersons.setMaritalStatus(MaritalStatusEnum.getValueByCode(MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.MARITAL_STATUS)));
        qpQuotePersons.setOccupationalClass(OccupationalClassEnum.getValueByCode(MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.OCCUPATIONAL_CLASS)));
        qpQuotePersons.setEmploymentDate(ZonedDateTimeUtil.zonedDateTimeOf(MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.EMPLOYMENT_DATE)));
        qpQuotePersons.setEmployeeCategory(MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.EMPLOYEE_CATEGORY));
        qpQuotePersons.setEffectiveDate(ZonedDateTimeUtil.zonedDateTimeOf(MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.EFFECTIVE_DATE)));
        List<Long> quoteConfigIdList = new ArrayList<>();
        // 处理险种
        for (String benefit : benefitList) {
            String benefitPlan = MapUtil.getStr(personalInfo, benefit);
            if (benefitMapping.containsKey(benefit + "#" + benefitPlan + "#" + MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.EMPLOYEE_CATEGORY))) {
                QuoteBenefitDto quoteBenefitDto = benefitMapping.get(benefit + "#" + benefitPlan + "#" + MapUtil.getStr(personalInfo, BizConstants.PersonsTemplate.EMPLOYEE_CATEGORY));
                quoteConfigIdList.add(quoteBenefitDto.getQuoteConfigId());
            }
        }
        qpQuotePersons.setQuoteConfigIdList(quoteConfigIdList);
        return qpQuotePersons;
    }


    /**
     * 校验人员信息
     */
    private QpPersonCheckResultDto checkPersonList(QuoteSavePersonDto quoteSavePersonDto,
                                                   List<QuoteBenefitDto> quoteBenefitList) {
        // 构建人员信息校验结果
        QpPersonCheckResultDto personCheckResultDto = new QpPersonCheckResultDto();
        // 获取人员信息
        List<Map<String, Object>> personalList = quoteSavePersonDto.getPersonalList();
        if (CollectionUtil.isEmpty(personalList)) {
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_sgp_042"));
        }
        // 获取险种列表
        List<String> benefitList = buildBenefitList(personalList);
        if (CollectionUtil.isEmpty(benefitList)) {
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_sgp_043"));
        }
        // 校验人员信息
        List<Map<String, QpPersonCheckResultDto.FieldDetail>> personalCheckResultList = doCheckPersonList(personalList, benefitList, quoteBenefitList);
        personCheckResultDto.setPersonalList(personalCheckResultList);
        boolean hasError = personCheckResultDto.hasError();
        personCheckResultDto.setHasError(hasError);
        return personCheckResultDto;
    }

    /**
     * 校验人员信息
     */
    private List<Map<String, QpPersonCheckResultDto.FieldDetail>> doCheckPersonList(List<Map<String, Object>> personalList,
                                                                                    List<String> benefitList,
                                                                                    List<QuoteBenefitDto> quoteBenefitList) {
        List<Map<String, QpPersonCheckResultDto.FieldDetail>> personalCheckResultList = new ArrayList<>();
        for (Map<String, Object> personal : personalList) {
            personalCheckResultList.add(personValidator.validate(personal, benefitList, quoteBenefitList));
        }
        return personalCheckResultList;
    }

    /**
     * 构建险种列表
     */
    private List<String> buildBenefitList(List<Map<String, Object>> personalList) {
        // 险种枚举的 code 转小写，放入 Set
        Set<String> benefitEnumCodeSet = Arrays.stream(BenefitEnum.values())
                .map(e -> e.getCode().toLowerCase(Locale.ROOT))
                .collect(Collectors.toSet());
        // 获取map的所有key后进行过滤，只保留在险种列表的key
        return personalList.stream()
                .findFirst()
                .map(Map::keySet)
                .orElse(Collections.emptySet())
                .stream()
                .filter(k -> benefitEnumCodeSet.contains(k.toLowerCase(Locale.ROOT)))
                .collect(Collectors.toList());
    }

    /**
     * 校验Excel文件数据
     */
    private QpPersonCheckResultDto checkExcelFileData(Long quoteInfoId,
                                                      MultipartFile file,
                                                      List<Map<String, Object>> excelFileData) {
        // 获取Coverage Benefit表头
        List<String> coverageBenefitHeaderList = getCoverageBenefitHeadersFromExcelFile(file);

        QpPersonCheckResultDto result = new QpPersonCheckResultDto();
        List<Map<String, QpPersonCheckResultDto.FieldDetail>> personalList = new ArrayList<>();

        // 根据quoteInfoId查询相关信息
        List<QuoteBenefitDto> quoteBenefitList = quoteSgpMapper.getQuoteBenefitList(quoteInfoId);
        // 循环校验
        for (Map<String, Object> personalInfo : excelFileData) {
            // 校验
            LinkedHashMap<String, QpPersonCheckResultDto.FieldDetail> personalInfoFieldDetail = personValidator.validate(personalInfo, coverageBenefitHeaderList, quoteBenefitList);
            personalList.add(personalInfoFieldDetail);
        }

        // 设置结果
        result.setPersonalList(personalList);
        result.setCommonError(Collections.emptyList());
        result.setHasError(result.hasError());

        return result;
    }


    /**
     * 获取Coverage Benefit的表头
     */
    private List<String> getCoverageBenefitHeadersFromExcelFile(MultipartFile file) {
        try (InputStream is = file.getInputStream(); Workbook workbook = new XSSFWorkbook(is)) {
            Sheet sheet = workbook.getSheetAt(0);

            // 读取表头
            Row headerRow = sheet.getRow(BizConstants.PersonsTemplate.HEADER_ROW - 1);
            int effectiveDateCol = ExcelUtil.findColumnByHeader(sheet, BizConstants.PersonsTemplate.HEADER_ROW - 1, BizConstants.PersonsTemplate.FIXED_SUBHEADER);
            List<String> coverageBenefitHeaderList = new ArrayList<>();
            // 读取effectiveDateCol后的表头
            for (int i = effectiveDateCol + 1; i <= headerRow.getLastCellNum(); i++) {
                String cellString = ExcelUtil.getCellString(headerRow.getCell(i));
                if (Objects.isNull(cellString)) {
                    break;
                }
                coverageBenefitHeaderList.add(cellString);
            }
            return coverageBenefitHeaderList;
        } catch (Exception e) {
            log.error("解析模版文件异常", e);
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_sgp_041"));
        }
    }

    /**
     * 读取Excel文件数据
     */
    private List<Map<String, Object>> readExcelFileData(MultipartFile file) {
        List<Map<String, Object>> resultList = new ArrayList<>();

        try (InputStream is = file.getInputStream(); Workbook workbook = new XSSFWorkbook(is)) {
            Sheet sheet = workbook.getSheetAt(0);

            // 读取表头
            Row headerRow = sheet.getRow(BizConstants.PersonsTemplate.HEADER_ROW - 1);
            List<String> headers = new ArrayList<>();
            for (Cell cell : headerRow) {
                headers.add(cell.getStringCellValue().trim());
            }

            // 遍历数据行
            for (int i = BizConstants.PersonsTemplate.DATA_START_ROW - 1; i <= sheet.getLastRowNum(); i++) {
                // 获取数据行
                Row row = sheet.getRow(i);
                if (row == null) {
                    continue;
                }
                // 构建人员信息
                Map<String, Object> personalInfo = buildPersonalInfo(row, headers);
                // 跳过空行
                if (Objects.isNull(personalInfo)) {
                    continue;
                }
                resultList.add(personalInfo);
            }

        } catch (Exception e) {
            log.error("解析模版文件异常", e);
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_sgp_041"));
        }
        return resultList;
    }

    /**
     * 模版校验
     */
    private void checkTemplate(Long quoteInfoId, MultipartFile file) {
        // 固定列
        List<String> memberFields = new ArrayList<>(Arrays.asList(
                "Member Last Name\n(Surname)",
                "Member Given Name\n(First Name)",
                "Member Type\n(E, S or C)",
                "Identity Type",
                "Identity  Number\n(As per NRIC/ FIN)",
                "Date of Birth\n(DD/MM/YYYY)",
                "Gender\n(M/F)",
                "Employee ID No. \n(NRIC/ FIN)",
                "Nationality",
                "Country of Residence",
                "Marital Status \n(S, M, D, W)",
                "Occupational Class",
                "Employment Date\n(DD/MM/YYYY)",
                "Employee Category",
                "Effective Date\n(DD/MM/YYYY)"
        ));

        // 从excel中获取表头
        List<String> headers = getHeadersFromExcelFile(file);
        // 根据quoteInfoId查询相关信息
        List<QuoteBenefitDto> quoteBenefitList = quoteSgpMapper.getQuoteBenefitList(quoteInfoId);
        List<String> benefitList = QuoteBenefitDto.buildCoverageBenefitList(quoteBenefitList);

        // 先比对固定列，再比对动态列
        // 合并固定列和动态列
        List<String> expectedHeaders = new ArrayList<>();
        expectedHeaders.addAll(memberFields);
        expectedHeaders.addAll(benefitList);

        // 检查传入的表头和预期表头是否一致
        if (headers.size() != expectedHeaders.size()) {
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_sgp_050"));
        }

        for (int i = 0; i < expectedHeaders.size(); i++) {
            String expected = expectedHeaders.get(i);
            String actual = headers.get(i);
            if (!expected.equals(actual)) {
                throw new QuoteException(-1, MessageUtil.get("b_b_quote_sgp_050"));
            }
        }

    }

    /**
     * 获取Excel文件的表头
     */
    private List<String> getHeadersFromExcelFile(MultipartFile file) {
        try (InputStream is = file.getInputStream(); Workbook workbook = new XSSFWorkbook(is)) {
            Sheet sheet = workbook.getSheetAt(0);

            // 读取表头
            Row headerRow = sheet.getRow(BizConstants.PersonsTemplate.HEADER_ROW - 1);
            List<String> headers = new ArrayList<>();
            for (Cell cell : headerRow) {
                headers.add(cell.getStringCellValue().trim());
            }

            return headers;
        } catch (Exception e) {
            log.error("解析模版文件异常", e);
            throw new QuoteException(-1, MessageUtil.get("b_b_quote_sgp_041"));
        }
    }

    /**
     * 构建人员信息
     */
    private Map<String, Object> buildPersonalInfo(Row row,
                                                  List<String> headers) {
        Map<String, Object> personalInfo = new LinkedHashMap<>();
        // 固定列赋值
        // 设置索引值，中间新增列无需对全部索引值进行修改
        // 这里的顺序需保证和模版的顺序严格一致
        int index = 0;
        personalInfo.put(BizConstants.PersonsTemplate.MEMBER_LAST_NAME, ExcelUtil.getCellString(row.getCell(index++)));
        personalInfo.put(BizConstants.PersonsTemplate.MEMBER_GIVEN_NAME, ExcelUtil.getCellString(row.getCell(index++)));
        personalInfo.put(BizConstants.PersonsTemplate.MEMBER_TYPE, ExcelUtil.getCellString(row.getCell(index++)));
        personalInfo.put(BizConstants.PersonsTemplate.IDENTITY_TYPE, ExcelUtil.getCellString(row.getCell(index++)));
        personalInfo.put(BizConstants.PersonsTemplate.IDENTITY_NUMBER, ExcelUtil.getCellString(row.getCell(index++)));
        personalInfo.put(BizConstants.PersonsTemplate.DATE_OF_BIRTH, ExcelUtil.getCellString(row.getCell(index++)));
        personalInfo.put(BizConstants.PersonsTemplate.GENDER, ExcelUtil.getCellString(row.getCell(index++)));
        personalInfo.put(BizConstants.PersonsTemplate.EMPLOYEE_ID_NO, ExcelUtil.getCellString(row.getCell(index++)));
        personalInfo.put(BizConstants.PersonsTemplate.NATIONALITY, ExcelUtil.getCellString(row.getCell(index++)));
        personalInfo.put(BizConstants.PersonsTemplate.COUNTRY_OF_RESIDENCE, ExcelUtil.getCellString(row.getCell(index++)));
        personalInfo.put(BizConstants.PersonsTemplate.MARITAL_STATUS, ExcelUtil.getCellString(row.getCell(index++)));
        personalInfo.put(BizConstants.PersonsTemplate.OCCUPATIONAL_CLASS, ExcelUtil.getCellString(row.getCell(index++)));
        personalInfo.put(BizConstants.PersonsTemplate.EMPLOYMENT_DATE, ExcelUtil.getCellString(row.getCell(index++)));
        personalInfo.put(BizConstants.PersonsTemplate.EMPLOYEE_CATEGORY, ExcelUtil.getCellString(row.getCell(index++)));
        personalInfo.put(BizConstants.PersonsTemplate.EFFECTIVE_DATE, ExcelUtil.getCellString(row.getCell(index++)));
        // 遍历表头，取动态列的表头转小写后放入map
        for (int i = index; i < headers.size(); i++) {
            personalInfo.put(headers.get(i).toLowerCase(Locale.ROOT), ExcelUtil.getCellString(row.getCell(i)));
        }
        // 检查是否所有列都为空
        boolean allEmpty = personalInfo.values().stream()
                .allMatch(value -> Objects.isNull(value) || StrUtil.isBlank(String.valueOf(value).trim()));
        // 如果都为空说明是空行
        if (allEmpty) {
            return null;
        }
        return personalInfo;
    }

    /**
     * 重建 Coverage Information 动态列
     * 每个动态列表头行与下一行垂直合并，说明行使用 Effective Date 列的说明行样式
     */
    private void rebuildCoverageColumns(XSSFWorkbook wb,
                                        XSSFSheet sheet,
                                        int effectiveDateCol,
                                        List<String> benefitList) {
        int firstDynamicCol = effectiveDateCol + 1;

        // 向右移动已有列，为新动态列腾出空间
        Row headerRow = sheet.getRow(BizConstants.PersonsTemplate.HEADER_ROW - 1);
        if (headerRow != null && CollectionUtil.isNotEmpty(benefitList)) {
            int lastColIndex = headerRow.getLastCellNum() - 1;
            if (firstDynamicCol <= lastColIndex) {
                sheet.shiftColumns(firstDynamicCol, lastColIndex, benefitList.size());
            }
        }

        // 获取模板样式
        CellStyle headerStyleTemplate = ExcelUtil.getStyleFromOrRightOf(sheet, BizConstants.PersonsTemplate.HEADER_ROW - 1, effectiveDateCol);

        // 获取 Effective Date 对应说明行样式，用于说明行
        CellStyle instructionStyleTemplate = ExcelUtil.getStyleFromOrRightOf(sheet, BizConstants.PersonsTemplate.INSTRUCTION_ROW - 1, effectiveDateCol);

        // 获取批注相关的工具对象
        Drawing<?> drawing = sheet.createDrawingPatriarch();
        CreationHelper factory = wb.getCreationHelper();

        // 遍历每个 Plan 列
        for (int i = 0; i < benefitList.size(); i++) {
            String benefit = benefitList.get(i);
            int col = firstDynamicCol + i;

            // 写入表头文本（两行合并显示 benefit 名）
            Row header = CellUtil.getRow(BizConstants.PersonsTemplate.HEADER_ROW - 1, sheet);
            Cell headerCell = CellUtil.getCell(header, col);
            headerCell.setCellValue(benefit);

            // 克隆表头样式并添加边框
            CellStyle headerStyle = ExcelUtil.clone(wb, headerStyleTemplate);
            headerStyle.setBorderTop(BorderStyle.THIN);
            headerStyle.setBorderBottom(BorderStyle.THIN);
            headerStyle.setBorderLeft(BorderStyle.THIN);
            headerStyle.setBorderRight(BorderStyle.THIN);

            // 克隆说明行样式并添加边框
            CellStyle instructionStyle = ExcelUtil.clone(wb, instructionStyleTemplate);
            instructionStyle.setBorderTop(BorderStyle.THIN);
            instructionStyle.setBorderBottom(BorderStyle.THIN);
            instructionStyle.setBorderLeft(BorderStyle.THIN);
            instructionStyle.setBorderRight(BorderStyle.THIN);

            // 合并表头行和下一行
            CellRangeAddress mergedRegion = new CellRangeAddress(
                    BizConstants.PersonsTemplate.HEADER_ROW - 1, // 起始行
                    BizConstants.PersonsTemplate.HEADER_ROW,     // 结束行
                    col,            // 起始列
                    col             // 结束列
            );
            sheet.addMergedRegion(mergedRegion);

            // 遍历合并区域的每个单元格，应用表头样式（保证边框完整）
            for (int r = mergedRegion.getFirstRow(); r <= mergedRegion.getLastRow(); r++) {
                Row row = CellUtil.getRow(r, sheet);
                Cell cell = CellUtil.getCell(row, col);
                cell.setCellStyle(headerStyle);
            }

            // 设置说明行样式（Effective Date 对应列）
            Row instructionRow = CellUtil.getRow(BizConstants.PersonsTemplate.INSTRUCTION_ROW - 1, sheet);
            Cell instructionCell = CellUtil.getCell(instructionRow, col);
            instructionCell.setCellStyle(instructionStyle);

            // 添加批注
            String commentText = BenefitEnum.getDescByCode(benefit);
            if (StrUtil.isNotBlank(commentText)) {
                // 定义批注位置
                ClientAnchor anchor = factory.createClientAnchor();
                // 设置批注框左上角的起始列
                anchor.setCol1(col);
                // 设置批注框右下角的结束列
                anchor.setCol2(col + 1);
                // 设置批注框左上角的起始行
                anchor.setRow1(BizConstants.PersonsTemplate.HEADER_ROW - 1);
                // 设置批注框右下角的结束行
                anchor.setRow2(BizConstants.PersonsTemplate.HEADER_ROW + 1);
                // 创建批注
                Comment comment = drawing.createCellComment(anchor);
                comment.setString(factory.createRichTextString(commentText));
                // 设置批注
                headerCell.setCellComment(comment);
            }
        }

        // 扩展 Coverage Information 合并单元格
        ExcelUtil.expandOrCreateMergedRegion(
                sheet,
                BizConstants.PersonsTemplate.HEADER_GROUP_ROW - 1,
                effectiveDateCol,
                benefitList.size() + 1,
                BizConstants.PersonsTemplate.GROUP_TITLE
        );

    }

    /**
     * 创建隐藏的下拉数据源表，并生成命名范围
     *
     * @param wb                      Excel 工作簿对象
     * @param categoryPlanMap         数据结构：Map<EmployeeCategory, Map<Benefit, List<PlanName>>>
     * @param employeeCategoryMapping
     */
    private void createHiddenSheetAndNamedRanges(XSSFWorkbook wb,
                                                 Map<String, Map<String, List<String>>> categoryPlanMap,
                                                 Map<String, String> employeeCategoryMapping) {

        // 创建隐藏的 sheet，用于存放下拉列表数据
        XSSFSheet sheet = wb.createSheet(BizConstants.PersonsTemplate.BENEFIT_DROP_DOWN_OPTION_LIST);
        // 隐藏 sheet，不让用户看到
        wb.setSheetHidden(wb.getSheetIndex(sheet), true);

        // 创建文本格式样式
        XSSFCellStyle textStyle = wb.createCellStyle();
        XSSFDataFormat format = wb.createDataFormat();
        // "@" 表示文本格式
        textStyle.setDataFormat(format.getFormat("@"));

        // 当前写入的行号，从 0 开始
        int rowIdx = 0;

        // 写表头，第一行固定三个列：EmployeeCategory | Benefit | PlanName
        Row header = sheet.createRow(rowIdx++);
        Cell headerCell0 = header.createCell(0);
        headerCell0.setCellStyle(textStyle);
        headerCell0.setCellValue("EmployeeCategory");

        Cell headerCell1 = header.createCell(1);
        headerCell1.setCellStyle(textStyle);
        headerCell1.setCellValue("Benefit");

        Cell headerCell2 = header.createCell(2);
        headerCell2.setCellStyle(textStyle);
        headerCell2.setCellValue("PlanName");

        // 用来记录每个 Category+Benefit 对应的起始行和结束行
        // key = Category_Benefit, value = [起始行, 结束行]
        Map<String, int[]> rangeMap = new HashMap<>();

        // 遍历整个数据结构，逐条生成数据行
        for (Map.Entry<String, Map<String, List<String>>> catEntry : categoryPlanMap.entrySet()) {
            String employeeCategory = catEntry.getKey();
            Map<String, List<String>> benefitMap = catEntry.getValue();

            // 遍历每个 benefit
            for (Map.Entry<String, List<String>> benefitEntry : benefitMap.entrySet()) {
                String benefit = benefitEntry.getKey();
                List<String> planList = benefitEntry.getValue();

                // 记录当前 Category+Benefit 的起始行
                // Excel 文件内显示的行号是从 1 开始，所以 +1
                int startRow = rowIdx + 1;

                // 遍历 PlanName，每条生成一行
                for (String plan : planList) {
                    Row row = sheet.createRow(rowIdx++);

                    Cell cell0 = row.createCell(0);
                    cell0.setCellStyle(textStyle);
                    cell0.setCellValue(employeeCategory);

                    Cell cell1 = row.createCell(1);
                    cell1.setCellStyle(textStyle);
                    cell1.setCellValue(benefit);

                    Cell cell2 = row.createCell(2);
                    cell2.setCellStyle(textStyle);
                    cell2.setCellValue(plan);
                }

                // 记录结束行
                // Excel 行号从 1 开始，rowIdx 已经指向下一行
                int endRow = rowIdx;

                // 保存到 rangeMap，key 用 Category_Benefit 命名规则
                String rangeName = benefit + "_" + employeeCategoryMapping.get(employeeCategory);
                rangeMap.put(rangeName, new int[]{startRow, endRow});
            }
        }

        // 根据 rangeMap 创建命名范围
        for (Map.Entry<String, int[]> entry : rangeMap.entrySet()) {
            String rangeName = entry.getKey();
            int startRow = entry.getValue()[0];
            int endRow = entry.getValue()[1];

            Name name = wb.createName();
            name.setNameName(rangeName);

            // 第三列是 PlanName (C列)，列号 2 表示 Excel 列字母 C
            String colLetter = CellReference.convertNumToColString(2);

            // 生成引用区域，例如 "BenefitDropdownOptionList!$C$2:$C$3"
            String refersTo = BizConstants.PersonsTemplate.BENEFIT_DROP_DOWN_OPTION_LIST
                    + "!$" + colLetter + "$" + startRow
                    + ":$" + colLetter + "$" + endRow;
            name.setRefersToFormula(refersTo);
        }
    }


    /**
     * 生成 Employee Category 下拉选项隐藏sheet（无表头）
     * 由于excel的命名范围名称中不能含部分特殊字符，所以这里做了个转换，将值映射为纯文本
     * 目的是在excel中通过提前设定好的公式，来获取用户单元格内选择的选项（这里就会做转换）并结合其他信息拼接成命名范围的名称
     */
    private void createEmployeeCategoryHiddenSheet(XSSFWorkbook wb, Map<String, String> employeeCategoryMapping) {
        // 创建隐藏的 sheet，用于存放下拉列表数据
        XSSFSheet sheet = wb.createSheet(BizConstants.PersonsTemplate.EMPLOYEE_CATEGORY_MAPPING);

        // 隐藏 sheet，不让用户看到
        wb.setSheetHidden(wb.getSheetIndex(sheet), true);

        // 创建文本格式样式
        XSSFCellStyle textStyle = wb.createCellStyle();
        XSSFDataFormat format = wb.createDataFormat();
        // "@" 表示文本格式
        textStyle.setDataFormat(format.getFormat("@"));

        // 当前写入的行号，从 0 开始
        int rowIdx = 0;

        // 写入自定义字典值
        for (Map.Entry<String, String> employeeCategoryMap : employeeCategoryMapping.entrySet()) {
            Row row = sheet.createRow(rowIdx++);

            // 创建单元格并设置文本格式
            Cell keyCell = row.createCell(0);
            keyCell.setCellStyle(textStyle);
            keyCell.setCellValue(employeeCategoryMap.getKey());

            Cell valueCell = row.createCell(1);
            valueCell.setCellStyle(textStyle);
            valueCell.setCellValue(employeeCategoryMap.getValue());
        }
    }

    /**
     * 构建Employee Category map映射
     */
    private Map<String, String> buildEmployeeCategoryCustomMapping(List<String> employeeCategoryOptionList) {
        Map<String, String> employeeCategoryMapping = new LinkedHashMap<>();
        for (int i = 0; i < employeeCategoryOptionList.size(); i++) {
            employeeCategoryMapping.put(employeeCategoryOptionList.get(i), String.valueOf(i));
        }
        return employeeCategoryMapping;
    }

    /**
     * 为每个动态 Plan 列添加下拉列表
     * 默认显式列表方式改为通过命名范围引用
     */
    private void addDropdownsForPlans(XSSFSheet sheet,
                                      int startCol,
                                      int count,
                                      int empCategoryCol) {
        // 获取数据验证辅助对象，用于创建下拉列表约束
        DataValidationHelper helper = sheet.getDataValidationHelper();

        // 定义基础偏移，表示动态列与Employee Category列的距离，此处主要是为了后面的公式中做使用
        int baseOffset = startCol - empCategoryCol;
        // 遍历每一列，为每列添加下拉列表
        for (int i = 0; i < count; i++) {
            // 当前列索引
            int col = startCol + i;
            // 获取当前列的表头文本，即 benefit
            String benefit = ExcelUtil.getString(sheet, BizConstants.PersonsTemplate.HEADER_ROW - 1, col);
            if (Objects.isNull(benefit)) {
                continue;
            }
            // 使用 INDIRECT 引用命名范围
            // 因为列是动态生成的，所以偏移量会发生变化
            int currentOffset = baseOffset + i;
            String formula = buildFormula(benefit, currentOffset);

            // 创建公式列表约束
            DataValidationConstraint constraint = helper.createFormulaListConstraint(formula);

            // 定义应用下拉列表的单元格区域
            // 行范围：数据开始行到最大行
            // 列范围：当前列到当前列（单列）
            CellRangeAddressList regions = new CellRangeAddressList(
                    BizConstants.PersonsTemplate.DATA_START_ROW - 1,
                    BizConstants.PersonsTemplate.DEFAULT_MAX_ROWS - 1,
                    col,
                    col
            );

            // 创建数据验证对象
            DataValidation validation = helper.createValidation(constraint, regions);
            // 设置下拉箭头显示（false表示显示箭头）
            validation.setSuppressDropDownArrow(false);
            // 设置显示错误提示框
            validation.setShowErrorBox(true);
            // 针对 XSSF 的一些版本兼容性处理
            // POI 4.x 某些版本需要此行，否则下拉箭头可能不显示
            if (validation instanceof XSSFDataValidation) {
                validation.setSuppressDropDownArrow(true);
            }

            // 将数据验证（下拉列表）应用到 sheet 中
            sheet.addValidationData(validation);
        }
    }

    /**
     * <p>构建 INDIRECT 函数公式</p>
     *
     * <p>功能说明：</p>
     * <p>该方法用于动态生成 Excel 中的 INDIRECT 函数公式。</p>
     *
     * <p>公式原理：</p>
     * <ol>
     *     <li>使用 INDIRECT 函数，将拼接出的命名范围字符串转换为实际的单元格引用。</li>
     *     <li>命名范围的命名规则为：benefit名称（小写） + "_" + 员工类别编号。</li>
     *     <li>员工类别映射的数字编号通过 VLOOKUP 在隐藏的映射表中查找得到。</li>
     * </ol>
     *
     * <p>特殊处理说明：</p>
     * <ul>
     *     <li>填写的 Employee Category（员工类别）可能包含特殊字符，而 Excel 命名范围不允许包含这些字符，直接引用会报错。</li>
     *     <li>为解决此问题，创建了隐藏的字典工作表（EmployeeCategoryMapping），将用户输入的 Employee Category 与数字编号进行映射。</li>
     *     <li>公式中使用 INDEX 获取当前行、偏移列的 Employee Category 填写的值，再用 VLOOKUP 查找对应的数字编号。</li>
     *     <li>将 benefit 前缀和编号拼接成合法命名范围名称，再通过 INDIRECT 获取最终单元格值。</li>
     * </ul>
     *
     * <p>示例（假设 benefit = "GTL"，currentOffset = 2）：</p>
     * <pre>
     * INDIRECT("gtl_" & VLOOKUP(
     *     INDEX($1:$1048576, ROW(), COLUMN() - 2),
     *     EmployeeCategoryMapping!A:B,
     *     2,
     *     FALSE
     * ))
     * </pre>
     *
     * @param benefit       命名范围的前缀（如 "GTL"、"GMM" 等）
     * @param currentOffset 当前列相对于 Employee Category 列的偏移量
     * @return 构建好的 Excel 公式字符串
     */
    private String buildFormula(String benefit, int currentOffset) {
        return "INDIRECT(\""
                + benefit.toLowerCase(Locale.ROOT)
                + "_\" & VLOOKUP(INDEX($1:$1048576,ROW(),COLUMN()-"
                + currentOffset
                + "), "
                + BizConstants.PersonsTemplate.EMPLOYEE_CATEGORY_MAPPING
                + "!A:B, 2, FALSE))";
    }

    /**
     * 为 Employee Category 列添加下拉列表（兼容 XSSF）
     */
    private void addDropdownForEmployeeCategory(XSSFSheet sheet,
                                                int empCategoryCol,
                                                List<String> optionList) {
        if (CollectionUtil.isEmpty(optionList)) {
            return;
        }

        DataValidationHelper helper = sheet.getDataValidationHelper();
        DataValidationConstraint constraint = helper.createExplicitListConstraint(optionList.toArray(new String[0]));

        // 设置下拉区域
        CellRangeAddressList regions = new CellRangeAddressList(
                BizConstants.PersonsTemplate.DATA_START_ROW - 1,
                BizConstants.PersonsTemplate.DEFAULT_MAX_ROWS - 1,
                empCategoryCol,
                empCategoryCol
        );

        // 创建下拉验证
        DataValidation validation = helper.createValidation(constraint, regions);

        // 兼容 XSSF 显示下拉箭头
        if (validation instanceof XSSFDataValidation) {
            validation.setSuppressDropDownArrow(true);
        }

        validation.setShowErrorBox(true);

        sheet.addValidationData(validation);
    }

}
