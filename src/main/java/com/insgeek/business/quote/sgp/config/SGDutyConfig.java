package com.insgeek.business.quote.sgp.config;

import com.insgeek.business.quote.sgp.config.dto.MutualExclusionConfigDto;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * @Description: 新加坡责任创建相关配置
 * @Date: 2025/8/30
 * @Param null:
 **/
@Getter
@Setter
@ToString
@Component
@RefreshScope
@ConfigurationProperties(prefix = "standard.sgp.duty")
public class SGDutyConfig {
    /**
     * @Description: 无需进行责任类型重复校验的产品类型
     * @Date: 2025/8/30
     * @Param null:
     **/
    private List<String> nonVaildRepeatProductList;
    /**
     * @Description: 单次新增的计划最大数
     * @Date: 2025/8/30
     * @Param null:
     **/
    private int maxPlanCount = 10;
    
    /**
     * @Description: 互斥校验配置
     * @Date: 2025/9/29
     * @Param null:
     **/
    private MutualExclusionConfigDto mutualExclusionCheckConfig;
}