package com.insgeek.business.quote.sgp.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.insgeek.boot.commons.exception.InsgeekException;
import com.insgeek.boot.web.consts.ResultCodeEnum;
import com.insgeek.boot.web.pojo.dto.CurrencyAmount;
import com.insgeek.boot.web.util.MessageUtil;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.enums.ChangeTypeEnum;
import com.insgeek.business.quote.quotation.dto.version.compare.quotation.QuotationConfigCompareSgpScene;
import com.insgeek.business.quote.sgp.dto.*;
import com.insgeek.business.quote.sgp.enums.QuotePLanStatusEnum;
import com.insgeek.business.quote.sgp.enums.YesNoEnum;
import com.insgeek.business.quote.sgp.service.QuotationSgpService;
import com.insgeek.business.quote.util.CommonUtil;
import com.insgeek.components.orm.model.impl.data.DataCondition;
import com.insgeek.components.orm.model.impl.data.DataMapper;
import com.insgeek.components.orm.model.querydsl.BQLQueryFactory;
import com.insgeek.insurance.meta.utils.context.IdentityUtil;
import com.insgeek.protocol.data.client.entity.*;
import com.insgeek.protocol.insurance.client.DutyClient;
import com.insgeek.protocol.insurance.client.SpecialInstanceClient;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontProductDutyInstanceDTO;
import com.insgeek.protocol.insurance.dto.special.instance.ListParamDTO;
import com.insgeek.protocol.insurance.entity.model.QIgProduct;
import com.insgeek.protocol.insurance.enums.SharedSpecialTypeEnum;
import com.insgeek.protocol.insurance.enums.SpecialSceneEnum;
import com.insgeek.protocol.insurance.vo.special.SharedSpecialListRequestVO;
import com.insgeek.protocol.insurance.vo.special.SharedSpecialResponseVO;
import com.insgeek.protocol.insurance.vo.special.SharedSpecialRuleVO;
import com.insgeek.protocol.insurance.vo.special.SharedSpecialSaveRequestVO;
import com.insgeek.protocol.platform.common.dto.entity.IgCompany;
import com.insgeek.protocol.platform.common.dto.entity.IgProduct;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Date: 2025-09-08 10:15
 * @Author: yang.li
 */

@Slf4j
@Setter
@Service
public class QuotationSgpServiceImpl implements QuotationSgpService {

    @Autowired
    private BQLQueryFactory bqlQueryFactory;
    @Autowired
    private DataMapper<QpQuotation> quotationMapper;
    @Autowired
    private DataMapper<QpQuotationConfig> quotationConfigDataMapper;
    @Autowired
    private DutyClient dutyClient;
    @Autowired
    private SpecialInstanceClient specialInstanceClient;
    @Autowired
    private SingaporeDutyExcelServiceImpl singaporeDutyExcelServiceImpl;
    @Autowired
    private QuotationConfigCompareSgpScene quotationConfigCompareSgpScene;

    @Resource
    DataMapper<IgCompany> companyDataMapper;

    @Resource(name = "batchCreateDutyPool")
    private ThreadPoolTaskExecutor threadPoolTaskExecutor;


    @Override
    public SingaporeDutyExcelPlanDTO compare(Long quotationId) {

        // 查询方案组信息
        QpQuotation quotation = quotationMapper.entity(QpQuotation.class).selectOne(quotationId, true);

        // 组装
        SingaporeDutyExcelPlanDTO singaporeDutyExcelPlanDTO = new SingaporeDutyExcelPlanDTO();
        singaporeDutyExcelPlanDTO.setQuotationInfoId(quotation.getQuotationInfoId());
        singaporeDutyExcelPlanDTO.setHistoryFlag(quotation.getHistoryFlag());
        singaporeDutyExcelPlanDTO.setPlanId(quotation.getId());
        singaporeDutyExcelPlanDTO.setPlanName(quotation.getName());
        singaporeDutyExcelPlanDTO.setEmployeeCategory(quotation.getCategory());
        singaporeDutyExcelPlanDTO.setStatus(quotation.getStatus());

        // 保司名称
        IgCompany company = companyDataMapper.entity(IgCompany.class).selectOne(quotation.getCompanyId(), true);
        if (company != null) {
            singaporeDutyExcelPlanDTO.setCompanyName(company.getCompanyName());
        }


        // 查询方案信息
        DataCondition<QpQuotationConfig> configCondition = new DataCondition<>();
        configCondition.eq("quotation_id", quotationId);
        List<QpQuotationConfig> quotationConfigList = quotationConfigDataMapper.entity(QpQuotationConfig.class).select(configCondition, true);

        // 总保费
        singaporeDutyExcelPlanDTO.setTotalPrice(sumPriceAmountSimple(quotationConfigList));

        List<SingaporeDutyExcelPlanConfigDTO> planConfigDTOList = quotationConfigList.stream().map(quotationConfig -> {
            SingaporeDutyExcelPlanConfigDTO planConfigDTO = new SingaporeDutyExcelPlanConfigDTO();
            planConfigDTO.setPlanConfigId(quotationConfig.getId());
            planConfigDTO.setPlanId(quotationConfig.getQuotationId());
            planConfigDTO.setPlanConfigName(quotationConfig.getName());
            planConfigDTO.setRemarks(quotationConfig.getRemarks());
            planConfigDTO.setParticipation(quotationConfig.getParticipation());
            planConfigDTO.setSumAssured(quotationConfig.getTotalAmount());
            planConfigDTO.setInsuranceTypeDetail(quotationConfig.getBenefit());
            planConfigDTO.setPrice(quotationConfig.getPrice());
            return planConfigDTO;
        }).collect(Collectors.toList());

        // config -> plan
        singaporeDutyExcelPlanDTO.setPlanConfigList(planConfigDTOList);

        // 从配置列表中提取配置ID
        List<Long> configList = singaporeDutyExcelServiceImpl.extractConfigIds(planConfigDTOList);

        assignDutiesToConfigs(configList, planConfigDTOList);
        // 查询共享保额配置信息，转为标准格式，并分配给对应的配置
        try {
            // 查询共享保额信息且添加对比信息
            assignSharedsToConfigs(singaporeDutyExcelPlanDTO);
        } catch (Exception e) {
            log.error("查询共享保额信息异常", e);
        }
        return singaporeDutyExcelPlanDTO;
    }

    public static CurrencyAmount sumPriceAmountSimple(List<QpQuotationConfig> configs) {
        if (configs == null) return CurrencyAmount.NULL;
        BigDecimal totalPrice = configs.stream()
                .filter(Objects::nonNull)
                .map(QpQuotationConfig::getPrice)
                .filter(Objects::nonNull)
                .map(CurrencyAmount::getAmount)
                .filter(Objects::nonNull)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        return new CurrencyAmount(totalPrice, configs.get(0).getPrice().getCurrency());
    }

    private void assignSharedsToConfigs(SingaporeDutyExcelPlanDTO singaporeDutyExcelPlanDTO) {

        // 查询所有产品信息
        List<SgpDutyDto> dutyList = queryDutyList(singaporeDutyExcelPlanDTO.getPlanId());

        // 构建dutyId到产品名称的映射
        Map<Long, String> productNameMap = new HashMap<>();
        dutyList.forEach(product -> {
            productNameMap.put(product.getId(), product.getName());
        });

        // 创建一个方案Id的映射map
        Map<Long, Long> configIdRelationMap = new HashMap<>();

        // configIds
        List<Long> configIdList = singaporeDutyExcelPlanDTO.getPlanConfigList().stream().map(SingaporeDutyExcelPlanConfigDTO::getPlanConfigId).collect(Collectors.toList());

        // 原始configIds
        configIdList.forEach(configId -> {
            Long originConfigId = quotationConfigCompareSgpScene.getOriginBindId(configId);
            configIdRelationMap.put(configId, originConfigId);
        });

        List<Long> queryConfigIdList = new ArrayList<>();
        // 把configIdRelationMap的所有的key 和 value 都加入得到queryConfigIdList
        queryConfigIdList.addAll(configIdRelationMap.keySet());
        queryConfigIdList.addAll(configIdRelationMap.values());

        // 查询共享保额特约
        SharedSpecialListRequestVO sharedSpecialListRequestVO = new SharedSpecialListRequestVO();
        List<String> stringList = queryConfigIdList.stream()
                .map(String::valueOf)
                .collect(Collectors.toList());
        sharedSpecialListRequestVO.setRelationIdList(stringList);
        sharedSpecialListRequestVO.setScene(SpecialSceneEnum.QUOTATION.getValue());
        ResponseVO<List<SharedSpecialResponseVO>> listResponseVO = specialInstanceClient.querySharedSpecialList(sharedSpecialListRequestVO);
        List<SharedSpecialResponseVO> sharedSpecialResponseVOs = listResponseVO.getData();

        // 遍历处理每一个config添加共享保额 特约的对比信息
        List<SingaporeDutyExcelPlanConfigDTO> planConfigList = singaporeDutyExcelPlanDTO.getPlanConfigList();
        planConfigList.forEach(planConfigDTO -> {
            Long configId = planConfigDTO.getPlanConfigId();

            // 待组装的共享保额特约
            List<SingaporeDutyExcelSharedDTO> sharedList = new ArrayList<>();

            // 筛选出所有的当前方案的共享保额特约
            List<SharedSpecialResponseVO> currenSpecialList = sharedSpecialResponseVOs.stream().filter(sharedSpecialResponseVO -> {
                return sharedSpecialResponseVO.getRelationId().equals(configId.toString());
            }).collect(Collectors.toList());

            //遍历currenSpecialList
            for (SharedSpecialResponseVO currenSpecial : currenSpecialList) {
                SingaporeDutyExcelSharedDTO shared = new SingaporeDutyExcelSharedDTO();
                shared.setPlanConfigId(Long.valueOf(currenSpecial.getRelationId()));
                shared.setSharedPlanName(null);
                String sharedLimit = getSharedLimit(currenSpecial);
                shared.setSharedLimit(sharedLimit);
                // 构建责任名称列表
                List<String> dutyNames = new ArrayList<>();
                List<Long> dutyIds = currenSpecial.getIdList();
                if (CollectionUtil.isNotEmpty(dutyIds)) {
                    for (Long dutyId : dutyIds) {
                        String productName = productNameMap.get(dutyId);
                        if (productName != null) {
                            dutyNames.add(productName);
                        }
                    }
                }
                shared.setDutyNameList(dutyNames);
                // 筛选出历史的共享特约
                List<SharedSpecialResponseVO> historySpecialList = sharedSpecialResponseVOs.stream().filter(sharedSpecialResponseVO -> {
                    return sharedSpecialResponseVO.getRelationId().equals(String.valueOf(configIdRelationMap.get(configId)))
                            && isEqualIgnoreOrder(sharedSpecialResponseVO.getIdList(), currenSpecial.getIdList());
                }).collect(Collectors.toList());
                if (CollectionUtils.isEmpty(historySpecialList)) {
                    shared.setOldValue(null);
                    shared.setChangeFlag(ChangeTypeEnum.ADD.getVal());
                } else {
                    String oldValue = getSharedLimit(historySpecialList.get(0));
                    shared.setOldValue(oldValue);
                    if (oldValue != null && oldValue.equals(sharedLimit)) {
                        // 无变化
                        shared.setChangeFlag(ChangeTypeEnum.UNCHANGED.getVal());
                    } else {
                        // 更新
                        shared.setChangeFlag(ChangeTypeEnum.UPDATE.getVal());
                    }
                }
                sharedList.add(shared);
            }

            // 删除的特约处理
            List<List<Long>> currenSpecialListAll = currenSpecialList.stream().map(SharedSpecialResponseVO::getIdList).collect(Collectors.toList());

            List<SharedSpecialResponseVO> deletedSpecialList = sharedSpecialResponseVOs.stream().filter(sharedSpecialResponseVO -> {
                return sharedSpecialResponseVO.getRelationId().equals(String.valueOf(configIdRelationMap.get(configId)))
                        && isInListIgnoreOrder(sharedSpecialResponseVO.getIdList(), currenSpecialListAll);
            }).collect(Collectors.toList());
            for (SharedSpecialResponseVO deletedSpecial : deletedSpecialList) {
                SingaporeDutyExcelSharedDTO deleteShared = new SingaporeDutyExcelSharedDTO();
                deleteShared.setPlanConfigId(Long.valueOf(deletedSpecial.getRelationId()));
                deleteShared.setSharedPlanName(null);
                deleteShared.setSharedLimit(null);
                // 构建责任名称列表
                List<String> dutyNames = new ArrayList<>();
                List<Long> dutyIds = deletedSpecial.getIdList();
                if (CollectionUtil.isNotEmpty(dutyIds)) {
                    for (Long dutyId : dutyIds) {
                        String productName = productNameMap.get(dutyId);
                        if (productName != null) {
                            dutyNames.add(productName);
                        }
                    }
                }
                deleteShared.setDutyNameList(dutyNames);
                deleteShared.setOldValue(getSharedLimit(deletedSpecial));
                deleteShared.setChangeFlag(ChangeTypeEnum.REMOVE.getVal());

                sharedList.add(deleteShared);
            }

            planConfigDTO.setSharedList(sharedList);
            Long originConfigId = configIdRelationMap.get(planConfigDTO.getPlanConfigId());
            if (originConfigId == null || originConfigId == 0) return;
            QpQuotationConfig originConfig = quotationConfigDataMapper.entity(QpQuotationConfig.class).selectOne(originConfigId, true);

            String o = originConfig.getRemarks();
            String n = planConfigDTO.getRemarks();
            if (!StringUtils.isBlank(o) || !StringUtils.isBlank(n)) {
                if (!Objects.equals(o, n)) {
                    planConfigDTO.setOldRemarks(StringUtils.isEmpty(o) ? " " : o);
                }
            }
        });
    }


    //获取共享保额信息
    public String getSharedLimit(SharedSpecialResponseVO sharedSpecialResponseVO) {
        // 从共享数据中获取保额值
        Map<String, Object> shareData = sharedSpecialResponseVO.getShareData();
        if (shareData != null && shareData.containsKey(SharedSpecialTypeEnum.ItemRangeEnum.CRAYF2I6BFD_AMOUNT.getItemKey())) {
            Object amountObj = shareData.get(SharedSpecialTypeEnum.ItemRangeEnum.CRAYF2I6BFD_AMOUNT.getItemKey());
            if (Objects.nonNull(amountObj) && amountObj instanceof Map) {
                return ((Map<String, String>) amountObj).get(SharedSpecialTypeEnum.ItemRangeEnum.CRAYF2I6BFD_AMOUNT.getItemKey());
            }
        }
        return null;
    }

    /**
     * 查询责任信息并分配给对应的配置
     *
     * @param configList        配置ID列表
     * @param planConfigDTOList 配置列表
     */
    private void assignDutiesToConfigs(List<Long> configList, List<SingaporeDutyExcelPlanConfigDTO> planConfigDTOList) {
        ListParamDTO params = new ListParamDTO();
        params.setScene(3);
        params.setType(2);
        params.setIdList(configList);
        // 报价方案和未拆分保司的原始方案进行对比
        ResponseVO<List<FrontProductDutyInstanceDTO>> resp = dutyClient.batchGetDutyListByConfigForFront(params.getScene(), 4, params);
        List<FrontProductDutyInstanceDTO> frontProductDutyInstanceList = CommonUtil.getResponseData(resp);
        if (CollectionUtils.isEmpty(frontProductDutyInstanceList)) return;

        // 根据FrontProductDutyInstanceDTO中的参数Map<String, Object> dutyField中的key为view_plan_config_id，
        Map<Long, List<FrontProductDutyInstanceDTO>> dutyInstanceMap = singaporeDutyExcelServiceImpl.buildDutyInstanceMapByViewPlanConfigId(frontProductDutyInstanceList);

        // 将责任列表分配给对应的配置
        for (SingaporeDutyExcelPlanConfigDTO configDTO : planConfigDTOList) {
            List<FrontProductDutyInstanceDTO> dutyList = dutyInstanceMap.get(configDTO.getPlanConfigId());
            configDTO.setFrontProductDutyInstanceList(dutyList);
        }
    }

    private List<SgpDutyDto> queryDutyList(Long quotationId) {
        if (quotationId == null) {
            return Collections.emptyList();
        }
        QQpQuotationDuty qpQuotationDuty = QQpQuotationDuty.qp_quotation_duty;
        QQpQuotationConfig qpQuotationConfig = QQpQuotationConfig.qp_quotation_config;
        QIgProduct qIgProduct = QIgProduct.ig_product;
        return bqlQueryFactory.select(qpQuotationDuty.id.as("id"), qIgProduct.name.as("name"))
                .from(qpQuotationConfig)
                .leftJoin(qpQuotationDuty)
                .on(qpQuotationConfig.id.eq(qpQuotationDuty.quotation_config_id))
                .leftJoin(qIgProduct)
                .on(qpQuotationDuty.product_id.eq(qIgProduct.id))
                .where(qpQuotationConfig.quotation_id.eq(quotationId))
                .findList(true, SgpDutyDto.class);
    }

    public static boolean isEqualIgnoreOrder(List<Long> list1, List<Long> list2) {
        if (list1 == list2) return true; // 同一引用
        if (list1 == null || list2 == null) return false;
        if (list1.size() != list2.size()) return false;

        return new HashSet<>(list1).equals(new HashSet<>(list2));
    }

    //
    public static boolean isInListIgnoreOrder(List<Long> list1, List<List<Long>> list2) {
        if (list2 == null) return false;
        for (List<Long> candidate : list2) {
            if (isEqualIgnoreOrder(list1, candidate)) {
                return true;
            }
        }
        return false;
    }


    @Override
    public void saveQuotation(SingaporeDutyExcelPlanDTO planDTO) {

        try {
            // 检查planDTO.status=2的时候如果所有planDTO.getPlanConfigList()的price都是空的话报错
            boolean allPriceEmpty = Objects.equals(planDTO.getStatus(), QuotePLanStatusEnum.QUOTE_RECEIVED.getCode())
                    && planDTO.getPlanConfigList() != null
                    && planDTO.getPlanConfigList().stream()
                    .allMatch(config -> config.getPrice() == null);
            if (allPriceEmpty) {
                throw new InsgeekException(ResultCodeEnum.FAILED.getCode(), MessageUtil.get("b_b_quote_sgp_054"));
            }
            // 更新方案
            updateQuotationPlan(planDTO);

            // 补全每个配置的方案id
            List<SingaporeDutyExcelPlanConfigDTO> planConfigList = planDTO.getPlanConfigList();
            for (SingaporeDutyExcelPlanConfigDTO planConfigDTO : planConfigList) {
                planConfigDTO.setPlanId(planDTO.getPlanId());
            }
            // 批量创建配置并补全配置id
            batchSaveOrUpdateQuoteConfig(planConfigList);

            // 多线程批次创建每个配置的责任信息
            saveAllDutyInfo(planConfigList);

            // 保存所有共享保额信息
            saveAllSharedLimitInfo(planDTO);
        } catch (InsgeekException e) {
            throw e;
        } catch (Exception e) {
            log.error("saveQuotation，错误信息", e);
            throw new InsgeekException(ResultCodeEnum.FAILED.getCode(), MessageUtil.get("b_sg_excel_error_012"));
        }
    }

    @Override
    public List<SingaporeDutyExcelPlanDTO> quotations(Long quotationInfoId) {
        List<SingaporeDutyExcelPlanDTO> result = new ArrayList<>();
        DataCondition<QpQuotation> condition = new DataCondition<>();
        condition.eq("quotation_info_id", quotationInfoId);
        List<QpQuotation> quotationList = quotationMapper.entity(QpQuotation.class).select(condition, true);
        quotationList.forEach(quotation -> {
            // 只查看原始方案
            if (quotation.getCompanyId() != null) return;
            if (quotation.getHistoryFlag() == null || quotation.getHistoryFlag().equals(YesNoEnum.YES.getValue())) return;
            SingaporeDutyExcelPlanDTO q = compare(quotation.getId());
            result.add(q);
        });
        return result;
    }

    private void updateQuotationPlan(SingaporeDutyExcelPlanDTO planDto) {
        QpQuotation qpQuotation = new QpQuotation();
        qpQuotation.setId(planDto.getPlanId());
        qpQuotation.setName(planDto.getPlanName());
        qpQuotation.setCategory(planDto.getEmployeeCategory());
        qpQuotation.setStatus(planDto.getStatus());// 更新状态
        //qpQuotation.setQuotationInfoId(planDto.getQuotationInfoId());
        //qpQuotation.setHistoryFlag(planDto.getHistoryFlag());
        quotationMapper.entity(QpQuotation.class).updateOne(qpQuotation, true);
    }

    private void batchSaveOrUpdateQuoteConfig(List<SingaporeDutyExcelPlanConfigDTO> singaporeDutyExcelPlanConfigDTOs) {
        if (CollectionUtil.isEmpty(singaporeDutyExcelPlanConfigDTOs)) {
            return;
        }
        List<QpQuotationConfig> saveList = new ArrayList<>();
        List<Map<String, Object>> updateList = new ArrayList<>();
        for (SingaporeDutyExcelPlanConfigDTO configDTO : singaporeDutyExcelPlanConfigDTOs) {
            QpQuotationConfig qpQuotationConfig = new QpQuotationConfig();
            qpQuotationConfig.setQuotationId(configDTO.getPlanId());
            qpQuotationConfig.setName(configDTO.getPlanConfigName());
            qpQuotationConfig.setBenefit(configDTO.getInsuranceTypeDetail());
            qpQuotationConfig.setRemarks(configDTO.getRemarks());
            qpQuotationConfig.setParticipation(configDTO.getParticipation());
            qpQuotationConfig.setTotalAmount(configDTO.getSumAssured());
            qpQuotationConfig.setPrice(configDTO.getPrice());
            if (configDTO.getPlanConfigId() != null) {
                qpQuotationConfig.setId(configDTO.getPlanConfigId());
                Map<String, Object> updateMap = new HashMap<>();
                updateMap.put("name", qpQuotationConfig.getName());
                updateMap.put("benefit", qpQuotationConfig.getBenefit());
                updateMap.put("remarks", qpQuotationConfig.getRemarks());
                updateMap.put("participation", qpQuotationConfig.getParticipation());
                updateMap.put("total_amount", qpQuotationConfig.getTotalAmount());
                updateMap.put("price", qpQuotationConfig.getPrice());
                updateMap.put("id", qpQuotationConfig.getId());
                updateList.add(updateMap);
            } else {
                saveList.add(qpQuotationConfig);
            }
        }
        if (CollectionUtil.isNotEmpty(updateList)) {
            quotationConfigDataMapper.entity(QpQuotationConfig.class).batchSuperUpdate(updateList);
        }
        if (CollectionUtil.isNotEmpty(saveList)) {
            quotationConfigDataMapper.entity(QpQuotationConfig.class).insertAll(saveList);
        }
        // 为新增的配置，设置ID
        for (QpQuotationConfig qpQuotationConfig : saveList) {
            singaporeDutyExcelPlanConfigDTOs.stream()
                    .filter(dto -> dto.getPlanConfigName().equals(qpQuotationConfig.getName()))
                    .findFirst()
                    .ifPresent(dto -> {
                        // 设置配置的id
                        dto.setPlanConfigId(qpQuotationConfig.getId());
                        // 为共享保额设置配置id信息
                        if (CollectionUtil.isNotEmpty(dto.getSharedList())) {
                            dto.getSharedList().forEach(shared -> shared.setPlanConfigId(qpQuotationConfig.getId()));
                        }
                        // 为责任设置配置id
                        for (FrontProductDutyInstanceDTO dutyDTO : dto.getFrontProductDutyInstanceList()) {
                            if (dutyDTO != null) {
                                if (dutyDTO.getDutyField() == null) {
                                    dutyDTO.setDutyField(new HashMap<>());
                                }
                                dutyDTO.getDutyField().put("plan_config_id", dto.getPlanConfigId());
                            }
                        }
                    });
        }
        for (Map<String, Object> qpQuotationConfig : updateList) {
            singaporeDutyExcelPlanConfigDTOs.stream()
                    .filter(dto -> dto.getPlanConfigName().equals(qpQuotationConfig.get("name")))
                    .findFirst()
                    .ifPresent(dto -> {
                        // 设置配置的id
                        dto.setPlanConfigId(Long.parseLong(qpQuotationConfig.get("id").toString()));
                        // 为共享保额设置配置id信息
                        if (CollectionUtil.isNotEmpty(dto.getSharedList())) {
                            dto.getSharedList().forEach(shared -> shared.setPlanConfigId(Long.parseLong(qpQuotationConfig.get("id").toString())));
                        }
                        // 为责任设置配置id
                        for (FrontProductDutyInstanceDTO dutyDTO : dto.getFrontProductDutyInstanceList()) {
                            if (dutyDTO != null) {
                                if (dutyDTO.getDutyField() == null) {
                                    dutyDTO.setDutyField(new HashMap<>());
                                }
                                dutyDTO.getDutyField().put("plan_config_id", dto.getPlanConfigId());
                            }
                        }
                    });
        }
    }

    private void saveAllDutyInfo(List<SingaporeDutyExcelPlanConfigDTO> planConfigList) {
        List<CompletableFuture<Void>> futures = new ArrayList<>();
        for (SingaporeDutyExcelPlanConfigDTO planConfigDTO : planConfigList) {
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                Long configId = planConfigDTO.getPlanConfigId();
                List<FrontProductDutyInstanceDTO> frontProductDutyInstanceList = planConfigDTO.getFrontProductDutyInstanceList();
                // 将每个责任的dutyField中添加productTypeName,其值为productTypeName
                if (frontProductDutyInstanceList != null) {
                    frontProductDutyInstanceList.forEach(duty -> {
                        if (duty.getDutyField() != null && duty.getProductTypeName() != null) {
                            duty.getDutyField().put("productTypeName", duty.getProductTypeName());
                        }
                    });
                    ResponseVO<Object> dutyForFront = dutyClient.createDutyForFront(6, configId, frontProductDutyInstanceList);
                    CommonUtil.getResponseData(dutyForFront);
                }
            }, threadPoolTaskExecutor);
            futures.add(future);
        }
        // 等待所有任务完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).join();
    }

    /**
     * @Description: 调用核心保存共享保额信息
     * @Date: 2025/9/8
     * @Param singaporeDutyExcelPlanDTOS:
     **/
    private void saveAllSharedLimitInfo(SingaporeDutyExcelPlanDTO planDTO) {
        // 获取保存的责任id与产品名称的映射
        List<SgQuotationDutyQueryDto> sgDutyQueryDtoList = queryQuotationDutyList(planDTO.getQuotationInfoId());
        // sgDutyQueryDtoList 根据构建configId-productName和dutyId的映射关系
        Map<String, Long> dutyIdProductNameMap = new HashMap<>();
        sgDutyQueryDtoList.forEach(sgDutyQueryDto -> {
            dutyIdProductNameMap.put(sgDutyQueryDto.getQuotationConfigId() + "-" + sgDutyQueryDto.getProductName(), sgDutyQueryDto.getId());
        });
        // 遍历所有方案下所有配置的共享保额区域
        List<SharedSpecialRuleVO> dataList = new ArrayList<>();

        List<SingaporeDutyExcelPlanConfigDTO> planConfigList = planDTO.getPlanConfigList();
        for (SingaporeDutyExcelPlanConfigDTO planConfigDTO : planConfigList) {
            Long planConfigId = planConfigDTO.getPlanConfigId();
            List<SingaporeDutyExcelSharedDTO> sharedList = planConfigDTO.getSharedList();
            if (planConfigId != null) {

                if (CollectionUtil.isNotEmpty(sharedList)) {
                    for (SingaporeDutyExcelSharedDTO sharedDTO : sharedList) {
                        // 构建调用保存特约内容的参数SharedSpecialRuleVO,其中
                        SharedSpecialRuleVO sharedSpecialRuleVO = new SharedSpecialRuleVO();
                        sharedSpecialRuleVO.setRelationId(planConfigId.toString());
                        sharedSpecialRuleVO.setShareApiKey(SharedSpecialTypeEnum.GENERAL_LIABILITY_SHARED_AMOUNT.getCode());
                        List<Long> sharedIdList = new ArrayList<>();
                        for (String dutyName : sharedDTO.getDutyNameList()) {
                            sharedIdList.add(dutyIdProductNameMap.get(sharedDTO.getPlanConfigId() + "-" + dutyName));
                        }
                        sharedSpecialRuleVO.setIdList(sharedIdList);
                        // 遍历枚举类的参数列表，设置需要传递的map参数
                        Map<String, Object> shareDataMap = new HashMap<>();
                        shareDataMap.put(SharedSpecialTypeEnum.ItemRangeEnum.CRAYF2I6BFD_AMOUNT.getItemKey(), CurrencyAmount.valueOf(Long.parseLong(sharedDTO.getSharedLimit()), IdentityUtil.getUserInfo().getBusinessCurrency()));
                        shareDataMap.put(SharedSpecialTypeEnum.ItemRangeEnum.CRAYF2I6BFD_RELATION_ID.getItemKey(), planConfigId.toString());
                        sharedSpecialRuleVO.setShareData(shareDataMap);
                        dataList.add(sharedSpecialRuleVO);
                    }
                }
            }
        }


        SharedSpecialSaveRequestVO sharedSpecialSaveRequestVO = new SharedSpecialSaveRequestVO();
        sharedSpecialSaveRequestVO.setScene(SpecialSceneEnum.QUOTATION.getValue());
        sharedSpecialSaveRequestVO.setShareList(dataList);
        try {
            specialInstanceClient.saveSharedSpecial(sharedSpecialSaveRequestVO);
        } catch (Exception e) {
            log.error("报价-保存共享保额区域失败", e);
            throw new InsgeekException(ResultCodeEnum.FAILED.getCode(), MessageUtil.get("b_sg_excel_error_011"));
        }
    }


    private List<SgQuotationDutyQueryDto> queryQuotationDutyList(Long quoteInfoId) {
        if (quoteInfoId == null) {
            return Collections.emptyList();
        }
        QQpQuotationDuty qpQuotationDuty = QQpQuotationDuty.qp_quotation_duty;
        QQpQuotation qpQuotation = QQpQuotation.qp_quotation;
        QQpQuotationConfig qpQuotationConfig = QQpQuotationConfig.qp_quotation_config;
        QIgProduct qIgProduct = QIgProduct.ig_product;
        return bqlQueryFactory.select(qpQuotationDuty.quotation_config_id.as("quotation_config_id"), qpQuotationDuty.id.as("id"), qpQuotationDuty.product_id.as("product_id"), qIgProduct.name.as("product_name"))
                .from(qpQuotation)
                .leftJoin(qpQuotationConfig)
                .on(qpQuotation.id.eq(qpQuotationConfig.quotation_id))
                .leftJoin(qpQuotationDuty)
                .on(qpQuotationConfig.id.eq(qpQuotationDuty.quotation_config_id))
                .leftJoin(qIgProduct)
                .on(qpQuotationDuty.product_id.eq(qIgProduct.id))
                .where(qpQuotation.quotation_info_id.eq(quoteInfoId))
                .findList(true, SgQuotationDutyQueryDto.class);
    }


}
