package com.insgeek.business.quote.sgp.enums;

import lombok.Getter;
import lombok.ToString;

/**
 * @Description: 保司和模版的对应关系
 * @Date: 2025-08-29  14:03
 * @Author: siyuan
 */
@Getter
@ToString
public enum PolicyTemplateTypeEnum {
    AIA("AIA", "AIA.ftl"),
    SINGLIFE("SINGLIFE", "SINGLIFE.ftl"),
    QBE("QBE", "QBE.ftl"),
    INCOME("INCOME", "INCOME.ftl"),
     // HSBC("HSBC", "HSBC.ftl"),
     // LIBERTY("LIBERTY", "LIBERTY.ftl"),
    ;

    private final String policyName;
    private final String templateName;

    PolicyTemplateTypeEnum(String policyName, String templateName) {
        this.policyName = policyName;
        this.templateName = templateName;
    }

    /**
     * 根据产品简称获取产品编码
     * @param policyName
     * @return
     */
    public static PolicyTemplateTypeEnum getEnumByProductCode(String policyName) {
        for (PolicyTemplateTypeEnum value : PolicyTemplateTypeEnum.values()) {
            if (value.policyName.equalsIgnoreCase(policyName)) {
                return value;
            }
        }
        return null;
    }
}
