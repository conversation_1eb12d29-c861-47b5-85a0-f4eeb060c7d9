
package com.insgeek.business.quote.sgp.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.insgeek.boot.commons.exception.InsgeekException;
import com.insgeek.boot.web.consts.ResultCodeEnum;
import com.insgeek.boot.web.util.MessageUtil;
import com.insgeek.boot.web.vo.ResponseVO;
import com.insgeek.business.quote.sgp.dto.ExcelExportReqDto;
import com.insgeek.business.quote.sgp.dto.SingaporeDutyExcelDutyDTO;
import com.insgeek.business.quote.sgp.dto.SingaporeDutyExcelPlanDTO;
import com.insgeek.business.quote.sgp.service.SingaporeDutyExcelService;
import com.insgeek.protocol.insurance.dto.product.request.front.FrontProductDutyInstanceDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import java.util.Objects;

/**
 * @Description: 新加坡批量上传责任接口
 * @Date: 2025/8/22
 * @Param null:
 **/
@RestController
@RequestMapping("/v1/standard/sgp/duty")
@Slf4j
public class SGDutyUpoladController {

    @Autowired
    private SingaporeDutyExcelService singaporeDutyExcelService;

    /**
     * @Description: 下载导入责任信息的excel模板
     * @Date: 2025/8/26
     * @Param null:
     **/
    @PostMapping("/excel/download")
    public void downloadTemplate(HttpServletResponse response) throws IOException {
        singaporeDutyExcelService.downloadTemplate(response);
    }
    /**
     * 上传并解析Excel
     */
    @PostMapping("/excel/upload")
    public ResponseVO uploadExcel(@RequestParam("file") MultipartFile file) throws InsgeekException {
        return ResponseVO.builder().data(singaporeDutyExcelService.convertExcelToQuotePlan(file)).build();
    }


    /**
     * 提交方案、配置、责任保存请求
     * @param quoteInfoId 报价信息ID
     * @param singaporeDutyExcelPlanDTOS 方案信息
     */

    @PostMapping("/excel/submit")
    public ResponseVO uploadExcel(@RequestParam("quote_info_id") Long quoteInfoId,@RequestParam(value = "history_flag",defaultValue = "0") String historyFlag,@RequestBody List<SingaporeDutyExcelPlanDTO> singaporeDutyExcelPlanDTOS) throws InsgeekException {
        if (Objects.isNull(quoteInfoId) || CollectionUtil.isEmpty(singaporeDutyExcelPlanDTOS)) {
            return ResponseVO.builder().code(ResultCodeEnum.FAILED.getCode()).message(MessageUtil.get("b_sg_excel_error_008")).build();
        }
        // 设置报价id和历史标识
        singaporeDutyExcelPlanDTOS.forEach(planDTO ->{
            planDTO.setHistoryFlag(historyFlag);
            planDTO.setQuoteInfoId(quoteInfoId);
        });
        // 判断是否提交成功，如果成功，则返回成功数据，否则返回失败数据;成功后会填充方案ID，失败会填充失败信息
        boolean submitted = singaporeDutyExcelService.submitExcelToQuotePlan(singaporeDutyExcelPlanDTOS);
        if (submitted){
            return ResponseVO.builder().data(singaporeDutyExcelPlanDTOS).build();
        }
        return ResponseVO.builder().code(ResultCodeEnum.FAILED.getCode()).message(MessageUtil.get("b_sg_excel_error_014")).data(singaporeDutyExcelPlanDTOS).build();
    }

    /**
     * @Description: 根据方案id查询方案配置责任信息
     * @Date: 2025/8/27
     * @Param null:
     **/
    @GetMapping("/excel/query-finish-plan")
    public ResponseVO queryFinishPlan(@RequestParam(value = "quote_info_id") Long quoteInfoId,@RequestParam(value = "history_flag",defaultValue = "0") String historyFlag) {
        return ResponseVO.builder().data(singaporeDutyExcelService. queryFinishPlanByQuote(quoteInfoId,historyFlag)).build();
    }
    /**
     * @Description: 根据方案导出 Excel
     * @Date: 2025/8/25
     * @Param planId:
     **/
    @PostMapping("/excel/export")
    public void exportPlan(@RequestBody ExcelExportReqDto excelExportReqDto , HttpServletResponse response) throws InsgeekException, IOException {
        singaporeDutyExcelService.exportPlanExcel(excelExportReqDto, null, response);
    }
    /**
     * @Description: 转化责任报价到展示信息
     * @Date: 2025/8/25
     * @Param file:
     **/
    @PostMapping("/excel/convert")
    public SingaporeDutyExcelDutyDTO convertDuty(@RequestBody FrontProductDutyInstanceDTO frontProductDutyInstanceDTO) throws InsgeekException {
        return singaporeDutyExcelService.convertDutyToExportDuty(frontProductDutyInstanceDTO);
    }
}