package com.insgeek.business.quote.sgp.config.dto;

import lombok.Data;
import java.util.List;

/**
 * 互斥配置DTO类
 */
@Data
public class MutualExclusionConfigDto {
    
    /**
     * 互斥产品配置
     */
    private List<MutualExclusionProductDto> mutualExclusionProduct;
    
    /**
     * 产品字段互斥项配置
     */
    private List<MutualExclusionItemDto> mutualExclusionItem;
    
    /**
     * 同名产品配置关联，即以第一个产品的值为准，后续其他产品的配置项都以第一个产品为准
     */
    private List<AssociatedSameNameConfigDto> associatedSameNameConfig;
}